Go to http://github.com/plack/Plack/issues for the roadmap and known issues.

1.0051  2024-01-05 15:09:37 PST
    [BUG FIXES]
        - Fix a bug where $req->headers can pick up non-PSGI header values from environment #703

    [IMPROVEMENTS]
        - Performance improvements on StackTrace middleware by avoiding overloaded methods #697
        - Added tests for Plack::Runner::mangle_host_port_socket #700

1.0050  2022-09-05 08:46:44 PDT
    [BUG FIXES]
        - Revert #660 where POST requests failed when Middleware::St<PERSON> was used with a code in `path` (#683)

1.0049  2022-09-01 10:43:42 PDT
    [IMPROVEMENTS]
        - Fix typos in docs #659
        - Test improvements, including moving from Travis CI to GitHub
        - Restrict non-GET/HEAD requests to App::File #662
        - Improved nginx FastCGI example #675
        - Added documentation about port 5000 for MacOS Montery
        - Added verify_SSL in Plack::LWPish #677
        - Added 'force' option to IIS6ScriptNameFix #679

1.0048  2020-11-29 16:20:00 PST
    [IMPROVEMENTS]
        - Updated documentation for cotent_length and content_type #625
        - Allow hyphens in file extensions for custom MIME types #614
        - Updated some python test script to work with Python 3 #639
    [BUG FIXES]
         - Fix HTTP::Message::PSGI to work with delayed writer without content #653
         - Plackup reloader allows restarting the server when the path contains .git or .svn, just not at the beginning of the path #632
         - Added MIME types for .webp, .ttf and .xlsx files (rrwo)
         - Fix Plack::Request content to not error when Content-Type is empty #655

1.0047  2018-02-10 01:23:37 PST
    [BUG FIXES]
        - Disable FCGI/lighttpd test that was supposed to be releng only #611

1.0046  2018-02-09 23:51:10 PST
    [NEW FEATURES]
        - Support psgix.cleanup and psgix.harakiri in FCGI handler (afresh1) #610

    [IMPROVEMENTS]
        - Do not set TCP_NODELAY when it's unavailable in embedded systems (dex4er) #579

1.0045  2017-12-31 12:40:52 PST
    [BUG FIXES]
        - Protect WrapCGI against SIGCHLD handlers #596

    [IMPROVEMENTS]
        - Set Content-Length to 0 in XSendfile middleware #602
        - Document options for XSendfile middleware
        - Remove #foo file for testing that was causing issues on Win32 systems #599
        - Add 103 Early Hints to Plack::Handler::CGI

1.0044  2017-04-26 22:30:27 PDT
    [IMPROVEMENTS]
        - Allow passing an already-open listen socket to HTTP::Server::PSGI
          and add an option to do so in Plack::Test::Server.

1.0043  2017-02-21 19:00:31 PST
    [IMPROVEMENTS]
        - Fixed typo in docs #578 #584
        - Remove test that expects cookie to be delimited by comma #573
        - Stop upper-casing all header keys in Plack::Request->headers #585
        
1.0042  2016-09-28 22:37:33 PDT
    [BUG FIXES]
        - Revert: Allow passing an already-open listen socket to HTTP::Server::PSGI #550

1.0041  2016-09-25 14:24:07 PDT
    [BUG FIXES]
        - Fix ConditionalGET middleware in case both Last-Modified and ETag exist. #569
        - Handle Cookie values with quotes correctly (alh, kazeburo) #564, #567

    [IMPROVEMENTS]
        - Several documentation fixes and error message improvements #556, #557, #558, #559

1.0040  2016-04-01 09:57:06 PDT
    [INCOMPATIBLE CHANGES]
        - Fixes a mistake in the value of %D and %T in Accesslog::Timed middleware.
          This is due to a bug in Apache::LogFormat::Compiler that sets a wrong value
          for these fields. This bug has been fixed and now it emits what the documentation
          has always said, in the same way as how Apache's log format works.

          However, this is a BREAKING CHANGE if you are using '%D' or '%T' in your log
          formats, and you'll likely need to swap them if you need the same values as
          previously. Read https://github.com/plack/Plack/issues/549 for more details.
          (astj) #549, #551

    [IMPROVEMENTS]
        - Remove the use of HTTP::Body in favor of HTTP::Entity::Parser (kazeburo) #538
        - Increase the buffer size to 1MB for requests already buffered by the PSGI server
        - Allow passing an already-open listen socket to HTTP::Server::PSGI (ilmari) #550

    [BUG FIXES]
        - Lint middleware now allows blessed code refs as a PSGI app (smcmurray) #542
        - Fix log4perl tests to pass when running in parallel (rsimoes) #545

1.0039  2015-12-06 03:27:59 PST
    [BUG FIXES]
        - Revert the change to use the faster header builder because it breaks the legit
          Plack::Response constructor that sets HTTP::Header object directly (#541)

1.0038  2015-11-25 12:36:29 PST
    [IMPROVEMENTS]
        - Added MIME type for apk files #514 (allanwhiteford)
        - Allow passing custom formatters to Plack::Middleware::AccessLog #515 (frew)
        - Allow '0' be a valid category for log4perl middleware #523
        - Doc fix for Plack::Runner #528 (polettix)
        - Doc improvements for -R/-M command line #529 (mickeyn)
        - Bind test server explicitly on 127.0.0.1, not 0.0.0.0
        - Support single-process manager in FCGI #535 (ianburrell)
        - Optimize Plack::Response header builder #540 (kazeburo)

1.0037  2015-06-19 10:01:31 PDT
    [Improvements]
        - Lint: Support HTTP/2 in SERVER_PROTOCOL (kazuho) #511
        - Bump HTTP::Headers::Fast dependency #512

1.0036  2015-06-03 12:01:53 PDT
    [BUG FIXES]
        - Fix CGIBin test to not use CGI.pm #509

1.0035  2015-04-16 10:08:21 CEST
    [BUG FIXES]
        - Fixed parsing of empty query string pairs (aristotle) #500

    [IMPROVEMENTS]
        - Documentation updates for FCGI (otrosien) #494
        - Use HTTP::Headers::Fast in Plack::Request
        - Big performance optimizations on Plack::Util::header_* (aristotle) #498
        - Added .webm to Plack::MIME (marlencrabapple) #503
        - Use Cookie::Baker to bake cookies in Plack::Response (oalders)
        - reduced the size of distribution by making binary files smaller

1.0034  2015-02-02 21:42:42 CET
    [SECURITY]
        - Fixed a possible directory traversal with Plack::App::File on Win32 (sri)

    [IMPROVEMENTS]
        - Documentation improvements (timbunce, oalders, autarch, frioux)
        - Avoid warnings in Plack::App::WrapCGI (frioux)

1.0033  2014-10-23 12:33:18 PDT
    [BUG FIXES]
        - Lint: Fix error messages (fgabolde) #473
        - ErrorDocument: Reverse the $done filtering (nwellnhof) #474

    [IMPROVEMENTS]
        - StackTrace: Improve the accuracy of thrown exceptions in case where an excpetion
          is thrown in destructors (nwellnhof) #476

1.0032  2014-10-04 11:13:24 PDT
    [IMPROVEMENTS]
        - Support Server::Starter in FCGI handler (yuryu) #435
        - Various documentation fixes

1.0031  2014-08-01 13:19:14 PDT
    [SECURITY]
        - Plack::App::File would previously strip trailing slashes off
          provided paths. This in combination with the common pattern
          of serving files with Plack::Middleware::Static could allow
          an attacker to bypass a whitelist of generated files (avar) #446

    [IMPROVEMENTS]
        - Let HTTP::Message::PSGI warn in case of invalid PSGI response (wchristian) #437
        - Update documentation on how response_cb works with writer (doy)
        - Make AccessLog work on non-POSIX environment (dex4er) #442
        - Plack::App::WrapCGI no longer warns under 5.19.9 (frew)
        - Avoid Rosetta Flash attack in JSONP middleware (nichtich) #464
        - Fix Plack::Util::inline_object to make it work with can() as a class method

    [NEW FEATURES]
        - Add $req->query_string shortcut to access QUERY_STRING in PSGI environment

1.0030  2013-11-23 08:54:01 CET
    [IMPROVEMENTS]
        - Middleware::LogDispatch stringifies objects (oalders) #427
        - Encode wide character strings as UTF-8 in HTTP::Server::PSGI #429
        - Optimize Plack::Response->finalize performance (kazeburo) #433
        - Optimize mount() performance in Plack::App::URLMap

    [BUG FIXES]
        - ErrorDocument: remove Content-Encoding and Transfer-Encoding (kazeburo) #430
        - Fix harakiri test hang on win32 (wchristian) #431
        - Handler::FCGI: Restore --keep-stderr option (mauzo) #432

1.0029  2013-08-22 14:05:44 PDT
    [NEW FEATURES]
        - Plack::Test now has a simpler object-oriented interface that doesn't
          take multiple callbacks. #420

    [IMPROVEMENTS]
        - bump dependencies for Test::TCP and HTTP::Tiny
        - Set no_proxy for HTTP::Tiny in tests (kazeburo)

    [INCOMPATIBLE CHANGES]
        - Split HTTP::Server::Simple handler from Plack distribution and merge to
          HTTP-Server-Simple-PSGI distribution

1.0028  2013-06-15 01:42:52 PDT
    [IMPROVEMENTS]
        - Skip cgi related tests for Win32 (chorny) #413
        - Skip tests that could potentially write empty bytes, which could cause
          issues on some servers on local sockets with HTTP::Tiny
        - Skip tests that require HTTP::Cookies, if not available #414

1.0027  2013-06-13 21:30:12 PDT
    [IMPROVEMENTS]
        - Not a dev release, including XS free version of Plack::Test*
        - Fix cgibin tests that often fail on Win32 #375

1.0026  2013-06-12 23:00:21 PDT
    [INCOMPATIBLE CHANGES]
        - use HTTP::Tiny in Plack::Test::Suite and Plack::Test::Server rather than skipping it.

1.0025  2013-06-12 13:08:58 PDT
    [INCOMPATIBLE CHANGES]
        - No XS! Eliminates dependency to LWP::UserAgent by making it completely optional for
          testing. If you run Plack::Test with Server implementation or run Plack::Test::Suite
          (for PSGI handlers) without LWP installed, the tests will automatically be skipped.
          This removes the eventual sub-dependency to HTML::Parser, which is the only XS dependency
          in Plack. #408

    [IMPROVEMENTS]
        - Fixed the warning in OO usage of Plack::Builder (doy) #407
        - Shotgun loader now dies if used in Win32 since it leaks memory #320, #400
        - Suppress warnings for Test::TCP (kazeburo) #406
        - $res->to_app shortcut (ether) #409

1.0024  2013-05-01 10:05:56 PDT
    [IMPROVEMENTS]
        - Fix warnings for Plack::App::WrapCGI (frioux)
        - Ignore emacs lock file from restarter (maio)
        - Add documentation for environment variable in Auth::Basic
        - Some Metadata cleanup and Travis CI

1.0023  2013-04-08 11:13:11 PDT
    [IMPROVEMENTS]
        - Use Apache::LogFormat::Compiler in AccessLog (kazeburo)

1.0022  2013-04-02 12:37:42 PDT
    [BUG FIXES]
        - Fixed a major bug in 1.0020-1.0021 where posix_default prevents arbitrary arguments
          for plackup-compat (e.g. starman) to handle them (Thanks to justnoxx) Starman#66

    [IMPROVEMENTS]
        - Fixed test warnings (Keedi Kim)

1.0021  2013-04-02 11:20:00 PDT
        - Repackage with Milla v0.9.6 #392

1.0020  2013-04-01 19:34:54 PDT
    [INCOMPATIBLE CHANGES]
        - Enable posix_default and gnu_compat in plackup Getopt, so that ambiguous
          option names do not match with long options accidentally

    [IMPROVEMENTS]
        - Document fix for the AccessLog (ether)
        - Special-case Content-Length and Content-Type for %{}i in AccessLog format #387


1.0019  2013-04-01 17:58:25 PDT
        - Trial release with Milla

1.0018 Fri Mar  8 10:43:45 PST 2013
    [IMPROVEMENTS]
        - Performance boost in Plack::Request#query_parameters (lestrrat)
        - Added custom log formats for %m, %U, %q and %H (Hiroshi Sakai)
        - Fixed warnings in SimpleContentFilter (earino)

    [DOCUMENTATION]
        - Added docs about plackup --path
        - Added docs about using manager object in Plack::Handler::FCGI

1.0017-TRIAL Thu Feb  7 19:21:24 PST 2013
    [INCOMPATIBLE CHANGES]
        - Gives you warnings when you use one of Plack::App objects in `plackup -e` or
          in .psgi files but forgot to call ->to_app to make it a PSGI application (#369)
          Still automatically converts them for backward compatibility, but in the
          loading time inside Plack::Builder.

    [BUG FIXES]
        - chdir to the CGI path when executing CGIBin (#338, #368)

1.0016 Thu Jan 31 13:21:14 PST 2013
    [SECURITY]
        - Fixed directory traversal bug in Plack::App::File on win32 environments

    [INCOMPATIBLE CHANGES]
        - Updated Plack::Builder OO interface to be more natural. Still keeps backward
          compatible to the old ->mount() and ->to_app() interface.

    [NEW FEATURES]
        - Static middleware 'path' callback now takes $env as a 2nd argument (avar)
        - Static middleware takes 'content_type' callback to determine custom MIME (pstadt)

    [IMPROVEMENTS]
        - Fixed regexp warning for blead (doy)
        - Documentation update for AccessLog::Timed to suggest Runtime (ether)
        - Ignore vim swap files on restarter (nihen)
        - Major doucmentation overhaul on Apache2 startup files (rkitover, avar)

1.0015 Thu Jan 10 15:19:17 PST 2013
    [BUG FIXES]
        - Fixed Lint complaining about Latin-1 range characters stored internally with
          utf8 flag on (Mark Fowler)
        - HTTP::Message::PSGI::res_from_psgi now always returns empty string
          for an empty response body, so streamed responses are consistent with
          non-streamed (ether)

1.0014 Mon Dec  3 10:27:43 PST 2012
    [BUG FIXES]
        - Fixed Hash order in tests for perl 5.17 (doy)
        - Fixed StackTrace tests to run with Devel::StackTrace

    [IMPROVEMENTS]
        - Plack::Middleware::AccessLog can now log the worker pid and server
          port (ether)

1.0013  Wed Nov 14 19:46:49 PST 2012
    [BUG FIXES]
        - Make sure psgi.input is seeked even when the input is buffered (Getty, leedo)
        - Delete invalid (empty) CONTENT_LENGTH and CONTENT_TYPE in FCGI (Getty, leedo)

1.0012  Wed Nov 14 12:00:17 PST 2012
    [IMPROVEMENTS]
        - Make conditional middleware work with initialization without an app (doy)
        - Added force option to BufferedStreaming 

1.0011  Sun Nov 11 11:05:30 PST 2012
    [BUG FIXES]
        - Fix bad Content-Length that could be caused with mod_perl (avar)
        - Allow an empty PATH_INFO in Lint per PSGI spec

1.0010  Fri Nov  2 13:30:50 PDT 2012
    [IMPROVEMENTS]
        - Added vim .swp files to the default ignore list in Restarter
        - Check if PATH_INFO begins with / in Lint

1.0009  Tue Oct 23 00:57:16 PDT 2012
    [BUG FIXES]
        - Correct fix to address drive letters for Win32

1.0008  Mon Oct 22 18:52:29 PDT 2012
    [BUG FIXES]
        - Allow drive letters for absolute paths for plackup and load_psgi #343

1.0007  Sat Oct 20 23:20:20 PDT 2012
    [IMPROVEMENTS]
        - Fix test failures with HTTP::Message 6.06. #345
        - relaxed plackup -R ignore files and directoris. #260

1.0006  Thu Oct 18 16:06:15 PDT 2012
    [INCOMPATIBLE CHANGES]
        - plackup foo.psgi will not search the file in @INC anymore before the current directory
          See https://github.com/plack/Plack/pull/343 for details (miyagawa)

    [NEW FEATURES]
        - plackup --path /foo will mount the application under /path (mattn)

    [BUG FIXES]
        - AccessLog: Fix the timezon offset for certain timezones
        - ErrorDocument: support streaming interface

1.0005  Tue Oct  9 13:33:47 PDT 2012
    [NEW FEATURES]
        - Support psgix.cleanup handlers in Apache2 (avar)
        - Added REMOTE_PORT environment variable to HTTP::Server::PSGI (dex4er)        

    [IMPROVEMENTS]
        - Documentation fix for multiple cookie values (miyagawa)
        - Delete MOD_PERL environment variable for better compatibilities (avar)
        - Split out Plack::TempBuffer as a standalone Stream::Buffered module (doy)
        - Bump Test::TCP dep

1.0004  Thu Sep 20 08:36:11 JST 2012
    [NEW FEATURES]
        - Added psgix.harakiri support in HTTP::Server::PSGI

    [IMPROVEMENTS]
        - Preload TempBuffer modules (avar)
        - Documentation fixes (autarch)

1.0003  Wed Aug 29 13:44:53 PDT 2012
    [BUG FIXES]
        - Fix Basic authentication error in case password contains a colon #319
        - Fix AccessLog middleware in platforms where %z strftime is not supported #318
        - Escape $_ in Plack::Request path method due to a possible URI::Escape bug

1.0002  Mon Aug 13 17:04:25 PDT 2012
    [NEW FEATURES]
        - Added --no-default-middleware option to plackup #290

    [BUG FIXES]
        - Use C locale for AccessLog strftime #313
        - Escape Plack::Request URI path using RFC 3986 definition (ssmccoy)

    [IMPROVEMENTS]
        - Documentation improvements (ether, Tom Heady)
        - Skip displaying ".." in Plack::App::Directory #277
        - Document load_class() doesn't validate user input. #285

1.0001  Thu Jul 26 16:24:13 PDT 2012
    [INCOMPATIBLE CHANGES]
        - Deleted lots of code, methods and warnings that have been deprecated since 0.99
          (which should have been done in the 1.0000 release)

    [DEVELOPERS]
        - Added bootstrap script to install devel dependencies

    [IMPROVEMENTS]
        - Fixed version numbers in some of the modules that have their own $VERSION

1.0000  Thu Jul 19 18:59:18 PDT 2012
        - This be 1.0! (Same as 0.9991)

0.9991  Thu Jul 19 17:27:52 PDT 2012
    [NEW FEATURES]
        - Added IIS7 fix middleware (t0m)

0.9990  Wed Jul 18 11:12:07 PDT 2012
    [INCOMPATIBILE CHANGES]
        - Plack::Request changes the way it parses QUERY_STRING for valueless keys such as
          "?a&b=1". Now "a" becomes part of query_parameters with empty string as its value (yannk)

    [IMPROVEMENTS]
        - Support max-age options in Plack::Response cookies (remorse)
        - Pass correct protocol from HTTP::Server::PSGI to display https URL correctly (siracusa)
        - Copy Authorization header from FastCGI handler (ray1729)
        - Stop special casing COOKIE environment variable in Plack::Request headers (doy)

0.9989  Thu Jun 21 13:39:11 PDT 2012
    [IMPROVEMENTS]
        - Support streaming in Head middleware (wreis)
        - Document middleware prefixing (Jon Swartz)
        - Make Basic authentication detection case insensitive per RFC (Mark Fowler)
        - Added backlog option to FCGI handler (xaicron)

0.9988  Fri May 11 12:25:09 CEST 2012
    [BUG FIXES]
        - Fixes HTTP_HOST in HTTP::Message::PSGI #287 (doy)

0.9987  Thu May 10 07:06:32 CEST 2012
    [IMPROVEMENTS]
        - Support streaming in AccessLog::Timed (Peter Makholm)
        - Support streaming in ErrorDocument
        - Removed UTF8 hack in HTTP::Message::PSGI. Depends on URI.pm 1.59 (wreis)
        - Set Host headers correctly in HTTP::Message::PSGI #177
        - Added documentation on supported %-flags in AccessLog (ether)
        - Skip unnecessary tests on non-developer environment

0.9986  Mon Mar 12 11:26:59 PDT 2012
    [IMPROVEMENTS]
        - Use I/O handles to FCGI::Request instead of global STDIN, STDOUT etc. (chansen)
        - Improved FastCGI docs (osfameron)
        - Cascade app now returns the last response code (aristotle)

0.9985  Mon Oct 31 13:11:19 PDT 2011
    [BUG FIXES]
        - Short circuit Plack::Handler fallback to avoid %INC bugs in perl 5.8 (mst)
        - Fixed Makefile.PL to avoid Test::SharedFork interferring with Module::Install (ambs)

0.9984  Mon Oct  3 09:55:05 PDT 2011
    [BUG FIXES]
        - WrapCGI: Close wrapped CGI's STDIN handle (rwstauner)

    [IMPROVEMENTS]
        - WrapCGI: improved docs (chromatic)
        - Request: Do not destroy HTTP::Body upload headers (mst)

0.9983  Tue Sep 27 09:55:48 PDT 2011
    [BUG FIXES]
        - Fixed a typo in nginx FastCGI configuration
        - Clone HTTP headers in Response->finalize #237 (chip)
        - Fixed Directory app not displaying the right path in its title
        - Changed IPv6 default listen address to wildcard (ollyg)
        - Fixed the FastCGI handler with web-server mode on Win32

    [NEW FEATURES]
        - Added psgix.harakiri for Apache handlers

    [IMPROVEMENTS]
        - Prefer Corona when Coro is detected #236 (chip)
        - Increased Pod::Usage dependency
        - Improved Plack::Test documentation (chromatic)
        - Lint now checks if SCRIPT_NAME eq '/' which is forbidden in the spec (chromatic)

0.9982  Tue Jul 19 13:07:35 PDT 2011
    [BUG FIXES]
        - Fixed the bug in restarter introduced in 0.9980 (nihen) #223 #234
        - Removed a debug statement left over in Plack::Util
        - Fixed warnings in Lint

0.9981  Mon Jul 18 17:24:11 PDT 2011
    [BUG FIXES]
        - Plack::Request: Added a sanity check to remove newlines from headers to follow
          the PSGI specification #224
        - HTTPParser::PP: Fixed warnings #225
        - plackup now prints errors to psgi.errors rather than STDERR
        - Fixes issues with undef returned from streaming handler in middleware #231
        - ContentLength: Do not auto-add Content-Length from block devices, pipes and
          character files

    [NEW FEATURES]
        - HTTPExceptions: Support ->as_psgi method on exceptions (doy)
        - FastCGI: Support psgix.harakiri

    [IMPROVEMENTS]
        - Lint: Added more checks to validate header values
        - StackTrace: Strip caller information since it is not useful anyway
        - HTTPExceptions: Added rethrow option (doy)
        - Misc. doc fixes on plackup (chromatic)
        - binmode STDIN for CGI handler for Win32 #218
        - Remove the test that tests Server specific handling of Transfer-Encoding
        - Fixed POD link (audreyt)

0.9980  Mon Jun  6 20:24:25 PDT 2011
    [BUG FIXES]
        - Fixed a bug where restarting loader doesn't terminate children (#209)
        - Strip URI fragments off of PATH_INFO and QUERY_STRING (#213)

    [IMPROVEMENTS]
        - Documented -r vs auto server detection caveat
        - Documented a default AccessLog format (ask)
        - Support %V in AccessLog formats (ask)
        - Document PLACK_HTTP_PARSER_PP (melo)

    [NEW FEATURES]
        - Added experimental IPv6 and SSL support for the built-in HTTP::Server::PSGI

0.9979  Tue May 17 09:54:03 PDT 2011
    [BUG FIXES]
        - Fixed Middleware::AccessLog's default %t format to match Apache's format
        - Fixed a warning in Apache1 handler where PATH_INFO doesn't exist #204
        - Fixed a bad test relying on new Test::More versions

    [IMPROVEMENTS]
        - Fixed Lint to accept bare in-memory filehandle per http://stackoverflow.com/questions/6011793/
        - Added setup_env() to Plack::Handler::CGI (markstos)
        - Added a non-blocking Hello World example in eg/dot-psgi
        - Doc cleanup

0.9978  Wed May  4 11:29:12 PDT 2011
    [TEST FIXES]
        - Fixed a failing output_encoding.t because of FCGI dependencies
        - Improved Plack::Test::Suite documentation

0.9977  Sun May  1 12:16:08 PDT 2011
    [BUG FIXES]
        - Fixed ConditionalGET to not die with streaming interface (reported by Paul Ervamaa)
        - Add a reason string to CGI/FastCGI Status header to comply with RFC 3875 (Stephen Clouse)
        - Fixed a CGI/FastCGI handler to ensure newlines are not mangled on Win32 platforms (Christian Walde)

    [IMPROVEMENTS]
        - localize @ARGV to empty when evaluating a PSGI application (https://github.com/sukria/Dancer/issues/473)
        - Fixed the use of Getopt::Long to make the pass_through flag local
        - Middleware::JSONP now supports more response types such as IO::Handle (reported by Theory)

0.9976  Fri Apr  8 18:07:11 PDT 2011
    [NEW FEATURES]
        - Support setting content_type in App::File (ajgb)

    [IMPROVEMENTS]
        - Document fixes (jhannah)
        - Skip bad tests failing on LWP 6 (daxim)

0.99_75 Thu Mar 24 11:29:22 PDT 2011
    [INCOMPATIBLE CHANGES]
        - builder {} now always returns a PSGI code reference, instead of inconsistently
          returning URLMap object when mount() is used. (reported by hoelzro)
        - Plack::Runner now automatically calls ->parse_options() if it hasn't been called,
          so the sane defaults for plackup can be applied. (reported by arcanez)

    [BUG FIXES]
        - Fixed the way to override %ENV to avoid test breakages in Win32 #179
        - Properly append '/' when linking to a directory in Plack::App::Directory (theory)

    [IMPROVEMENTS]
        - Skips the current directory in Plack::App::Directory
        - Plack::App::Directory now redirects to a canonical URL that has a trailing slash
          just like Apache (hobbs)
        - Fixed some typos and outdated information in the PODs

0.9974  Thu Mar  3 20:55:28 PST 2011
        - Added a documentation about using relative URI paths beginning with //
        - Added IIS6ScriptNameFix that fixes SCRIPT_NAME for IIS6 FastCGI, extracted from Catalyst (rafl)
        - Moved the wrapcgi/exec tests for Win32 #174
        - Fixed a warning for the new Test::TCP in FCGI testing
        - Clear %ENV when running the Plack::Test::Suite with Server implementation (hachi)

0.9973  Sat Feb 26 09:40:15 PST 2011
        - Fixed the regexp in the code check added in 0.9972 (leedo)

0.9972  Thu Feb 24 10:50:01 PST 2011
        - Fixed the Plack::Runner docs to avoid the cargo cult issue of __FILE__ eq $0
        - Added a silly check to give warnings if the idiom __FILE__ eq $0 is used in .psgi
        
0.9971  Wed Feb 23 14:02:35 PST 2011
    [INCOMPATIBLE CHANGES]
        - Localize $0 to the given .psgi path when evaluating it in Plack::Util::load_psgi()
          This fixes the unexpected values and/or crashes with Starman when your application
          uses FindBin module.

0.9970  Tue Feb 22 08:35:50 PST 2011
        - Apache2: Fixed a bug where dispatcher fails to parse first path when it begins with two or
          more slashes (clkao)

0.9969  Fri Feb 18 21:35:29 PST 2011
        - Suppress the use of unlocalized $_ in Plack::Runner (mst)
        - Plack::Handler::Net::FastCGI is now removed from Plack core dist.
          It will be released as a separate distribution on CPAN.
        - Fixed Plack::Handler::Apache2 so that it can safely call log (Andy Wardley)
        - StackTrace: Display graceful fallback errors when $SIG{__DIE__} is overridden in the application (mkanat)

0.9968  Wed Feb  9 19:07:48 PST 2011
        - Fixed Recursive middleware to rethrow unknown exceptions. #166 (reported by waba)
        - Document response_cb. #121
        - Plack::Loader to print errors if it is really a compilation error
        - Fixed the Cascade app to work with all 404 responses with the streaming interface. #171 (reported by eevee)

0.9967  Tue Jan 25 14:26:37 PST 2011
        - Fixed StackTrace to require D::ST::WithLexicals 0.08 that supports 'message' (doy)

0.9966  Tue Jan 25 12:00:25 PST 2011
        - Fixed a memory leak in SimpleLogger (vti)
        - Support %v in AccessLog (Ranguard)
        - Force set CONTENT_LENGTH in req_to_psgi when $content is given to HTTP::Request (timbunce) #150
        - Fixed a case where SCRIPT_NAME and PATH_INFO can both get empty in req_to_psgi (doy) #163

0.9965  Mon Jan 24 23:08:04 PST 2011
        - Requires Devel::StackTrace 0.11
        - Fixed a regression where StackTrace wasn't able to get the thrown exception as an error message (hachi)

0.9964  Mon Jan 24 16:29:08 PST 2011
        - Various documentation improvements (miyagawa, schwern)
        - Improved the way it eliminates Plack::Middleware::StackTrace from its own stacktrace (Jonathan Swartz)

0.9963  Mon Jan 10 16:46:33 PST 2011
        - Fixed fcgi.t for lighttpd < 1.4.23 (confound)

0.9962  Sat Jan  8 21:07:30 PST 2011
        - Same fix as 0.9961 but works around the issues with Strawberry unarchiver

0.9961  Fri Jan  7 21:54:04 PST 2011
        - Skip directory.t on win32 since the directory "stuff.." can't be created [RT:64545]

0.9960  Sat Dec 25 11:16:08 PST 2010
        - FCGI: Fixed the regression in 0.9958 where PATH_INFO gets wrong value when hosted under a
          non-root path (ambs)
        - Improved the FastCGI and Apache2 test infrastructure to test SCRIPT_NAME values

0.9959  Tue Dec 21 11:38:08 PST 2010
        - Apache2: Fixed the regression bug around LocationMatch caused by fixes in 0.9958 (cho45)

0.9958  Mon Dec 20 15:18:54 PST 2010
        - Plack::Handler::Apache[12] now handles Authorization: header automatically, no need for
          mod_rewrite workaround anymore (cho45)
        - Fixed Apache[12] and FCGI where multiple forward slashes were munged (cho45)
        - Static: Added pass_through option to pass non-existent paths to the app. Fixing the
          docs to match with the code (beanz) #154

0.9957  Thu Dec 16 11:27:29 PST 2010
        - Fixed warnings in Plack::Request cookie parsing (typester)
        - removed MethodOverride middleware. Now it is a standalone distribution on CPAN (theory)

0.9956  Thu Dec  9 19:32:46 PST 2010
        - FastCGI: Fixed an empty PATH_INFO with mod_fastcgi (and possibly others)
        - FastCGI: Improved the automatic detection of the case when invoked from web server. #141
          (reported by LeoNerd)
        - plackup: Document that -e 'enable ...' doesn't assume app.psgi when there's no argument. #106 (clkao)
        - Plack::App::FCGIDispatcher: Remove the Status: header #123 (reported by Htbaa)
        - Apache2: Work around issues where SCRIPT_NAME gets wrong when LocationMatch is used. #136
          (reported by atiking)

0.9955  Thu Dec  9 18:02:50 PST 2010
        - More fixes to a possible directory traversal

0.9954  Thu Dec  9 17:45:59 PST 2010
        - Fixed a directory traversal bug in Plack::App::File etc. RT:63020

0.9953  Fri Dec  3 14:50:09 PST 2010
        - Include the original error message in the StackTrace text output on console.
          This requires Devel::StackTrace 1.23 and Devel::StackTrace::WithLexicals 0.08 (optional)
        - Fixed AccessLog middleware to handle multiple dashes in %{} (Jiro)

0.9952  Thu Dec  2 14:03:48 PST 2010
        - Fixed the potential deadlocks in WrapCGI's read/write pipe (typester)
        - Improved documentations on plackup -e
        - Fixed a potential DoS vulnerability in HTTP::Server::PSGI (kazuho)
        - Allows setting names of FCGI process with proc_title option (rafl)

0.9951  Mon Oct 25 13:50:33 PDT 2010
        - Added Feersum to the benchmark script (stash)
        - Lint: fixed the body handle check to see if the file has getline() method (tokuhirom)
        - StackTrace: store the stacktrace in $env->{'plack.stacktrace.text'} and $env->{'plack.stacktrace.html'} (theory)
        - Added ->mount method to the Plack::Builder OO interface (franckcuny)
        - HTTPExceptions: Don't set an invalid Content-Length when the exception is not an object (ask)
        - ErrorDocument: Fixed wrong Content-Length header be set (ask)

0.9950  Thu Sep 30 14:11:33 PDT 2010
        - Fixed typos in middleware docs (miyagawa, theory, tokuhirom)
        - App::Directory: fixed URL generation escape bug (chiba)
        - Middleware::JSONP: support callback parameter name (franck)

0.9949  Tue Sep 14 11:59:36 PDT 2010
        - Fixed FCGI handler docs
        - Auth::Basic: Pass $env to the callback so .htpasswd based auth can be implemented with PATH_INFO (doy)

0.9948  Thu Sep  9 16:01:53 PDT 2010
        - Fixed a bug introduced in 0.9947 where $req->upload loses the temporary files when
          Plack::Request object is instantiated multiple times. It could happen if one of the
          pre-processing middleware uses Plack::Request and then again in the application or
          frameworks.

0.9947  Thu Sep  9 02:26:14 PDT 2010
        - Plack::Loader: Fixed a typo in ENV that prevents warnings messages in development
        - Added flymake temporary file in Restarter (hirose31)
        - Plack::Request: Fixed a bug that HTTP::Body temporary files were not cleaned up (plu)
        - Middleware::AccessLog: Fixed a bug where %{key}i ignores the value '0' (nekoya)

0.9946  Sat Aug 28 22:32:16 PDT 2010
        - Fixes UUV warnings in Apache2 handler RT:60472
        - Fixed various test failures due to dependencies

0.9945  Thu Aug 19 16:24:30 PDT 2010
        - Support executing (non-perl) CGI scripts in CGIBin and WrapCGI
        - Fixed tests for win32

0.9944  Sun Aug  8 23:35:52 PDT 2010
        - Fixed Restarter for Starlet where SIGTERM doesn't quit the process (chiba)

0.9943  Fri Jul 30 13:24:15 PDT 2010
        - Updated Apache* handler so it could duck type on Loader (jnap)
        - Added --access-log to plackup (grantm)
        - Added support for streaming stdio in Net::FastCGI handler (chansen)

0.9942  Fri Jul 23 23:42:43 PDT 2010
        - Allow passing FCGI manager object to Handler::FCGI (confound)
        - Call FCGI::Request::Finish() before pm_post_dispatch (confound)
        - Moved response_cb() to Plack::Util (confound)
        - re-enable WithLexicals now that PadWalker segfaults with 5.12 is fixed #98

0.9941  Thu Jul  8 18:17:30 PDT 2010
        - Makes Lint not warn about ASCII-only strings with UTF8 flag because they're safe

0.9940  Fri Jul  2 23:37:51 PDT 2010
        - Fixed META.yml

0.9939  Fri Jul  2 17:56:10 PDT 2010
        - Improved middleware documentation (miyagawa, leedo, bobtfish)
        - Added a test about Transfer-Encoding with Content-Length: 0 (chiba)
        - Fixed NullLogger middleware (haarg)
        - Fixed Plack::Util inline object's can() (haarg)
        - Middleware::HTTPException now honors ->location method of the exception (frodwith)
        - Middleware::AccessLog: Fixes %D to be microsec so it's compatible to Apache #119 (cho45)
        - Fixed Plack::Request->uri when PATH_INFO conatins URI reserved characters #118 (leedo)

0.9938  Sun May 23 17:13:05 PDT 2010
        - ErrorDocument: Added Content-Length to error responses (hachi)
        - Improved docs about conditional middleware loading
        - XSendfile: Updated (undocumented) environment key to switch frontend
        - Auth::Basic: Added notes about how to use it with Apache (mod_perl and CGI) [RT #57436]

0.9937  Fri May 14 23:11:27 PDT 2010
        - Fixed -I broken in 0.9936 (juster) #114

0.9936  Fri May 14 15:58:02 PDT 2010
        - Remove 'use lib "lib"' from plackup
        - Remove HTTP_CONTENT_* environment variables in FastCGI handlers to deal with buggy web servers.
          (Justin Davis)

0.9935  Wed May  5 15:17:06 PDT 2010
        - Set an empty PATH_INFO if CGI environment doesn't set so (hachi) #109
        - Fixed a possible weird combination of SCRIPT_NAME and PATH_INFO in CGI handlers
        - localize PATH_INFO and SCRIPT_NAME in App::File and subclasses #100
        - updated COPYRIGHT notice for Debian
        - Middleware::StackTrace now displays text trace to psgi.errors like Rack::ShowExceptions
          (castaway, theorbtwo)
        - Middleware::StackTrace: Fixed the text stack trace format to be more readable

0.9934  Tue May  4 15:47:33 PDT 2010
        - Added a test in CGIBin where binmode ":utf8" causes bad content-length #110
        - Doc fix for the deprecated servers
        - Initialize Module::Refresh (hiratara)
        - Added mime_type to ErorrDocument (kakuno)

0.9933  Tue Apr 27 14:32:23 PDT 2010
        - refactored the app.psgi loading error handling
        - Enable type checking of the app in Lint->wrap
        - allow plackup -e'...'
        - Disable FCGI::Client/Net::FastCGI test by default

0.9932  Mon Apr 19 15:23:55 JST 2010
        - Enable Lint middleware by default in the development env
        - Lint middleware now validates $app on startup
        - Fixed documentations on middleware and handlers

0.9931  Fri Apr 16 23:52:27 PDT 2010
        - replace kyoto.jpg test image file with smaller baybridge.jpg to strip down the tarball size
          from 2.5MB to 212KB.

0.9930  Tue Apr 13 20:18:06 PDT 2010
        - Added Plack::Handler::Net::FastCGI (chansen)
        - Made Test::TCP a hard dependency since Plack::Test needs it
        - Added Delayed loader for Starlet and Starman (clkao)
        - Hide logger middleware from log4perl's caller stack (haarg)

0.9929  Wed Mar 31 00:33:10 PDT 2010
        - Middleware::JSONP: Simplified code and does not support IO response body type
        - fcgi.t: skip tests with lighttpd < 1.4.17 per CPAN Testers #7040400

0.9928  Mon Mar 29 17:02:42 PDT 2010
        - log_dispatch.t: require Log::Dispatch::Array

0.9927  Mon Mar 29 12:43:44 PDT 2010
        - require newer Log::Dispatch (confound)
        - StackTrace: Encode exceptions in utf-8 in case they include wide characters #95 (tokuhirom)
        - StackTrace: Depends on a new Devel::StackTrace::AsHTML that escapes wide characters
        - StackTrace: Display stacktrace only if the thrown exception is a direct error #91 (frodwith)
        - StackTrace: Added 'force' option to force stacktrace in 500 errors
        - Avoid warnings when response_cb filter returns undef in ARRAY response body #92 (hiratara)
        - URLMap: Ignore port number if it matches with SERVER_PORT #90 (omega)
        - URLMap: Enable debug print with PLACK_URLMAP_DEBUG=1 #94
        - JSONP: Fixed possible infinite-loop when using with IO response body (hiratara)
        - Fixed the compatiblity issues with FastCGI docs and tests with lighttpd 1.4.26 (tadam)
        - LighttpdScriptNameFix: Added 'script_name' option (tadam)

0.9926  Sun Mar 28 14:37:03 PDT 2010
        - Added -v|--version option to plackup and the ability for Plack::Runner users to override

0.9925  Sat Mar 27 19:03:57 PDT 2010
        - Make this a non-devel release

0.99_24 Sat Mar 27 13:31:51 PDT 2010
        - Disable Devel::StackTrace::WithLexicals for now until PadWalker RT #55242 is fixed

0.99_23 Sat Mar 27 01:02:24 PDT 2010
        - Dropped keep-alive code from HTTP::Server::PSGI now that Starlet clones the code
        - Special case --disable-* and --enable-* command line options in plackup and Plack::Runner

0.99_22 Thu Mar 25 19:48:08 PDT 2010
        - INCOMPATIBLE: removed --max-workers option from the default standalone server.
          Now it gives you warnings and falls back to the single process mode.

0.99_21 Thu Mar 25 15:05:53 PDT 2010
        - INCOMPATIBLE: removed a workaround for lighttpd SCRIPT_NAME bug in FCGI handler
          See http://github.com/plack/Plack/issues#issue/68 for details.
        - HTTPException now logs standard exceptions to psgi.errors
        - micro optimization for Plack::Request content() method

0.9920  Thu Mar 18 23:48:06 PDT 2010
        - Fixed URL path prefix matching in URLMap (hiratara)
        - Fixed Plack::Request->content on GET with FastCGI servers (sunnavy)
        - Added new middleware Middleware::Head
        - Fixed localization bug in Plack-Util/load.t

0.9919  Wed Mar 17 22:50:09 PDT 2010
        - Properly rethrow .psgi compilation errors

0.9918  Wed Mar 17 22:35:00 PDT 2010
        - Load .psgi file in an unique package rather than Plack::Util to avoid
          namespace pollution gh-88

0.9917  Wed Mar 17 15:33:43 PDT 2010
        - Added Plack::Handler::Apache2::Registry (hiratara)
        - Set default PLACK_ENV in Plack::Util::load_psgi

0.9916  Fri Mar 12 12:52:39 JST 2010
        - Added support for a new (renamed) web server Corona
        - Document enable coderef in Plack::Middleware (clkao)
        - Middleware::StackTrace: Send plain text errors to clients that probably do
          not understand HTML like curl

0.9915  Mon Mar  8 18:22:33 JST 2010
        - Fixed a dumb bug in Plack::Handler::Apache2, broken in 0.9914 (hiratara)
        - Added a warning if you misuse mount()

0.9914  Wed Mar  3 16:02:38 PST 2010
        - Fixed psgix.io and nested closure for perl 5.8 (hiratara)
        - Added an inheritance friendly Apache2 interface (frodwith)
        - HTTP::Server::PSGI: Close client connection in the first run (hirose31)
        - Fixed Loader/auto.t to reset env var (gugod)

0.9913  Thu Feb 25 19:14:40 PST 2010
        - Revive psgix.io in HTTP::Server::PSGI (hiratara)
        - Fix packaging issue

0.9912  Thu Feb 25 01:28:21 PST 2010
        - Fixed the possible source of memory leak in middleware + streamer + HTTP::Server::PSGI
          with perl 5.8.x (hiratara)

0.9911  Tue Feb 23 01:55:04 PST 2010
        - Removed psgix.io extension to fix streaming choke issue on HTTP::Server::PSGI (tomyhero)

0.9910  Mon Feb 22 19:03:17 PST 2010
        - This is the first non-dev release since 0.99. Read all the change logs below.
        - Support streaming in JSONP (hiratara)
        - Fixed various handler docs (markstos)
        - Added Starman and Twiggy to benchmark script
        - INCOMPATIBLE: Loader now prefers Twiggy when AnyEvent is loaded
        - Implemented (experimental) psgix.io and psgix.input.buffered extensions
        - Fixed Plack::Request POST parser to use psgix.input.buffered for better performance
        - Added PLACK_ENV environment support in plackup #63
        - Added HTTPExceptions middleware
        - Added Recursive middleware

0.99_05 Wed Feb 10 12:46:05 PST 2010
        - Changed the Loader command line options to -L from -l
        - Runner now folds --host, --port and --socket to --listen and vice verca
        - Added -D and --daemonize to plackup/Runner standard options
        - Fixed FCGI handler to work with the new --listen and --daemonize option
        - Fixed a bug in static.t where it chdir's before loading modules
        - Renamed Writer to BufferedStreaming middleware and added docs
        - Support streaming apps in Shotgun loader
        - Falls back to Standalone handler when auto-detected backend is not available (hiratara)
        - Support chunked-input in HTTP::Request->to_psgi
        - Make the Reloader work with preforked server (chiba)
        - Added 'Auto' backend in TempBuffer
        - Added Nomo backend to the benchmark script
        - Updated HTTP::Server::PSGI to support experimental psgix.input.buffered
        - Plack::Request now honors psgix.input.buffered to see psgi.input is seekable
        - Renamed Standalone handler to HTTP::Server::PSGI for consistency while keeping
          'Standalone' as a nickname

0.99_04 Fri Feb  5 23:10:48 PST 2010
        - Updated Test suite for multiple request headers to relax a bit for AE::HTTPD
        - Added a test for large POST body which revealed FCGI::Client bug
        - Added a handler for HTTP::Server::Simple::PSGI
        - Depend on a decent version of URI (tomyhero)
        - Reworked Loader API so the default loader can autodetect the backend again
        - run_app now doesn't use Try::Tiny but use plain eval {}

0.99_03 Wed Feb  3 16:09:14 PST 2010
        - Use 0 as a default address in the server_ready hook in Plack::Runner
        - Document Plack::Handler naming scheme
        - Fixed how Plack::Server::Standalone saves args
        - Supported streaming interface in Cascade and URLMap
        - mentions awesome WSGI Paste in Plack documentation
        - Removed URI caching in Plack::Request since it's fast enough
        - Fixed packaging issue due to Module::Install::Share bug (rafl)
        - Support 'file' option in App::File and its subclasses
        - Fixed SCRIPT_NAME and PATH_INFO in App::CGIBin
        - Fixed App::Directory and ::File not to use Path::Class and its canonicalization.
          It's now 300% faster!

0.99_02 Sat Jan 30 22:10:45 PST 2010
        - Fixed Plack::TempBuffer to work with 5.8 and 5.11.3
        - Do not use <$input> in FCGIDispatcher
        - Skip fcgi_client.t unless explicitly stated (clkao)
        - clarify and drop some CPAN dependencies (andk)

0.99_01 Fri Jan 29 14:02:04 PST 2010
  Incompatible Changes
        - Rename Standalone servers to HTTP::Server::PSGI
        - Rename Plack::Server adapters to Plack::Handler. These changes should be transparnt
          since we have a compatible code to work with the older names as well.
        - Dropped sendfile(2) AIO support from Standalone server
        - Plack::Request and Response are now in core, deprecating many methods.
          Read `perldoc Plack::Request` and its INCOMPATIBILITIES section

  New Features
        - New middleware: WrapCGI to convert a single CGI script into a PSGI applciation
        - Support psgix.logger and psgix.session in Plack::Request
        - New logger middleware: NullLogger, SimpleLogger, Log4perl and LogDispatch
        - Refactored Loader classes and added a new Shotgun loader (like rack's Shotgun)
        - Added -l option to plackup which specifies the Loader backend
        - New middleware: Refresh reloads modules in %INC in every N seconds
        - Wraps -e code with 'builder { }' by default. You can also use with *.psgi to add middleware
          components without editing the file!

  Bug Fixes and Improvements
        - Do not call ->canonical in HTTP::Message::PSGI to keep the URI encoded params in Plack::Test
          tests (rafl, t0m)
        - Fixed a bug in stupid corner case in HTTP::Message::PSGI where passed URI has UTF-8 encoded
          strings *and* URI escaped UTF-8 bytes. (chmrr)
        - Depend on new HTTP::Request::AsCGI that has better REQUEST_URI
        - Plack::Runner/plackup does not autoload AccessLog in CGI mode anymore
        - Added server_ready hook to PSGI servers so you can disable them in tests etc. (clkao, rafl)
        - Escape user-supplied values in AccessLog to avoid control sequence injection (tokuhirom, kazuho)
        - Support -foo (single dash) style option in Plack::Runner and plackup
        - Relax the runtime.t check since it still fails on low-res time environments
        - Now depends on Digest::MD5, HTTP::Body and Hash::MultiValue
        - Revert the 'require' in load_psgi to do 'do'
        - Delay load unnecessary modules in Plack::Runner
        - Fixed psgi.multiprocess value on HTTP::Server::PSGI
        - PSGI/1.1 support in Lint

0.9031 Mon Jan 11 11:29:04 PST 2010
        - Fixed Plack::App::Directory directory listing by switching to Plack::MIME (tokuhirom)
          This has been broken since 0.9025
        - Fixed body filtering middleware such as Plack::Middleware::Deflater (hiratara)
          This has been broken since 0.9026

0.9030 Sat Jan  9 13:13:17 PST 2010
        - Support streaming interface in HTTP::Message::PSGI, Lint and Plack::Test (rafl)
        - plackup -e doesn't enable strict and warnings by default, like perl
        - Improved Middleware::Auth::Basic performance and error check

0.9029 Thu Jan  7 19:09:17 PST 2010
        - Fixed runtime.t to relax test condition to avoid failures on Win32 (xaicron)
        - Fixed a bug in FCGI engine where it creates a bogus response when running under a
          buffered I/O with lighttpd. (fcharlier, typester)
        - FCGI and CGI server now autoflushes STDOUT to do non-buffering output
        - Fixed a Plack::MIME bug where extensions like .mp3 fails

0.9028 Tue Jan  5 18:42:07 PST 2010
        - Fixed a long standing bug where errors are not printed correctly when the
          application dies. (tokuhirom)
        - Fixed FCGIClient passing bogus psgi.* environment values to the backend
        - Implemented psgi.streaming in all blocking servers (miyagawa, rafl)

0.9027 Sun Jan  3 16:33:23 PST 2010
        - Added new middleware Runtime that adds X-Runtime header
        - Delay load Pod::Usage in Plack::Runner and plackup

0.9026 Fri Jan  1 10:35:26 JST 2010
        - Auth::Basic now accepts an object that duck types to ->authenticate (e.g. Authen::Simple)
        - Reworked how response_cb body callback works, so Content-Length will be updated correctly

0.9025 Sat Dec 26 10:11:59 JST 2009
        - Server::Standalone::* should now display the correct Server: value
        - Fixed a bug in AccessLog::Timed where %D and %T do not work
        - Fixed a bug in AccessLog::Timed to work with filehandles
        - Removed a dependency to MIME::Types and include Plack::MIME
        - Refactored plackup into Plack::Runner
        - Fixed a failing test under stupid Win32 filesystem
        - Fixed ConditionalGET to work with delayed response

0.9024 Sat Dec 19 12:25:52 PST 2009
        - Overwhauled how -r and -R works in plackup. Looks at .psgi and lib/ under that by default.

0.9023 Thu Dec 17 13:16:38 PST 2009
        - Document the use of Plack:: namespace
        - Use safer Unicode characters in tests to silence warnings #66
        - Plack::Util::load_psgi now takes a class name as well. Added notes about the security of its use
        - Set default host in MockHTTP and keep them if explicitly set (nihen)

0.9022 Sun Dec 13 10:53:01 PST 2009
        - Added more assertions to Middleware::Lint
        - Added a new test to test big HTTP header, which reveals the FCGI::Client bug (zrail, tokuhirom)
        - plackup -e now automatically loads Plack::Builder
        - Fixed fcgi tests (tokuhirom)
        - Fixed Test::MockHTTP to make 500 response when the app died
        - Fixed a memory leak in StackTrace when WithLexicals is used (chiba, Sartak)
        - Fixed Middleware::ConditionalGET to deal with stupid IE headers (chiba)
        - Fixed lots of typos (Sartak)

0.9021 Tue Dec  8 14:29:08 PST 2009
        - Doc patches to Plack about CONTRIBUTING (stevan)
        - Remove Class::Accessor::Fast and added Plack::Util::Accessor (stevan)
        - Added Plack::Component the common base class for both App:: and Middleware (stevan)
        - Plack::Test test_psgi now accepts $app, $client in positional args
        - Plack::Test client callback can now omit host names like $cb->(GET "/")

0.9020 Mon Dec  7 10:38:37 GMT 2009
        - Fixed a test (psgibin.t) failure in case sensitive filesystem
        - Fixed a warning in Plack::Util::header_set

0.9019 Sun Dec  6 05:56:30 GMT 2009
        - Fixed a bug in Plack::Util::header_set when to clear multiple headers (chiba)
        - Added Plack::App::CGIBin that runs cgi-bin scripts as a PSGI application
        - Added Plack::App::PSGIBin that loads .psgi files from local filesystem

0.9018 Thu Dec  3 00:48:04 PST 2009
        - Allow Plack::Middleware->new to accept plain hashes
        - Added Plack::App::Cascade to create a compound apps that cascade requests
        - Added POE backend to benchmarks/ab.pl
        - Implemented Plack::Server::Apache[12]->preload to preload apps in <Perl> or startup file

0.9017 Sun Nov 29 17:33:36 JST 2009
        - Fixed more tests that fail on Win32 (charsbar)

0.9016 Sun Nov 29 16:39:40 JST 2009
        - removed Middleware::Deflater from the dist.
        - Fixed Standalone so as not to use Time::HiRes::Alarm on Win32 systems (charsbar, kazuho)
        - Fixed App::File to set file path using forward slashes on Win32 (charsbar) #49

0.9015 Thu Nov 26 17:31:33 JST 2009
        - Fixed a bug in URLMap where $env is shallow copied and some middleware don't work
        - Added -e and -M to plackup
        - plackup -r with args (directories to watch) is deprecated. Use -R instead
        - plackup foo.psgi now DWIMs. -a (--app) continues to work
        - Optimizaitons to Middleware and docs to explicitly call to_app because overloading happens
          every request and is inefficient.
        - The abilitiy to auto-select server backends for POE/AnyEvent/Coro is restored. Doesn't work with -r though. #50
        - Display server package name in the Standalone/Prefork startup
        - Fixed a bug in Plack::Test::MockHTTP where $res doesn't return the request (teejay)
        - Fixed a bug in URLMap where requests to / fails in some cases (chiba)

0.9014 Fri Nov 20 21:51:47 PST 2009
        - Updated docs for Standalone keep-alive options
        - Added Auth::Basic middleware
        - Fixed dependencies and MakeMaker issues in the archive

0.9013 Wed Nov 18 18:26:31 PST 2009
        - Disable keep-alive in Standalone by default (kazuho, frew)
        - Fixed a bug in Standalone where 'timeout' property is ignored in the first request (kazuho)
        - Fixed a documentation bug in Middleware::Conditional (confound, scook)
       
0.9012 Tue Nov 17 13:38:38 PST 2009
        - Added Middleware::Conditional and enable_if DSL for dynamic builder (scook)

0.9011 Thu Nov 12 03:53:28 PST 2009
        - Added Apache1 support (Aaron Trevena)

0.9010 Wed Nov 11 23:18:37 PST 2009
        - You can now omit Plack::Middleware:: in Builder DSL's enable()

0.9009 Sat Nov  7 20:43:17 PST 2009
        - Fixed dependencies for tests

0.9008 Tue Oct 27 14:15:28 PDT 2009
        - Removed optional deps from Makefile.PL and moved them to Task::Plack (mst)
        - Make some middleware deps as required to make it simple, for now

0.9007 Sat Oct 24 17:41:33 PDT 2009
        - Fixed Server::CGI to really inline fuctions to avoid warnings
        - Fixed Middleware::AccessLog to let %{..}t strftime log format work (beppu)
        - Fixed a flush bug in gzip encoding in Middleware::Deflater
        - Fixed a bug in Middleware::AccessLog so POSIX strftime always works in English (fayland)
        - Added Middleware::ContentMD5 (Fayland)
        - Fixed plackup -r to actually reload the application code (robinsmidsrod)

0.9006 Fri Oct 23 01:21:13 PDT 2009
        - Support streaming interface in most middlewares
        - Added Middleware::Deflater (not recommended to use: see POD)
        - Document FCGI configuration in Server::FCGI pod (dhoss)
        - Inline Plack::Util functions in Server::CGI to speed up (mst)

0.9005 Wed Oct 21 20:53:19 PDT 2009
        - Switch to Filesys::Notify::Simple to watch directory to trim down deps
        - Made some dependencies optional since they're actually optional

0.9004 Tue Oct 20 22:57:48 PDT 2009
        - Fixed File::ShareDir dependency (mst)
        - App::File and Middleware::Static now auto follows symlinks (chiba)
        - Implemented plackup -r as Plack::Loader::Reloadable (nothingmuch)
        - Removed poll_cb from Writer middleware
        - Added plackup common command line options: -o for --host and -p for --port 

0.9003 Sun Oct 18 19:16:26 PDT 2009
        - Added POE to Plack::Loader autoload
        - Implemented callback style streaming in selected middlewares
        - Bump up HTTP::Parser::XS to fix memory leaks
        - Added Middleware::Chunked
        - Added Middleware::JSONP
        - Added twitter-stream.psgi example to do streaming server push
        - Fixed Middleware::StackTrace to DWIM in framework generated 500 errors
        - Fixed Restarter to do what doc says

0.9002 Wed Oct 14 11:26:28 PDT 2009
        - Added a workaround in Server::Apache2 when Location and SCRIPT_NAME don't match
        - Use Try::Tiny and parent for smaller memory footprint and better error handling

0.9001 Tue Oct 13 00:55:34 PDT 2009
        - Downgrade EUMM in inc/

0.9000 Tue Oct 13 00:14:01 PDT 2009
        - original version
