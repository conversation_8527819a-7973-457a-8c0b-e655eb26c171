---
abstract: 'faster implementation of HTTP::Headers'
author:
  - '<PERSON><PERSON><PERSON> <<EMAIL>>'
build_requires:
  Test::More: '0.98'
  Test::Requires: '0'
configure_requires:
  Module::Build::Tiny: '0.035'
dynamic_config: 0
generated_by: 'Minilla/v3.1.4, CPAN::Meta::Converter version 2.150010'
license: perl
meta-spec:
  url: http://module-build.sourceforge.net/META-spec-v1.4.html
  version: '1.4'
name: HTTP-Headers-Fast
no_index:
  directory:
    - t
    - xt
    - inc
    - share
    - eg
    - examples
    - author
    - builder
provides:
  HTTP::Headers::Fast:
    file: lib/HTTP/Headers/Fast.pm
    version: '0.22'
requires:
  HTTP::Date: '0'
  perl: '5.008001'
resources:
  bugtracker: https://github.com/tokuhirom/HTTP-Headers-Fast/issues
  homepage: https://github.com/tokuhirom/HTTP-Headers-Fast
  repository: git://github.com/tokuhirom/HTTP-Headers-Fast.git
version: '0.22'
x_contributors:
  - '<PERSON><PERSON> <<EMAIL>>'
  - '<PERSON>osberg <<EMAIL>>'
  - 'Mark Stosberg <<EMAIL>>'
  - 'Masahiro Nagano <<EMAIL>>'
  - 'Neil Bowers <<EMAIL>>'
  - 'Nigel Gregoire <<EMAIL>>'
  - 'Sawyer X <<EMAIL>>'
  - 'Tatsuhiko Miyagawa <<EMAIL>>'
  - 'Tatsuhiko Miyagawa <<EMAIL>>'
  - 'daisuke <daisuke@d0d07461-0603-4401-acd4-de1884942a52>'
  - 'tokuhirom <tokuhirom@d0d07461-0603-4401-acd4-de1884942a52>'
  - 'yappo <yappo@d0d07461-0603-4401-acd4-de1884942a52>'
x_serialization_backend: 'CPAN::Meta::YAML version 0.020'
x_static_install: 1
