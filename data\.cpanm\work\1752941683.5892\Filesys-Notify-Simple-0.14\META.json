{"abstract": "Simple and dumb file system watcher", "author": ["<PERSON><PERSON><PERSON> <<EMAIL>>"], "dynamic_config": 0, "generated_by": "Dist::Milla version v1.0.20, Dist::Zilla version 6.012, CPAN::Meta::Converter version 2.143240", "license": ["perl_5"], "meta-spec": {"url": "http://search.cpan.org/perldoc?CPAN::Meta::Spec", "version": 2}, "name": "Filesys-Notify-Simple", "no_index": {"directory": ["eg", "examples", "inc", "share", "t", "xt"]}, "prereqs": {"configure": {"requires": {"ExtUtils::MakeMaker": "0"}}, "develop": {"requires": {"Dist::Milla": "v1.0.20", "Test::Pod": "1.41"}}, "runtime": {"requires": {"perl": "5.008001"}}, "test": {"requires": {"File::Temp": "0", "Test::More": "0", "Test::SharedFork": "0"}}}, "release_status": "stable", "resources": {"bugtracker": {"web": "https://github.com/miyagawa/Filesys-Notify-Simple/issues"}, "homepage": "https://github.com/miyagawa/Filesys-Notify-Simple", "repository": {"type": "git", "url": "https://github.com/miyagawa/Filesys-Notify-Simple.git", "web": "https://github.com/miyagawa/Filesys-Notify-Simple"}}, "version": "0.14", "x_contributors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <i.p<PERSON><PERSON>@primetech.ru>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "mono <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "Yasutaka ATARASHI <<EMAIL>>"], "x_generated_by_perl": "v5.20.1", "x_serialization_backend": "Cpanel::JSON::XS version 4.09", "x_static_install": 1}