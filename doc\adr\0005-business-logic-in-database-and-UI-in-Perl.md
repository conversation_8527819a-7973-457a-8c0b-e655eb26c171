# 0005 Business logic in database and UI in Perl

Date: Early in the 1.3 release cycle

*Note*: This decision was taken when the application was still
completely a CGI application.

## Status

Accepted

## Context

Given the decisions in ADRs [0001 (Only support PostgreSQL at the database
layer)](./0001-database-restricted-to-postgresql.md) and [0002 (Ensure database
consistency through procedural API)](./0002-database-consistency-procedural-api.md),
it is evident that logic will be built at the database level beyond the logic
required to assert column and row level constraints and referential integrity.

The fact that (more) logic will be built *in* the database, requires design choices
as to the division of what goes into the database and what goes into other layers,
where the other layers in the application are:

 * the web browser
 * the web server
 * the Perl layer
 * the database layer

This decision is *not* about changing the fact that the HTML sent to the
browser is static (and thereby not about changing the role of the browser
in the application stack).

## Decision

The role of the Perl layer is to:

* marshall data from HTTP requests to the database layer; and
* generate the UI for the browser to present

The role of the database is - in addition of what was decided in ADR 0002 - to
deliver the required business functionality.  E.g. to identify invoices payable,
reconcile bank statements, apply payment to open invoices or generate balance
sheet and income statement reports.


## Consequences

- The web browser has no other role than rendering the HTML-based UI as
  generated by the server components.
- The web server has no other role than receiving requests and distributing
  these to available backends
- The Perl layer does minimally what's required to map request data into a
  database connection.
- The database holds significant portions of business logic.
- Non-Perl languages may be used to connect to the database; an important aspect
  given the waning popularity at the time of this decision.

## Annotations

See

* [0104 Business logic in Perl (revised)](./0104-business-logic-in-perl.md)
