=pod

=for comment
DO NOT EDIT. This Pod was generated by Swim v0.1.48.
See http://github.com/ingydotnet/swim-pm#readme

=encoding utf8

=head1 NAME

YAML::Loader - YAML class for loading Perl objects to YAML

=head1 SYNOPSIS

    use YAML::Loader;
    my $loader = YAML::Loader->new;
    my $hash = $loader->load(<<'...');
    foo: bar
    ...

=head1 DESCRIPTION

YAML::Loader is the module that YAML.pm used to deserialize YAML to Perl
objects. It is fully object oriented and usable on its own.

=head1 AUTHOR

Ingy döt Net <<EMAIL>>

=head1 COPYRIGHT

Copyright 2001-2014. Ingy döt Net

This program is free software; you can redistribute it and/or modify it under
the same terms as Perl itself.

See L<http://www.perl.com/perl/misc/Artistic.html>

=cut
