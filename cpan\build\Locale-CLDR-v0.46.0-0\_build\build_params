do{ my $x = [
       {
         'ARGV' => []
       },
       {},
       {
         'PL_files' => undef,
         '_added_to_INC' => [],
         'allow_mb_mismatch' => 0,
         'allow_pureperl' => 0,
         'auto_configure_requires' => 1,
         'autosplit' => undef,
         'base_dir' => 'E:/mohammi/LedgerSMB/cpan/build/Locale-CLDR-v0.46.0-0',
         'bindoc_dirs' => [
                            'blib\\script'
                          ],
         'blib' => 'blib',
         'build_bat' => 0,
         'build_class' => 'Module::Build',
         'build_elements' => [
                               'PL',
                               'support',
                               'pm',
                               'xs',
                               'share_dir',
                               'pod',
                               'script'
                             ],
         'build_requires' => {
                               'File::Spec' => 0,
                               'Test::Exception' => 0,
                               'Test::More' => '0.98',
                               'ok' => 0
                             },
         'build_script' => 'Build',
         'bundle_inc' => [],
         'bundle_inc_preload' => [],
         'c_source' => undef,
         'config' => undef,
         'config_dir' => '_build',
         'configure_requires' => {
                                   'Module::Build' => '0.40'
                                 },
         'conflicts' => {},
         'cover' => undef,
         'cpan_client' => 'cpan',
         'create_license' => undef,
         'create_makefile_pl' => undef,
         'create_packlist' => 1,
         'create_readme' => undef,
         'debug' => undef,
         'debugger' => undef,
         'destdir' => undef,
         'dist_abstract' => undef,
         'dist_author' => [
                            'John Imrie <<EMAIL>>'
                          ],
         'dist_name' => 'Locale-CLDR',
         'dist_suffix' => undef,
         'dist_version' => 'v0.46.0',
         'dist_version_from' => 'lib/Locale/CLDR.pm',
         'dynamic_config' => 1,
         'extra_compiler_flags' => [],
         'extra_linker_flags' => [],
         'extra_manify_args' => undef,
         'get_options' => {},
         'has_config_data' => undef,
         'html_css' => '',
         'include_dirs' => [],
         'install_base' => undef,
         'install_base_relpaths' => {},
         'install_path' => {},
         'install_sets' => {},
         'installdirs' => 'site',
         'libdoc_dirs' => [
                            'blib\\lib',
                            'blib\\arch'
                          ],
         'license' => 'perl',
         'magic_number' => undef,
         'mb_version' => '0.4234',
         'meta_add' => {
                         'keywords' => [
                                         'locale',
                                         'CLDR'
                                       ],
                         'resources' => {
                                          'bugtracker' => 'https://github.com/ThePilgrim/perlcldr/issues',
                                          'homepage' => 'https://github.com/ThePilgrim/perlcldr',
                                          'repository' => 'https://github.com/ThePilgrim/perlcldr.git'
                                        }
                       },
         'meta_merge' => {},
         'metafile' => 'META.yml',
         'metafile2' => 'META.json',
         'module_name' => 'Locale::CLDR',
         'mymetafile' => 'MYMETA.yml',
         'mymetafile2' => 'MYMETA.json',
         'needs_compiler' => !!0,
         'orig_dir' => 'E:/mohammi/LedgerSMB/cpan/build/Locale-CLDR-v0.46.0-0',
         'original_prefix' => {},
         'perl' => 'E:\\mohammi\\LedgerSMB\\perl\\bin\\perl.exe',
         'pm_files' => undef,
         'pod_files' => undef,
         'pollute' => undef,
         'prefix' => undef,
         'prefix_relpaths' => {},
         'prereq_action_types' => [
                                    'requires',
                                    'build_requires',
                                    'test_requires',
                                    'conflicts',
                                    'recommends'
                                  ],
         'program_name' => undef,
         'pureperl_only' => 0,
         'quiet' => undef,
         'recommends' => {},
         'recurse_into' => [],
         'recursive_test_files' => undef,
         'release_status' => 'stable',
         'requires' => {
                         'Class::Load' => 0,
                         'DateTime' => '0.72',
                         'DateTime::Locale' => 0,
                         'List::Util' => '1.45',
                         'Moo' => '2',
                         'MooX::ClassAttribute' => '0.011',
                         'Type::Tiny' => 0,
                         'Unicode::Regex::Set' => 0,
                         'bigfloat' => 0,
                         'namespace::autoclean' => '0.16',
                         'perl' => '5.12.0',
                         'version' => '0.95'
                       },
         'script_files' => undef,
         'scripts' => undef,
         'share_dir' => undef,
         'sign' => undef,
         'tap_harness_args' => {},
         'test_file_exts' => [
                               '.t'
                             ],
         'test_files' => undef,
         'test_requires' => {},
         'use_rcfile' => 1,
         'use_tap_harness' => 0,
         'verbose' => undef,
         'xs_files' => undef
       }
     ];
$x; }