    Installing MooX-ClassAttribute should be straightforward.

INSTALLATION WITH CPANMINUS
    If you have cpanm, you only need one line:

            % cpanm MooX::ClassAttribute

    If you are installing into a system-wide directory, you may need to pass
    the "-S" flag to cpanm, which uses sudo to install the module:

            % cpanm -S MooX::ClassAttribute

INSTALLATION WITH THE CPAN SHELL
    Alternatively, if your CPAN shell is set up, you should just be able to
    do:

            % cpan MooX::ClassAttribute

MANUAL INSTALLATION
    As a last resort, you can manually install it. Download the tarball and
    unpack it.

    Consult the file META.json for a list of pre-requisites. Install these
    first.

    To build MooX-ClassAttribute:

            % perl Makefile.PL
            % make && make test

    Then install it:

            % make install

    If you are installing into a system-wide directory, you may need to run:

            % sudo make install

