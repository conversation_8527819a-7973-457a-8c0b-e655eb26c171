#ifndef _GDFONTMB_H_
#define _GDFONTMB_H_ 1

#ifdef __cplusplus
extern "C"
{
#endif

/*
	This is a header file for gd font, generated using
	bdftogd version 0.5 by <PERSON>, <PERSON><PERSON><PERSON>@fi.muni.cz
	from bdf font
	-misc-fixed-bold-r-normal-sans-13-94-100-100-c-70-iso8859-2
	at Thu Jan  8 13:54:57 1998.
	No copyright info was found in the original bdf.
 */

#include "gd.h"

extern BGD_EXPORT_DATA_PROT gdFontPtr gdFontMediumBold;
BGD_DECLARE(gdFontPtr) gdFontGetMediumBold(void);

#ifdef __cplusplus
}
#endif

#endif
