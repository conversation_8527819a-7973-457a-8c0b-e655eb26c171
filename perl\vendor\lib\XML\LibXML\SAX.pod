=head1 NAME

XML::LibXML::SAX - XML::LibXML direct SAX parser

=head1 DESCRIPTION

XML::LibXML provides an interface to libxml2 direct SAX interface. Through this
interface it is possible to generate SAX events directly while parsing a
document. While using the SAX parser XML::LibXML will not create a DOM Document
tree.

Such an interface is useful if very large XML documents have to be processed
and no DOM functions are required. By using this interface it is possible to
read data stored within an XML document directly into the application data
structures without loading the document into memory.

The SAX interface of XML::LibXML is based on the famous XML::SAX interface. It
uses the generic interface as provided by XML::SAX::Base.

Additionally to the generic functions, which are only able to process entire
documents, XML::LibXML::SAX provides I<<<<<< parse_chunk() >>>>>>. This method generates SAX events from well balanced data such as is often
provided by databases.


=head1 FEATURES

I<<<<<< NOTE: >>>>>> This feature is experimental.

You can enable character data joining which may yield a significant speed boost
in your XML processing in lower markup ratio situations by enabling the
http://xmlns.perl.org/sax/join-character-data feature of this parser. This is
done via the set_feature method like this:



  $p->set_feature('http://xmlns.perl.org/sax/join-character-data', 1);

You can also specify a 0 to disable. The default is to have this feature
disabled.

=head1 AUTHORS

Matt Sergeant,
Christian Glahn,
Petr Pajas


=head1 VERSION

2.0210

=head1 COPYRIGHT

2001-2007, AxKit.com Ltd.

2002-2006, Christian Glahn.

2006-2009, Petr Pajas.

=cut


=head1 LICENSE

This program is free software; you can redistribute it and/or modify it under
the same terms as Perl itself.

