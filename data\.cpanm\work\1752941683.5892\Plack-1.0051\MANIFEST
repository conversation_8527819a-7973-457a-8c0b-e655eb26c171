# This file was automatically generated by Dist::Zilla::Plugin::Manifest v6.025.
Changes
LICENSE
MANIFEST
META.json
META.yml
Makefile.PL
README
benchmarks/ab.pl
benchmarks/fcgi.pl
cpanfile
dist.ini
eg/dot-psgi/Dumper.psgi
eg/dot-psgi/Hello.psgi
eg/dot-psgi/cgi-pm.psgi
eg/dot-psgi/cgi-script.psgi
eg/dot-psgi/echo-stream-sync.psgi
eg/dot-psgi/echo-stream.psgi
eg/dot-psgi/echo.psgi
eg/dot-psgi/error.psgi
eg/dot-psgi/image.psgi
eg/dot-psgi/nonblock-hello.psgi
eg/dot-psgi/plack-req.psgi
eg/dot-psgi/runnable.psgi
eg/dot-psgi/slowapp.psgi
eg/dot-psgi/static.psgi
eg/dot-psgi/static/index.html
eg/dot-psgi/static/test.css
eg/dot-psgi/static/test.js
eg/dot-psgi/twitter-stream.psgi
lib/HTTP/Message/PSGI.pm
lib/HTTP/Server/PSGI.pm
lib/Plack.pm
lib/Plack/App/CGIBin.pm
lib/Plack/App/Cascade.pm
lib/Plack/App/Directory.pm
lib/Plack/App/File.pm
lib/Plack/App/PSGIBin.pm
lib/Plack/App/URLMap.pm
lib/Plack/App/WrapCGI.pm
lib/Plack/Builder.pm
lib/Plack/Component.pm
lib/Plack/HTTPParser.pm
lib/Plack/HTTPParser/PP.pm
lib/Plack/Handler.pm
lib/Plack/Handler/Apache1.pm
lib/Plack/Handler/Apache2.pm
lib/Plack/Handler/Apache2/Registry.pm
lib/Plack/Handler/CGI.pm
lib/Plack/Handler/FCGI.pm
lib/Plack/Handler/HTTP/Server/PSGI.pm
lib/Plack/Handler/Standalone.pm
lib/Plack/LWPish.pm
lib/Plack/Loader.pm
lib/Plack/Loader/Delayed.pm
lib/Plack/Loader/Restarter.pm
lib/Plack/Loader/Shotgun.pm
lib/Plack/MIME.pm
lib/Plack/Middleware.pm
lib/Plack/Middleware/AccessLog.pm
lib/Plack/Middleware/AccessLog/Timed.pm
lib/Plack/Middleware/Auth/Basic.pm
lib/Plack/Middleware/BufferedStreaming.pm
lib/Plack/Middleware/Chunked.pm
lib/Plack/Middleware/Conditional.pm
lib/Plack/Middleware/ConditionalGET.pm
lib/Plack/Middleware/ContentLength.pm
lib/Plack/Middleware/ContentMD5.pm
lib/Plack/Middleware/ErrorDocument.pm
lib/Plack/Middleware/HTTPExceptions.pm
lib/Plack/Middleware/Head.pm
lib/Plack/Middleware/IIS6ScriptNameFix.pm
lib/Plack/Middleware/IIS7KeepAliveFix.pm
lib/Plack/Middleware/JSONP.pm
lib/Plack/Middleware/LighttpdScriptNameFix.pm
lib/Plack/Middleware/Lint.pm
lib/Plack/Middleware/Log4perl.pm
lib/Plack/Middleware/LogDispatch.pm
lib/Plack/Middleware/NullLogger.pm
lib/Plack/Middleware/RearrangeHeaders.pm
lib/Plack/Middleware/Recursive.pm
lib/Plack/Middleware/Refresh.pm
lib/Plack/Middleware/Runtime.pm
lib/Plack/Middleware/SimpleContentFilter.pm
lib/Plack/Middleware/SimpleLogger.pm
lib/Plack/Middleware/StackTrace.pm
lib/Plack/Middleware/Static.pm
lib/Plack/Middleware/XFramework.pm
lib/Plack/Middleware/XSendfile.pm
lib/Plack/Request.pm
lib/Plack/Request/Upload.pm
lib/Plack/Response.pm
lib/Plack/Runner.pm
lib/Plack/TempBuffer.pm
lib/Plack/Test.pm
lib/Plack/Test/MockHTTP.pm
lib/Plack/Test/Server.pm
lib/Plack/Test/Suite.pm
lib/Plack/Util.pm
lib/Plack/Util/Accessor.pm
script/plackup
share/baybridge.jpg
share/face.jpg
t/HTTP-Message-PSGI/content_length.t
t/HTTP-Message-PSGI/empty_delayed_writer.t
t/HTTP-Message-PSGI/empty_streamed_response.t
t/HTTP-Message-PSGI/host.t
t/HTTP-Message-PSGI/path_info.t
t/HTTP-Message-PSGI/unknown_response.t
t/HTTP-Message-PSGI/utf8_req.t
t/HTTP-Server-PSGI/harakiri.t
t/HTTP-Server-PSGI/listen.t
t/HTTP-Server-PSGI/post.t
t/Plack-Builder/builder.t
t/Plack-Builder/mount.t
t/Plack-Builder/oo_interface.t
t/Plack-HTTPParser-PP/simple.t
t/Plack-Handler/FCGIUtils.pm
t/Plack-Handler/apache1.t
t/Plack-Handler/apache2-registry.t
t/Plack-Handler/apache2.t
t/Plack-Handler/cgi.t
t/Plack-Handler/fcgi.t
t/Plack-Handler/fcgi_cleanup.t
t/Plack-Handler/output_encoding.t
t/Plack-Handler/standalone.t
t/Plack-Handler/try_mangle.pl
t/Plack-Loader/auto.t
t/Plack-Loader/auto_fallback.t
t/Plack-Loader/delayed.t
t/Plack-Loader/restarter.t
t/Plack-Loader/restarter_valid.t
t/Plack-Loader/shotgun.t
t/Plack-MIME/add_type.t
t/Plack-MIME/basic.t
t/Plack-MIME/fallback.t
t/Plack-Middleware/access_log.t
t/Plack-Middleware/access_log_timed.t
t/Plack-Middleware/access_log_value_zero.t
t/Plack-Middleware/auth_basic.t
t/Plack-Middleware/auth_basic_env.t
t/Plack-Middleware/auth_basic_simple.t
t/Plack-Middleware/bufferedstreaming.t
t/Plack-Middleware/cascade/basic.t
t/Plack-Middleware/cascade/streaming.t
t/Plack-Middleware/cgi-bin/cgi_dir.cgi
t/Plack-Middleware/cgi-bin/hello.cgi
t/Plack-Middleware/cgi-bin/hello.sh
t/Plack-Middleware/cgi-bin/hello2.cgi
t/Plack-Middleware/cgi-bin/hello3.cgi
t/Plack-Middleware/cgi-bin/utf8.cgi
t/Plack-Middleware/cgibin.t
t/Plack-Middleware/cgibin_exec.t
t/Plack-Middleware/chunked.t
t/Plack-Middleware/component-leak.t
t/Plack-Middleware/component.t
t/Plack-Middleware/conditional.t
t/Plack-Middleware/conditional_new.t
t/Plack-Middleware/conditionalget.t
t/Plack-Middleware/conditionalget_writer.t
t/Plack-Middleware/content_length.t
t/Plack-Middleware/directory.t
t/Plack-Middleware/error_document.t
t/Plack-Middleware/error_document_streaming_app.t
t/Plack-Middleware/errors/404.html
t/Plack-Middleware/errors/500.html
t/Plack-Middleware/file.t
t/Plack-Middleware/head.t
t/Plack-Middleware/head_streaming.t
t/Plack-Middleware/htpasswd
t/Plack-Middleware/httpexceptions.t
t/Plack-Middleware/httpexceptions_streaming.t
t/Plack-Middleware/iis6_script_name_fix.t
t/Plack-Middleware/iis7_keep_alive_fix.t
t/Plack-Middleware/jsonp.t
t/Plack-Middleware/lint.t
t/Plack-Middleware/lint_env.t
t/Plack-Middleware/lint_utf8_false_alarm.t
t/Plack-Middleware/lint_wrong_header_info.t
t/Plack-Middleware/log4perl-category.t
t/Plack-Middleware/log4perl.t
t/Plack-Middleware/log_dispatch.t
t/Plack-Middleware/order.t
t/Plack-Middleware/prefix.t
t/Plack-Middleware/psgibin.t
t/Plack-Middleware/rearrange_headers.t
t/Plack-Middleware/recursive/base.t
t/Plack-Middleware/recursive/streaming.t
t/Plack-Middleware/recursive/throw.t
t/Plack-Middleware/recursive/throw_streaming.t
t/Plack-Middleware/refresh-init.t
t/Plack-Middleware/runtime.t
t/Plack-Middleware/simple_content_filter.t
t/Plack-Middleware/simple_logger.t
t/Plack-Middleware/stacktrace/basic.t
t/Plack-Middleware/stacktrace/force.t
t/Plack-Middleware/stacktrace/multiple_exceptions.t
t/Plack-Middleware/stacktrace/sigdie.t
t/Plack-Middleware/stacktrace/streaming.t
t/Plack-Middleware/stacktrace/utf8.t
t/Plack-Middleware/static.foo
t/Plack-Middleware/static.t
t/Plack-Middleware/static.txt
t/Plack-Middleware/static_env.t
t/Plack-Middleware/urlmap.t
t/Plack-Middleware/urlmap_builder.t
t/Plack-Middleware/urlmap_env.t
t/Plack-Middleware/urlmap_ports.t
t/Plack-Middleware/wrapcgi.t
t/Plack-Middleware/wrapcgi_exec.t
t/Plack-Middleware/xframework.t
t/Plack-Middleware/xsendfile.t
t/Plack-Request/base.t
t/Plack-Request/body-unbuffered.t
t/Plack-Request/body.t
t/Plack-Request/content-on-get.t
t/Plack-Request/content.t
t/Plack-Request/cookie.t
t/Plack-Request/double_port.t
t/Plack-Request/foo1.txt
t/Plack-Request/foo2.txt
t/Plack-Request/headers.t
t/Plack-Request/hostname.t
t/Plack-Request/many_upload.t
t/Plack-Request/multi_read.t
t/Plack-Request/new.t
t/Plack-Request/parameters.t
t/Plack-Request/params.t
t/Plack-Request/path_info.t
t/Plack-Request/path_info_escaped.t
t/Plack-Request/query_string.t
t/Plack-Request/readbody.t
t/Plack-Request/request_uri.t
t/Plack-Request/upload-basename.t
t/Plack-Request/upload-large.t
t/Plack-Request/upload.t
t/Plack-Request/uri.t
t/Plack-Request/uri_utf8.t
t/Plack-Response/body.t
t/Plack-Response/compatible.t
t/Plack-Response/cookie.t
t/Plack-Response/headers.t
t/Plack-Response/new.t
t/Plack-Response/redirect.t
t/Plack-Response/response.t
t/Plack-Response/to_app.t
t/Plack-Runner/options.t
t/Plack-Runner/path.t
t/Plack-TempBuffer/print.t
t/Plack-Test/2args.t
t/Plack-Test/cookie.t
t/Plack-Test/hello.t
t/Plack-Test/hello_server.t
t/Plack-Test/suite.t
t/Plack-Util/Hello.pm
t/Plack-Util/bad.psgi
t/Plack-Util/bad2.psgi
t/Plack-Util/bin/findbin.psgi
t/Plack-Util/can.t
t/Plack-Util/error.psgi
t/Plack-Util/foreach.t
t/Plack-Util/header_exists.t
t/Plack-Util/header_get.t
t/Plack-Util/header_push.t
t/Plack-Util/header_remove.t
t/Plack-Util/header_set.t
t/Plack-Util/headers_obj.t
t/Plack-Util/hello.psgi
t/Plack-Util/inc/hello.psgi
t/Plack-Util/inline_object.t
t/Plack-Util/io_with_path.t
t/Plack-Util/is_real_fh.t
t/Plack-Util/load.t
t/Plack-Util/response_cb.t
t/author-pod-syntax.t
t/test.txt
xt/author-downstream.t
