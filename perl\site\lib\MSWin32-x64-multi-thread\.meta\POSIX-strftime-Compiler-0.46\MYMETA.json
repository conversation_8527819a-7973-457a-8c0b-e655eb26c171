{"abstract": "GNU C library compatible strftime for loggers and servers", "author": ["<PERSON><PERSON><PERSON> <<EMAIL>>"], "dynamic_config": 0, "generated_by": "Minilla/v3.1.23, CPAN::Meta::Converter version 2.150010", "license": ["perl_5"], "meta-spec": {"url": "http://search.cpan.org/perldoc?CPAN::Meta::Spec", "version": 2}, "name": "POSIX-strftime-Compiler", "no_index": {"directory": ["t", "xt", "inc", "share", "eg", "examples", "author", "builder"]}, "prereqs": {"configure": {"requires": {"Module::Build::Tiny": "0.035"}}, "develop": {"requires": {"Test::CPAN::Meta": "0", "Test::MinimumVersion::Fast": "0.04", "Test::PAUSE::Permissions": "0.07", "Test::Pod": "1.41", "Test::Spellunker": "v0.2.7"}}, "runtime": {"requires": {"Carp": "0", "Exporter": "0", "POSIX": "0", "Time::Local": "0", "perl": "5.008001"}, "suggests": {"Time::TZOffset": "0"}}, "test": {"requires": {"Test::More": "0.98"}}}, "provides": {"POSIX::strftime::Compiler": {"file": "lib/POSIX/strftime/Compiler.pm", "version": "0.46"}}, "release_status": "stable", "resources": {"bugtracker": {"web": "https://github.com/kazeburo/POSIX-strftime-Compiler/issues"}, "homepage": "https://github.com/kazeburo/POSIX-strftime-Compiler", "repository": {"type": "git", "url": "https://github.com/kazeburo/POSIX-strftime-Compiler.git", "web": "https://github.com/kazeburo/POSIX-strftime-Compiler"}}, "version": "0.46", "x_contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <piotr.r<PERSON>@gmail.com>", "Slaven Rezic <<EMAIL>>", "<PERSON> <<EMAIL>>"], "x_serialization_backend": "JSON::PP version 4.16", "x_static_install": 1}