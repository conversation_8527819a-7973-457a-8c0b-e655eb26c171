# This is a 'vhost' definition file example for use with Starman/LedgerSMB
# reverse proxying.
#
# Please replace the following parameters:
#
#   * WORKING_DIR
#   * YOUR_SERVER_NAME
#   * SSL_KEY_FILE
#   * SSL_CERT_FILE
#   * SSL_CHAIN_FILE
#   * STARMAN_HOST
#
#

# this block also requires mod_ssl and mod_rewrite to be enabled

# Comment out the 'Listen' and/or 'NameVirtualHost' when Apache complains
Listen 443
# NameVirtualHost is ignored by Apache 2.4
NameVirtualHost *:443
<VirtualHost *:443>
  ServerName YOUR_SERVER_NAME

  DocumentRoot WORKING_DIR/UI

  # If you own a publicly exposed server, consider submitting it
  # to the SSL security tests available at
  #    https://www.ssllabs.com/ssltest/
  SSLEngine On
  SSLCertificateFile SSL_CERT_FILE
  SSLCertificateKeyFile SSL_KEY_FILE
  SSLCertificateChainFile SSL_CHAIN_FILE

  <Location "/">
    SSLRequireSSL
  </Location>

  RewriteEngine On

  # Rewrite '/' URL to /login.pl script
  RewriteRule "^/$" "/login.pl" [R=301,L]
  # "hidden" files (those starting with a dot), don't exist
  RewriteRule "^/\." - [R=404,L]
  # configuration files (those ending in '.conf'), don't exist
  RewriteRule "\.conf$" - [R=404,L]

  RewriteCond "%{REQUEST_FILENAME}" -f
  RewriteRule .* "-" [L]

  RewriteCond "%{REQUEST_URI}" "/[a-z0-9A-Z]+(/.*)"
  RewriteCond "%{DOCUMENT_ROOT}%1" -f
  RewriteRule .* "-" [L]

  # Rewrite non-static content to the application backend
  RequestHeader set X-Forwarded-Proto "https"
  RequestHeader set X-Forwarded-Port "443"
  RewriteCond "%{REQUEST_FILENAME}" !-f
  RewriteCond "%{REQUEST_FILENAME}" !-d
  RewriteRule "^/(.*)" "http://STARMAN_HOST:5762/$1" [P]
  ProxyPassReverse "/" "http://STARMAN_HOST:5762/"
  # If you host LedgerSMB on a path other than "/" in exposed
  # URL space, you need
  #ProxyPassReverseCookiePath / /THE-EXPOSED-PATH

  # Timeout settings, if you receive reverse proxy
  # timeout or 50x errors, especially during company
  # database creation, try to raise these setting.
  Timeout 300
  ProxyTimeout 300

</VirtualHost>
