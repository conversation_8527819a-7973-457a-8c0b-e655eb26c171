---
abstract: 'testing TCP program'
author:
  - '<PERSON><PERSON><PERSON> <<EMAIL>>'
build_requires:
  File::Temp: '0'
  Socket: '0'
  Test::More: '0.98'
configure_requires:
  ExtUtils::MakeMaker: '6.64'
dynamic_config: 0
generated_by: 'Minilla/v3.1.4, CPAN::Meta::Converter version 2.150010'
license: perl
meta-spec:
  url: http://module-build.sourceforge.net/META-spec-v1.4.html
  version: '1.4'
name: Test-TCP
no_index:
  directory:
    - t
    - xt
    - inc
    - share
    - eg
    - examples
    - author
    - builder
provides:
  Net::EmptyPort:
    file: lib/Net/EmptyPort.pm
  Test::TCP:
    file: lib/Test/TCP.pm
    version: '2.22'
  Test::TCP::CheckPort:
    file: lib/Test/TCP/CheckPort.pm
requires:
  IO::Socket::INET: '0'
  IO::Socket::IP: '0'
  Test::More: '0'
  Test::SharedFork: '0.29'
  Time::HiRes: '0'
  perl: '5.008001'
resources:
  bugtracker: https://github.com/tokuhirom/Test-TCP/issues
  homepage: https://github.com/tokuhirom/Test-TCP
  repository: git://github.com/tokuhirom/Test-TCP.git
version: '2.22'
x_contributors:
  - 'Alex Vandiver <<EMAIL>>'
  - 'Andrii Melnykov <<EMAIL>>'
  - 'Bartosz Jakubski <<EMAIL>>'
  - 'Brendan Byrd <<EMAIL>>'
  - 'Chad Granum <<EMAIL>>'
  - 'Christian Walde <<EMAIL>>'
  - 'Dagfinn Ilmari Mannsåker <<EMAIL>>'
  - 'David Precious <<EMAIL>>'
  - 'Ichito Nagata <<EMAIL>>'
  - 'Ivan Baidakou <<EMAIL>>'
  - 'Kazuho Oku <<EMAIL>>'
  - 'Kenichi Ishigaki <<EMAIL>>'
  - 'Kent Fredric <<EMAIL>>'
  - 'Masahiro Nagano <<EMAIL>>'
  - 'Mohammad S Anwar <<EMAIL>>'
  - 'Neil Bowers <<EMAIL>>'
  - 'Pavel Shaydo <<EMAIL>>'
  - 'Pete Houston <<EMAIL>>'
  - 'Petr Písař <<EMAIL>>'
  - 'Syohei YOSHIDA <<EMAIL>>'
  - 'Tatsuhiko Miyagawa <<EMAIL>>'
  - 'Tatsuhiko Miyagawa <<EMAIL>>'
  - 'Thomas Klausner <<EMAIL>>'
  - 'Yasuhiro Matsumoto <<EMAIL>>'
  - 'gfx <<EMAIL>>'
  - 'lestrrat <<EMAIL>>'
  - 'mattn <mattn@d0d07461-0603-4401-acd4-de1884942a52>'
  - 'openstrike <********************>'
  - 'richard.leach <<EMAIL>>'
  - 'yappo <yappo@d0d07461-0603-4401-acd4-de1884942a52>'
  - '奥 一穂 <<EMAIL>>'
x_serialization_backend: 'CPAN::Meta::YAML version 0.020'
x_static_install: 1
