Revision history for Perl extension <PERSON><PERSON><PERSON><PERSON>

0.12 2024-01-05T08:27:56Z

   - Support CHIPS #20
   - Set secure when SameSite=None #21
   Thank you yoshi<PERSON><PERSON><PERSON><PERSON>.

0.11 2019-06-07T06:27:50Z

   - Supportt SameSite=None https://github.com/kazeburo/<PERSON><PERSON>-<PERSON>/pull/15

0.10 2018-09-21T07:40:25Z

   - Add samesite https://github.com/kazeburo/<PERSON><PERSON>-<PERSON>/pull/13
   - Documentation cleanup

0.09 2018-03-03T04:33:20Z

   - +Ny means +N*60*60*24*365 seconds

0.08 2017-07-18T03:36:55Z

   - Disallow , as a delimiter
   - accept max-age=0 #5 (Thank you skaji)

0.07 2016-09-21T01:35:56Z

   - <PERSON><PERSON> quoted cookie values, as per RFC 6265. #8

0.06 2015-06-29T05:53:57Z

   - fix compatibility issue. #4 (Thank you shogo82148)

0.05 2014-11-26T05:49:50Z

   - fix bug. accepts expires => 0

0.04 2014-11-25T02:36:43Z

    - requires perl-5.8.1

0.03 2014-02-26T14:04:28Z

    - use Cookie::Baker::XS if available

0.02 2013-10-25T00:08:34Z

    - fix a document bug

0.01 2013-10-08T01:46:21Z

    - original version

