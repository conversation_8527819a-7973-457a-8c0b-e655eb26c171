#ifndef _GDFONTL_H_
#define _GDFONTL_H_ 1

#ifdef __cplusplus
extern "C"
{
#endif

/*
	This is a header file for gd font, generated using
	bdftogd version 0.5 by <PERSON>, ad<PERSON><PERSON>@fi.muni.cz
	from bdf font
	-misc-fixed-medium-r-normal--16-140-75-75-c-80-iso8859-2
	at Tue Jan  6 19:39:27 1998.

	The original bdf was holding following copyright:
	"Libor Skarvada, <EMAIL>"
 */

#include "gd.h"

extern BGD_EXPORT_DATA_PROT gdFontPtr gdFontLarge;
BGD_DECLARE(gdFontPtr) gdFontGetLarge(void);

#ifdef __cplusplus
}
#endif

#endif
