package Locale::CLDR::LikelySubtags;
# This file auto generated from Data\common\supplemental\likelySubtags.xml
#	on Fri 17 Jan 12:03:31 pm GMT

use strict;
use warnings;
use version;

our $VERSION = version->declare('v0.46.0');

use v5.12.0;
use mro 'c3';
use utf8;
use feature 'unicode_strings';
use Types::Standard qw( Str Int HashRef ArrayRef CodeRef RegexpRef );
use Moo::Role;

has 'likely_subtags' => (
	is			=> 'ro',
	isa			=> HashRef,
	init_arg	=> undef,
	default	=> sub { return {
		'aa'	=> 'aa_Latn_ET',
		'ab'	=> 'ab_Cyrl_GE',
		'abr'	=> 'abr_Latn_GH',
		'ace'	=> 'ace_Latn_ID',
		'ach'	=> 'ach_Latn_UG',
		'ada'	=> 'ada_Latn_GH',
		'ady'	=> 'ady_Cyrl_RU',
		'ae'	=> 'ae_Avst_IR',
		'aeb'	=> 'aeb_Arab_TN',
		'af'	=> 'af_Latn_ZA',
		'agq'	=> 'agq_Latn_CM',
		'aho'	=> 'aho_Ahom_IN',
		'ak'	=> 'ak_Latn_GH',
		'akk'	=> 'akk_Xsux_IQ',
		'aln'	=> 'aln_Latn_XK',
		'alt'	=> 'alt_Cyrl_RU',
		'am'	=> 'am_Ethi_ET',
		'amo'	=> 'amo_Latn_NG',
		'an'	=> 'an_Latn_ES',
		'ann'	=> 'ann_Latn_NG',
		'aoz'	=> 'aoz_Latn_ID',
		'apc'	=> 'apc_Arab_SY',
		'apd'	=> 'apd_Arab_TG',
		'ar'	=> 'ar_Arab_EG',
		'arc'	=> 'arc_Armi_IR',
		'arc_Hatr'	=> 'arc_Hatr_IQ',
		'arc_Nbat'	=> 'arc_Nbat_JO',
		'arc_Palm'	=> 'arc_Palm_SY',
		'arn'	=> 'arn_Latn_CL',
		'aro'	=> 'aro_Latn_BO',
		'arq'	=> 'arq_Arab_DZ',
		'ars'	=> 'ars_Arab_SA',
		'ary'	=> 'ary_Arab_MA',
		'arz'	=> 'arz_Arab_EG',
		'as'	=> 'as_Beng_IN',
		'asa'	=> 'asa_Latn_TZ',
		'ase'	=> 'ase_Sgnw_US',
		'ast'	=> 'ast_Latn_ES',
		'atj'	=> 'atj_Latn_CA',
		'av'	=> 'av_Cyrl_RU',
		'awa'	=> 'awa_Deva_IN',
		'ay'	=> 'ay_Latn_BO',
		'az'	=> 'az_Latn_AZ',
		'az_IQ'	=> 'az_Arab_IQ',
		'az_IR'	=> 'az_Arab_IR',
		'az_RU'	=> 'az_Cyrl_RU',
		'az_Arab'	=> 'az_Arab_IR',
		'ba'	=> 'ba_Cyrl_RU',
		'bal'	=> 'bal_Arab_PK',
		'ban'	=> 'ban_Latn_ID',
		'bap'	=> 'bap_Deva_NP',
		'bap_Krai'	=> 'bap_Krai_IN',
		'bar'	=> 'bar_Latn_AT',
		'bas'	=> 'bas_Latn_CM',
		'bax'	=> 'bax_Bamu_CM',
		'bbc'	=> 'bbc_Latn_ID',
		'bbj'	=> 'bbj_Latn_CM',
		'bci'	=> 'bci_Latn_CI',
		'be'	=> 'be_Cyrl_BY',
		'bej'	=> 'bej_Arab_SD',
		'bem'	=> 'bem_Latn_ZM',
		'bew'	=> 'bew_Latn_ID',
		'bez'	=> 'bez_Latn_TZ',
		'bfd'	=> 'bfd_Latn_CM',
		'bfq'	=> 'bfq_Taml_IN',
		'bft'	=> 'bft_Arab_PK',
		'bfy'	=> 'bfy_Deva_IN',
		'bg'	=> 'bg_Cyrl_BG',
		'bgc'	=> 'bgc_Deva_IN',
		'bgn'	=> 'bgn_Arab_PK',
		'bgx'	=> 'bgx_Grek_TR',
		'bhb'	=> 'bhb_Deva_IN',
		'bhi'	=> 'bhi_Deva_IN',
		'bho'	=> 'bho_Deva_IN',
		'bi'	=> 'bi_Latn_VU',
		'bik'	=> 'bik_Latn_PH',
		'bin'	=> 'bin_Latn_NG',
		'bjj'	=> 'bjj_Deva_IN',
		'bjn'	=> 'bjn_Latn_ID',
		'bjt'	=> 'bjt_Latn_SN',
		'bkm'	=> 'bkm_Latn_CM',
		'bku'	=> 'bku_Latn_PH',
		'bla'	=> 'bla_Latn_CA',
		'blo'	=> 'blo_Latn_BJ',
		'blt'	=> 'blt_Tavt_VN',
		'bm'	=> 'bm_Latn_ML',
		'bmq'	=> 'bmq_Latn_ML',
		'bn'	=> 'bn_Beng_BD',
		'bo'	=> 'bo_Tibt_CN',
		'bpy'	=> 'bpy_Beng_IN',
		'bqi'	=> 'bqi_Arab_IR',
		'bqv'	=> 'bqv_Latn_CI',
		'br'	=> 'br_Latn_FR',
		'bra'	=> 'bra_Deva_IN',
		'brh'	=> 'brh_Arab_PK',
		'brx'	=> 'brx_Deva_IN',
		'bs'	=> 'bs_Latn_BA',
		'bsc'	=> 'bsc_Latn_SN',
		'bsq'	=> 'bsq_Bass_LR',
		'bss'	=> 'bss_Latn_CM',
		'bto'	=> 'bto_Latn_PH',
		'btv'	=> 'btv_Deva_PK',
		'bua'	=> 'bua_Cyrl_RU',
		'buc'	=> 'buc_Latn_YT',
		'bug'	=> 'bug_Latn_ID',
		'bum'	=> 'bum_Latn_CM',
		'bvb'	=> 'bvb_Latn_GQ',
		'byn'	=> 'byn_Ethi_ER',
		'byv'	=> 'byv_Latn_CM',
		'bze'	=> 'bze_Latn_ML',
		'ca'	=> 'ca_Latn_ES',
		'cad'	=> 'cad_Latn_US',
		'cch'	=> 'cch_Latn_NG',
		'ccp'	=> 'ccp_Cakm_BD',
		'ce'	=> 'ce_Cyrl_RU',
		'ceb'	=> 'ceb_Latn_PH',
		'cgg'	=> 'cgg_Latn_UG',
		'ch'	=> 'ch_Latn_GU',
		'chk'	=> 'chk_Latn_FM',
		'chm'	=> 'chm_Cyrl_RU',
		'cho'	=> 'cho_Latn_US',
		'chp'	=> 'chp_Latn_CA',
		'chr'	=> 'chr_Cher_US',
		'cic'	=> 'cic_Latn_US',
		'cja'	=> 'cja_Arab_KH',
		'cjm'	=> 'cjm_Cham_VN',
		'ckb'	=> 'ckb_Arab_IQ',
		'clc'	=> 'clc_Latn_CA',
		'cmg'	=> 'cmg_Soyo_MN',
		'co'	=> 'co_Latn_FR',
		'cop'	=> 'cop_Copt_EG',
		'cps'	=> 'cps_Latn_PH',
		'cr'	=> 'cr_Cans_CA',
		'crg'	=> 'crg_Latn_CA',
		'crh'	=> 'crh_Cyrl_UA',
		'crk'	=> 'crk_Cans_CA',
		'crl'	=> 'crl_Cans_CA',
		'crs'	=> 'crs_Latn_SC',
		'cs'	=> 'cs_Latn_CZ',
		'csb'	=> 'csb_Latn_PL',
		'csw'	=> 'csw_Cans_CA',
		'ctd'	=> 'ctd_Pauc_MM',
		'cu'	=> 'cu_Cyrl_RU',
		'cu_Glag'	=> 'cu_Glag_BG',
		'cv'	=> 'cv_Cyrl_RU',
		'cy'	=> 'cy_Latn_GB',
		'da'	=> 'da_Latn_DK',
		'dak'	=> 'dak_Latn_US',
		'dar'	=> 'dar_Cyrl_RU',
		'dav'	=> 'dav_Latn_KE',
		'dcc'	=> 'dcc_Arab_IN',
		'de'	=> 'de_Latn_DE',
		'den'	=> 'den_Latn_CA',
		'dgr'	=> 'dgr_Latn_CA',
		'dje'	=> 'dje_Latn_NE',
		'dmf'	=> 'dmf_Medf_NG',
		'dnj'	=> 'dnj_Latn_CI',
		'doi'	=> 'doi_Deva_IN',
		'dsb'	=> 'dsb_Latn_DE',
		'dtm'	=> 'dtm_Latn_ML',
		'dtp'	=> 'dtp_Latn_MY',
		'dty'	=> 'dty_Deva_NP',
		'dua'	=> 'dua_Latn_CM',
		'dv'	=> 'dv_Thaa_MV',
		'dyo'	=> 'dyo_Latn_SN',
		'dyu'	=> 'dyu_Latn_BF',
		'dz'	=> 'dz_Tibt_BT',
		'ebu'	=> 'ebu_Latn_KE',
		'ecy'	=> 'ecy_Cprt_CY',
		'ee'	=> 'ee_Latn_GH',
		'efi'	=> 'efi_Latn_NG',
		'egl'	=> 'egl_Latn_IT',
		'egy'	=> 'egy_Egyp_EG',
		'eky'	=> 'eky_Kali_MM',
		'el'	=> 'el_Grek_GR',
		'en'	=> 'en_Latn_US',
		'en_Shaw'	=> 'en_Shaw_GB',
		'eo'	=> 'eo_Latn_001',
		'es'	=> 'es_Latn_ES',
		'esg'	=> 'esg_Gonm_IN',
		'esu'	=> 'esu_Latn_US',
		'et'	=> 'et_Latn_EE',
		'ett'	=> 'ett_Ital_IT',
		'eu'	=> 'eu_Latn_ES',
		'ewo'	=> 'ewo_Latn_CM',
		'ext'	=> 'ext_Latn_ES',
		'fa'	=> 'fa_Arab_IR',
		'fan'	=> 'fan_Latn_GQ',
		'fbl'	=> 'fbl_Latn_PH',
		'ff'	=> 'ff_Latn_SN',
		'ff_Adlm'	=> 'ff_Adlm_GN',
		'ffm'	=> 'ffm_Latn_ML',
		'fi'	=> 'fi_Latn_FI',
		'fia'	=> 'fia_Arab_SD',
		'fil'	=> 'fil_Latn_PH',
		'fit'	=> 'fit_Latn_SE',
		'fj'	=> 'fj_Latn_FJ',
		'fo'	=> 'fo_Latn_FO',
		'fon'	=> 'fon_Latn_BJ',
		'fr'	=> 'fr_Latn_FR',
		'frc'	=> 'frc_Latn_US',
		'frp'	=> 'frp_Latn_FR',
		'frr'	=> 'frr_Latn_DE',
		'frs'	=> 'frs_Latn_DE',
		'fub'	=> 'fub_Arab_CM',
		'fud'	=> 'fud_Latn_WF',
		'fuf'	=> 'fuf_Latn_GN',
		'fuq'	=> 'fuq_Latn_NE',
		'fur'	=> 'fur_Latn_IT',
		'fuv'	=> 'fuv_Latn_NG',
		'fvr'	=> 'fvr_Latn_SD',
		'fy'	=> 'fy_Latn_NL',
		'ga'	=> 'ga_Latn_IE',
		'gaa'	=> 'gaa_Latn_GH',
		'gag'	=> 'gag_Latn_MD',
		'gan'	=> 'gan_Hans_CN',
		'gay'	=> 'gay_Latn_ID',
		'gbm'	=> 'gbm_Deva_IN',
		'gbz'	=> 'gbz_Arab_IR',
		'gcr'	=> 'gcr_Latn_GF',
		'gd'	=> 'gd_Latn_GB',
		'gez'	=> 'gez_Ethi_ET',
		'gil'	=> 'gil_Latn_KI',
		'gjk'	=> 'gjk_Arab_PK',
		'gju'	=> 'gju_Arab_PK',
		'gl'	=> 'gl_Latn_ES',
		'glk'	=> 'glk_Arab_IR',
		'gmy'	=> 'gmy_Linb_GR',
		'gn'	=> 'gn_Latn_PY',
		'gon'	=> 'gon_Deva_IN',
		'gor'	=> 'gor_Latn_ID',
		'gos'	=> 'gos_Latn_NL',
		'got'	=> 'got_Goth_UA',
		'grc'	=> 'grc_Grek_GR',
		'grt'	=> 'grt_Beng_IN',
		'gsw'	=> 'gsw_Latn_CH',
		'gu'	=> 'gu_Gujr_IN',
		'gub'	=> 'gub_Latn_BR',
		'guc'	=> 'guc_Latn_CO',
		'gur'	=> 'gur_Latn_GH',
		'guz'	=> 'guz_Latn_KE',
		'gv'	=> 'gv_Latn_IM',
		'gvr'	=> 'gvr_Deva_NP',
		'gwi'	=> 'gwi_Latn_CA',
		'ha'	=> 'ha_Latn_NG',
		'ha_CM'	=> 'ha_Arab_CM',
		'ha_SD'	=> 'ha_Arab_SD',
		'hak'	=> 'hak_Hans_CN',
		'haw'	=> 'haw_Latn_US',
		'haz'	=> 'haz_Arab_AF',
		'he'	=> 'he_Hebr_IL',
		'hi'	=> 'hi_Deva_IN',
		'hif'	=> 'hif_Deva_FJ',
		'hil'	=> 'hil_Latn_PH',
		'hlu'	=> 'hlu_Hluw_TR',
		'hmd'	=> 'hmd_Plrd_CN',
		'hnd'	=> 'hnd_Arab_PK',
		'hne'	=> 'hne_Deva_IN',
		'hnj'	=> 'hnj_Hmnp_US',
		'hnj_Hmng'	=> 'hnj_Hmng_LA',
		'hnn'	=> 'hnn_Latn_PH',
		'hno'	=> 'hno_Arab_PK',
		'ho'	=> 'ho_Latn_PG',
		'hoc'	=> 'hoc_Deva_IN',
		'hoj'	=> 'hoj_Deva_IN',
		'hr'	=> 'hr_Latn_HR',
		'hsb'	=> 'hsb_Latn_DE',
		'hsn'	=> 'hsn_Hans_CN',
		'ht'	=> 'ht_Latn_HT',
		'hu'	=> 'hu_Latn_HU',
		'hur'	=> 'hur_Latn_CA',
		'hy'	=> 'hy_Armn_AM',
		'hz'	=> 'hz_Latn_NA',
		'ia'	=> 'ia_Latn_001',
		'iba'	=> 'iba_Latn_MY',
		'ibb'	=> 'ibb_Latn_NG',
		'id'	=> 'id_Latn_ID',
		'ie'	=> 'ie_Latn_EE',
		'ife'	=> 'ife_Latn_TG',
		'ig'	=> 'ig_Latn_NG',
		'ii'	=> 'ii_Yiii_CN',
		'ik'	=> 'ik_Latn_US',
		'ilo'	=> 'ilo_Latn_PH',
		'in'	=> 'in_Latn_ID',
		'inh'	=> 'inh_Cyrl_RU',
		'io'	=> 'io_Latn_001',
		'is'	=> 'is_Latn_IS',
		'it'	=> 'it_Latn_IT',
		'iu'	=> 'iu_Cans_CA',
		'iw'	=> 'iw_Hebr_IL',
		'izh'	=> 'izh_Latn_RU',
		'ja'	=> 'ja_Jpan_JP',
		'jam'	=> 'jam_Latn_JM',
		'jbo'	=> 'jbo_Latn_001',
		'jgo'	=> 'jgo_Latn_CM',
		'ji'	=> 'ji_Hebr_UA',
		'jmc'	=> 'jmc_Latn_TZ',
		'jml'	=> 'jml_Deva_NP',
		'jut'	=> 'jut_Latn_DK',
		'jv'	=> 'jv_Latn_ID',
		'jw'	=> 'jw_Latn_ID',
		'ka'	=> 'ka_Geor_GE',
		'kaa'	=> 'kaa_Cyrl_UZ',
		'kab'	=> 'kab_Latn_DZ',
		'kac'	=> 'kac_Latn_MM',
		'kaj'	=> 'kaj_Latn_NG',
		'kam'	=> 'kam_Latn_KE',
		'kao'	=> 'kao_Latn_ML',
		'kaw'	=> 'kaw_Bali_ID',
		'kbd'	=> 'kbd_Cyrl_RU',
		'kby'	=> 'kby_Arab_NE',
		'kcg'	=> 'kcg_Latn_NG',
		'kck'	=> 'kck_Latn_ZW',
		'kde'	=> 'kde_Latn_TZ',
		'kdh'	=> 'kdh_Latn_TG',
		'kdt'	=> 'kdt_Thai_TH',
		'kea'	=> 'kea_Latn_CV',
		'ken'	=> 'ken_Latn_CM',
		'kfo'	=> 'kfo_Latn_CI',
		'kfr'	=> 'kfr_Deva_IN',
		'kfy'	=> 'kfy_Deva_IN',
		'kg'	=> 'kg_Latn_CD',
		'kge'	=> 'kge_Latn_ID',
		'kgp'	=> 'kgp_Latn_BR',
		'kha'	=> 'kha_Latn_IN',
		'khb'	=> 'khb_Talu_CN',
		'khn'	=> 'khn_Deva_IN',
		'khq'	=> 'khq_Latn_ML',
		'kht'	=> 'kht_Mymr_IN',
		'khw'	=> 'khw_Arab_PK',
		'ki'	=> 'ki_Latn_KE',
		'kiu'	=> 'kiu_Latn_TR',
		'kj'	=> 'kj_Latn_NA',
		'kjg'	=> 'kjg_Laoo_LA',
		'kk'	=> 'kk_Cyrl_KZ',
		'kk_AF'	=> 'kk_Arab_AF',
		'kk_CN'	=> 'kk_Arab_CN',
		'kk_IR'	=> 'kk_Arab_IR',
		'kk_MN'	=> 'kk_Arab_MN',
		'kk_Arab'	=> 'kk_Arab_CN',
		'kkj'	=> 'kkj_Latn_CM',
		'kl'	=> 'kl_Latn_GL',
		'kln'	=> 'kln_Latn_KE',
		'km'	=> 'km_Khmr_KH',
		'kmb'	=> 'kmb_Latn_AO',
		'kn'	=> 'kn_Knda_IN',
		'knf'	=> 'knf_Latn_GW',
		'knn'	=> 'knn_Deva_IN',
		'ko'	=> 'ko_Kore_KR',
		'koi'	=> 'koi_Cyrl_RU',
		'kok'	=> 'kok_Deva_IN',
		'kos'	=> 'kos_Latn_FM',
		'kpe'	=> 'kpe_Latn_LR',
		'krc'	=> 'krc_Cyrl_RU',
		'kri'	=> 'kri_Latn_SL',
		'krj'	=> 'krj_Latn_PH',
		'krl'	=> 'krl_Latn_RU',
		'kru'	=> 'kru_Deva_IN',
		'ks'	=> 'ks_Arab_IN',
		'ksb'	=> 'ksb_Latn_TZ',
		'ksf'	=> 'ksf_Latn_CM',
		'ksh'	=> 'ksh_Latn_DE',
		'ku'	=> 'ku_Latn_TR',
		'ku_LB'	=> 'ku_Arab_LB',
		'ku_Arab'	=> 'ku_Arab_IQ',
		'ku_Yezi'	=> 'ku_Yezi_GE',
		'kum'	=> 'kum_Cyrl_RU',
		'kv'	=> 'kv_Cyrl_RU',
		'kvr'	=> 'kvr_Latn_ID',
		'kvx'	=> 'kvx_Arab_PK',
		'kw'	=> 'kw_Latn_GB',
		'kwk'	=> 'kwk_Latn_CA',
		'kxm'	=> 'kxm_Thai_TH',
		'kxp'	=> 'kxp_Arab_PK',
		'kxv'	=> 'kxv_Latn_IN',
		'ky'	=> 'ky_Cyrl_KG',
		'ky_CN'	=> 'ky_Arab_CN',
		'ky_TR'	=> 'ky_Latn_TR',
		'ky_Arab'	=> 'ky_Arab_CN',
		'ky_Latn'	=> 'ky_Latn_TR',
		'la'	=> 'la_Latn_VA',
		'lab'	=> 'lab_Lina_GR',
		'lad'	=> 'lad_Hebr_IL',
		'lag'	=> 'lag_Latn_TZ',
		'lah'	=> 'lah_Arab_PK',
		'laj'	=> 'laj_Latn_UG',
		'lb'	=> 'lb_Latn_LU',
		'lbe'	=> 'lbe_Cyrl_RU',
		'lbw'	=> 'lbw_Latn_ID',
		'lcp'	=> 'lcp_Thai_CN',
		'lep'	=> 'lep_Lepc_IN',
		'lez'	=> 'lez_Cyrl_RU',
		'lg'	=> 'lg_Latn_UG',
		'li'	=> 'li_Latn_NL',
		'lif'	=> 'lif_Deva_NP',
		'lif_Limb'	=> 'lif_Limb_IN',
		'lij'	=> 'lij_Latn_IT',
		'lil'	=> 'lil_Latn_CA',
		'lis'	=> 'lis_Lisu_CN',
		'ljp'	=> 'ljp_Latn_ID',
		'lki'	=> 'lki_Arab_IR',
		'lkt'	=> 'lkt_Latn_US',
		'lld'	=> 'lld_Latn_IT',
		'lmn'	=> 'lmn_Telu_IN',
		'lmo'	=> 'lmo_Latn_IT',
		'ln'	=> 'ln_Latn_CD',
		'lo'	=> 'lo_Laoo_LA',
		'lol'	=> 'lol_Latn_CD',
		'loz'	=> 'loz_Latn_ZM',
		'lrc'	=> 'lrc_Arab_IR',
		'lt'	=> 'lt_Latn_LT',
		'ltg'	=> 'ltg_Latn_LV',
		'lu'	=> 'lu_Latn_CD',
		'lua'	=> 'lua_Latn_CD',
		'luo'	=> 'luo_Latn_KE',
		'luy'	=> 'luy_Latn_KE',
		'luz'	=> 'luz_Arab_IR',
		'lv'	=> 'lv_Latn_LV',
		'lwl'	=> 'lwl_Thai_TH',
		'lzh'	=> 'lzh_Hans_CN',
		'lzz'	=> 'lzz_Latn_TR',
		'mad'	=> 'mad_Latn_ID',
		'maf'	=> 'maf_Latn_CM',
		'mag'	=> 'mag_Deva_IN',
		'mai'	=> 'mai_Deva_IN',
		'mak'	=> 'mak_Latn_ID',
		'man'	=> 'man_Latn_GM',
		'man_GN'	=> 'man_Nkoo_GN',
		'man_Nkoo'	=> 'man_Nkoo_GN',
		'mas'	=> 'mas_Latn_KE',
		'maz'	=> 'maz_Latn_MX',
		'mdf'	=> 'mdf_Cyrl_RU',
		'mdh'	=> 'mdh_Latn_PH',
		'mdr'	=> 'mdr_Latn_ID',
		'men'	=> 'men_Latn_SL',
		'mer'	=> 'mer_Latn_KE',
		'mey'	=> 'mey_Latn_SN',
		'mfa'	=> 'mfa_Arab_TH',
		'mfe'	=> 'mfe_Latn_MU',
		'mfv'	=> 'mfv_Latn_SN',
		'mg'	=> 'mg_Latn_MG',
		'mgh'	=> 'mgh_Latn_MZ',
		'mgo'	=> 'mgo_Latn_CM',
		'mgp'	=> 'mgp_Deva_NP',
		'mgy'	=> 'mgy_Latn_TZ',
		'mh'	=> 'mh_Latn_MH',
		'mhn'	=> 'mhn_Latn_IT',
		'mi'	=> 'mi_Latn_NZ',
		'mic'	=> 'mic_Latn_CA',
		'min'	=> 'min_Latn_ID',
		'mk'	=> 'mk_Cyrl_MK',
		'ml'	=> 'ml_Mlym_IN',
		'mls'	=> 'mls_Latn_SD',
		'mn'	=> 'mn_Cyrl_MN',
		'mn_CN'	=> 'mn_Mong_CN',
		'mn_Mong'	=> 'mn_Mong_CN',
		'mni'	=> 'mni_Beng_IN',
		'mnw'	=> 'mnw_Mymr_MM',
		'mo'	=> 'mo_Latn_RO',
		'moe'	=> 'moe_Latn_CA',
		'moh'	=> 'moh_Latn_CA',
		'mos'	=> 'mos_Latn_BF',
		'mr'	=> 'mr_Deva_IN',
		'mrd'	=> 'mrd_Deva_NP',
		'mrj'	=> 'mrj_Cyrl_RU',
		'mro'	=> 'mro_Mroo_BD',
		'ms'	=> 'ms_Latn_MY',
		'ms_CC'	=> 'ms_Arab_CC',
		'mt'	=> 'mt_Latn_MT',
		'mtr'	=> 'mtr_Deva_IN',
		'mua'	=> 'mua_Latn_CM',
		'mus'	=> 'mus_Latn_US',
		'mvy'	=> 'mvy_Arab_PK',
		'mwk'	=> 'mwk_Latn_ML',
		'mwr'	=> 'mwr_Deva_IN',
		'mwv'	=> 'mwv_Latn_ID',
		'mww'	=> 'mww_Hmnp_US',
		'mxc'	=> 'mxc_Latn_ZW',
		'my'	=> 'my_Mymr_MM',
		'myv'	=> 'myv_Cyrl_RU',
		'myx'	=> 'myx_Latn_UG',
		'myz'	=> 'myz_Mand_IR',
		'mzn'	=> 'mzn_Arab_IR',
		'na'	=> 'na_Latn_NR',
		'nan'	=> 'nan_Hans_CN',
		'nap'	=> 'nap_Latn_IT',
		'naq'	=> 'naq_Latn_NA',
		'nb'	=> 'nb_Latn_NO',
		'nch'	=> 'nch_Latn_MX',
		'nd'	=> 'nd_Latn_ZW',
		'ndc'	=> 'ndc_Latn_MZ',
		'nds'	=> 'nds_Latn_DE',
		'ne'	=> 'ne_Deva_NP',
		'new'	=> 'new_Deva_NP',
		'ng'	=> 'ng_Latn_NA',
		'ngl'	=> 'ngl_Latn_MZ',
		'nhe'	=> 'nhe_Latn_MX',
		'nhw'	=> 'nhw_Latn_MX',
		'nij'	=> 'nij_Latn_ID',
		'niu'	=> 'niu_Latn_NU',
		'njo'	=> 'njo_Latn_IN',
		'nl'	=> 'nl_Latn_NL',
		'nmg'	=> 'nmg_Latn_CM',
		'nn'	=> 'nn_Latn_NO',
		'nnh'	=> 'nnh_Latn_CM',
		'nnp'	=> 'nnp_Wcho_IN',
		'no'	=> 'no_Latn_NO',
		'nod'	=> 'nod_Lana_TH',
		'noe'	=> 'noe_Deva_IN',
		'non'	=> 'non_Runr_SE',
		'nqo'	=> 'nqo_Nkoo_GN',
		'nr'	=> 'nr_Latn_ZA',
		'nsk'	=> 'nsk_Cans_CA',
		'nso'	=> 'nso_Latn_ZA',
		'nst'	=> 'nst_Tnsa_IN',
		'nus'	=> 'nus_Latn_SS',
		'nv'	=> 'nv_Latn_US',
		'nxq'	=> 'nxq_Latn_CN',
		'ny'	=> 'ny_Latn_MW',
		'nym'	=> 'nym_Latn_TZ',
		'nyn'	=> 'nyn_Latn_UG',
		'nzi'	=> 'nzi_Latn_GH',
		'oc'	=> 'oc_Latn_FR',
		'oj'	=> 'oj_Cans_CA',
		'ojs'	=> 'ojs_Cans_CA',
		'oka'	=> 'oka_Latn_CA',
		'om'	=> 'om_Latn_ET',
		'or'	=> 'or_Orya_IN',
		'os'	=> 'os_Cyrl_GE',
		'osa'	=> 'osa_Osge_US',
		'otk'	=> 'otk_Orkh_MN',
		'oui'	=> 'oui_Ougr_CN',
		'pa'	=> 'pa_Guru_IN',
		'pa_PK'	=> 'pa_Arab_PK',
		'pa_Arab'	=> 'pa_Arab_PK',
		'pag'	=> 'pag_Latn_PH',
		'pal'	=> 'pal_Phli_IR',
		'pal_Phlp'	=> 'pal_Phlp_CN',
		'pam'	=> 'pam_Latn_PH',
		'pap'	=> 'pap_Latn_CW',
		'pau'	=> 'pau_Latn_PW',
		'pcd'	=> 'pcd_Latn_FR',
		'pcm'	=> 'pcm_Latn_NG',
		'pdc'	=> 'pdc_Latn_US',
		'pdt'	=> 'pdt_Latn_CA',
		'peo'	=> 'peo_Xpeo_IR',
		'pfl'	=> 'pfl_Latn_DE',
		'phn'	=> 'phn_Phnx_LB',
		'pis'	=> 'pis_Latn_SB',
		'pka'	=> 'pka_Brah_IN',
		'pko'	=> 'pko_Latn_KE',
		'pl'	=> 'pl_Latn_PL',
		'pms'	=> 'pms_Latn_IT',
		'pnt'	=> 'pnt_Grek_GR',
		'pon'	=> 'pon_Latn_FM',
		'pqm'	=> 'pqm_Latn_CA',
		'pra'	=> 'pra_Khar_PK',
		'prd'	=> 'prd_Arab_IR',
		'prg'	=> 'prg_Latn_PL',
		'ps'	=> 'ps_Arab_AF',
		'pt'	=> 'pt_Latn_BR',
		'puu'	=> 'puu_Latn_GA',
		'qu'	=> 'qu_Latn_PE',
		'quc'	=> 'quc_Latn_GT',
		'qug'	=> 'qug_Latn_EC',
		'raj'	=> 'raj_Deva_IN',
		'rcf'	=> 'rcf_Latn_RE',
		'rej'	=> 'rej_Latn_ID',
		'rgn'	=> 'rgn_Latn_IT',
		'rhg'	=> 'rhg_Rohg_MM',
		'ria'	=> 'ria_Latn_IN',
		'rif'	=> 'rif_Latn_MA',
		'rjs'	=> 'rjs_Deva_NP',
		'rkt'	=> 'rkt_Beng_BD',
		'rm'	=> 'rm_Latn_CH',
		'rmf'	=> 'rmf_Latn_FI',
		'rmo'	=> 'rmo_Latn_CH',
		'rmt'	=> 'rmt_Arab_IR',
		'rmu'	=> 'rmu_Latn_SE',
		'rn'	=> 'rn_Latn_BI',
		'rng'	=> 'rng_Latn_MZ',
		'ro'	=> 'ro_Latn_RO',
		'rob'	=> 'rob_Latn_ID',
		'rof'	=> 'rof_Latn_TZ',
		'rtm'	=> 'rtm_Latn_FJ',
		'ru'	=> 'ru_Cyrl_RU',
		'rue'	=> 'rue_Cyrl_UA',
		'rug'	=> 'rug_Latn_SB',
		'rw'	=> 'rw_Latn_RW',
		'rwk'	=> 'rwk_Latn_TZ',
		'ryu'	=> 'ryu_Kana_JP',
		'sa'	=> 'sa_Deva_IN',
		'saf'	=> 'saf_Latn_GH',
		'sah'	=> 'sah_Cyrl_RU',
		'saq'	=> 'saq_Latn_KE',
		'sas'	=> 'sas_Latn_ID',
		'sat'	=> 'sat_Olck_IN',
		'sav'	=> 'sav_Latn_SN',
		'saz'	=> 'saz_Saur_IN',
		'sbp'	=> 'sbp_Latn_TZ',
		'sc'	=> 'sc_Latn_IT',
		'sck'	=> 'sck_Deva_IN',
		'scn'	=> 'scn_Latn_IT',
		'sco'	=> 'sco_Latn_GB',
		'sd'	=> 'sd_Arab_PK',
		'sd_IN'	=> 'sd_Deva_IN',
		'sd_Deva'	=> 'sd_Deva_IN',
		'sd_Khoj'	=> 'sd_Khoj_IN',
		'sd_Sind'	=> 'sd_Sind_IN',
		'sdc'	=> 'sdc_Latn_IT',
		'sdh'	=> 'sdh_Arab_IR',
		'se'	=> 'se_Latn_NO',
		'sef'	=> 'sef_Latn_CI',
		'seh'	=> 'seh_Latn_MZ',
		'sei'	=> 'sei_Latn_MX',
		'ses'	=> 'ses_Latn_ML',
		'sg'	=> 'sg_Latn_CF',
		'sga'	=> 'sga_Ogam_IE',
		'sgs'	=> 'sgs_Latn_LT',
		'shi'	=> 'shi_Tfng_MA',
		'shn'	=> 'shn_Mymr_MM',
		'si'	=> 'si_Sinh_LK',
		'sid'	=> 'sid_Latn_ET',
		'sk'	=> 'sk_Latn_SK',
		'skr'	=> 'skr_Arab_PK',
		'sl'	=> 'sl_Latn_SI',
		'sli'	=> 'sli_Latn_PL',
		'sly'	=> 'sly_Latn_ID',
		'sm'	=> 'sm_Latn_WS',
		'sma'	=> 'sma_Latn_SE',
		'smj'	=> 'smj_Latn_SE',
		'smn'	=> 'smn_Latn_FI',
		'smp'	=> 'smp_Samr_IL',
		'sms'	=> 'sms_Latn_FI',
		'sn'	=> 'sn_Latn_ZW',
		'snf'	=> 'snf_Latn_SN',
		'snk'	=> 'snk_Latn_ML',
		'so'	=> 'so_Latn_SO',
		'sog'	=> 'sog_Sogd_UZ',
		'sou'	=> 'sou_Thai_TH',
		'sq'	=> 'sq_Latn_AL',
		'sr'	=> 'sr_Cyrl_RS',
		'sr_ME'	=> 'sr_Latn_ME',
		'sr_RO'	=> 'sr_Latn_RO',
		'sr_RU'	=> 'sr_Latn_RU',
		'sr_TR'	=> 'sr_Latn_TR',
		'srb'	=> 'srb_Sora_IN',
		'srn'	=> 'srn_Latn_SR',
		'srr'	=> 'srr_Latn_SN',
		'srx'	=> 'srx_Deva_IN',
		'ss'	=> 'ss_Latn_ZA',
		'ssy'	=> 'ssy_Latn_ER',
		'st'	=> 'st_Latn_ZA',
		'stq'	=> 'stq_Latn_DE',
		'su'	=> 'su_Latn_ID',
		'suk'	=> 'suk_Latn_TZ',
		'sus'	=> 'sus_Latn_GN',
		'suz'	=> 'suz_Sunu_NP',
		'sv'	=> 'sv_Latn_SE',
		'sw'	=> 'sw_Latn_TZ',
		'swb'	=> 'swb_Arab_YT',
		'swg'	=> 'swg_Latn_DE',
		'swv'	=> 'swv_Deva_IN',
		'sxn'	=> 'sxn_Latn_ID',
		'syl'	=> 'syl_Beng_BD',
		'syr'	=> 'syr_Syrc_IQ',
		'szl'	=> 'szl_Latn_PL',
		'ta'	=> 'ta_Taml_IN',
		'taj'	=> 'taj_Deva_NP',
		'tbw'	=> 'tbw_Latn_PH',
		'tcy'	=> 'tcy_Knda_IN',
		'tdd'	=> 'tdd_Tale_CN',
		'tdg'	=> 'tdg_Deva_NP',
		'tdh'	=> 'tdh_Deva_NP',
		'te'	=> 'te_Telu_IN',
		'tem'	=> 'tem_Latn_SL',
		'teo'	=> 'teo_Latn_UG',
		'tet'	=> 'tet_Latn_TL',
		'tg'	=> 'tg_Cyrl_TJ',
		'tg_PK'	=> 'tg_Arab_PK',
		'tg_Arab'	=> 'tg_Arab_PK',
		'th'	=> 'th_Thai_TH',
		'thl'	=> 'thl_Deva_NP',
		'thq'	=> 'thq_Deva_NP',
		'thr'	=> 'thr_Deva_NP',
		'ti'	=> 'ti_Ethi_ET',
		'tig'	=> 'tig_Ethi_ER',
		'tiv'	=> 'tiv_Latn_NG',
		'tk'	=> 'tk_Latn_TM',
		'tkl'	=> 'tkl_Latn_TK',
		'tkr'	=> 'tkr_Latn_AZ',
		'tkt'	=> 'tkt_Deva_NP',
		'tl'	=> 'tl_Latn_PH',
		'tly'	=> 'tly_Latn_AZ',
		'tmh'	=> 'tmh_Latn_NE',
		'tn'	=> 'tn_Latn_ZA',
		'tnr'	=> 'tnr_Latn_SN',
		'to'	=> 'to_Latn_TO',
		'tog'	=> 'tog_Latn_MW',
		'tok'	=> 'tok_Latn_001',
		'tpi'	=> 'tpi_Latn_PG',
		'tr'	=> 'tr_Latn_TR',
		'tru'	=> 'tru_Latn_TR',
		'trv'	=> 'trv_Latn_TW',
		'trw'	=> 'trw_Arab_PK',
		'ts'	=> 'ts_Latn_ZA',
		'tsd'	=> 'tsd_Grek_GR',
		'tsg'	=> 'tsg_Latn_PH',
		'tsj'	=> 'tsj_Tibt_BT',
		'tt'	=> 'tt_Cyrl_RU',
		'ttj'	=> 'ttj_Latn_UG',
		'tts'	=> 'tts_Thai_TH',
		'ttt'	=> 'ttt_Latn_AZ',
		'tum'	=> 'tum_Latn_MW',
		'tvl'	=> 'tvl_Latn_TV',
		'twq'	=> 'twq_Latn_NE',
		'txg'	=> 'txg_Tang_CN',
		'txo'	=> 'txo_Toto_IN',
		'ty'	=> 'ty_Latn_PF',
		'tyv'	=> 'tyv_Cyrl_RU',
		'tzm'	=> 'tzm_Latn_MA',
		'udm'	=> 'udm_Cyrl_RU',
		'ug'	=> 'ug_Arab_CN',
		'ug_KZ'	=> 'ug_Cyrl_KZ',
		'ug_MN'	=> 'ug_Cyrl_MN',
		'ug_Cyrl'	=> 'ug_Cyrl_KZ',
		'uga'	=> 'uga_Ugar_SY',
		'uk'	=> 'uk_Cyrl_UA',
		'uli'	=> 'uli_Latn_FM',
		'umb'	=> 'umb_Latn_AO',
		'unr'	=> 'unr_Beng_IN',
		'unr_NP'	=> 'unr_Deva_NP',
		'unr_Deva'	=> 'unr_Deva_NP',
		'unx'	=> 'unx_Beng_IN',
		'ur'	=> 'ur_Arab_PK',
		'uz'	=> 'uz_Latn_UZ',
		'uz_AF'	=> 'uz_Arab_AF',
		'uz_CN'	=> 'uz_Cyrl_CN',
		'uz_Arab'	=> 'uz_Arab_AF',
		'vai'	=> 'vai_Vaii_LR',
		've'	=> 've_Latn_ZA',
		'vec'	=> 'vec_Latn_IT',
		'vep'	=> 'vep_Latn_RU',
		'vi'	=> 'vi_Latn_VN',
		'vic'	=> 'vic_Latn_SX',
		'vls'	=> 'vls_Latn_BE',
		'vmf'	=> 'vmf_Latn_DE',
		'vmw'	=> 'vmw_Latn_MZ',
		'vo'	=> 'vo_Latn_001',
		'vot'	=> 'vot_Latn_RU',
		'vro'	=> 'vro_Latn_EE',
		'vun'	=> 'vun_Latn_TZ',
		'wa'	=> 'wa_Latn_BE',
		'wae'	=> 'wae_Latn_CH',
		'wal'	=> 'wal_Ethi_ET',
		'war'	=> 'war_Latn_PH',
		'wbp'	=> 'wbp_Latn_AU',
		'wbq'	=> 'wbq_Telu_IN',
		'wbr'	=> 'wbr_Deva_IN',
		'wls'	=> 'wls_Latn_WF',
		'wni'	=> 'wni_Arab_KM',
		'wo'	=> 'wo_Latn_SN',
		'wsg'	=> 'wsg_Gong_IN',
		'wtm'	=> 'wtm_Deva_IN',
		'wuu'	=> 'wuu_Hans_CN',
		'xag'	=> 'xag_Aghb_AZ',
		'xav'	=> 'xav_Latn_BR',
		'xco'	=> 'xco_Chrs_UZ',
		'xcr'	=> 'xcr_Cari_TR',
		'xh'	=> 'xh_Latn_ZA',
		'xlc'	=> 'xlc_Lyci_TR',
		'xld'	=> 'xld_Lydi_TR',
		'xmf'	=> 'xmf_Geor_GE',
		'xmn'	=> 'xmn_Mani_CN',
		'xmr'	=> 'xmr_Merc_SD',
		'xna'	=> 'xna_Narb_SA',
		'xnr'	=> 'xnr_Deva_IN',
		'xog'	=> 'xog_Latn_UG',
		'xpr'	=> 'xpr_Prti_IR',
		'xsa'	=> 'xsa_Sarb_YE',
		'xsr'	=> 'xsr_Deva_NP',
		'yao'	=> 'yao_Latn_MZ',
		'yap'	=> 'yap_Latn_FM',
		'yav'	=> 'yav_Latn_CM',
		'ybb'	=> 'ybb_Latn_CM',
		'yi'	=> 'yi_Hebr_UA',
		'yo'	=> 'yo_Latn_NG',
		'yrl'	=> 'yrl_Latn_BR',
		'yua'	=> 'yua_Latn_MX',
		'yue'	=> 'yue_Hant_HK',
		'yue_CN'	=> 'yue_Hans_CN',
		'yue_Hans'	=> 'yue_Hans_CN',
		'za'	=> 'za_Latn_CN',
		'zag'	=> 'zag_Latn_SD',
		'zdj'	=> 'zdj_Arab_KM',
		'zea'	=> 'zea_Latn_NL',
		'zgh'	=> 'zgh_Tfng_MA',
		'zh'	=> 'zh_Hans_CN',
		'zh_AU'	=> 'zh_Hant_AU',
		'zh_BN'	=> 'zh_Hant_BN',
		'zh_GB'	=> 'zh_Hant_GB',
		'zh_GF'	=> 'zh_Hant_GF',
		'zh_HK'	=> 'zh_Hant_HK',
		'zh_ID'	=> 'zh_Hant_ID',
		'zh_MO'	=> 'zh_Hant_MO',
		'zh_PA'	=> 'zh_Hant_PA',
		'zh_PF'	=> 'zh_Hant_PF',
		'zh_PH'	=> 'zh_Hant_PH',
		'zh_SR'	=> 'zh_Hant_SR',
		'zh_TH'	=> 'zh_Hant_TH',
		'zh_TW'	=> 'zh_Hant_TW',
		'zh_US'	=> 'zh_Hant_US',
		'zh_VN'	=> 'zh_Hant_VN',
		'zh_Bopo'	=> 'zh_Bopo_TW',
		'zh_Hanb'	=> 'zh_Hanb_TW',
		'zh_Hant'	=> 'zh_Hant_TW',
		'zhx'	=> 'zhx_Nshu_CN',
		'zkt'	=> 'zkt_Kits_CN',
		'zlm'	=> 'zlm_Latn_TG',
		'zmi'	=> 'zmi_Latn_MY',
		'zu'	=> 'zu_Latn_ZA',
		'zza'	=> 'zza_Latn_TR',
		'und'	=> 'en_Latn_US',
		'und_419'	=> 'es_Latn_419',
		'und_AD'	=> 'ca_Latn_AD',
		'und_AE'	=> 'ar_Arab_AE',
		'und_AF'	=> 'fa_Arab_AF',
		'und_AL'	=> 'sq_Latn_AL',
		'und_AM'	=> 'hy_Armn_AM',
		'und_AO'	=> 'pt_Latn_AO',
		'und_AR'	=> 'es_Latn_AR',
		'und_AS'	=> 'sm_Latn_AS',
		'und_AT'	=> 'de_Latn_AT',
		'und_AW'	=> 'nl_Latn_AW',
		'und_AX'	=> 'sv_Latn_AX',
		'und_AZ'	=> 'az_Latn_AZ',
		'und_BA'	=> 'bs_Latn_BA',
		'und_BD'	=> 'bn_Beng_BD',
		'und_BE'	=> 'nl_Latn_BE',
		'und_BF'	=> 'fr_Latn_BF',
		'und_BG'	=> 'bg_Cyrl_BG',
		'und_BH'	=> 'ar_Arab_BH',
		'und_BI'	=> 'rn_Latn_BI',
		'und_BJ'	=> 'fr_Latn_BJ',
		'und_BL'	=> 'fr_Latn_BL',
		'und_BN'	=> 'ms_Latn_BN',
		'und_BO'	=> 'es_Latn_BO',
		'und_BQ'	=> 'pap_Latn_BQ',
		'und_BR'	=> 'pt_Latn_BR',
		'und_BT'	=> 'dz_Tibt_BT',
		'und_BY'	=> 'be_Cyrl_BY',
		'und_CC'	=> 'ms_Arab_CC',
		'und_CD'	=> 'sw_Latn_CD',
		'und_CF'	=> 'fr_Latn_CF',
		'und_CG'	=> 'fr_Latn_CG',
		'und_CH'	=> 'de_Latn_CH',
		'und_CI'	=> 'fr_Latn_CI',
		'und_CL'	=> 'es_Latn_CL',
		'und_CM'	=> 'fr_Latn_CM',
		'und_CN'	=> 'zh_Hans_CN',
		'und_CO'	=> 'es_Latn_CO',
		'und_CR'	=> 'es_Latn_CR',
		'und_CU'	=> 'es_Latn_CU',
		'und_CV'	=> 'pt_Latn_CV',
		'und_CW'	=> 'pap_Latn_CW',
		'und_CY'	=> 'el_Grek_CY',
		'und_CZ'	=> 'cs_Latn_CZ',
		'und_DE'	=> 'de_Latn_DE',
		'und_DJ'	=> 'aa_Latn_DJ',
		'und_DK'	=> 'da_Latn_DK',
		'und_DO'	=> 'es_Latn_DO',
		'und_DZ'	=> 'ar_Arab_DZ',
		'und_EA'	=> 'es_Latn_EA',
		'und_EC'	=> 'es_Latn_EC',
		'und_EE'	=> 'et_Latn_EE',
		'und_EG'	=> 'ar_Arab_EG',
		'und_EH'	=> 'ar_Arab_EH',
		'und_ER'	=> 'ti_Ethi_ER',
		'und_ES'	=> 'es_Latn_ES',
		'und_ET'	=> 'am_Ethi_ET',
		'und_FI'	=> 'fi_Latn_FI',
		'und_FO'	=> 'fo_Latn_FO',
		'und_FR'	=> 'fr_Latn_FR',
		'und_GA'	=> 'fr_Latn_GA',
		'und_GE'	=> 'ka_Geor_GE',
		'und_GF'	=> 'fr_Latn_GF',
		'und_GH'	=> 'ak_Latn_GH',
		'und_GL'	=> 'kl_Latn_GL',
		'und_GN'	=> 'fr_Latn_GN',
		'und_GP'	=> 'fr_Latn_GP',
		'und_GQ'	=> 'es_Latn_GQ',
		'und_GR'	=> 'el_Grek_GR',
		'und_GT'	=> 'es_Latn_GT',
		'und_GW'	=> 'pt_Latn_GW',
		'und_HK'	=> 'zh_Hant_HK',
		'und_HN'	=> 'es_Latn_HN',
		'und_HR'	=> 'hr_Latn_HR',
		'und_HT'	=> 'ht_Latn_HT',
		'und_HU'	=> 'hu_Latn_HU',
		'und_IC'	=> 'es_Latn_IC',
		'und_ID'	=> 'id_Latn_ID',
		'und_IL'	=> 'he_Hebr_IL',
		'und_IN'	=> 'hi_Deva_IN',
		'und_IQ'	=> 'ar_Arab_IQ',
		'und_IR'	=> 'fa_Arab_IR',
		'und_IS'	=> 'is_Latn_IS',
		'und_IT'	=> 'it_Latn_IT',
		'und_JO'	=> 'ar_Arab_JO',
		'und_JP'	=> 'ja_Jpan_JP',
		'und_KE'	=> 'sw_Latn_KE',
		'und_KG'	=> 'ky_Cyrl_KG',
		'und_KH'	=> 'km_Khmr_KH',
		'und_KM'	=> 'ar_Arab_KM',
		'und_KP'	=> 'ko_Kore_KP',
		'und_KR'	=> 'ko_Kore_KR',
		'und_KW'	=> 'ar_Arab_KW',
		'und_KZ'	=> 'ru_Cyrl_KZ',
		'und_LA'	=> 'lo_Laoo_LA',
		'und_LB'	=> 'ar_Arab_LB',
		'und_LI'	=> 'de_Latn_LI',
		'und_LK'	=> 'si_Sinh_LK',
		'und_LS'	=> 'st_Latn_LS',
		'und_LT'	=> 'lt_Latn_LT',
		'und_LU'	=> 'fr_Latn_LU',
		'und_LV'	=> 'lv_Latn_LV',
		'und_LY'	=> 'ar_Arab_LY',
		'und_MA'	=> 'ar_Arab_MA',
		'und_MC'	=> 'fr_Latn_MC',
		'und_MD'	=> 'ro_Latn_MD',
		'und_ME'	=> 'sr_Latn_ME',
		'und_MF'	=> 'fr_Latn_MF',
		'und_MG'	=> 'mg_Latn_MG',
		'und_MK'	=> 'mk_Cyrl_MK',
		'und_ML'	=> 'bm_Latn_ML',
		'und_MM'	=> 'my_Mymr_MM',
		'und_MN'	=> 'mn_Cyrl_MN',
		'und_MO'	=> 'zh_Hant_MO',
		'und_MQ'	=> 'fr_Latn_MQ',
		'und_MR'	=> 'ar_Arab_MR',
		'und_MT'	=> 'mt_Latn_MT',
		'und_MU'	=> 'mfe_Latn_MU',
		'und_MV'	=> 'dv_Thaa_MV',
		'und_MX'	=> 'es_Latn_MX',
		'und_MY'	=> 'ms_Latn_MY',
		'und_MZ'	=> 'pt_Latn_MZ',
		'und_NA'	=> 'af_Latn_NA',
		'und_NC'	=> 'fr_Latn_NC',
		'und_NE'	=> 'ha_Latn_NE',
		'und_NI'	=> 'es_Latn_NI',
		'und_NL'	=> 'nl_Latn_NL',
		'und_NO'	=> 'nb_Latn_NO',
		'und_NP'	=> 'ne_Deva_NP',
		'und_OM'	=> 'ar_Arab_OM',
		'und_PA'	=> 'es_Latn_PA',
		'und_PE'	=> 'es_Latn_PE',
		'und_PF'	=> 'fr_Latn_PF',
		'und_PG'	=> 'tpi_Latn_PG',
		'und_PH'	=> 'fil_Latn_PH',
		'und_PK'	=> 'ur_Arab_PK',
		'und_PL'	=> 'pl_Latn_PL',
		'und_PM'	=> 'fr_Latn_PM',
		'und_PR'	=> 'es_Latn_PR',
		'und_PS'	=> 'ar_Arab_PS',
		'und_PT'	=> 'pt_Latn_PT',
		'und_PW'	=> 'pau_Latn_PW',
		'und_PY'	=> 'gn_Latn_PY',
		'und_QA'	=> 'ar_Arab_QA',
		'und_RE'	=> 'fr_Latn_RE',
		'und_RO'	=> 'ro_Latn_RO',
		'und_RS'	=> 'sr_Cyrl_RS',
		'und_RU'	=> 'ru_Cyrl_RU',
		'und_RW'	=> 'rw_Latn_RW',
		'und_SA'	=> 'ar_Arab_SA',
		'und_SC'	=> 'fr_Latn_SC',
		'und_SD'	=> 'ar_Arab_SD',
		'und_SE'	=> 'sv_Latn_SE',
		'und_SI'	=> 'sl_Latn_SI',
		'und_SJ'	=> 'nb_Latn_SJ',
		'und_SK'	=> 'sk_Latn_SK',
		'und_SL'	=> 'kri_Latn_SL',
		'und_SM'	=> 'it_Latn_SM',
		'und_SN'	=> 'fr_Latn_SN',
		'und_SO'	=> 'so_Latn_SO',
		'und_SR'	=> 'nl_Latn_SR',
		'und_SS'	=> 'ar_Arab_SS',
		'und_ST'	=> 'pt_Latn_ST',
		'und_SV'	=> 'es_Latn_SV',
		'und_SY'	=> 'ar_Arab_SY',
		'und_TD'	=> 'fr_Latn_TD',
		'und_TF'	=> 'fr_Latn_TF',
		'und_TG'	=> 'fr_Latn_TG',
		'und_TH'	=> 'th_Thai_TH',
		'und_TJ'	=> 'tg_Cyrl_TJ',
		'und_TK'	=> 'tkl_Latn_TK',
		'und_TL'	=> 'pt_Latn_TL',
		'und_TM'	=> 'tk_Latn_TM',
		'und_TN'	=> 'ar_Arab_TN',
		'und_TO'	=> 'to_Latn_TO',
		'und_TR'	=> 'tr_Latn_TR',
		'und_TV'	=> 'tvl_Latn_TV',
		'und_TW'	=> 'zh_Hant_TW',
		'und_TZ'	=> 'sw_Latn_TZ',
		'und_UA'	=> 'uk_Cyrl_UA',
		'und_UG'	=> 'sw_Latn_UG',
		'und_UY'	=> 'es_Latn_UY',
		'und_UZ'	=> 'uz_Latn_UZ',
		'und_VA'	=> 'it_Latn_VA',
		'und_VE'	=> 'es_Latn_VE',
		'und_VN'	=> 'vi_Latn_VN',
		'und_VU'	=> 'bi_Latn_VU',
		'und_WF'	=> 'fr_Latn_WF',
		'und_WS'	=> 'sm_Latn_WS',
		'und_XK'	=> 'sq_Latn_XK',
		'und_YE'	=> 'ar_Arab_YE',
		'und_YT'	=> 'fr_Latn_YT',
		'und_ZM'	=> 'bem_Latn_ZM',
		'und_ZW'	=> 'sn_Latn_ZW',
		'und_Adlm'	=> 'ff_Adlm_GN',
		'und_Aghb'	=> 'xag_Aghb_AZ',
		'und_Ahom'	=> 'aho_Ahom_IN',
		'und_Arab'	=> 'ar_Arab_EG',
		'und_Arab_AF'	=> 'fa_Arab_AF',
		'und_Arab_BN'	=> 'ms_Arab_BN',
		'und_Arab_CC'	=> 'ms_Arab_CC',
		'und_Arab_CN'	=> 'ug_Arab_CN',
		'und_Arab_GB'	=> 'ur_Arab_GB',
		'und_Arab_ID'	=> 'ms_Arab_ID',
		'und_Arab_IN'	=> 'ur_Arab_IN',
		'und_Arab_IR'	=> 'fa_Arab_IR',
		'und_Arab_KH'	=> 'cja_Arab_KH',
		'und_Arab_MM'	=> 'rhg_Arab_MM',
		'und_Arab_MN'	=> 'kk_Arab_MN',
		'und_Arab_MU'	=> 'ur_Arab_MU',
		'und_Arab_NG'	=> 'ha_Arab_NG',
		'und_Arab_PK'	=> 'ur_Arab_PK',
		'und_Arab_TG'	=> 'apd_Arab_TG',
		'und_Arab_TH'	=> 'mfa_Arab_TH',
		'und_Arab_TJ'	=> 'fa_Arab_TJ',
		'und_Arab_TR'	=> 'apc_Arab_TR',
		'und_Arab_YT'	=> 'swb_Arab_YT',
		'und_Armi'	=> 'arc_Armi_IR',
		'und_Armn'	=> 'hy_Armn_AM',
		'und_Avst'	=> 'ae_Avst_IR',
		'und_Bali'	=> 'ban_Bali_ID',
		'und_Bamu'	=> 'bax_Bamu_CM',
		'und_Bass'	=> 'bsq_Bass_LR',
		'und_Batk'	=> 'bbc_Batk_ID',
		'und_Beng'	=> 'bn_Beng_BD',
		'und_Bhks'	=> 'sa_Bhks_IN',
		'und_Bopo'	=> 'zh_Bopo_TW',
		'und_Brah'	=> 'pka_Brah_IN',
		'und_Brai'	=> 'fr_Brai_FR',
		'und_Bugi'	=> 'bug_Bugi_ID',
		'und_Buhd'	=> 'bku_Buhd_PH',
		'und_Cakm'	=> 'ccp_Cakm_BD',
		'und_Cans'	=> 'iu_Cans_CA',
		'und_Cari'	=> 'xcr_Cari_TR',
		'und_Cham'	=> 'cjm_Cham_VN',
		'und_Cher'	=> 'chr_Cher_US',
		'und_Chrs'	=> 'xco_Chrs_UZ',
		'und_Copt'	=> 'cop_Copt_EG',
		'und_Cpmn'	=> 'und_Cpmn_CY',
		'und_Cprt'	=> 'ecy_Cprt_CY',
		'und_Cyrl'	=> 'ru_Cyrl_RU',
		'und_Cyrl_AF'	=> 'kaa_Cyrl_AF',
		'und_Cyrl_AL'	=> 'mk_Cyrl_AL',
		'und_Cyrl_AZ'	=> 'az_Cyrl_AZ',
		'und_Cyrl_BA'	=> 'sr_Cyrl_BA',
		'und_Cyrl_BG'	=> 'bg_Cyrl_BG',
		'und_Cyrl_BY'	=> 'be_Cyrl_BY',
		'und_Cyrl_GE'	=> 'ab_Cyrl_GE',
		'und_Cyrl_GR'	=> 'mk_Cyrl_GR',
		'und_Cyrl_IR'	=> 'kaa_Cyrl_IR',
		'und_Cyrl_KG'	=> 'ky_Cyrl_KG',
		'und_Cyrl_MD'	=> 'uk_Cyrl_MD',
		'und_Cyrl_ME'	=> 'sr_Cyrl_ME',
		'und_Cyrl_MK'	=> 'mk_Cyrl_MK',
		'und_Cyrl_MN'	=> 'mn_Cyrl_MN',
		'und_Cyrl_RO'	=> 'bg_Cyrl_RO',
		'und_Cyrl_RS'	=> 'sr_Cyrl_RS',
		'und_Cyrl_SK'	=> 'uk_Cyrl_SK',
		'und_Cyrl_TJ'	=> 'tg_Cyrl_TJ',
		'und_Cyrl_TR'	=> 'kbd_Cyrl_TR',
		'und_Cyrl_UA'	=> 'uk_Cyrl_UA',
		'und_Cyrl_UZ'	=> 'uz_Cyrl_UZ',
		'und_Cyrl_XK'	=> 'sr_Cyrl_XK',
		'und_Deva'	=> 'hi_Deva_IN',
		'und_Deva_BT'	=> 'ne_Deva_BT',
		'und_Deva_FJ'	=> 'hif_Deva_FJ',
		'und_Deva_MU'	=> 'bho_Deva_MU',
		'und_Deva_NP'	=> 'ne_Deva_NP',
		'und_Deva_PK'	=> 'btv_Deva_PK',
		'und_Diak'	=> 'dv_Diak_MV',
		'und_Dogr'	=> 'doi_Dogr_IN',
		'und_Dupl'	=> 'fr_Dupl_FR',
		'und_Egyp'	=> 'egy_Egyp_EG',
		'und_Elba'	=> 'sq_Elba_AL',
		'und_Elym'	=> 'arc_Elym_IR',
		'und_Ethi'	=> 'am_Ethi_ET',
		'und_Ethi_ER'	=> 'ti_Ethi_ER',
		'und_Gara'	=> 'wo_Gara_SN',
		'und_Geor'	=> 'ka_Geor_GE',
		'und_Glag'	=> 'cu_Glag_BG',
		'und_Gong'	=> 'wsg_Gong_IN',
		'und_Gonm'	=> 'esg_Gonm_IN',
		'und_Goth'	=> 'got_Goth_UA',
		'und_Gran'	=> 'sa_Gran_IN',
		'und_Grek'	=> 'el_Grek_GR',
		'und_Grek_TR'	=> 'bgx_Grek_TR',
		'und_Gujr'	=> 'gu_Gujr_IN',
		'und_Gukh'	=> 'gvr_Gukh_NP',
		'und_Guru'	=> 'pa_Guru_IN',
		'und_Hanb'	=> 'zh_Hanb_TW',
		'und_Hang'	=> 'ko_Hang_KR',
		'und_Hani'	=> 'zh_Hani_CN',
		'und_Hano'	=> 'hnn_Hano_PH',
		'und_Hans'	=> 'zh_Hans_CN',
		'und_Hant'	=> 'zh_Hant_TW',
		'und_Hant_CA'	=> 'yue_Hant_CA',
		'und_Hant_CN'	=> 'yue_Hant_CN',
		'und_Hatr'	=> 'arc_Hatr_IQ',
		'und_Hebr'	=> 'he_Hebr_IL',
		'und_Hebr_SE'	=> 'yi_Hebr_SE',
		'und_Hebr_UA'	=> 'yi_Hebr_UA',
		'und_Hebr_US'	=> 'yi_Hebr_US',
		'und_Hira'	=> 'ja_Hira_JP',
		'und_Hluw'	=> 'hlu_Hluw_TR',
		'und_Hmng'	=> 'hnj_Hmng_LA',
		'und_Hmnp'	=> 'hnj_Hmnp_US',
		'und_Hung'	=> 'hu_Hung_HU',
		'und_Ital'	=> 'ett_Ital_IT',
		'und_Jamo'	=> 'ko_Jamo_KR',
		'und_Java'	=> 'jv_Java_ID',
		'und_Jpan'	=> 'ja_Jpan_JP',
		'und_Kali'	=> 'eky_Kali_MM',
		'und_Kana'	=> 'ja_Kana_JP',
		'und_Kawi'	=> 'kaw_Kawi_ID',
		'und_Khar'	=> 'pra_Khar_PK',
		'und_Khmr'	=> 'km_Khmr_KH',
		'und_Khoj'	=> 'sd_Khoj_IN',
		'und_Kits'	=> 'zkt_Kits_CN',
		'und_Knda'	=> 'kn_Knda_IN',
		'und_Kore'	=> 'ko_Kore_KR',
		'und_Krai'	=> 'bap_Krai_IN',
		'und_Kthi'	=> 'bho_Kthi_IN',
		'und_Lana'	=> 'nod_Lana_TH',
		'und_Laoo'	=> 'lo_Laoo_LA',
		'und_Latn_AE'	=> 'en_Latn_AE',
		'und_Latn_AF'	=> 'tk_Latn_AF',
		'und_Latn_AM'	=> 'ku_Latn_AM',
		'und_Latn_BD'	=> 'en_Latn_BD',
		'und_Latn_BG'	=> 'en_Latn_BG',
		'und_Latn_BT'	=> 'en_Latn_BT',
		'und_Latn_CC'	=> 'en_Latn_CC',
		'und_Latn_CN'	=> 'za_Latn_CN',
		'und_Latn_CY'	=> 'tr_Latn_CY',
		'und_Latn_DZ'	=> 'fr_Latn_DZ',
		'und_Latn_EG'	=> 'en_Latn_EG',
		'und_Latn_ER'	=> 'en_Latn_ER',
		'und_Latn_ET'	=> 'en_Latn_ET',
		'und_Latn_GE'	=> 'ku_Latn_GE',
		'und_Latn_GR'	=> 'en_Latn_GR',
		'und_Latn_HK'	=> 'en_Latn_HK',
		'und_Latn_IL'	=> 'en_Latn_IL',
		'und_Latn_IN'	=> 'en_Latn_IN',
		'und_Latn_IQ'	=> 'en_Latn_IQ',
		'und_Latn_IR'	=> 'tk_Latn_IR',
		'und_Latn_JO'	=> 'en_Latn_JO',
		'und_Latn_KM'	=> 'fr_Latn_KM',
		'und_Latn_KZ'	=> 'en_Latn_KZ',
		'und_Latn_LB'	=> 'en_Latn_LB',
		'und_Latn_LK'	=> 'en_Latn_LK',
		'und_Latn_MA'	=> 'fr_Latn_MA',
		'und_Latn_MK'	=> 'sq_Latn_MK',
		'und_Latn_MM'	=> 'kac_Latn_MM',
		'und_Latn_MO'	=> 'pt_Latn_MO',
		'und_Latn_MR'	=> 'fr_Latn_MR',
		'und_Latn_MV'	=> 'en_Latn_MV',
		'und_Latn_NP'	=> 'en_Latn_NP',
		'und_Latn_PK'	=> 'en_Latn_PK',
		'und_Latn_RU'	=> 'krl_Latn_RU',
		'und_Latn_SD'	=> 'en_Latn_SD',
		'und_Latn_SS'	=> 'en_Latn_SS',
		'und_Latn_SY'	=> 'fr_Latn_SY',
		'und_Latn_TH'	=> 'en_Latn_TH',
		'und_Latn_TN'	=> 'fr_Latn_TN',
		'und_Latn_TW'	=> 'trv_Latn_TW',
		'und_Latn_UA'	=> 'pl_Latn_UA',
		'und_Latn_YE'	=> 'en_Latn_YE',
		'und_Lepc'	=> 'lep_Lepc_IN',
		'und_Limb'	=> 'lif_Limb_IN',
		'und_Lina'	=> 'lab_Lina_GR',
		'und_Linb'	=> 'gmy_Linb_GR',
		'und_Lisu'	=> 'lis_Lisu_CN',
		'und_Lyci'	=> 'xlc_Lyci_TR',
		'und_Lydi'	=> 'xld_Lydi_TR',
		'und_Mahj'	=> 'hi_Mahj_IN',
		'und_Maka'	=> 'mak_Maka_ID',
		'und_Mand'	=> 'myz_Mand_IR',
		'und_Mani'	=> 'xmn_Mani_CN',
		'und_Marc'	=> 'bo_Marc_CN',
		'und_Medf'	=> 'dmf_Medf_NG',
		'und_Mend'	=> 'men_Mend_SL',
		'und_Merc'	=> 'xmr_Merc_SD',
		'und_Mero'	=> 'xmr_Mero_SD',
		'und_Mlym'	=> 'ml_Mlym_IN',
		'und_Modi'	=> 'mr_Modi_IN',
		'und_Mong'	=> 'mn_Mong_CN',
		'und_Mroo'	=> 'mro_Mroo_BD',
		'und_Mtei'	=> 'mni_Mtei_IN',
		'und_Mult'	=> 'skr_Mult_PK',
		'und_Mymr'	=> 'my_Mymr_MM',
		'und_Mymr_IN'	=> 'kht_Mymr_IN',
		'und_Mymr_TH'	=> 'mnw_Mymr_TH',
		'und_Nagm'	=> 'unr_Nagm_IN',
		'und_Nand'	=> 'sa_Nand_IN',
		'und_Narb'	=> 'xna_Narb_SA',
		'und_Nbat'	=> 'arc_Nbat_JO',
		'und_Newa'	=> 'new_Newa_NP',
		'und_Nkoo'	=> 'man_Nkoo_GN',
		'und_Nkoo_ML'	=> 'bm_Nkoo_ML',
		'und_Nshu'	=> 'zhx_Nshu_CN',
		'und_Ogam'	=> 'sga_Ogam_IE',
		'und_Olck'	=> 'sat_Olck_IN',
		'und_Onao'	=> 'unr_Onao_IN',
		'und_Orkh'	=> 'otk_Orkh_MN',
		'und_Orya'	=> 'or_Orya_IN',
		'und_Osge'	=> 'osa_Osge_US',
		'und_Osma'	=> 'so_Osma_SO',
		'und_Ougr'	=> 'oui_Ougr_CN',
		'und_Palm'	=> 'arc_Palm_SY',
		'und_Pauc'	=> 'ctd_Pauc_MM',
		'und_Perm'	=> 'kv_Perm_RU',
		'und_Phag'	=> 'lzh_Phag_CN',
		'und_Phli'	=> 'pal_Phli_IR',
		'und_Phlp'	=> 'pal_Phlp_CN',
		'und_Phnx'	=> 'phn_Phnx_LB',
		'und_Plrd'	=> 'hmd_Plrd_CN',
		'und_Prti'	=> 'xpr_Prti_IR',
		'und_Rjng'	=> 'rej_Rjng_ID',
		'und_Rohg'	=> 'rhg_Rohg_MM',
		'und_Runr'	=> 'non_Runr_SE',
		'und_Samr'	=> 'smp_Samr_IL',
		'und_Sarb'	=> 'xsa_Sarb_YE',
		'und_Saur'	=> 'saz_Saur_IN',
		'und_Sgnw'	=> 'ase_Sgnw_US',
		'und_Shaw'	=> 'en_Shaw_GB',
		'und_Shrd'	=> 'sa_Shrd_IN',
		'und_Sidd'	=> 'sa_Sidd_IN',
		'und_Sind'	=> 'sd_Sind_IN',
		'und_Sinh'	=> 'si_Sinh_LK',
		'und_Sogd'	=> 'sog_Sogd_UZ',
		'und_Sogo'	=> 'sog_Sogo_UZ',
		'und_Sora'	=> 'srb_Sora_IN',
		'und_Soyo'	=> 'cmg_Soyo_MN',
		'und_Sund'	=> 'su_Sund_ID',
		'und_Sunu'	=> 'suz_Sunu_NP',
		'und_Sylo'	=> 'syl_Sylo_BD',
		'und_Syrc'	=> 'syr_Syrc_IQ',
		'und_Tagb'	=> 'tbw_Tagb_PH',
		'und_Takr'	=> 'doi_Takr_IN',
		'und_Tale'	=> 'tdd_Tale_CN',
		'und_Talu'	=> 'khb_Talu_CN',
		'und_Taml'	=> 'ta_Taml_IN',
		'und_Tang'	=> 'txg_Tang_CN',
		'und_Tavt'	=> 'blt_Tavt_VN',
		'und_Telu'	=> 'te_Telu_IN',
		'und_Tfng'	=> 'zgh_Tfng_MA',
		'und_Tglg'	=> 'fil_Tglg_PH',
		'und_Thaa'	=> 'dv_Thaa_MV',
		'und_Thai'	=> 'th_Thai_TH',
		'und_Thai_CN'	=> 'lcp_Thai_CN',
		'und_Thai_KH'	=> 'kdt_Thai_KH',
		'und_Thai_LA'	=> 'kdt_Thai_LA',
		'und_Tibt'	=> 'bo_Tibt_CN',
		'und_Tibt_BT'	=> 'dz_Tibt_BT',
		'und_Tirh'	=> 'mai_Tirh_IN',
		'und_Tnsa'	=> 'nst_Tnsa_IN',
		'und_Todr'	=> 'sq_Todr_AL',
		'und_Toto'	=> 'txo_Toto_IN',
		'und_Tutg'	=> 'sa_Tutg_IN',
		'und_Ugar'	=> 'uga_Ugar_SY',
		'und_Vaii'	=> 'vai_Vaii_LR',
		'und_Vith'	=> 'sq_Vith_AL',
		'und_Wara'	=> 'hoc_Wara_IN',
		'und_Wcho'	=> 'nnp_Wcho_IN',
		'und_Xpeo'	=> 'peo_Xpeo_IR',
		'und_Xsux'	=> 'akk_Xsux_IQ',
		'und_Yezi'	=> 'ku_Yezi_GE',
		'und_Yiii'	=> 'ii_Yiii_CN',
		'und_Zanb'	=> 'cmg_Zanb_MN',
		'aaa'	=> 'aaa_Latn_NG',
		'aab'	=> 'aab_Latn_NG',
		'aac'	=> 'aac_Latn_PG',
		'aad'	=> 'aad_Latn_PG',
		'aae'	=> 'aae_Latn_IT',
		'aaf'	=> 'aaf_Mlym_IN',
		'aag'	=> 'aag_Latn_PG',
		'aah'	=> 'aah_Latn_PG',
		'aai'	=> 'aai_Latn_PG',
		'aak'	=> 'aak_Latn_PG',
		'aal'	=> 'aal_Latn_CM',
		'aan'	=> 'aan_Latn_BR',
		'aao'	=> 'aao_Arab_DZ',
		'aap'	=> 'aap_Latn_BR',
		'aaq'	=> 'aaq_Latn_US',
		'aas'	=> 'aas_Latn_TZ',
		'aat'	=> 'aat_Grek_GR',
		'aau'	=> 'aau_Latn_PG',
		'aaw'	=> 'aaw_Latn_PG',
		'aax'	=> 'aax_Latn_ID',
		'aaz'	=> 'aaz_Latn_ID',
		'aba'	=> 'aba_Latn_CI',
		'abb'	=> 'abb_Latn_CM',
		'abc'	=> 'abc_Latn_PH',
		'abd'	=> 'abd_Latn_PH',
		'abe'	=> 'abe_Latn_CA',
		'abf'	=> 'abf_Latn_MY',
		'abg'	=> 'abg_Latn_PG',
		'abh'	=> 'abh_Arab_TJ',
		'abi'	=> 'abi_Latn_CI',
		'abl'	=> 'abl_Rjng_ID',
		'abm'	=> 'abm_Latn_NG',
		'abn'	=> 'abn_Latn_NG',
		'abo'	=> 'abo_Latn_NG',
		'abp'	=> 'abp_Latn_PH',
		'abs'	=> 'abs_Latn_ID',
		'abt'	=> 'abt_Latn_PG',
		'abu'	=> 'abu_Latn_CI',
		'abv'	=> 'abv_Arab_BH',
		'abw'	=> 'abw_Latn_PG',
		'abx'	=> 'abx_Latn_PH',
		'aby'	=> 'aby_Latn_PG',
		'abz'	=> 'abz_Latn_ID',
		'aca'	=> 'aca_Latn_CO',
		'acb'	=> 'acb_Latn_NG',
		'acd'	=> 'acd_Latn_GH',
		'acf'	=> 'acf_Latn_LC',
		'acm'	=> 'acm_Arab_IQ',
		'acn'	=> 'acn_Latn_CN',
		'acp'	=> 'acp_Latn_NG',
		'acq'	=> 'acq_Arab_YE',
		'acr'	=> 'acr_Latn_GT',
		'acs'	=> 'acs_Latn_BR',
		'act'	=> 'act_Latn_NL',
		'acu'	=> 'acu_Latn_EC',
		'acv'	=> 'acv_Latn_US',
		'acw'	=> 'acw_Arab_SA',
		'acx'	=> 'acx_Arab_OM',
		'acy'	=> 'acy_Latn_CY',
		'acz'	=> 'acz_Latn_SD',
		'adb'	=> 'adb_Latn_TL',
		'add'	=> 'add_Latn_CM',
		'ade'	=> 'ade_Latn_TG',
		'adf'	=> 'adf_Arab_OM',
		'adg'	=> 'adg_Latn_AU',
		'adh'	=> 'adh_Latn_UG',
		'adi'	=> 'adi_Latn_IN',
		'adj'	=> 'adj_Latn_CI',
		'adl'	=> 'adl_Latn_IN',
		'adn'	=> 'adn_Latn_ID',
		'ado'	=> 'ado_Latn_PG',
		'adq'	=> 'adq_Latn_GH',
		'adr'	=> 'adr_Latn_ID',
		'adt'	=> 'adt_Latn_AU',
		'adu'	=> 'adu_Latn_NG',
		'adw'	=> 'adw_Latn_BR',
		'adx'	=> 'adx_Tibt_CN',
		'adz'	=> 'adz_Latn_PG',
		'aea'	=> 'aea_Latn_AU',
		'aec'	=> 'aec_Arab_EG',
		'aee'	=> 'aee_Arab_AF',
		'aek'	=> 'aek_Latn_NC',
		'ael'	=> 'ael_Latn_CM',
		'aem'	=> 'aem_Latn_VN',
		'aeq'	=> 'aeq_Arab_PK',
		'aer'	=> 'aer_Latn_AU',
		'aeu'	=> 'aeu_Latn_CN',
		'aew'	=> 'aew_Latn_PG',
		'aey'	=> 'aey_Latn_PG',
		'aez'	=> 'aez_Latn_PG',
		'afb'	=> 'afb_Arab_KW',
		'afd'	=> 'afd_Latn_PG',
		'afe'	=> 'afe_Latn_NG',
		'afh'	=> 'afh_Latn_GH',
		'afi'	=> 'afi_Latn_PG',
		'afk'	=> 'afk_Latn_PG',
		'afn'	=> 'afn_Latn_NG',
		'afo'	=> 'afo_Latn_NG',
		'afp'	=> 'afp_Latn_PG',
		'afs'	=> 'afs_Latn_MX',
		'afu'	=> 'afu_Latn_GH',
		'afz'	=> 'afz_Latn_ID',
		'aga'	=> 'aga_Latn_PE',
		'agb'	=> 'agb_Latn_NG',
		'agc'	=> 'agc_Latn_NG',
		'agd'	=> 'agd_Latn_PG',
		'age'	=> 'age_Latn_PG',
		'agf'	=> 'agf_Latn_ID',
		'agg'	=> 'agg_Latn_PG',
		'agh'	=> 'agh_Latn_CD',
		'agi'	=> 'agi_Deva_IN',
		'agj'	=> 'agj_Ethi_ET',
		'agk'	=> 'agk_Latn_PH',
		'agl'	=> 'agl_Latn_PG',
		'agm'	=> 'agm_Latn_PG',
		'agn'	=> 'agn_Latn_PH',
		'ago'	=> 'ago_Latn_PG',
		'agr'	=> 'agr_Latn_PE',
		'ags'	=> 'ags_Latn_CM',
		'agt'	=> 'agt_Latn_PH',
		'agu'	=> 'agu_Latn_GT',
		'agv'	=> 'agv_Latn_PH',
		'agw'	=> 'agw_Latn_SB',
		'agx'	=> 'agx_Cyrl_RU',
		'agy'	=> 'agy_Latn_PH',
		'agz'	=> 'agz_Latn_PH',
		'aha'	=> 'aha_Latn_GH',
		'ahb'	=> 'ahb_Latn_VU',
		'ahg'	=> 'ahg_Ethi_ET',
		'ahh'	=> 'ahh_Latn_ID',
		'ahi'	=> 'ahi_Latn_CI',
		'ahk'	=> 'ahk_Latn_MM',
		'ahl'	=> 'ahl_Latn_TG',
		'ahm'	=> 'ahm_Latn_CI',
		'ahn'	=> 'ahn_Latn_NG',
		'ahp'	=> 'ahp_Latn_CI',
		'ahr'	=> 'ahr_Deva_IN',
		'ahs'	=> 'ahs_Latn_NG',
		'aht'	=> 'aht_Latn_US',
		'aia'	=> 'aia_Latn_SB',
		'aib'	=> 'aib_Arab_CN',
		'aic'	=> 'aic_Latn_PG',
		'aid'	=> 'aid_Latn_AU',
		'aie'	=> 'aie_Latn_PG',
		'aif'	=> 'aif_Latn_PG',
		'aig'	=> 'aig_Latn_AG',
		'aii'	=> 'aii_Syrc_IQ',
		'aij'	=> 'aij_Hebr_IL',
		'aik'	=> 'aik_Latn_NG',
		'ail'	=> 'ail_Latn_PG',
		'aim'	=> 'aim_Latn_IN',
		'ain'	=> 'ain_Kana_JP',
		'aio'	=> 'aio_Mymr_IN',
		'aip'	=> 'aip_Latn_ID',
		'aiq'	=> 'aiq_Arab_AF',
		'air'	=> 'air_Latn_ID',
		'ait'	=> 'ait_Latn_BR',
		'aiw'	=> 'aiw_Latn_ET',
		'aix'	=> 'aix_Latn_PG',
		'aiy'	=> 'aiy_Latn_CF',
		'aja'	=> 'aja_Latn_SS',
		'ajg'	=> 'ajg_Latn_BJ',
		'aji'	=> 'aji_Latn_NC',
		'ajn'	=> 'ajn_Latn_AU',
		'ajw'	=> 'ajw_Latn_NG',
		'ajz'	=> 'ajz_Latn_IN',
		'akb'	=> 'akb_Latn_ID',
		'akc'	=> 'akc_Latn_ID',
		'akd'	=> 'akd_Latn_NG',
		'ake'	=> 'ake_Latn_GY',
		'akf'	=> 'akf_Latn_NG',
		'akg'	=> 'akg_Latn_ID',
		'akh'	=> 'akh_Latn_PG',
		'aki'	=> 'aki_Latn_PG',
		'akl'	=> 'akl_Latn_PH',
		'ako'	=> 'ako_Latn_SR',
		'akp'	=> 'akp_Latn_GH',
		'akq'	=> 'akq_Latn_PG',
		'akr'	=> 'akr_Latn_VU',
		'aks'	=> 'aks_Latn_TG',
		'akt'	=> 'akt_Latn_PG',
		'aku'	=> 'aku_Latn_CM',
		'akv'	=> 'akv_Cyrl_RU',
		'akw'	=> 'akw_Latn_CG',
		'akz'	=> 'akz_Latn_US',
		'ala'	=> 'ala_Latn_NG',
		'alc'	=> 'alc_Latn_CL',
		'ald'	=> 'ald_Latn_CI',
		'ale'	=> 'ale_Latn_US',
		'alf'	=> 'alf_Latn_NG',
		'alh'	=> 'alh_Latn_AU',
		'ali'	=> 'ali_Latn_PG',
		'alj'	=> 'alj_Latn_PH',
		'alk'	=> 'alk_Laoo_LA',
		'all'	=> 'all_Mlym_IN',
		'alm'	=> 'alm_Latn_VU',
		'alo'	=> 'alo_Latn_ID',
		'alp'	=> 'alp_Latn_ID',
		'alq'	=> 'alq_Latn_CA',
		'alr'	=> 'alr_Cyrl_RU',
		'alu'	=> 'alu_Latn_SB',
		'alw'	=> 'alw_Ethi_ET',
		'alx'	=> 'alx_Latn_PG',
		'aly'	=> 'aly_Latn_AU',
		'alz'	=> 'alz_Latn_CD',
		'ama'	=> 'ama_Latn_BR',
		'amb'	=> 'amb_Latn_NG',
		'amc'	=> 'amc_Latn_PE',
		'ame'	=> 'ame_Latn_PE',
		'amf'	=> 'amf_Latn_ET',
		'amg'	=> 'amg_Latn_AU',
		'ami'	=> 'ami_Latn_TW',
		'amj'	=> 'amj_Latn_TD',
		'amk'	=> 'amk_Latn_ID',
		'amm'	=> 'amm_Latn_PG',
		'amn'	=> 'amn_Latn_PG',
		'amp'	=> 'amp_Latn_PG',
		'amq'	=> 'amq_Latn_ID',
		'amr'	=> 'amr_Latn_PE',
		'ams'	=> 'ams_Jpan_JP',
		'amt'	=> 'amt_Latn_PG',
		'amu'	=> 'amu_Latn_MX',
		'amv'	=> 'amv_Latn_ID',
		'amw'	=> 'amw_Syrc_SY',
		'amx'	=> 'amx_Latn_AU',
		'amy'	=> 'amy_Latn_AU',
		'amz'	=> 'amz_Latn_AU',
		'ana'	=> 'ana_Latn_CO',
		'anb'	=> 'anb_Latn_PE',
		'anc'	=> 'anc_Latn_NG',
		'and'	=> 'and_Latn_ID',
		'ane'	=> 'ane_Latn_NC',
		'anf'	=> 'anf_Latn_GH',
		'ang'	=> 'ang_Latn_GB',
		'anh'	=> 'anh_Latn_PG',
		'ani'	=> 'ani_Cyrl_RU',
		'anj'	=> 'anj_Latn_PG',
		'ank'	=> 'ank_Latn_NG',
		'anl'	=> 'anl_Latn_MM',
		'anm'	=> 'anm_Latn_IN',
		'ano'	=> 'ano_Latn_CO',
		'anp'	=> 'anp_Deva_IN',
		'anq'	=> 'anq_Deva_IN',
		'anr'	=> 'anr_Deva_IN',
		'ans'	=> 'ans_Latn_CO',
		'ant'	=> 'ant_Latn_AU',
		'anu'	=> 'anu_Ethi_ET',
		'anv'	=> 'anv_Latn_CM',
		'anw'	=> 'anw_Latn_NG',
		'anx'	=> 'anx_Latn_PG',
		'any'	=> 'any_Latn_CI',
		'anz'	=> 'anz_Latn_PG',
		'aoa'	=> 'aoa_Latn_ST',
		'aob'	=> 'aob_Latn_PG',
		'aoc'	=> 'aoc_Latn_VE',
		'aod'	=> 'aod_Latn_PG',
		'aoe'	=> 'aoe_Latn_PG',
		'aof'	=> 'aof_Latn_PG',
		'aog'	=> 'aog_Latn_PG',
		'aoi'	=> 'aoi_Latn_AU',
		'aoj'	=> 'aoj_Latn_PG',
		'aok'	=> 'aok_Latn_NC',
		'aol'	=> 'aol_Latn_ID',
		'aom'	=> 'aom_Latn_PG',
		'aon'	=> 'aon_Latn_PG',
		'aor'	=> 'aor_Latn_VU',
		'aos'	=> 'aos_Latn_ID',
		'aot'	=> 'aot_Beng_BD',
		'aox'	=> 'aox_Latn_GY',
		'apb'	=> 'apb_Latn_SB',
		'ape'	=> 'ape_Latn_PG',
		'apf'	=> 'apf_Latn_PH',
		'apg'	=> 'apg_Latn_ID',
		'aph'	=> 'aph_Deva_NP',
		'api'	=> 'api_Latn_BR',
		'apj'	=> 'apj_Latn_US',
		'apk'	=> 'apk_Latn_US',
		'apl'	=> 'apl_Latn_US',
		'apm'	=> 'apm_Latn_US',
		'apn'	=> 'apn_Latn_BR',
		'apo'	=> 'apo_Latn_PG',
		'app'	=> 'app_Latn_VU',
		'apr'	=> 'apr_Latn_PG',
		'aps'	=> 'aps_Latn_PG',
		'apt'	=> 'apt_Latn_IN',
		'apu'	=> 'apu_Latn_BR',
		'apv'	=> 'apv_Latn_BR',
		'apw'	=> 'apw_Latn_US',
		'apx'	=> 'apx_Latn_ID',
		'apy'	=> 'apy_Latn_BR',
		'apz'	=> 'apz_Latn_PG',
		'aqc'	=> 'aqc_Cyrl_RU',
		'aqd'	=> 'aqd_Latn_ML',
		'aqg'	=> 'aqg_Latn_NG',
		'aqk'	=> 'aqk_Latn_NG',
		'aqm'	=> 'aqm_Latn_ID',
		'aqn'	=> 'aqn_Latn_PH',
		'aqr'	=> 'aqr_Latn_NC',
		'aqt'	=> 'aqt_Latn_PY',
		'aqz'	=> 'aqz_Latn_BR',
		'ard'	=> 'ard_Latn_AU',
		'are'	=> 'are_Latn_AU',
		'arh'	=> 'arh_Latn_CO',
		'ari'	=> 'ari_Latn_US',
		'arj'	=> 'arj_Latn_BR',
		'ark'	=> 'ark_Latn_BR',
		'arl'	=> 'arl_Latn_PE',
		'arp'	=> 'arp_Latn_US',
		'arr'	=> 'arr_Latn_BR',
		'aru'	=> 'aru_Latn_BR',
		'arw'	=> 'arw_Latn_SR',
		'arx'	=> 'arx_Latn_BR',
		'asb'	=> 'asb_Latn_CA',
		'asc'	=> 'asc_Latn_ID',
		'asg'	=> 'asg_Latn_NG',
		'ash'	=> 'ash_Latn_PE',
		'asi'	=> 'asi_Latn_ID',
		'asj'	=> 'asj_Latn_CM',
		'ask'	=> 'ask_Arab_AF',
		'asl'	=> 'asl_Latn_ID',
		'asn'	=> 'asn_Latn_BR',
		'aso'	=> 'aso_Latn_PG',
		'asr'	=> 'asr_Deva_IN',
		'ass'	=> 'ass_Latn_CM',
		'asu'	=> 'asu_Latn_BR',
		'asv'	=> 'asv_Latn_CD',
		'asx'	=> 'asx_Latn_PG',
		'asy'	=> 'asy_Latn_ID',
		'asz'	=> 'asz_Latn_ID',
		'ata'	=> 'ata_Latn_PG',
		'atb'	=> 'atb_Latn_CN',
		'atc'	=> 'atc_Latn_PE',
		'atd'	=> 'atd_Latn_PH',
		'ate'	=> 'ate_Latn_PG',
		'atg'	=> 'atg_Latn_NG',
		'ati'	=> 'ati_Latn_CI',
		'atk'	=> 'atk_Latn_PH',
		'atl'	=> 'atl_Latn_PH',
		'atm'	=> 'atm_Latn_PH',
		'atn'	=> 'atn_Arab_IR',
		'ato'	=> 'ato_Latn_CM',
		'atp'	=> 'atp_Latn_PH',
		'atq'	=> 'atq_Latn_ID',
		'atr'	=> 'atr_Latn_BR',
		'ats'	=> 'ats_Latn_US',
		'att'	=> 'att_Latn_PH',
		'atu'	=> 'atu_Latn_SS',
		'atv'	=> 'atv_Cyrl_RU',
		'atw'	=> 'atw_Latn_US',
		'atx'	=> 'atx_Latn_BR',
		'aty'	=> 'aty_Latn_VU',
		'atz'	=> 'atz_Latn_PH',
		'aua'	=> 'aua_Latn_SB',
		'auc'	=> 'auc_Latn_EC',
		'aud'	=> 'aud_Latn_SB',
		'aug'	=> 'aug_Latn_BJ',
		'auh'	=> 'auh_Latn_ZM',
		'aui'	=> 'aui_Latn_PG',
		'auj'	=> 'auj_Arab_LY',
		'auk'	=> 'auk_Latn_PG',
		'aul'	=> 'aul_Latn_VU',
		'aum'	=> 'aum_Latn_NG',
		'aun'	=> 'aun_Latn_PG',
		'auo'	=> 'auo_Latn_NG',
		'aup'	=> 'aup_Latn_PG',
		'auq'	=> 'auq_Latn_ID',
		'aur'	=> 'aur_Latn_PG',
		'aut'	=> 'aut_Latn_PF',
		'auu'	=> 'auu_Latn_ID',
		'auw'	=> 'auw_Latn_ID',
		'auy'	=> 'auy_Latn_PG',
		'auz'	=> 'auz_Arab_UZ',
		'avb'	=> 'avb_Latn_PG',
		'avd'	=> 'avd_Arab_IR',
		'avi'	=> 'avi_Latn_CI',
		'avk'	=> 'avk_Latn_001',
		'avl'	=> 'avl_Arab_EG',
		'avm'	=> 'avm_Latn_AU',
		'avn'	=> 'avn_Latn_GH',
		'avo'	=> 'avo_Latn_BR',
		'avs'	=> 'avs_Latn_PE',
		'avt'	=> 'avt_Latn_PG',
		'avu'	=> 'avu_Latn_SS',
		'avv'	=> 'avv_Latn_BR',
		'awb'	=> 'awb_Latn_PG',
		'awc'	=> 'awc_Latn_NG',
		'awe'	=> 'awe_Latn_BR',
		'awg'	=> 'awg_Latn_AU',
		'awh'	=> 'awh_Latn_ID',
		'awi'	=> 'awi_Latn_PG',
		'awk'	=> 'awk_Latn_AU',
		'awm'	=> 'awm_Latn_PG',
		'awn'	=> 'awn_Ethi_ET',
		'awo'	=> 'awo_Latn_NG',
		'awr'	=> 'awr_Latn_ID',
		'aws'	=> 'aws_Latn_ID',
		'awt'	=> 'awt_Latn_BR',
		'awu'	=> 'awu_Latn_ID',
		'awv'	=> 'awv_Latn_ID',
		'aww'	=> 'aww_Latn_PG',
		'awx'	=> 'awx_Latn_PG',
		'awy'	=> 'awy_Latn_ID',
		'axb'	=> 'axb_Latn_AR',
		'axe'	=> 'axe_Latn_AU',
		'axg'	=> 'axg_Latn_BR',
		'axk'	=> 'axk_Latn_CF',
		'axl'	=> 'axl_Latn_AU',
		'axm'	=> 'axm_Armn_AM',
		'axx'	=> 'axx_Latn_NC',
		'aya'	=> 'aya_Latn_PG',
		'ayb'	=> 'ayb_Latn_BJ',
		'ayc'	=> 'ayc_Latn_PE',
		'ayd'	=> 'ayd_Latn_AU',
		'aye'	=> 'aye_Latn_NG',
		'ayg'	=> 'ayg_Latn_TG',
		'ayh'	=> 'ayh_Arab_YE',
		'ayi'	=> 'ayi_Latn_NG',
		'ayk'	=> 'ayk_Latn_NG',
		'ayl'	=> 'ayl_Arab_LY',
		'ayn'	=> 'ayn_Arab_YE',
		'ayo'	=> 'ayo_Latn_PY',
		'ayp'	=> 'ayp_Arab_IQ',
		'ayq'	=> 'ayq_Latn_PG',
		'ays'	=> 'ays_Latn_PH',
		'ayt'	=> 'ayt_Latn_PH',
		'ayu'	=> 'ayu_Latn_NG',
		'ayz'	=> 'ayz_Latn_ID',
		'azb'	=> 'azb_Arab_IR',
		'azd'	=> 'azd_Latn_MX',
		'azg'	=> 'azg_Latn_MX',
		'azm'	=> 'azm_Latn_MX',
		'azn'	=> 'azn_Latn_MX',
		'azo'	=> 'azo_Latn_CM',
		'azt'	=> 'azt_Latn_PH',
		'azz'	=> 'azz_Latn_MX',
		'baa'	=> 'baa_Latn_SB',
		'bab'	=> 'bab_Latn_GW',
		'bac'	=> 'bac_Latn_ID',
		'bae'	=> 'bae_Latn_VE',
		'baf'	=> 'baf_Latn_CM',
		'bag'	=> 'bag_Latn_CM',
		'bah'	=> 'bah_Latn_BS',
		'baj'	=> 'baj_Latn_ID',
		'bao'	=> 'bao_Latn_CO',
		'bau'	=> 'bau_Latn_NG',
		'bav'	=> 'bav_Latn_CM',
		'baw'	=> 'baw_Latn_CM',
		'bay'	=> 'bay_Latn_ID',
		'bba'	=> 'bba_Latn_BJ',
		'bbb'	=> 'bbb_Latn_PG',
		'bbd'	=> 'bbd_Latn_PG',
		'bbe'	=> 'bbe_Latn_CD',
		'bbf'	=> 'bbf_Latn_PG',
		'bbg'	=> 'bbg_Latn_GA',
		'bbi'	=> 'bbi_Latn_CM',
		'bbk'	=> 'bbk_Latn_CM',
		'bbl'	=> 'bbl_Geor_GE',
		'bbm'	=> 'bbm_Latn_CD',
		'bbn'	=> 'bbn_Latn_PG',
		'bbo'	=> 'bbo_Latn_BF',
		'bbp'	=> 'bbp_Latn_CF',
		'bbq'	=> 'bbq_Latn_CM',
		'bbr'	=> 'bbr_Latn_PG',
		'bbs'	=> 'bbs_Latn_NG',
		'bbt'	=> 'bbt_Latn_NG',
		'bbu'	=> 'bbu_Latn_NG',
		'bbv'	=> 'bbv_Latn_PG',
		'bbw'	=> 'bbw_Latn_CM',
		'bbx'	=> 'bbx_Latn_CM',
		'bby'	=> 'bby_Latn_CM',
		'bca'	=> 'bca_Latn_CN',
		'bcb'	=> 'bcb_Latn_SN',
		'bcd'	=> 'bcd_Latn_ID',
		'bce'	=> 'bce_Latn_CM',
		'bcf'	=> 'bcf_Latn_PG',
		'bcg'	=> 'bcg_Latn_GN',
		'bch'	=> 'bch_Latn_PG',
		'bcj'	=> 'bcj_Latn_AU',
		'bck'	=> 'bck_Latn_AU',
		'bcm'	=> 'bcm_Latn_PG',
		'bcn'	=> 'bcn_Latn_NG',
		'bco'	=> 'bco_Latn_PG',
		'bcp'	=> 'bcp_Latn_CD',
		'bcq'	=> 'bcq_Ethi_ET',
		'bcr'	=> 'bcr_Latn_CA',
		'bcs'	=> 'bcs_Latn_NG',
		'bct'	=> 'bct_Latn_CD',
		'bcu'	=> 'bcu_Latn_PG',
		'bcv'	=> 'bcv_Latn_NG',
		'bcw'	=> 'bcw_Latn_CM',
		'bcy'	=> 'bcy_Latn_NG',
		'bcz'	=> 'bcz_Latn_SN',
		'bda'	=> 'bda_Latn_SN',
		'bdb'	=> 'bdb_Latn_ID',
		'bdc'	=> 'bdc_Latn_CO',
		'bdd'	=> 'bdd_Latn_PG',
		'bde'	=> 'bde_Latn_NG',
		'bdf'	=> 'bdf_Latn_PG',
		'bdg'	=> 'bdg_Latn_MY',
		'bdh'	=> 'bdh_Latn_SS',
		'bdi'	=> 'bdi_Latn_SD',
		'bdj'	=> 'bdj_Latn_SS',
		'bdk'	=> 'bdk_Latn_AZ',
		'bdl'	=> 'bdl_Latn_ID',
		'bdm'	=> 'bdm_Latn_TD',
		'bdn'	=> 'bdn_Latn_CM',
		'bdo'	=> 'bdo_Latn_TD',
		'bdp'	=> 'bdp_Latn_TZ',
		'bdq'	=> 'bdq_Latn_VN',
		'bdr'	=> 'bdr_Latn_MY',
		'bds'	=> 'bds_Latn_TZ',
		'bdt'	=> 'bdt_Latn_CF',
		'bdu'	=> 'bdu_Latn_CM',
		'bdv'	=> 'bdv_Orya_IN',
		'bdw'	=> 'bdw_Latn_ID',
		'bdx'	=> 'bdx_Latn_ID',
		'bdy'	=> 'bdy_Latn_AU',
		'bdz'	=> 'bdz_Arab_PK',
		'bea'	=> 'bea_Latn_CA',
		'beb'	=> 'beb_Latn_CM',
		'bec'	=> 'bec_Latn_CM',
		'bed'	=> 'bed_Latn_ID',
		'bee'	=> 'bee_Deva_IN',
		'bef'	=> 'bef_Latn_PG',
		'beh'	=> 'beh_Latn_BJ',
		'bei'	=> 'bei_Latn_ID',
		'bek'	=> 'bek_Latn_PG',
		'beo'	=> 'beo_Latn_PG',
		'bep'	=> 'bep_Latn_ID',
		'beq'	=> 'beq_Latn_CG',
		'bes'	=> 'bes_Latn_TD',
		'bet'	=> 'bet_Latn_CI',
		'beu'	=> 'beu_Latn_ID',
		'bev'	=> 'bev_Latn_CI',
		'bex'	=> 'bex_Latn_SS',
		'bey'	=> 'bey_Latn_PG',
		'bfa'	=> 'bfa_Latn_SS',
		'bfb'	=> 'bfb_Deva_IN',
		'bfc'	=> 'bfc_Latn_CN',
		'bfe'	=> 'bfe_Latn_ID',
		'bff'	=> 'bff_Latn_CF',
		'bfg'	=> 'bfg_Latn_ID',
		'bfh'	=> 'bfh_Latn_PG',
		'bfj'	=> 'bfj_Latn_CM',
		'bfl'	=> 'bfl_Latn_CF',
		'bfm'	=> 'bfm_Latn_CM',
		'bfn'	=> 'bfn_Latn_TL',
		'bfo'	=> 'bfo_Latn_BF',
		'bfp'	=> 'bfp_Latn_CM',
		'bfs'	=> 'bfs_Latn_CN',
		'bfu'	=> 'bfu_Tibt_IN',
		'bfw'	=> 'bfw_Orya_IN',
		'bfx'	=> 'bfx_Latn_PH',
		'bfz'	=> 'bfz_Deva_IN',
		'bga'	=> 'bga_Latn_NG',
		'bgb'	=> 'bgb_Latn_ID',
		'bgd'	=> 'bgd_Deva_IN',
		'bgf'	=> 'bgf_Latn_CM',
		'bgg'	=> 'bgg_Latn_IN',
		'bgi'	=> 'bgi_Latn_PH',
		'bgj'	=> 'bgj_Latn_CM',
		'bgo'	=> 'bgo_Latn_GN',
		'bgp'	=> 'bgp_Arab_PK',
		'bgq'	=> 'bgq_Deva_IN',
		'bgr'	=> 'bgr_Latn_IN',
		'bgs'	=> 'bgs_Latn_PH',
		'bgt'	=> 'bgt_Latn_SB',
		'bgu'	=> 'bgu_Latn_NG',
		'bgv'	=> 'bgv_Latn_ID',
		'bgw'	=> 'bgw_Deva_IN',
		'bgy'	=> 'bgy_Latn_ID',
		'bgz'	=> 'bgz_Latn_ID',
		'bha'	=> 'bha_Deva_IN',
		'bhc'	=> 'bhc_Latn_ID',
		'bhd'	=> 'bhd_Deva_IN',
		'bhe'	=> 'bhe_Arab_PK',
		'bhf'	=> 'bhf_Latn_PG',
		'bhg'	=> 'bhg_Latn_PG',
		'bhh'	=> 'bhh_Cyrl_IL',
		'bhj'	=> 'bhj_Deva_NP',
		'bhl'	=> 'bhl_Latn_PG',
		'bhm'	=> 'bhm_Arab_OM',
		'bhn'	=> 'bhn_Syrc_GE',
		'bhp'	=> 'bhp_Latn_ID',
		'bhq'	=> 'bhq_Latn_ID',
		'bhr'	=> 'bhr_Latn_MG',
		'bhs'	=> 'bhs_Latn_CM',
		'bht'	=> 'bht_Deva_IN',
		'bhu'	=> 'bhu_Deva_IN',
		'bhv'	=> 'bhv_Latn_ID',
		'bhw'	=> 'bhw_Latn_ID',
		'bhy'	=> 'bhy_Latn_CD',
		'bhz'	=> 'bhz_Latn_ID',
		'bia'	=> 'bia_Latn_AU',
		'bib'	=> 'bib_Latn_BF',
		'bid'	=> 'bid_Latn_TD',
		'bie'	=> 'bie_Latn_PG',
		'bif'	=> 'bif_Latn_GW',
		'big'	=> 'big_Latn_PG',
		'bil'	=> 'bil_Latn_NG',
		'bim'	=> 'bim_Latn_GH',
		'bio'	=> 'bio_Latn_PG',
		'bip'	=> 'bip_Latn_CD',
		'biq'	=> 'biq_Latn_PG',
		'bir'	=> 'bir_Latn_PG',
		'bit'	=> 'bit_Latn_PG',
		'biu'	=> 'biu_Latn_IN',
		'biv'	=> 'biv_Latn_GH',
		'biw'	=> 'biw_Latn_CM',
		'biy'	=> 'biy_Deva_IN',
		'biz'	=> 'biz_Latn_CD',
		'bja'	=> 'bja_Latn_CD',
		'bjb'	=> 'bjb_Latn_AU',
		'bjc'	=> 'bjc_Latn_PG',
		'bjf'	=> 'bjf_Syrc_IL',
		'bjg'	=> 'bjg_Latn_GW',
		'bjh'	=> 'bjh_Latn_PG',
		'bji'	=> 'bji_Latn_ET',
		'bjk'	=> 'bjk_Latn_PG',
		'bjl'	=> 'bjl_Latn_PG',
		'bjm'	=> 'bjm_Arab_IQ',
		'bjo'	=> 'bjo_Latn_CF',
		'bjp'	=> 'bjp_Latn_PG',
		'bjr'	=> 'bjr_Latn_PG',
		'bjs'	=> 'bjs_Latn_BB',
		'bju'	=> 'bju_Latn_CM',
		'bjv'	=> 'bjv_Latn_TD',
		'bjw'	=> 'bjw_Latn_CI',
		'bjx'	=> 'bjx_Latn_PH',
		'bjy'	=> 'bjy_Latn_AU',
		'bjz'	=> 'bjz_Latn_PG',
		'bka'	=> 'bka_Latn_NG',
		'bkc'	=> 'bkc_Latn_CM',
		'bkd'	=> 'bkd_Latn_PH',
		'bkf'	=> 'bkf_Latn_CD',
		'bkg'	=> 'bkg_Latn_CF',
		'bkh'	=> 'bkh_Latn_CM',
		'bki'	=> 'bki_Latn_VU',
		'bkj'	=> 'bkj_Latn_CF',
		'bkk'	=> 'bkk_Tibt_IN',
		'bkl'	=> 'bkl_Latn_ID',
		'bkn'	=> 'bkn_Latn_ID',
		'bko'	=> 'bko_Latn_CM',
		'bkp'	=> 'bkp_Latn_CD',
		'bkq'	=> 'bkq_Latn_BR',
		'bkr'	=> 'bkr_Latn_ID',
		'bks'	=> 'bks_Latn_PH',
		'bkt'	=> 'bkt_Latn_CD',
		'bkv'	=> 'bkv_Latn_NG',
		'bkw'	=> 'bkw_Latn_CG',
		'bkx'	=> 'bkx_Latn_TL',
		'bky'	=> 'bky_Latn_NG',
		'bkz'	=> 'bkz_Latn_ID',
		'blb'	=> 'blb_Latn_SB',
		'blc'	=> 'blc_Latn_CA',
		'bld'	=> 'bld_Latn_ID',
		'ble'	=> 'ble_Latn_GW',
		'blf'	=> 'blf_Latn_ID',
		'blh'	=> 'blh_Latn_LR',
		'bli'	=> 'bli_Latn_CD',
		'blj'	=> 'blj_Latn_ID',
		'blk'	=> 'blk_Mymr_MM',
		'blm'	=> 'blm_Latn_SS',
		'bln'	=> 'bln_Latn_PH',
		'blp'	=> 'blp_Latn_SB',
		'blq'	=> 'blq_Latn_PG',
		'blr'	=> 'blr_Latn_CN',
		'bls'	=> 'bls_Latn_ID',
		'blv'	=> 'blv_Latn_AO',
		'blw'	=> 'blw_Latn_PH',
		'blx'	=> 'blx_Latn_PH',
		'bly'	=> 'bly_Latn_BJ',
		'blz'	=> 'blz_Latn_ID',
		'bma'	=> 'bma_Latn_NG',
		'bmb'	=> 'bmb_Latn_CD',
		'bmc'	=> 'bmc_Latn_PG',
		'bmd'	=> 'bmd_Latn_GN',
		'bme'	=> 'bme_Latn_CF',
		'bmf'	=> 'bmf_Latn_SL',
		'bmg'	=> 'bmg_Latn_CD',
		'bmh'	=> 'bmh_Latn_PG',
		'bmi'	=> 'bmi_Latn_TD',
		'bmj'	=> 'bmj_Deva_NP',
		'bmk'	=> 'bmk_Latn_PG',
		'bml'	=> 'bml_Latn_CD',
		'bmm'	=> 'bmm_Latn_MG',
		'bmn'	=> 'bmn_Latn_PG',
		'bmo'	=> 'bmo_Latn_CM',
		'bmp'	=> 'bmp_Latn_PG',
		'bmr'	=> 'bmr_Latn_CO',
		'bms'	=> 'bms_Latn_NE',
		'bmu'	=> 'bmu_Latn_PG',
		'bmv'	=> 'bmv_Latn_CM',
		'bmw'	=> 'bmw_Latn_CG',
		'bmx'	=> 'bmx_Latn_PG',
		'bmz'	=> 'bmz_Latn_PG',
		'bna'	=> 'bna_Latn_ID',
		'bnb'	=> 'bnb_Latn_MY',
		'bnc'	=> 'bnc_Latn_PH',
		'bnd'	=> 'bnd_Latn_ID',
		'bne'	=> 'bne_Latn_ID',
		'bnf'	=> 'bnf_Latn_ID',
		'bng'	=> 'bng_Latn_GQ',
		'bni'	=> 'bni_Latn_CD',
		'bnj'	=> 'bnj_Latn_PH',
		'bnk'	=> 'bnk_Latn_VU',
		'bnm'	=> 'bnm_Latn_GQ',
		'bnn'	=> 'bnn_Latn_TW',
		'bno'	=> 'bno_Latn_PH',
		'bnp'	=> 'bnp_Latn_PG',
		'bnq'	=> 'bnq_Latn_ID',
		'bnr'	=> 'bnr_Latn_VU',
		'bns'	=> 'bns_Deva_IN',
		'bnu'	=> 'bnu_Latn_ID',
		'bnv'	=> 'bnv_Latn_ID',
		'bnw'	=> 'bnw_Latn_PG',
		'bnx'	=> 'bnx_Latn_CD',
		'bny'	=> 'bny_Latn_MY',
		'bnz'	=> 'bnz_Latn_CM',
		'boa'	=> 'boa_Latn_PE',
		'bob'	=> 'bob_Latn_KE',
		'boe'	=> 'boe_Latn_CM',
		'bof'	=> 'bof_Latn_BF',
		'boh'	=> 'boh_Latn_CD',
		'boj'	=> 'boj_Latn_PG',
		'bok'	=> 'bok_Latn_CG',
		'bol'	=> 'bol_Latn_NG',
		'bom'	=> 'bom_Latn_NG',
		'bon'	=> 'bon_Latn_PG',
		'boo'	=> 'boo_Latn_ML',
		'bop'	=> 'bop_Latn_PG',
		'boq'	=> 'boq_Latn_PG',
		'bor'	=> 'bor_Latn_BR',
		'bot'	=> 'bot_Latn_SS',
		'bou'	=> 'bou_Latn_TZ',
		'bov'	=> 'bov_Latn_GH',
		'bow'	=> 'bow_Latn_PG',
		'box'	=> 'box_Latn_BF',
		'boy'	=> 'boy_Latn_CF',
		'boz'	=> 'boz_Latn_ML',
		'bpa'	=> 'bpa_Latn_VU',
		'bpc'	=> 'bpc_Latn_CM',
		'bpd'	=> 'bpd_Latn_CF',
		'bpe'	=> 'bpe_Latn_PG',
		'bpg'	=> 'bpg_Latn_ID',
		'bph'	=> 'bph_Cyrl_RU',
		'bpi'	=> 'bpi_Latn_PG',
		'bpj'	=> 'bpj_Latn_CD',
		'bpk'	=> 'bpk_Latn_NC',
		'bpl'	=> 'bpl_Latn_AU',
		'bpm'	=> 'bpm_Latn_PG',
		'bpo'	=> 'bpo_Latn_ID',
		'bpp'	=> 'bpp_Latn_ID',
		'bpq'	=> 'bpq_Latn_ID',
		'bpr'	=> 'bpr_Latn_PH',
		'bps'	=> 'bps_Latn_PH',
		'bpt'	=> 'bpt_Latn_AU',
		'bpu'	=> 'bpu_Latn_PG',
		'bpv'	=> 'bpv_Latn_ID',
		'bpw'	=> 'bpw_Latn_PG',
		'bpx'	=> 'bpx_Deva_IN',
		'bpz'	=> 'bpz_Latn_ID',
		'bqa'	=> 'bqa_Latn_BJ',
		'bqb'	=> 'bqb_Latn_ID',
		'bqc'	=> 'bqc_Latn_BJ',
		'bqd'	=> 'bqd_Latn_CM',
		'bqf'	=> 'bqf_Latn_GN',
		'bqg'	=> 'bqg_Latn_TG',
		'bqj'	=> 'bqj_Latn_SN',
		'bqk'	=> 'bqk_Latn_CF',
		'bql'	=> 'bql_Latn_PG',
		'bqm'	=> 'bqm_Latn_CM',
		'bqo'	=> 'bqo_Latn_CM',
		'bqp'	=> 'bqp_Latn_NG',
		'bqq'	=> 'bqq_Latn_ID',
		'bqr'	=> 'bqr_Latn_ID',
		'bqs'	=> 'bqs_Latn_PG',
		'bqt'	=> 'bqt_Latn_CM',
		'bqu'	=> 'bqu_Latn_CD',
		'bqw'	=> 'bqw_Latn_NG',
		'bqx'	=> 'bqx_Latn_NG',
		'bqz'	=> 'bqz_Latn_CM',
		'brb'	=> 'brb_Khmr_KH',
		'brc'	=> 'brc_Latn_GY',
		'brd'	=> 'brd_Deva_NP',
		'brf'	=> 'brf_Latn_CD',
		'brg'	=> 'brg_Latn_BO',
		'bri'	=> 'bri_Latn_CM',
		'brj'	=> 'brj_Latn_VU',
		'brk'	=> 'brk_Arab_SD',
		'brl'	=> 'brl_Latn_BW',
		'brm'	=> 'brm_Latn_CD',
		'brn'	=> 'brn_Latn_CR',
		'bro'	=> 'bro_Tibt_BT',
		'brp'	=> 'brp_Latn_ID',
		'brq'	=> 'brq_Latn_PG',
		'brr'	=> 'brr_Latn_SB',
		'brs'	=> 'brs_Latn_ID',
		'brt'	=> 'brt_Latn_NG',
		'bru'	=> 'bru_Latn_VN',
		'brv'	=> 'brv_Laoo_LA',
		'brw'	=> 'brw_Knda_IN',
		'bry'	=> 'bry_Latn_PG',
		'brz'	=> 'brz_Latn_PG',
		'bsa'	=> 'bsa_Latn_ID',
		'bsb'	=> 'bsb_Latn_BN',
		'bse'	=> 'bse_Latn_CM',
		'bsf'	=> 'bsf_Latn_NG',
		'bsh'	=> 'bsh_Arab_AF',
		'bsi'	=> 'bsi_Latn_CM',
		'bsj'	=> 'bsj_Latn_NG',
		'bsk'	=> 'bsk_Arab_PK',
		'bsl'	=> 'bsl_Latn_NG',
		'bsm'	=> 'bsm_Latn_ID',
		'bsn'	=> 'bsn_Latn_CO',
		'bso'	=> 'bso_Latn_TD',
		'bsp'	=> 'bsp_Latn_GN',
		'bsr'	=> 'bsr_Latn_NG',
		'bst'	=> 'bst_Ethi_ET',
		'bsu'	=> 'bsu_Latn_ID',
		'bsv'	=> 'bsv_Latn_GN',
		'bsw'	=> 'bsw_Latn_ET',
		'bsx'	=> 'bsx_Latn_NG',
		'bsy'	=> 'bsy_Latn_MY',
		'bta'	=> 'bta_Latn_NG',
		'btc'	=> 'btc_Latn_CM',
		'btd'	=> 'btd_Batk_ID',
		'bte'	=> 'bte_Latn_NG',
		'btf'	=> 'btf_Latn_TD',
		'btg'	=> 'btg_Latn_CI',
		'bth'	=> 'bth_Latn_MY',
		'bti'	=> 'bti_Latn_ID',
		'btj'	=> 'btj_Latn_ID',
		'btm'	=> 'btm_Batk_ID',
		'btn'	=> 'btn_Latn_PH',
		'btp'	=> 'btp_Latn_PG',
		'btq'	=> 'btq_Latn_MY',
		'btr'	=> 'btr_Latn_VU',
		'bts'	=> 'bts_Latn_ID',
		'btt'	=> 'btt_Latn_NG',
		'btu'	=> 'btu_Latn_NG',
		'btw'	=> 'btw_Latn_PH',
		'btx'	=> 'btx_Latn_ID',
		'bty'	=> 'bty_Latn_ID',
		'btz'	=> 'btz_Latn_ID',
		'bub'	=> 'bub_Latn_TD',
		'bud'	=> 'bud_Latn_TG',
		'bue'	=> 'bue_Latn_CA',
		'buf'	=> 'buf_Latn_CD',
		'buh'	=> 'buh_Latn_CN',
		'bui'	=> 'bui_Latn_CG',
		'buj'	=> 'buj_Latn_NG',
		'buk'	=> 'buk_Latn_PG',
		'bun'	=> 'bun_Latn_SL',
		'buo'	=> 'buo_Latn_PG',
		'bup'	=> 'bup_Latn_ID',
		'buq'	=> 'buq_Latn_PG',
		'bus'	=> 'bus_Latn_NG',
		'but'	=> 'but_Latn_PG',
		'buu'	=> 'buu_Latn_CD',
		'buv'	=> 'buv_Latn_PG',
		'buw'	=> 'buw_Latn_GA',
		'bux'	=> 'bux_Latn_NG',
		'buy'	=> 'buy_Latn_SL',
		'buz'	=> 'buz_Latn_NG',
		'bva'	=> 'bva_Latn_TD',
		'bvc'	=> 'bvc_Latn_SB',
		'bvd'	=> 'bvd_Latn_SB',
		'bve'	=> 'bve_Latn_ID',
		'bvf'	=> 'bvf_Latn_TD',
		'bvg'	=> 'bvg_Latn_CM',
		'bvh'	=> 'bvh_Latn_NG',
		'bvi'	=> 'bvi_Latn_SS',
		'bvj'	=> 'bvj_Latn_NG',
		'bvk'	=> 'bvk_Latn_ID',
		'bvm'	=> 'bvm_Latn_CM',
		'bvn'	=> 'bvn_Latn_PG',
		'bvo'	=> 'bvo_Latn_TD',
		'bvq'	=> 'bvq_Latn_CF',
		'bvr'	=> 'bvr_Latn_AU',
		'bvt'	=> 'bvt_Latn_ID',
		'bvu'	=> 'bvu_Latn_ID',
		'bvv'	=> 'bvv_Latn_VE',
		'bvw'	=> 'bvw_Latn_NG',
		'bvx'	=> 'bvx_Latn_CG',
		'bvy'	=> 'bvy_Latn_PH',
		'bvz'	=> 'bvz_Latn_ID',
		'bwa'	=> 'bwa_Latn_NC',
		'bwb'	=> 'bwb_Latn_FJ',
		'bwc'	=> 'bwc_Latn_ZM',
		'bwd'	=> 'bwd_Latn_PG',
		'bwe'	=> 'bwe_Mymr_MM',
		'bwf'	=> 'bwf_Latn_PG',
		'bwg'	=> 'bwg_Latn_MZ',
		'bwh'	=> 'bwh_Latn_CM',
		'bwi'	=> 'bwi_Latn_VE',
		'bwj'	=> 'bwj_Latn_BF',
		'bwk'	=> 'bwk_Latn_PG',
		'bwl'	=> 'bwl_Latn_CD',
		'bwm'	=> 'bwm_Latn_PG',
		'bwo'	=> 'bwo_Latn_ET',
		'bwp'	=> 'bwp_Latn_ID',
		'bwq'	=> 'bwq_Latn_BF',
		'bwr'	=> 'bwr_Latn_NG',
		'bws'	=> 'bws_Latn_CD',
		'bwt'	=> 'bwt_Latn_CM',
		'bwu'	=> 'bwu_Latn_GH',
		'bww'	=> 'bww_Latn_CD',
		'bwx'	=> 'bwx_Latn_CN',
		'bwy'	=> 'bwy_Latn_BF',
		'bwz'	=> 'bwz_Latn_CG',
		'bxa'	=> 'bxa_Latn_SB',
		'bxb'	=> 'bxb_Latn_SS',
		'bxc'	=> 'bxc_Latn_GQ',
		'bxf'	=> 'bxf_Latn_PG',
		'bxg'	=> 'bxg_Latn_CD',
		'bxh'	=> 'bxh_Latn_PG',
		'bxi'	=> 'bxi_Latn_AU',
		'bxj'	=> 'bxj_Latn_AU',
		'bxl'	=> 'bxl_Latn_BF',
		'bxm'	=> 'bxm_Cyrl_MN',
		'bxn'	=> 'bxn_Latn_AU',
		'bxo'	=> 'bxo_Latn_NG',
		'bxp'	=> 'bxp_Latn_CM',
		'bxq'	=> 'bxq_Latn_NG',
		'bxs'	=> 'bxs_Latn_CM',
		'bxu'	=> 'bxu_Mong_CN',
		'bxv'	=> 'bxv_Latn_TD',
		'bxw'	=> 'bxw_Latn_ML',
		'bxz'	=> 'bxz_Latn_PG',
		'bya'	=> 'bya_Latn_PH',
		'byb'	=> 'byb_Latn_CM',
		'byc'	=> 'byc_Latn_NG',
		'byd'	=> 'byd_Latn_ID',
		'bye'	=> 'bye_Latn_PG',
		'byf'	=> 'byf_Latn_NG',
		'byh'	=> 'byh_Deva_NP',
		'byi'	=> 'byi_Latn_CD',
		'byj'	=> 'byj_Latn_NG',
		'byk'	=> 'byk_Latn_CN',
		'byl'	=> 'byl_Latn_ID',
		'bym'	=> 'bym_Latn_AU',
		'byp'	=> 'byp_Latn_NG',
		'byr'	=> 'byr_Latn_PG',
		'bys'	=> 'bys_Latn_NG',
		'byw'	=> 'byw_Deva_NP',
		'byx'	=> 'byx_Latn_PG',
		'byz'	=> 'byz_Latn_PG',
		'bza'	=> 'bza_Latn_LR',
		'bzb'	=> 'bzb_Latn_ID',
		'bzc'	=> 'bzc_Latn_MG',
		'bzd'	=> 'bzd_Latn_CR',
		'bzf'	=> 'bzf_Latn_PG',
		'bzh'	=> 'bzh_Latn_PG',
		'bzi'	=> 'bzi_Thai_TH',
		'bzj'	=> 'bzj_Latn_BZ',
		'bzk'	=> 'bzk_Latn_NI',
		'bzl'	=> 'bzl_Latn_ID',
		'bzm'	=> 'bzm_Latn_CD',
		'bzn'	=> 'bzn_Latn_ID',
		'bzo'	=> 'bzo_Latn_CD',
		'bzp'	=> 'bzp_Latn_ID',
		'bzq'	=> 'bzq_Latn_ID',
		'bzr'	=> 'bzr_Latn_AU',
		'bzt'	=> 'bzt_Latn_001',
		'bzu'	=> 'bzu_Latn_ID',
		'bzv'	=> 'bzv_Latn_CM',
		'bzw'	=> 'bzw_Latn_NG',
		'bzx'	=> 'bzx_Latn_ML',
		'bzy'	=> 'bzy_Latn_NG',
		'bzz'	=> 'bzz_Latn_NG',
		'caa'	=> 'caa_Latn_GT',
		'cab'	=> 'cab_Latn_HN',
		'cac'	=> 'cac_Latn_GT',
		'cae'	=> 'cae_Latn_SN',
		'caf'	=> 'caf_Latn_CA',
		'cag'	=> 'cag_Latn_PY',
		'cah'	=> 'cah_Latn_PE',
		'caj'	=> 'caj_Latn_BO',
		'cak'	=> 'cak_Latn_GT',
		'cal'	=> 'cal_Latn_MP',
		'cam'	=> 'cam_Latn_NC',
		'can'	=> 'can_Latn_PG',
		'cao'	=> 'cao_Latn_BO',
		'cap'	=> 'cap_Latn_BO',
		'caq'	=> 'caq_Latn_IN',
		'car'	=> 'car_Latn_VE',
		'cas'	=> 'cas_Latn_BO',
		'cav'	=> 'cav_Latn_BO',
		'caw'	=> 'caw_Latn_BO',
		'cax'	=> 'cax_Latn_BO',
		'cay'	=> 'cay_Latn_CA',
		'caz'	=> 'caz_Latn_BO',
		'cbb'	=> 'cbb_Latn_CO',
		'cbc'	=> 'cbc_Latn_CO',
		'cbd'	=> 'cbd_Latn_CO',
		'cbg'	=> 'cbg_Latn_CO',
		'cbi'	=> 'cbi_Latn_EC',
		'cbj'	=> 'cbj_Latn_BJ',
		'cbk'	=> 'cbk_Latn_PH',
		'cbl'	=> 'cbl_Latn_MM',
		'cbn'	=> 'cbn_Thai_TH',
		'cbo'	=> 'cbo_Latn_NG',
		'cbq'	=> 'cbq_Latn_NG',
		'cbr'	=> 'cbr_Latn_PE',
		'cbs'	=> 'cbs_Latn_PE',
		'cbt'	=> 'cbt_Latn_PE',
		'cbu'	=> 'cbu_Latn_PE',
		'cbv'	=> 'cbv_Latn_CO',
		'cbw'	=> 'cbw_Latn_PH',
		'cby'	=> 'cby_Latn_CO',
		'ccc'	=> 'ccc_Latn_PE',
		'ccd'	=> 'ccd_Latn_BR',
		'cce'	=> 'cce_Latn_MZ',
		'ccg'	=> 'ccg_Latn_NG',
		'ccj'	=> 'ccj_Latn_GW',
		'ccl'	=> 'ccl_Latn_TZ',
		'ccm'	=> 'ccm_Latn_MY',
		'cco'	=> 'cco_Latn_MX',
		'ccr'	=> 'ccr_Latn_SV',
		'cde'	=> 'cde_Telu_IN',
		'cdf'	=> 'cdf_Latn_IN',
		'cdh'	=> 'cdh_Deva_IN',
		'cdi'	=> 'cdi_Gujr_IN',
		'cdj'	=> 'cdj_Deva_IN',
		'cdm'	=> 'cdm_Deva_NP',
		'cdo'	=> 'cdo_Hans_CN',
		'cdr'	=> 'cdr_Latn_NG',
		'cdz'	=> 'cdz_Beng_IN',
		'cea'	=> 'cea_Latn_US',
		'ceg'	=> 'ceg_Latn_PY',
		'cek'	=> 'cek_Latn_MM',
		'cen'	=> 'cen_Latn_NG',
		'cet'	=> 'cet_Latn_NG',
		'cey'	=> 'cey_Latn_MM',
		'cfa'	=> 'cfa_Latn_NG',
		'cfd'	=> 'cfd_Latn_NG',
		'cfg'	=> 'cfg_Latn_NG',
		'cfm'	=> 'cfm_Latn_MM',
		'cga'	=> 'cga_Latn_PG',
		'cgc'	=> 'cgc_Latn_PH',
		'cgk'	=> 'cgk_Tibt_BT',
		'chb'	=> 'chb_Latn_CO',
		'chd'	=> 'chd_Latn_MX',
		'chf'	=> 'chf_Latn_MX',
		'chg'	=> 'chg_Arab_TM',
		'chh'	=> 'chh_Latn_US',
		'chj'	=> 'chj_Latn_MX',
		'chl'	=> 'chl_Latn_US',
		'chn'	=> 'chn_Latn_US',
		'chq'	=> 'chq_Latn_MX',
		'cht'	=> 'cht_Latn_PE',
		'chw'	=> 'chw_Latn_MZ',
		'chx'	=> 'chx_Deva_NP',
		'chy'	=> 'chy_Latn_US',
		'chz'	=> 'chz_Latn_MX',
		'cia'	=> 'cia_Latn_ID',
		'cib'	=> 'cib_Latn_BJ',
		'cie'	=> 'cie_Latn_NG',
		'cih'	=> 'cih_Deva_IN',
		'cim'	=> 'cim_Latn_IT',
		'cin'	=> 'cin_Latn_BR',
		'cip'	=> 'cip_Latn_MX',
		'cir'	=> 'cir_Latn_NC',
		'ciw'	=> 'ciw_Latn_US',
		'ciy'	=> 'ciy_Latn_VE',
		'cje'	=> 'cje_Latn_VN',
		'cjh'	=> 'cjh_Latn_US',
		'cji'	=> 'cji_Cyrl_RU',
		'cjk'	=> 'cjk_Latn_AO',
		'cjn'	=> 'cjn_Latn_PG',
		'cjo'	=> 'cjo_Latn_PE',
		'cjp'	=> 'cjp_Latn_CR',
		'cjs'	=> 'cjs_Latn_RU',
		'cjv'	=> 'cjv_Latn_PG',
		'cjy'	=> 'cjy_Hans_CN',
		'ckl'	=> 'ckl_Latn_NG',
		'ckm'	=> 'ckm_Latn_HR',
		'ckn'	=> 'ckn_Latn_MM',
		'cko'	=> 'cko_Latn_GH',
		'ckq'	=> 'ckq_Latn_TD',
		'ckr'	=> 'ckr_Latn_PG',
		'cks'	=> 'cks_Latn_NC',
		'ckt'	=> 'ckt_Cyrl_RU',
		'cku'	=> 'cku_Latn_US',
		'ckv'	=> 'ckv_Latn_TW',
		'ckx'	=> 'ckx_Latn_CM',
		'cky'	=> 'cky_Latn_NG',
		'ckz'	=> 'ckz_Latn_GT',
		'cla'	=> 'cla_Latn_NG',
		'cle'	=> 'cle_Latn_MX',
		'clh'	=> 'clh_Arab_PK',
		'cli'	=> 'cli_Latn_GH',
		'clj'	=> 'clj_Latn_MM',
		'clk'	=> 'clk_Latn_IN',
		'cll'	=> 'cll_Latn_GH',
		'clm'	=> 'clm_Latn_US',
		'clo'	=> 'clo_Latn_MX',
		'clt'	=> 'clt_Latn_MM',
		'clu'	=> 'clu_Latn_PH',
		'clw'	=> 'clw_Cyrl_RU',
		'cly'	=> 'cly_Latn_MX',
		'cma'	=> 'cma_Latn_VN',
		'cme'	=> 'cme_Latn_BF',
		'cmi'	=> 'cmi_Latn_CO',
		'cml'	=> 'cml_Latn_ID',
		'cmo'	=> 'cmo_Latn_VN',
		'cmr'	=> 'cmr_Latn_MM',
		'cms'	=> 'cms_Latn_IT',
		'cmt'	=> 'cmt_Latn_ZA',
		'cna'	=> 'cna_Tibt_IN',
		'cnb'	=> 'cnb_Latn_MM',
		'cnc'	=> 'cnc_Latn_VN',
		'cng'	=> 'cng_Latn_CN',
		'cnh'	=> 'cnh_Latn_MM',
		'cni'	=> 'cni_Latn_PE',
		'cnk'	=> 'cnk_Latn_MM',
		'cnl'	=> 'cnl_Latn_MX',
		'cnp'	=> 'cnp_Hans_CN',
		'cnq'	=> 'cnq_Latn_CM',
		'cns'	=> 'cns_Latn_ID',
		'cnt'	=> 'cnt_Latn_MX',
		'cnw'	=> 'cnw_Latn_MM',
		'cnx'	=> 'cnx_Latn_GB',
		'coa'	=> 'coa_Latn_AU',
		'cob'	=> 'cob_Latn_MX',
		'coc'	=> 'coc_Latn_MX',
		'cod'	=> 'cod_Latn_PE',
		'coe'	=> 'coe_Latn_CO',
		'cof'	=> 'cof_Latn_EC',
		'cog'	=> 'cog_Thai_TH',
		'coh'	=> 'coh_Latn_KE',
		'coj'	=> 'coj_Latn_MX',
		'cok'	=> 'cok_Latn_MX',
		'col'	=> 'col_Latn_US',
		'com'	=> 'com_Latn_US',
		'coo'	=> 'coo_Latn_CA',
		'coq'	=> 'coq_Latn_US',
		'cot'	=> 'cot_Latn_PE',
		'cou'	=> 'cou_Latn_SN',
		'cox'	=> 'cox_Latn_PE',
		'coz'	=> 'coz_Latn_MX',
		'cpa'	=> 'cpa_Latn_MX',
		'cpb'	=> 'cpb_Latn_PE',
		'cpc'	=> 'cpc_Latn_PE',
		'cpg'	=> 'cpg_Grek_GR',
		'cpi'	=> 'cpi_Latn_NR',
		'cpn'	=> 'cpn_Latn_GH',
		'cpo'	=> 'cpo_Latn_BF',
		'cpu'	=> 'cpu_Latn_PE',
		'cpx'	=> 'cpx_Latn_CN',
		'cpy'	=> 'cpy_Latn_PE',
		'cqd'	=> 'cqd_Latn_CN',
		'cra'	=> 'cra_Latn_ET',
		'crb'	=> 'crb_Latn_VC',
		'crc'	=> 'crc_Latn_VU',
		'crd'	=> 'crd_Latn_US',
		'crf'	=> 'crf_Latn_CO',
		'cri'	=> 'cri_Latn_ST',
		'crj'	=> 'crj_Cans_CA',
		'crm'	=> 'crm_Cans_CA',
		'crn'	=> 'crn_Latn_MX',
		'cro'	=> 'cro_Latn_US',
		'crq'	=> 'crq_Latn_AR',
		'crt'	=> 'crt_Latn_AR',
		'crv'	=> 'crv_Latn_IN',
		'crw'	=> 'crw_Latn_VN',
		'crx'	=> 'crx_Latn_CA',
		'cry'	=> 'cry_Latn_NG',
		'crz'	=> 'crz_Latn_US',
		'csa'	=> 'csa_Latn_MX',
		'csh'	=> 'csh_Mymr_MM',
		'csj'	=> 'csj_Latn_MM',
		'csk'	=> 'csk_Latn_SN',
		'csm'	=> 'csm_Latn_US',
		'cso'	=> 'cso_Latn_MX',
		'csp'	=> 'csp_Hans_CN',
		'css'	=> 'css_Latn_US',
		'cst'	=> 'cst_Latn_US',
		'csv'	=> 'csv_Latn_MM',
		'csy'	=> 'csy_Latn_MM',
		'csz'	=> 'csz_Latn_US',
		'cta'	=> 'cta_Latn_MX',
		'ctc'	=> 'ctc_Latn_US',
		'cte'	=> 'cte_Latn_MX',
		'ctg'	=> 'ctg_Beng_BD',
		'cth'	=> 'cth_Latn_MM',
		'ctl'	=> 'ctl_Latn_MX',
		'ctm'	=> 'ctm_Latn_US',
		'ctn'	=> 'ctn_Deva_NP',
		'cto'	=> 'cto_Latn_CO',
		'ctp'	=> 'ctp_Latn_MX',
		'cts'	=> 'cts_Latn_PH',
		'ctt'	=> 'ctt_Taml_IN',
		'ctu'	=> 'ctu_Latn_MX',
		'cty'	=> 'cty_Taml_IN',
		'ctz'	=> 'ctz_Latn_MX',
		'cua'	=> 'cua_Latn_VN',
		'cub'	=> 'cub_Latn_CO',
		'cuc'	=> 'cuc_Latn_MX',
		'cuh'	=> 'cuh_Latn_KE',
		'cui'	=> 'cui_Latn_CO',
		'cuj'	=> 'cuj_Latn_PE',
		'cuk'	=> 'cuk_Latn_PA',
		'cul'	=> 'cul_Latn_BR',
		'cuo'	=> 'cuo_Latn_VE',
		'cup'	=> 'cup_Latn_US',
		'cut'	=> 'cut_Latn_MX',
		'cuu'	=> 'cuu_Lana_CN',
		'cuv'	=> 'cuv_Latn_CM',
		'cux'	=> 'cux_Latn_MX',
		'cuy'	=> 'cuy_Latn_MX',
		'cvg'	=> 'cvg_Latn_IN',
		'cvn'	=> 'cvn_Latn_MX',
		'cwa'	=> 'cwa_Latn_TZ',
		'cwb'	=> 'cwb_Latn_MZ',
		'cwe'	=> 'cwe_Latn_TZ',
		'cwg'	=> 'cwg_Latn_MY',
		'cwt'	=> 'cwt_Latn_SN',
		'cxh'	=> 'cxh_Latn_NG',
		'cya'	=> 'cya_Latn_MX',
		'cyb'	=> 'cyb_Latn_BO',
		'cyo'	=> 'cyo_Latn_PH',
		'czh'	=> 'czh_Hans_CN',
		'czk'	=> 'czk_Hebr_CZ',
		'czn'	=> 'czn_Latn_MX',
		'czt'	=> 'czt_Latn_MM',
		'daa'	=> 'daa_Latn_TD',
		'dac'	=> 'dac_Latn_PG',
		'dad'	=> 'dad_Latn_PG',
		'dae'	=> 'dae_Latn_CM',
		'dag'	=> 'dag_Latn_GH',
		'dah'	=> 'dah_Latn_PG',
		'dai'	=> 'dai_Latn_TD',
		'daj'	=> 'daj_Latn_SD',
		'dal'	=> 'dal_Latn_KE',
		'dam'	=> 'dam_Latn_NG',
		'dao'	=> 'dao_Latn_MM',
		'daq'	=> 'daq_Deva_IN',
		'das'	=> 'das_Latn_CI',
		'dau'	=> 'dau_Latn_TD',
		'daw'	=> 'daw_Latn_PH',
		'dax'	=> 'dax_Latn_AU',
		'daz'	=> 'daz_Latn_ID',
		'dba'	=> 'dba_Latn_ML',
		'dbb'	=> 'dbb_Latn_NG',
		'dbd'	=> 'dbd_Latn_NG',
		'dbe'	=> 'dbe_Latn_ID',
		'dbf'	=> 'dbf_Latn_ID',
		'dbg'	=> 'dbg_Latn_ML',
		'dbi'	=> 'dbi_Latn_NG',
		'dbj'	=> 'dbj_Latn_MY',
		'dbl'	=> 'dbl_Latn_AU',
		'dbm'	=> 'dbm_Latn_NG',
		'dbn'	=> 'dbn_Latn_ID',
		'dbo'	=> 'dbo_Latn_NG',
		'dbp'	=> 'dbp_Latn_NG',
		'dbq'	=> 'dbq_Latn_CM',
		'dbt'	=> 'dbt_Latn_ML',
		'dbu'	=> 'dbu_Latn_ML',
		'dbv'	=> 'dbv_Latn_NG',
		'dbw'	=> 'dbw_Latn_ML',
		'dby'	=> 'dby_Latn_PG',
		'dcr'	=> 'dcr_Latn_VI',
		'dda'	=> 'dda_Latn_AU',
		'ddd'	=> 'ddd_Latn_SS',
		'dde'	=> 'dde_Latn_CG',
		'ddg'	=> 'ddg_Latn_TL',
		'ddi'	=> 'ddi_Latn_PG',
		'ddj'	=> 'ddj_Latn_AU',
		'ddn'	=> 'ddn_Latn_BJ',
		'ddo'	=> 'ddo_Cyrl_RU',
		'ddr'	=> 'ddr_Latn_AU',
		'dds'	=> 'dds_Latn_ML',
		'ddw'	=> 'ddw_Latn_ID',
		'dec'	=> 'dec_Latn_SD',
		'ded'	=> 'ded_Latn_PG',
		'dee'	=> 'dee_Latn_LR',
		'def'	=> 'def_Arab_IR',
		'deg'	=> 'deg_Latn_NG',
		'deh'	=> 'deh_Arab_PK',
		'dei'	=> 'dei_Latn_ID',
		'dek'	=> 'dek_Latn_CM',
		'del'	=> 'del_Latn_US',
		'dem'	=> 'dem_Latn_ID',
		'deq'	=> 'deq_Latn_CF',
		'der'	=> 'der_Beng_IN',
		'des'	=> 'des_Latn_BR',
		'dev'	=> 'dev_Latn_PG',
		'dez'	=> 'dez_Latn_CD',
		'dga'	=> 'dga_Latn_GH',
		'dgb'	=> 'dgb_Latn_ML',
		'dgc'	=> 'dgc_Latn_PH',
		'dgd'	=> 'dgd_Latn_BF',
		'dge'	=> 'dge_Latn_PG',
		'dgg'	=> 'dgg_Latn_PG',
		'dgh'	=> 'dgh_Latn_NG',
		'dgi'	=> 'dgi_Latn_BF',
		'dgk'	=> 'dgk_Latn_CF',
		'dgl'	=> 'dgl_Arab_SD',
		'dgn'	=> 'dgn_Latn_AU',
		'dgs'	=> 'dgs_Latn_BF',
		'dgt'	=> 'dgt_Latn_AU',
		'dgw'	=> 'dgw_Latn_AU',
		'dgx'	=> 'dgx_Latn_PG',
		'dgz'	=> 'dgz_Latn_PG',
		'dhg'	=> 'dhg_Latn_AU',
		'dhi'	=> 'dhi_Deva_NP',
		'dhl'	=> 'dhl_Latn_AU',
		'dhm'	=> 'dhm_Latn_AO',
		'dhn'	=> 'dhn_Gujr_IN',
		'dho'	=> 'dho_Deva_IN',
		'dhr'	=> 'dhr_Latn_AU',
		'dhs'	=> 'dhs_Latn_TZ',
		'dhu'	=> 'dhu_Latn_AU',
		'dhv'	=> 'dhv_Latn_NC',
		'dhw'	=> 'dhw_Deva_NP',
		'dhx'	=> 'dhx_Latn_AU',
		'dia'	=> 'dia_Latn_PG',
		'dib'	=> 'dib_Latn_SS',
		'dic'	=> 'dic_Latn_CI',
		'did'	=> 'did_Latn_SS',
		'dif'	=> 'dif_Latn_AU',
		'dig'	=> 'dig_Latn_KE',
		'dih'	=> 'dih_Latn_MX',
		'dii'	=> 'dii_Latn_CM',
		'dij'	=> 'dij_Latn_ID',
		'dil'	=> 'dil_Latn_SD',
		'din'	=> 'din_Latn_SS',
		'dio'	=> 'dio_Latn_NG',
		'dip'	=> 'dip_Latn_SS',
		'dir'	=> 'dir_Latn_NG',
		'dis'	=> 'dis_Latn_IN',
		'diu'	=> 'diu_Latn_NA',
		'diw'	=> 'diw_Latn_SS',
		'dix'	=> 'dix_Latn_VU',
		'diy'	=> 'diy_Latn_ID',
		'diz'	=> 'diz_Latn_CD',
		'dja'	=> 'dja_Latn_AU',
		'djb'	=> 'djb_Latn_AU',
		'djc'	=> 'djc_Latn_TD',
		'djd'	=> 'djd_Latn_AU',
		'djf'	=> 'djf_Latn_AU',
		'dji'	=> 'dji_Latn_AU',
		'djj'	=> 'djj_Latn_AU',
		'djk'	=> 'djk_Latn_SR',
		'djm'	=> 'djm_Latn_ML',
		'djn'	=> 'djn_Latn_AU',
		'djo'	=> 'djo_Latn_ID',
		'djr'	=> 'djr_Latn_AU',
		'dju'	=> 'dju_Latn_PG',
		'djw'	=> 'djw_Latn_AU',
		'dka'	=> 'dka_Tibt_BT',
		'dkg'	=> 'dkg_Latn_NG',
		'dkk'	=> 'dkk_Latn_ID',
		'dkr'	=> 'dkr_Latn_MY',
		'dks'	=> 'dks_Latn_SS',
		'dkx'	=> 'dkx_Latn_CM',
		'dlg'	=> 'dlg_Cyrl_RU',
		'dlm'	=> 'dlm_Latn_HR',
		'dln'	=> 'dln_Latn_IN',
		'dma'	=> 'dma_Latn_GA',
		'dmb'	=> 'dmb_Latn_ML',
		'dmc'	=> 'dmc_Latn_PG',
		'dmd'	=> 'dmd_Latn_AU',
		'dme'	=> 'dme_Latn_CM',
		'dmg'	=> 'dmg_Latn_MY',
		'dmk'	=> 'dmk_Arab_PK',
		'dml'	=> 'dml_Arab_PK',
		'dmm'	=> 'dmm_Latn_CM',
		'dmo'	=> 'dmo_Latn_CM',
		'dmr'	=> 'dmr_Latn_ID',
		'dms'	=> 'dms_Latn_ID',
		'dmu'	=> 'dmu_Latn_ID',
		'dmv'	=> 'dmv_Latn_MY',
		'dmw'	=> 'dmw_Latn_AU',
		'dmx'	=> 'dmx_Latn_MZ',
		'dmy'	=> 'dmy_Latn_ID',
		'dna'	=> 'dna_Latn_ID',
		'dnd'	=> 'dnd_Latn_PG',
		'dne'	=> 'dne_Latn_TZ',
		'dng'	=> 'dng_Cyrl_KG',
		'dni'	=> 'dni_Latn_ID',
		'dnk'	=> 'dnk_Latn_ID',
		'dnn'	=> 'dnn_Latn_BF',
		'dno'	=> 'dno_Latn_CD',
		'dnr'	=> 'dnr_Latn_PG',
		'dnt'	=> 'dnt_Latn_ID',
		'dnu'	=> 'dnu_Mymr_MM',
		'dnv'	=> 'dnv_Mymr_MM',
		'dnw'	=> 'dnw_Latn_ID',
		'dny'	=> 'dny_Latn_BR',
		'doa'	=> 'doa_Latn_PG',
		'dob'	=> 'dob_Latn_PG',
		'doc'	=> 'doc_Latn_CN',
		'doe'	=> 'doe_Latn_TZ',
		'dof'	=> 'dof_Latn_PG',
		'doh'	=> 'doh_Latn_NG',
		'dok'	=> 'dok_Latn_ID',
		'dol'	=> 'dol_Latn_PG',
		'don'	=> 'don_Latn_PG',
		'doo'	=> 'doo_Latn_CD',
		'dop'	=> 'dop_Latn_BJ',
		'dor'	=> 'dor_Latn_SB',
		'dos'	=> 'dos_Latn_BF',
		'dot'	=> 'dot_Latn_NG',
		'dov'	=> 'dov_Latn_ZW',
		'dow'	=> 'dow_Latn_CM',
		'dox'	=> 'dox_Ethi_ET',
		'doy'	=> 'doy_Latn_GH',
		'dpp'	=> 'dpp_Latn_MY',
		'drc'	=> 'drc_Latn_PT',
		'dre'	=> 'dre_Tibt_NP',
		'drg'	=> 'drg_Latn_MY',
		'dri'	=> 'dri_Latn_NG',
		'drl'	=> 'drl_Latn_AU',
		'drn'	=> 'drn_Latn_ID',
		'dro'	=> 'dro_Latn_MY',
		'drq'	=> 'drq_Deva_NP',
		'drs'	=> 'drs_Ethi_ET',
		'drt'	=> 'drt_Latn_NL',
		'dru'	=> 'dru_Latn_TW',
		'dry'	=> 'dry_Deva_NP',
		'dsh'	=> 'dsh_Latn_KE',
		'dsi'	=> 'dsi_Latn_TD',
		'dsk'	=> 'dsk_Latn_NG',
		'dsn'	=> 'dsn_Latn_ID',
		'dso'	=> 'dso_Orya_IN',
		'dsq'	=> 'dsq_Latn_ML',
		'dta'	=> 'dta_Latn_CN',
		'dtb'	=> 'dtb_Latn_MY',
		'dtd'	=> 'dtd_Latn_CA',
		'dth'	=> 'dth_Latn_AU',
		'dti'	=> 'dti_Latn_ML',
		'dtk'	=> 'dtk_Latn_ML',
		'dto'	=> 'dto_Latn_ML',
		'dtr'	=> 'dtr_Latn_MY',
		'dts'	=> 'dts_Latn_ML',
		'dtt'	=> 'dtt_Latn_ML',
		'dtu'	=> 'dtu_Latn_ML',
		'dub'	=> 'dub_Gujr_IN',
		'duc'	=> 'duc_Latn_PG',
		'due'	=> 'due_Latn_PH',
		'duf'	=> 'duf_Latn_NC',
		'dug'	=> 'dug_Latn_KE',
		'duh'	=> 'duh_Deva_IN',
		'dui'	=> 'dui_Latn_PG',
		'duk'	=> 'duk_Latn_PG',
		'dul'	=> 'dul_Latn_PH',
		'dum'	=> 'dum_Latn_NL',
		'dun'	=> 'dun_Latn_ID',
		'duo'	=> 'duo_Latn_PH',
		'dup'	=> 'dup_Latn_ID',
		'duq'	=> 'duq_Latn_ID',
		'dur'	=> 'dur_Latn_CM',
		'dus'	=> 'dus_Deva_NP',
		'duu'	=> 'duu_Latn_CN',
		'duv'	=> 'duv_Latn_ID',
		'duw'	=> 'duw_Latn_ID',
		'dux'	=> 'dux_Latn_ML',
		'duy'	=> 'duy_Latn_PH',
		'duz'	=> 'duz_Latn_CM',
		'dva'	=> 'dva_Latn_PG',
		'dwa'	=> 'dwa_Latn_NG',
		'dwk'	=> 'dwk_Orya_IN',
		'dwr'	=> 'dwr_Latn_ET',
		'dws'	=> 'dws_Latn_001',
		'dwu'	=> 'dwu_Latn_AU',
		'dww'	=> 'dww_Latn_PG',
		'dwy'	=> 'dwy_Latn_AU',
		'dwz'	=> 'dwz_Deva_NP',
		'dya'	=> 'dya_Latn_BF',
		'dyb'	=> 'dyb_Latn_AU',
		'dyd'	=> 'dyd_Latn_AU',
		'dyg'	=> 'dyg_Latn_PH',
		'dyi'	=> 'dyi_Latn_CI',
		'dym'	=> 'dym_Latn_ML',
		'dyn'	=> 'dyn_Latn_AU',
		'dyr'	=> 'dyr_Latn_NG',
		'dyy'	=> 'dyy_Latn_AU',
		'dza'	=> 'dza_Latn_NG',
		'dzd'	=> 'dzd_Latn_NG',
		'dze'	=> 'dze_Latn_AU',
		'dzg'	=> 'dzg_Latn_TD',
		'dzl'	=> 'dzl_Tibt_BT',
		'dzn'	=> 'dzn_Latn_CD',
		'eaa'	=> 'eaa_Latn_AU',
		'ebc'	=> 'ebc_Latn_ID',
		'ebg'	=> 'ebg_Latn_NG',
		'ebk'	=> 'ebk_Latn_PH',
		'ebo'	=> 'ebo_Latn_CG',
		'ebr'	=> 'ebr_Latn_CI',
		'ecr'	=> 'ecr_Grek_GR',
		'efa'	=> 'efa_Latn_NG',
		'efe'	=> 'efe_Latn_CD',
		'ega'	=> 'ega_Latn_CI',
		'egm'	=> 'egm_Latn_TZ',
		'ego'	=> 'ego_Latn_NG',
		'ehu'	=> 'ehu_Latn_NG',
		'eip'	=> 'eip_Latn_ID',
		'eit'	=> 'eit_Latn_PG',
		'eiv'	=> 'eiv_Latn_PG',
		'eja'	=> 'eja_Latn_GW',
		'eka'	=> 'eka_Latn_NG',
		'eke'	=> 'eke_Latn_NG',
		'ekg'	=> 'ekg_Latn_ID',
		'eki'	=> 'eki_Latn_NG',
		'ekl'	=> 'ekl_Latn_BD',
		'ekm'	=> 'ekm_Latn_CM',
		'eko'	=> 'eko_Latn_MZ',
		'ekp'	=> 'ekp_Latn_NG',
		'ekr'	=> 'ekr_Latn_NG',
		'ele'	=> 'ele_Latn_PG',
		'elk'	=> 'elk_Latn_PG',
		'elm'	=> 'elm_Latn_NG',
		'elo'	=> 'elo_Latn_KE',
		'elu'	=> 'elu_Latn_PG',
		'ema'	=> 'ema_Latn_NG',
		'emb'	=> 'emb_Latn_ID',
		'eme'	=> 'eme_Latn_GF',
		'emg'	=> 'emg_Deva_NP',
		'emi'	=> 'emi_Latn_PG',
		'emm'	=> 'emm_Latn_MX',
		'emn'	=> 'emn_Latn_CM',
		'emp'	=> 'emp_Latn_PA',
		'ems'	=> 'ems_Latn_US',
		'emu'	=> 'emu_Deva_IN',
		'emw'	=> 'emw_Latn_ID',
		'emx'	=> 'emx_Latn_FR',
		'emz'	=> 'emz_Latn_CM',
		'ena'	=> 'ena_Latn_PG',
		'enb'	=> 'enb_Latn_KE',
		'enc'	=> 'enc_Latn_VN',
		'end'	=> 'end_Latn_ID',
		'enf'	=> 'enf_Cyrl_RU',
		'enh'	=> 'enh_Cyrl_RU',
		'enl'	=> 'enl_Latn_PY',
		'enm'	=> 'enm_Latn_GB',
		'enn'	=> 'enn_Latn_NG',
		'eno'	=> 'eno_Latn_ID',
		'enq'	=> 'enq_Latn_PG',
		'enr'	=> 'enr_Latn_ID',
		'env'	=> 'env_Latn_NG',
		'enw'	=> 'enw_Latn_NG',
		'enx'	=> 'enx_Latn_PY',
		'eot'	=> 'eot_Latn_CI',
		'epi'	=> 'epi_Latn_NG',
		'era'	=> 'era_Taml_IN',
		'erg'	=> 'erg_Latn_VU',
		'erh'	=> 'erh_Latn_NG',
		'eri'	=> 'eri_Latn_PG',
		'erk'	=> 'erk_Latn_VU',
		'err'	=> 'err_Latn_AU',
		'ers'	=> 'ers_Latn_CN',
		'ert'	=> 'ert_Latn_ID',
		'erw'	=> 'erw_Latn_ID',
		'ese'	=> 'ese_Latn_BO',
		'esh'	=> 'esh_Arab_IR',
		'esi'	=> 'esi_Latn_US',
		'esm'	=> 'esm_Latn_CI',
		'ess'	=> 'ess_Latn_US',
		'esy'	=> 'esy_Latn_PH',
		'etb'	=> 'etb_Latn_NG',
		'etn'	=> 'etn_Latn_VU',
		'eto'	=> 'eto_Latn_CM',
		'etr'	=> 'etr_Latn_PG',
		'ets'	=> 'ets_Latn_NG',
		'etu'	=> 'etu_Latn_NG',
		'etx'	=> 'etx_Latn_NG',
		'etz'	=> 'etz_Latn_ID',
		'eud'	=> 'eud_Latn_MX',
		'eve'	=> 'eve_Cyrl_RU',
		'evh'	=> 'evh_Latn_NG',
		'evn'	=> 'evn_Cyrl_RU',
		'eya'	=> 'eya_Latn_US',
		'eyo'	=> 'eyo_Latn_KE',
		'eza'	=> 'eza_Latn_NG',
		'eze'	=> 'eze_Latn_NG',
		'faa'	=> 'faa_Latn_PG',
		'fab'	=> 'fab_Latn_GQ',
		'fad'	=> 'fad_Latn_PG',
		'faf'	=> 'faf_Latn_SB',
		'fag'	=> 'fag_Latn_PG',
		'fah'	=> 'fah_Latn_NG',
		'fai'	=> 'fai_Latn_PG',
		'faj'	=> 'faj_Latn_PG',
		'fak'	=> 'fak_Latn_CM',
		'fal'	=> 'fal_Latn_CM',
		'fam'	=> 'fam_Latn_NG',
		'fap'	=> 'fap_Latn_SN',
		'far'	=> 'far_Latn_SB',
		'fau'	=> 'fau_Latn_ID',
		'fax'	=> 'fax_Latn_ES',
		'fay'	=> 'fay_Arab_IR',
		'faz'	=> 'faz_Arab_IR',
		'fer'	=> 'fer_Latn_SS',
		'ffi'	=> 'ffi_Latn_PG',
		'fgr'	=> 'fgr_Latn_TD',
		'fie'	=> 'fie_Latn_NG',
		'fif'	=> 'fif_Latn_SA',
		'fip'	=> 'fip_Latn_TZ',
		'fir'	=> 'fir_Latn_NG',
		'fiw'	=> 'fiw_Latn_PG',
		'fkk'	=> 'fkk_Latn_NG',
		'fkv'	=> 'fkv_Latn_NO',
		'fla'	=> 'fla_Latn_US',
		'flh'	=> 'flh_Latn_ID',
		'fli'	=> 'fli_Latn_NG',
		'fll'	=> 'fll_Latn_CM',
		'fln'	=> 'fln_Latn_AU',
		'flr'	=> 'flr_Latn_CD',
		'fly'	=> 'fly_Latn_ZA',
		'fmp'	=> 'fmp_Latn_CM',
		'fmu'	=> 'fmu_Deva_IN',
		'fnb'	=> 'fnb_Latn_VU',
		'fng'	=> 'fng_Latn_ZA',
		'fni'	=> 'fni_Latn_TD',
		'fod'	=> 'fod_Latn_BJ',
		'foi'	=> 'foi_Latn_PG',
		'fom'	=> 'fom_Latn_CD',
		'for'	=> 'for_Latn_PG',
		'fos'	=> 'fos_Latn_TW',
		'fpe'	=> 'fpe_Latn_GQ',
		'fqs'	=> 'fqs_Latn_PG',
		'frd'	=> 'frd_Latn_ID',
		'frk'	=> 'frk_Latn_DE',
		'frm'	=> 'frm_Latn_FR',
		'fro'	=> 'fro_Latn_FR',
		'frq'	=> 'frq_Latn_PG',
		'frt'	=> 'frt_Latn_VU',
		'fue'	=> 'fue_Latn_BJ',
		'fuh'	=> 'fuh_Latn_NE',
		'fui'	=> 'fui_Latn_TD',
		'fum'	=> 'fum_Latn_NG',
		'fun'	=> 'fun_Latn_BR',
		'fut'	=> 'fut_Latn_VU',
		'fuu'	=> 'fuu_Latn_CD',
		'fuy'	=> 'fuy_Latn_PG',
		'fwa'	=> 'fwa_Latn_NC',
		'fwe'	=> 'fwe_Latn_NA',
		'gab'	=> 'gab_Latn_TD',
		'gac'	=> 'gac_Latn_IN',
		'gad'	=> 'gad_Latn_PH',
		'gae'	=> 'gae_Latn_VE',
		'gaf'	=> 'gaf_Latn_PG',
		'gah'	=> 'gah_Latn_PG',
		'gai'	=> 'gai_Latn_PG',
		'gaj'	=> 'gaj_Latn_PG',
		'gak'	=> 'gak_Latn_ID',
		'gal'	=> 'gal_Latn_TL',
		'gam'	=> 'gam_Latn_PG',
		'gao'	=> 'gao_Latn_PG',
		'gap'	=> 'gap_Latn_PG',
		'gaq'	=> 'gaq_Orya_IN',
		'gar'	=> 'gar_Latn_PG',
		'gas'	=> 'gas_Gujr_IN',
		'gat'	=> 'gat_Latn_PG',
		'gau'	=> 'gau_Telu_IN',
		'gaw'	=> 'gaw_Latn_PG',
		'gax'	=> 'gax_Latn_ET',
		'gba'	=> 'gba_Latn_CF',
		'gbb'	=> 'gbb_Latn_AU',
		'gbd'	=> 'gbd_Latn_AU',
		'gbe'	=> 'gbe_Latn_PG',
		'gbf'	=> 'gbf_Latn_PG',
		'gbg'	=> 'gbg_Latn_CF',
		'gbh'	=> 'gbh_Latn_BJ',
		'gbi'	=> 'gbi_Latn_ID',
		'gbj'	=> 'gbj_Orya_IN',
		'gbk'	=> 'gbk_Deva_IN',
		'gbl'	=> 'gbl_Gujr_IN',
		'gbn'	=> 'gbn_Latn_SS',
		'gbp'	=> 'gbp_Latn_CF',
		'gbq'	=> 'gbq_Latn_CF',
		'gbr'	=> 'gbr_Latn_NG',
		'gbs'	=> 'gbs_Latn_BJ',
		'gbu'	=> 'gbu_Latn_AU',
		'gbv'	=> 'gbv_Latn_CF',
		'gbw'	=> 'gbw_Latn_AU',
		'gbx'	=> 'gbx_Latn_BJ',
		'gby'	=> 'gby_Latn_NG',
		'gcc'	=> 'gcc_Latn_PG',
		'gcd'	=> 'gcd_Latn_AU',
		'gcf'	=> 'gcf_Latn_GP',
		'gcl'	=> 'gcl_Latn_GD',
		'gcn'	=> 'gcn_Latn_PG',
		'gct'	=> 'gct_Latn_VE',
		'gdb'	=> 'gdb_Orya_IN',
		'gdc'	=> 'gdc_Latn_AU',
		'gdd'	=> 'gdd_Latn_PG',
		'gde'	=> 'gde_Latn_NG',
		'gdf'	=> 'gdf_Latn_NG',
		'gdg'	=> 'gdg_Latn_PH',
		'gdh'	=> 'gdh_Latn_AU',
		'gdi'	=> 'gdi_Latn_CF',
		'gdj'	=> 'gdj_Latn_AU',
		'gdk'	=> 'gdk_Latn_TD',
		'gdl'	=> 'gdl_Latn_ET',
		'gdm'	=> 'gdm_Latn_TD',
		'gdn'	=> 'gdn_Latn_PG',
		'gdo'	=> 'gdo_Cyrl_RU',
		'gdq'	=> 'gdq_Latn_YE',
		'gdr'	=> 'gdr_Latn_PG',
		'gdt'	=> 'gdt_Latn_AU',
		'gdu'	=> 'gdu_Latn_NG',
		'gdx'	=> 'gdx_Deva_IN',
		'gea'	=> 'gea_Latn_NG',
		'geb'	=> 'geb_Latn_PG',
		'gec'	=> 'gec_Latn_LR',
		'ged'	=> 'ged_Latn_NG',
		'gef'	=> 'gef_Latn_ID',
		'geg'	=> 'geg_Latn_NG',
		'geh'	=> 'geh_Latn_CA',
		'gei'	=> 'gei_Latn_ID',
		'gej'	=> 'gej_Latn_TG',
		'gek'	=> 'gek_Latn_NG',
		'gel'	=> 'gel_Latn_NG',
		'geq'	=> 'geq_Latn_CF',
		'ges'	=> 'ges_Latn_ID',
		'gev'	=> 'gev_Latn_GA',
		'gew'	=> 'gew_Latn_NG',
		'gex'	=> 'gex_Latn_SO',
		'gey'	=> 'gey_Latn_CD',
		'gfk'	=> 'gfk_Latn_PG',
		'gga'	=> 'gga_Latn_SB',
		'ggb'	=> 'ggb_Latn_LR',
		'ggd'	=> 'ggd_Latn_AU',
		'gge'	=> 'gge_Latn_AU',
		'ggg'	=> 'ggg_Arab_PK',
		'ggk'	=> 'ggk_Latn_AU',
		'ggl'	=> 'ggl_Latn_PG',
		'ggt'	=> 'ggt_Latn_PG',
		'ggu'	=> 'ggu_Latn_CI',
		'ggw'	=> 'ggw_Latn_PG',
		'gha'	=> 'gha_Arab_LY',
		'ghc'	=> 'ghc_Latn_GB',
		'ghe'	=> 'ghe_Deva_NP',
		'ghk'	=> 'ghk_Latn_MM',
		'ghn'	=> 'ghn_Latn_SB',
		'gho'	=> 'gho_Tfng_MA',
		'ghr'	=> 'ghr_Arab_PK',
		'ghs'	=> 'ghs_Latn_PG',
		'ght'	=> 'ght_Tibt_NP',
		'gia'	=> 'gia_Latn_AU',
		'gib'	=> 'gib_Latn_NG',
		'gic'	=> 'gic_Latn_ZA',
		'gid'	=> 'gid_Latn_CM',
		'gie'	=> 'gie_Latn_CI',
		'gig'	=> 'gig_Arab_PK',
		'gih'	=> 'gih_Latn_AU',
		'gim'	=> 'gim_Latn_PG',
		'gin'	=> 'gin_Cyrl_RU',
		'gip'	=> 'gip_Latn_PG',
		'giq'	=> 'giq_Latn_VN',
		'gir'	=> 'gir_Latn_VN',
		'gis'	=> 'gis_Latn_CM',
		'git'	=> 'git_Latn_CA',
		'gix'	=> 'gix_Latn_CD',
		'giy'	=> 'giy_Latn_AU',
		'giz'	=> 'giz_Latn_CM',
		'gjm'	=> 'gjm_Latn_AU',
		'gjn'	=> 'gjn_Latn_GH',
		'gjr'	=> 'gjr_Latn_AU',
		'gka'	=> 'gka_Latn_PG',
		'gkd'	=> 'gkd_Latn_PG',
		'gke'	=> 'gke_Latn_CM',
		'gkn'	=> 'gkn_Latn_NG',
		'gko'	=> 'gko_Latn_AU',
		'gkp'	=> 'gkp_Latn_GN',
		'gku'	=> 'gku_Latn_ZA',
		'glb'	=> 'glb_Latn_NG',
		'glc'	=> 'glc_Latn_TD',
		'gld'	=> 'gld_Cyrl_RU',
		'glh'	=> 'glh_Arab_AF',
		'glj'	=> 'glj_Latn_TD',
		'gll'	=> 'gll_Latn_AU',
		'glo'	=> 'glo_Latn_NG',
		'glr'	=> 'glr_Latn_LR',
		'glu'	=> 'glu_Latn_TD',
		'glw'	=> 'glw_Latn_NG',
		'gma'	=> 'gma_Latn_AU',
		'gmb'	=> 'gmb_Latn_SB',
		'gmd'	=> 'gmd_Latn_NG',
		'gmg'	=> 'gmg_Latn_PG',
		'gmh'	=> 'gmh_Latn_DE',
		'gml'	=> 'gml_Latf_DE',
		'gmm'	=> 'gmm_Latn_CM',
		'gmn'	=> 'gmn_Latn_CM',
		'gmr'	=> 'gmr_Latn_AU',
		'gmu'	=> 'gmu_Latn_PG',
		'gmv'	=> 'gmv_Ethi_ET',
		'gmx'	=> 'gmx_Latn_TZ',
		'gmz'	=> 'gmz_Latn_NG',
		'gna'	=> 'gna_Latn_BF',
		'gnb'	=> 'gnb_Latn_IN',
		'gnc'	=> 'gnc_Latn_ES',
		'gnd'	=> 'gnd_Latn_CM',
		'gne'	=> 'gne_Latn_NG',
		'gng'	=> 'gng_Latn_TG',
		'gnh'	=> 'gnh_Latn_NG',
		'gni'	=> 'gni_Latn_AU',
		'gnj'	=> 'gnj_Latn_CI',
		'gnk'	=> 'gnk_Latn_BW',
		'gnl'	=> 'gnl_Latn_AU',
		'gnm'	=> 'gnm_Latn_PG',
		'gnn'	=> 'gnn_Latn_AU',
		'gnq'	=> 'gnq_Latn_MY',
		'gnr'	=> 'gnr_Latn_AU',
		'gnt'	=> 'gnt_Latn_PG',
		'gnu'	=> 'gnu_Latn_PG',
		'gnw'	=> 'gnw_Latn_BO',
		'gnz'	=> 'gnz_Latn_CF',
		'goa'	=> 'goa_Latn_CI',
		'gob'	=> 'gob_Latn_CO',
		'goc'	=> 'goc_Latn_PG',
		'god'	=> 'god_Latn_CI',
		'goe'	=> 'goe_Tibt_BT',
		'gof'	=> 'gof_Ethi_ET',
		'gog'	=> 'gog_Latn_TZ',
		'goh'	=> 'goh_Latn_DE',
		'goi'	=> 'goi_Latn_PG',
		'goj'	=> 'goj_Deva_IN',
		'gok'	=> 'gok_Deva_IN',
		'gol'	=> 'gol_Latn_LR',
		'goo'	=> 'goo_Latn_FJ',
		'gop'	=> 'gop_Latn_ID',
		'goq'	=> 'goq_Latn_ID',
		'gou'	=> 'gou_Latn_CM',
		'gov'	=> 'gov_Latn_CI',
		'gow'	=> 'gow_Latn_TZ',
		'gox'	=> 'gox_Latn_CD',
		'goy'	=> 'goy_Latn_TD',
		'gpa'	=> 'gpa_Latn_NG',
		'gpe'	=> 'gpe_Latn_GH',
		'gpn'	=> 'gpn_Latn_PG',
		'gqa'	=> 'gqa_Latn_NG',
		'gqn'	=> 'gqn_Latn_BR',
		'gqr'	=> 'gqr_Latn_TD',
		'gra'	=> 'gra_Deva_IN',
		'grb'	=> 'grb_Latn_LR',
		'grd'	=> 'grd_Latn_NG',
		'grg'	=> 'grg_Latn_PG',
		'grh'	=> 'grh_Latn_NG',
		'gri'	=> 'gri_Latn_SB',
		'grj'	=> 'grj_Latn_LR',
		'grm'	=> 'grm_Latn_MY',
		'grq'	=> 'grq_Latn_PG',
		'grs'	=> 'grs_Latn_ID',
		'gru'	=> 'gru_Ethi_ET',
		'grv'	=> 'grv_Latn_LR',
		'grw'	=> 'grw_Latn_PG',
		'grx'	=> 'grx_Latn_PG',
		'gry'	=> 'gry_Latn_LR',
		'grz'	=> 'grz_Latn_PG',
		'gsl'	=> 'gsl_Latn_SN',
		'gsn'	=> 'gsn_Latn_PG',
		'gso'	=> 'gso_Latn_CF',
		'gsp'	=> 'gsp_Latn_PG',
		'gta'	=> 'gta_Latn_BR',
		'gtu'	=> 'gtu_Latn_AU',
		'gua'	=> 'gua_Latn_NG',
		'gud'	=> 'gud_Latn_CI',
		'gue'	=> 'gue_Latn_AU',
		'guf'	=> 'guf_Latn_AU',
		'guh'	=> 'guh_Latn_CO',
		'gui'	=> 'gui_Latn_BO',
		'guk'	=> 'guk_Latn_ET',
		'gul'	=> 'gul_Latn_US',
		'gum'	=> 'gum_Latn_CO',
		'gun'	=> 'gun_Latn_BR',
		'guo'	=> 'guo_Latn_CO',
		'gup'	=> 'gup_Latn_AU',
		'guq'	=> 'guq_Latn_PY',
		'gut'	=> 'gut_Latn_CR',
		'guu'	=> 'guu_Latn_VE',
		'guw'	=> 'guw_Latn_BJ',
		'gux'	=> 'gux_Latn_BF',
		'gva'	=> 'gva_Latn_PY',
		'gvc'	=> 'gvc_Latn_BR',
		'gve'	=> 'gve_Latn_PG',
		'gvf'	=> 'gvf_Latn_PG',
		'gvj'	=> 'gvj_Latn_BR',
		'gvl'	=> 'gvl_Latn_TD',
		'gvm'	=> 'gvm_Latn_NG',
		'gvn'	=> 'gvn_Latn_AU',
		'gvo'	=> 'gvo_Latn_BR',
		'gvp'	=> 'gvp_Latn_BR',
		'gvs'	=> 'gvs_Latn_PG',
		'gvy'	=> 'gvy_Latn_AU',
		'gwa'	=> 'gwa_Latn_CI',
		'gwb'	=> 'gwb_Latn_NG',
		'gwc'	=> 'gwc_Arab_PK',
		'gwd'	=> 'gwd_Latn_ET',
		'gwe'	=> 'gwe_Latn_TZ',
		'gwf'	=> 'gwf_Arab_PK',
		'gwg'	=> 'gwg_Latn_NG',
		'gwj'	=> 'gwj_Latn_BW',
		'gwm'	=> 'gwm_Latn_AU',
		'gwn'	=> 'gwn_Latn_NG',
		'gwr'	=> 'gwr_Latn_UG',
		'gwt'	=> 'gwt_Arab_AF',
		'gwu'	=> 'gwu_Latn_AU',
		'gww'	=> 'gww_Latn_AU',
		'gwx'	=> 'gwx_Latn_GH',
		'gxx'	=> 'gxx_Latn_CI',
		'gyb'	=> 'gyb_Latn_PG',
		'gyd'	=> 'gyd_Latn_AU',
		'gye'	=> 'gye_Latn_NG',
		'gyf'	=> 'gyf_Latn_AU',
		'gyg'	=> 'gyg_Latn_CF',
		'gyi'	=> 'gyi_Latn_CM',
		'gyl'	=> 'gyl_Latn_ET',
		'gym'	=> 'gym_Latn_PA',
		'gyn'	=> 'gyn_Latn_GY',
		'gyo'	=> 'gyo_Deva_NP',
		'gyr'	=> 'gyr_Latn_BO',
		'gyy'	=> 'gyy_Latn_AU',
		'gyz'	=> 'gyz_Latn_NG',
		'gza'	=> 'gza_Latn_SD',
		'gzi'	=> 'gzi_Arab_IR',
		'gzn'	=> 'gzn_Latn_ID',
		'haa'	=> 'haa_Latn_US',
		'hac'	=> 'hac_Arab_IR',
		'had'	=> 'had_Latn_ID',
		'hae'	=> 'hae_Latn_ET',
		'hag'	=> 'hag_Latn_GH',
		'hah'	=> 'hah_Latn_PG',
		'hai'	=> 'hai_Latn_CA',
		'haj'	=> 'haj_Latn_IN',
		'hal'	=> 'hal_Latn_VN',
		'ham'	=> 'ham_Latn_PG',
		'han'	=> 'han_Latn_TZ',
		'hao'	=> 'hao_Latn_PG',
		'hap'	=> 'hap_Latn_ID',
		'haq'	=> 'haq_Latn_TZ',
		'har'	=> 'har_Ethi_ET',
		'has'	=> 'has_Latn_CA',
		'hav'	=> 'hav_Latn_CD',
		'hax'	=> 'hax_Latn_CA',
		'hay'	=> 'hay_Latn_TZ',
		'hba'	=> 'hba_Latn_CD',
		'hbb'	=> 'hbb_Latn_NG',
		'hbn'	=> 'hbn_Latn_SD',
		'hbo'	=> 'hbo_Hebr_IL',
		'hbu'	=> 'hbu_Latn_TL',
		'hch'	=> 'hch_Latn_MX',
		'hdy'	=> 'hdy_Ethi_ET',
		'hed'	=> 'hed_Latn_TD',
		'heg'	=> 'heg_Latn_ID',
		'heh'	=> 'heh_Latn_TZ',
		'hei'	=> 'hei_Latn_CA',
		'hem'	=> 'hem_Latn_CD',
		'hgm'	=> 'hgm_Latn_NA',
		'hgw'	=> 'hgw_Latn_PG',
		'hhi'	=> 'hhi_Latn_PG',
		'hhr'	=> 'hhr_Latn_SN',
		'hhy'	=> 'hhy_Latn_PG',
		'hia'	=> 'hia_Latn_NG',
		'hib'	=> 'hib_Latn_PE',
		'hid'	=> 'hid_Latn_US',
		'hig'	=> 'hig_Latn_NG',
		'hih'	=> 'hih_Latn_PG',
		'hii'	=> 'hii_Takr_IN',
		'hij'	=> 'hij_Latn_CM',
		'hik'	=> 'hik_Latn_ID',
		'hio'	=> 'hio_Latn_BW',
		'hir'	=> 'hir_Latn_BR',
		'hit'	=> 'hit_Xsux_TR',
		'hiw'	=> 'hiw_Latn_VU',
		'hix'	=> 'hix_Latn_BR',
		'hji'	=> 'hji_Latn_ID',
		'hka'	=> 'hka_Latn_TZ',
		'hke'	=> 'hke_Latn_CD',
		'hkh'	=> 'hkh_Arab_IN',
		'hkk'	=> 'hkk_Latn_PG',
		'hla'	=> 'hla_Latn_PG',
		'hlb'	=> 'hlb_Deva_IN',
		'hld'	=> 'hld_Latn_VN',
		'hlt'	=> 'hlt_Latn_MM',
		'hma'	=> 'hma_Latn_CN',
		'hmb'	=> 'hmb_Latn_ML',
		'hmf'	=> 'hmf_Latn_VN',
		'hmj'	=> 'hmj_Bopo_CN',
		'hmm'	=> 'hmm_Latn_CN',
		'hmn'	=> 'hmn_Latn_CN',
		'hmp'	=> 'hmp_Latn_CN',
		'hmq'	=> 'hmq_Bopo_CN',
		'hmr'	=> 'hmr_Latn_IN',
		'hms'	=> 'hms_Latn_CN',
		'hmt'	=> 'hmt_Latn_PG',
		'hmu'	=> 'hmu_Latn_ID',
		'hmv'	=> 'hmv_Latn_VN',
		'hmw'	=> 'hmw_Latn_CN',
		'hmy'	=> 'hmy_Latn_CN',
		'hmz'	=> 'hmz_Latn_CN',
		'hna'	=> 'hna_Latn_CM',
		'hng'	=> 'hng_Latn_AO',
		'hnh'	=> 'hnh_Latn_BW',
		'hni'	=> 'hni_Latn_CN',
		'hns'	=> 'hns_Latn_SR',
		'hoa'	=> 'hoa_Latn_SB',
		'hob'	=> 'hob_Latn_PG',
		'hod'	=> 'hod_Latn_NG',
		'hoe'	=> 'hoe_Latn_NG',
		'hoh'	=> 'hoh_Arab_OM',
		'hoi'	=> 'hoi_Latn_US',
		'hol'	=> 'hol_Latn_AO',
		'hom'	=> 'hom_Latn_SS',
		'hoo'	=> 'hoo_Latn_CD',
		'hop'	=> 'hop_Latn_US',
		'hor'	=> 'hor_Latn_TD',
		'hot'	=> 'hot_Latn_PG',
		'hov'	=> 'hov_Latn_ID',
		'how'	=> 'how_Hani_CN',
		'hoy'	=> 'hoy_Deva_IN',
		'hpo'	=> 'hpo_Mymr_MM',
		'hra'	=> 'hra_Latn_IN',
		'hrc'	=> 'hrc_Latn_PG',
		'hre'	=> 'hre_Latn_VN',
		'hrk'	=> 'hrk_Latn_ID',
		'hrm'	=> 'hrm_Latn_CN',
		'hro'	=> 'hro_Latn_VN',
		'hrp'	=> 'hrp_Latn_AU',
		'hrt'	=> 'hrt_Syrc_TR',
		'hru'	=> 'hru_Latn_IN',
		'hrw'	=> 'hrw_Latn_PG',
		'hrx'	=> 'hrx_Latn_BR',
		'hrz'	=> 'hrz_Arab_IR',
		'hss'	=> 'hss_Arab_OM',
		'hti'	=> 'hti_Latn_ID',
		'hto'	=> 'hto_Latn_CO',
		'hts'	=> 'hts_Latn_TZ',
		'htu'	=> 'htu_Latn_ID',
		'htx'	=> 'htx_Xsux_TR',
		'hub'	=> 'hub_Latn_PE',
		'huc'	=> 'huc_Latn_BW',
		'hud'	=> 'hud_Latn_ID',
		'hue'	=> 'hue_Latn_MX',
		'huf'	=> 'huf_Latn_PG',
		'hug'	=> 'hug_Latn_PE',
		'huh'	=> 'huh_Latn_CL',
		'hui'	=> 'hui_Latn_PG',
		'huk'	=> 'huk_Latn_ID',
		'hul'	=> 'hul_Latn_PG',
		'hum'	=> 'hum_Latn_CD',
		'hup'	=> 'hup_Latn_US',
		'hus'	=> 'hus_Latn_MX',
		'hut'	=> 'hut_Deva_NP',
		'huu'	=> 'huu_Latn_PE',
		'huv'	=> 'huv_Latn_MX',
		'huw'	=> 'huw_Latn_ID',
		'hux'	=> 'hux_Latn_PE',
		'huy'	=> 'huy_Hebr_IL',
		'huz'	=> 'huz_Cyrl_RU',
		'hvc'	=> 'hvc_Latn_HT',
		'hve'	=> 'hve_Latn_MX',
		'hvk'	=> 'hvk_Latn_NC',
		'hvn'	=> 'hvn_Latn_ID',
		'hvv'	=> 'hvv_Latn_MX',
		'hwa'	=> 'hwa_Latn_CI',
		'hwc'	=> 'hwc_Latn_US',
		'hwo'	=> 'hwo_Latn_NG',
		'hya'	=> 'hya_Latn_CM',
		'hyw'	=> 'hyw_Armn_AM',
		'iai'	=> 'iai_Latn_NC',
		'ian'	=> 'ian_Latn_PG',
		'iar'	=> 'iar_Latn_PG',
		'ibd'	=> 'ibd_Latn_AU',
		'ibe'	=> 'ibe_Latn_NG',
		'ibg'	=> 'ibg_Latn_PH',
		'ibh'	=> 'ibh_Latn_VN',
		'ibl'	=> 'ibl_Latn_PH',
		'ibm'	=> 'ibm_Latn_NG',
		'ibn'	=> 'ibn_Latn_NG',
		'ibr'	=> 'ibr_Latn_NG',
		'ibu'	=> 'ibu_Latn_ID',
		'iby'	=> 'iby_Latn_NG',
		'ica'	=> 'ica_Latn_BJ',
		'ich'	=> 'ich_Latn_NG',
		'icr'	=> 'icr_Latn_CO',
		'ida'	=> 'ida_Latn_KE',
		'idb'	=> 'idb_Latn_IN',
		'idc'	=> 'idc_Latn_NG',
		'idd'	=> 'idd_Latn_BJ',
		'ide'	=> 'ide_Latn_NG',
		'idi'	=> 'idi_Latn_PG',
		'idr'	=> 'idr_Latn_SS',
		'ids'	=> 'ids_Latn_NG',
		'idt'	=> 'idt_Latn_TL',
		'idu'	=> 'idu_Latn_NG',
		'ifa'	=> 'ifa_Latn_PH',
		'ifb'	=> 'ifb_Latn_PH',
		'iff'	=> 'iff_Latn_VU',
		'ifk'	=> 'ifk_Latn_PH',
		'ifm'	=> 'ifm_Latn_CG',
		'ifu'	=> 'ifu_Latn_PH',
		'ify'	=> 'ify_Latn_PH',
		'igb'	=> 'igb_Latn_NG',
		'ige'	=> 'ige_Latn_NG',
		'igg'	=> 'igg_Latn_PG',
		'igl'	=> 'igl_Latn_NG',
		'igm'	=> 'igm_Latn_PG',
		'ign'	=> 'ign_Latn_BO',
		'igo'	=> 'igo_Latn_PG',
		'igs'	=> 'igs_Latn_001',
		'igw'	=> 'igw_Latn_NG',
		'ihb'	=> 'ihb_Latn_ID',
		'ihi'	=> 'ihi_Latn_NG',
		'ihp'	=> 'ihp_Latn_ID',
		'ihw'	=> 'ihw_Latn_AU',
		'iin'	=> 'iin_Latn_AU',
		'ijc'	=> 'ijc_Latn_NG',
		'ije'	=> 'ije_Latn_NG',
		'ijj'	=> 'ijj_Latn_BJ',
		'ijn'	=> 'ijn_Latn_NG',
		'ijs'	=> 'ijs_Latn_NG',
		'ikh'	=> 'ikh_Latn_NG',
		'iki'	=> 'iki_Latn_NG',
		'ikk'	=> 'ikk_Latn_NG',
		'ikl'	=> 'ikl_Latn_NG',
		'iko'	=> 'iko_Latn_NG',
		'ikp'	=> 'ikp_Latn_NG',
		'ikr'	=> 'ikr_Latn_AU',
		'ikt'	=> 'ikt_Latn_CA',
		'ikv'	=> 'ikv_Latn_NG',
		'ikw'	=> 'ikw_Latn_NG',
		'ikx'	=> 'ikx_Latn_UG',
		'ikz'	=> 'ikz_Latn_TZ',
		'ila'	=> 'ila_Latn_ID',
		'ilb'	=> 'ilb_Latn_ZM',
		'ilg'	=> 'ilg_Latn_AU',
		'ili'	=> 'ili_Latn_CN',
		'ilk'	=> 'ilk_Latn_PH',
		'ilm'	=> 'ilm_Latn_MY',
		'ilp'	=> 'ilp_Latn_PH',
		'ilu'	=> 'ilu_Latn_ID',
		'ilv'	=> 'ilv_Latn_NG',
		'imi'	=> 'imi_Latn_PG',
		'iml'	=> 'iml_Latn_US',
		'imn'	=> 'imn_Latn_PG',
		'imo'	=> 'imo_Latn_PG',
		'imr'	=> 'imr_Latn_ID',
		'ims'	=> 'ims_Latn_IT',
		'imt'	=> 'imt_Latn_SS',
		'imy'	=> 'imy_Lyci_TR',
		'inb'	=> 'inb_Latn_CO',
		'ing'	=> 'ing_Latn_US',
		'inj'	=> 'inj_Latn_CO',
		'inn'	=> 'inn_Latn_PH',
		'ino'	=> 'ino_Latn_PG',
		'inp'	=> 'inp_Latn_PE',
		'int'	=> 'int_Mymr_MM',
		'ior'	=> 'ior_Ethi_ET',
		'iou'	=> 'iou_Latn_PG',
		'iow'	=> 'iow_Latn_US',
		'ipi'	=> 'ipi_Latn_PG',
		'ipo'	=> 'ipo_Latn_PG',
		'iqu'	=> 'iqu_Latn_PE',
		'iqw'	=> 'iqw_Latn_NG',
		'ire'	=> 'ire_Latn_ID',
		'irh'	=> 'irh_Latn_ID',
		'iri'	=> 'iri_Latn_NG',
		'irk'	=> 'irk_Latn_TZ',
		'irn'	=> 'irn_Latn_BR',
		'iru'	=> 'iru_Taml_IN',
		'irx'	=> 'irx_Latn_ID',
		'iry'	=> 'iry_Latn_PH',
		'isa'	=> 'isa_Latn_PG',
		'isc'	=> 'isc_Latn_PE',
		'isd'	=> 'isd_Latn_PH',
		'ish'	=> 'ish_Latn_NG',
		'isi'	=> 'isi_Latn_NG',
		'isk'	=> 'isk_Arab_AF',
		'ism'	=> 'ism_Latn_ID',
		'isn'	=> 'isn_Latn_TZ',
		'iso'	=> 'iso_Latn_NG',
		'ist'	=> 'ist_Latn_HR',
		'isu'	=> 'isu_Latn_CM',
		'itb'	=> 'itb_Latn_PH',
		'itd'	=> 'itd_Latn_ID',
		'ite'	=> 'ite_Latn_BO',
		'iti'	=> 'iti_Latn_PH',
		'itk'	=> 'itk_Hebr_IT',
		'itl'	=> 'itl_Cyrl_RU',
		'itm'	=> 'itm_Latn_NG',
		'ito'	=> 'ito_Latn_BO',
		'itr'	=> 'itr_Latn_PG',
		'its'	=> 'its_Latn_NG',
		'itt'	=> 'itt_Latn_PH',
		'itv'	=> 'itv_Latn_PH',
		'itw'	=> 'itw_Latn_NG',
		'itx'	=> 'itx_Latn_ID',
		'ity'	=> 'ity_Latn_PH',
		'itz'	=> 'itz_Latn_GT',
		'ium'	=> 'ium_Latn_CN',
		'ivb'	=> 'ivb_Latn_PH',
		'ivv'	=> 'ivv_Latn_PH',
		'iwk'	=> 'iwk_Latn_PH',
		'iwm'	=> 'iwm_Latn_PG',
		'iwo'	=> 'iwo_Latn_ID',
		'iws'	=> 'iws_Latn_PG',
		'ixc'	=> 'ixc_Latn_MX',
		'ixl'	=> 'ixl_Latn_GT',
		'iya'	=> 'iya_Latn_NG',
		'iyo'	=> 'iyo_Latn_CM',
		'iyx'	=> 'iyx_Latn_CG',
		'izm'	=> 'izm_Latn_NG',
		'izr'	=> 'izr_Latn_NG',
		'izz'	=> 'izz_Latn_NG',
		'jaa'	=> 'jaa_Latn_BR',
		'jab'	=> 'jab_Latn_NG',
		'jac'	=> 'jac_Latn_GT',
		'jad'	=> 'jad_Arab_GN',
		'jae'	=> 'jae_Latn_PG',
		'jaf'	=> 'jaf_Latn_NG',
		'jah'	=> 'jah_Latn_MY',
		'jaj'	=> 'jaj_Latn_SB',
		'jak'	=> 'jak_Latn_MY',
		'jal'	=> 'jal_Latn_ID',
		'jan'	=> 'jan_Latn_AU',
		'jao'	=> 'jao_Latn_AU',
		'jaq'	=> 'jaq_Latn_ID',
		'jas'	=> 'jas_Latn_NC',
		'jat'	=> 'jat_Arab_AF',
		'jau'	=> 'jau_Latn_ID',
		'jax'	=> 'jax_Latn_ID',
		'jay'	=> 'jay_Latn_AU',
		'jaz'	=> 'jaz_Latn_NC',
		'jbe'	=> 'jbe_Hebr_IL',
		'jbi'	=> 'jbi_Latn_AU',
		'jbj'	=> 'jbj_Latn_ID',
		'jbk'	=> 'jbk_Latn_PG',
		'jbm'	=> 'jbm_Latn_NG',
		'jbn'	=> 'jbn_Arab_LY',
		'jbr'	=> 'jbr_Latn_ID',
		'jbt'	=> 'jbt_Latn_BR',
		'jbu'	=> 'jbu_Latn_CM',
		'jbw'	=> 'jbw_Latn_AU',
		'jct'	=> 'jct_Cyrl_UA',
		'jda'	=> 'jda_Tibt_IN',
		'jdg'	=> 'jdg_Arab_PK',
		'jdt'	=> 'jdt_Cyrl_RU',
		'jeb'	=> 'jeb_Latn_PE',
		'jee'	=> 'jee_Deva_NP',
		'jeh'	=> 'jeh_Latn_VN',
		'jei'	=> 'jei_Latn_ID',
		'jek'	=> 'jek_Latn_CI',
		'jel'	=> 'jel_Latn_ID',
		'jen'	=> 'jen_Latn_NG',
		'jer'	=> 'jer_Latn_NG',
		'jet'	=> 'jet_Latn_PG',
		'jeu'	=> 'jeu_Latn_TD',
		'jgb'	=> 'jgb_Latn_CD',
		'jge'	=> 'jge_Geor_GE',
		'jgk'	=> 'jgk_Latn_NG',
		'jhi'	=> 'jhi_Latn_MY',
		'jia'	=> 'jia_Latn_CM',
		'jib'	=> 'jib_Latn_NG',
		'jic'	=> 'jic_Latn_HN',
		'jid'	=> 'jid_Latn_NG',
		'jie'	=> 'jie_Latn_NG',
		'jig'	=> 'jig_Latn_AU',
		'jil'	=> 'jil_Latn_PG',
		'jim'	=> 'jim_Latn_CM',
		'jit'	=> 'jit_Latn_TZ',
		'jiu'	=> 'jiu_Latn_CN',
		'jiv'	=> 'jiv_Latn_EC',
		'jiy'	=> 'jiy_Latn_CN',
		'jje'	=> 'jje_Hang_KR',
		'jjr'	=> 'jjr_Latn_NG',
		'jka'	=> 'jka_Latn_ID',
		'jkm'	=> 'jkm_Mymr_MM',
		'jko'	=> 'jko_Latn_PG',
		'jku'	=> 'jku_Latn_NG',
		'jle'	=> 'jle_Latn_SD',
		'jma'	=> 'jma_Latn_PG',
		'jmb'	=> 'jmb_Latn_NG',
		'jmd'	=> 'jmd_Latn_ID',
		'jmi'	=> 'jmi_Latn_NG',
		'jmn'	=> 'jmn_Latn_MM',
		'jmr'	=> 'jmr_Latn_GH',
		'jms'	=> 'jms_Latn_NG',
		'jmw'	=> 'jmw_Latn_PG',
		'jmx'	=> 'jmx_Latn_MX',
		'jna'	=> 'jna_Takr_IN',
		'jnd'	=> 'jnd_Arab_PK',
		'jng'	=> 'jng_Latn_AU',
		'jni'	=> 'jni_Latn_NG',
		'jnj'	=> 'jnj_Latn_ET',
		'jnl'	=> 'jnl_Deva_IN',
		'jns'	=> 'jns_Deva_IN',
		'job'	=> 'job_Latn_CD',
		'jod'	=> 'jod_Latn_CI',
		'jog'	=> 'jog_Arab_PK',
		'jor'	=> 'jor_Latn_BO',
		'jow'	=> 'jow_Latn_ML',
		'jpa'	=> 'jpa_Hebr_PS',
		'jpr'	=> 'jpr_Hebr_IL',
		'jqr'	=> 'jqr_Latn_PE',
		'jra'	=> 'jra_Latn_VN',
		'jrb'	=> 'jrb_Hebr_IL',
		'jrr'	=> 'jrr_Latn_NG',
		'jrt'	=> 'jrt_Latn_NG',
		'jru'	=> 'jru_Latn_VE',
		'jua'	=> 'jua_Latn_BR',
		'jub'	=> 'jub_Latn_NG',
		'jud'	=> 'jud_Latn_CI',
		'juh'	=> 'juh_Latn_NG',
		'jui'	=> 'jui_Latn_AU',
		'juk'	=> 'juk_Latn_NG',
		'jul'	=> 'jul_Deva_NP',
		'jum'	=> 'jum_Latn_SD',
		'jun'	=> 'jun_Orya_IN',
		'juo'	=> 'juo_Latn_NG',
		'jup'	=> 'jup_Latn_BR',
		'jur'	=> 'jur_Latn_BR',
		'juu'	=> 'juu_Latn_NG',
		'juw'	=> 'juw_Latn_NG',
		'juy'	=> 'juy_Orya_IN',
		'jvd'	=> 'jvd_Latn_ID',
		'jvn'	=> 'jvn_Latn_SR',
		'jwi'	=> 'jwi_Latn_GH',
		'jya'	=> 'jya_Tibt_CN',
		'jye'	=> 'jye_Hebr_IL',
		'jyy'	=> 'jyy_Latn_TD',
		'kad'	=> 'kad_Latn_NG',
		'kag'	=> 'kag_Latn_MY',
		'kah'	=> 'kah_Latn_CF',
		'kai'	=> 'kai_Latn_NG',
		'kak'	=> 'kak_Latn_PH',
		'kap'	=> 'kap_Cyrl_RU',
		'kaq'	=> 'kaq_Latn_PE',
		'kav'	=> 'kav_Latn_BR',
		'kax'	=> 'kax_Latn_ID',
		'kay'	=> 'kay_Latn_BR',
		'kba'	=> 'kba_Latn_AU',
		'kbb'	=> 'kbb_Latn_BR',
		'kbc'	=> 'kbc_Latn_BR',
		'kbe'	=> 'kbe_Latn_AU',
		'kbg'	=> 'kbg_Tibt_IN',
		'kbh'	=> 'kbh_Latn_CO',
		'kbi'	=> 'kbi_Latn_ID',
		'kbj'	=> 'kbj_Latn_CD',
		'kbk'	=> 'kbk_Latn_PG',
		'kbl'	=> 'kbl_Latn_TD',
		'kbm'	=> 'kbm_Latn_PG',
		'kbn'	=> 'kbn_Latn_CF',
		'kbo'	=> 'kbo_Latn_SS',
		'kbp'	=> 'kbp_Latn_TG',
		'kbq'	=> 'kbq_Latn_PG',
		'kbr'	=> 'kbr_Latn_ET',
		'kbs'	=> 'kbs_Latn_GA',
		'kbt'	=> 'kbt_Latn_PG',
		'kbu'	=> 'kbu_Arab_PK',
		'kbv'	=> 'kbv_Latn_ID',
		'kbw'	=> 'kbw_Latn_PG',
		'kbx'	=> 'kbx_Latn_PG',
		'kbz'	=> 'kbz_Latn_NG',
		'kca'	=> 'kca_Cyrl_RU',
		'kcb'	=> 'kcb_Latn_PG',
		'kcc'	=> 'kcc_Latn_NG',
		'kcd'	=> 'kcd_Latn_ID',
		'kce'	=> 'kce_Latn_NG',
		'kcf'	=> 'kcf_Latn_NG',
		'kch'	=> 'kch_Latn_NG',
		'kci'	=> 'kci_Latn_NG',
		'kcj'	=> 'kcj_Latn_GW',
		'kcl'	=> 'kcl_Latn_PG',
		'kcm'	=> 'kcm_Latn_CF',
		'kcn'	=> 'kcn_Latn_UG',
		'kco'	=> 'kco_Latn_PG',
		'kcp'	=> 'kcp_Latn_SD',
		'kcq'	=> 'kcq_Latn_NG',
		'kcs'	=> 'kcs_Latn_NG',
		'kct'	=> 'kct_Latn_PG',
		'kcu'	=> 'kcu_Latn_TZ',
		'kcv'	=> 'kcv_Latn_CD',
		'kcw'	=> 'kcw_Latn_CD',
		'kcy'	=> 'kcy_Arab_DZ',
		'kcz'	=> 'kcz_Latn_TZ',
		'kda'	=> 'kda_Latn_AU',
		'kdc'	=> 'kdc_Latn_TZ',
		'kdd'	=> 'kdd_Latn_AU',
		'kdf'	=> 'kdf_Latn_PG',
		'kdg'	=> 'kdg_Latn_CD',
		'kdi'	=> 'kdi_Latn_UG',
		'kdj'	=> 'kdj_Latn_UG',
		'kdk'	=> 'kdk_Latn_NC',
		'kdl'	=> 'kdl_Latn_NG',
		'kdm'	=> 'kdm_Latn_NG',
		'kdn'	=> 'kdn_Latn_ZW',
		'kdp'	=> 'kdp_Latn_NG',
		'kdq'	=> 'kdq_Beng_IN',
		'kdr'	=> 'kdr_Latn_LT',
		'kdw'	=> 'kdw_Latn_ID',
		'kdx'	=> 'kdx_Latn_NG',
		'kdy'	=> 'kdy_Latn_ID',
		'kdz'	=> 'kdz_Latn_CM',
		'keb'	=> 'keb_Latn_GA',
		'kec'	=> 'kec_Latn_SD',
		'ked'	=> 'ked_Latn_TZ',
		'kee'	=> 'kee_Latn_US',
		'kef'	=> 'kef_Latn_TG',
		'keg'	=> 'keg_Latn_SD',
		'keh'	=> 'keh_Latn_PG',
		'kei'	=> 'kei_Latn_ID',
		'kek'	=> 'kek_Latn_GT',
		'kel'	=> 'kel_Latn_CD',
		'kem'	=> 'kem_Latn_TL',
		'keo'	=> 'keo_Latn_UG',
		'ker'	=> 'ker_Latn_TD',
		'kes'	=> 'kes_Latn_NG',
		'ket'	=> 'ket_Cyrl_RU',
		'keu'	=> 'keu_Latn_TG',
		'kev'	=> 'kev_Mlym_IN',
		'kew'	=> 'kew_Latn_PG',
		'kex'	=> 'kex_Deva_IN',
		'key'	=> 'key_Telu_IN',
		'kez'	=> 'kez_Latn_NG',
		'kfa'	=> 'kfa_Knda_IN',
		'kfb'	=> 'kfb_Deva_IN',
		'kfc'	=> 'kfc_Telu_IN',
		'kfd'	=> 'kfd_Knda_IN',
		'kfe'	=> 'kfe_Taml_IN',
		'kff'	=> 'kff_Latn_IN',
		'kfg'	=> 'kfg_Knda_IN',
		'kfh'	=> 'kfh_Mlym_IN',
		'kfi'	=> 'kfi_Taml_IN',
		'kfk'	=> 'kfk_Deva_IN',
		'kfl'	=> 'kfl_Latn_CM',
		'kfm'	=> 'kfm_Arab_IR',
		'kfn'	=> 'kfn_Latn_CM',
		'kfp'	=> 'kfp_Deva_IN',
		'kfq'	=> 'kfq_Deva_IN',
		'kfs'	=> 'kfs_Deva_IN',
		'kfu'	=> 'kfu_Deva_IN',
		'kfv'	=> 'kfv_Latn_IN',
		'kfw'	=> 'kfw_Latn_IN',
		'kfx'	=> 'kfx_Deva_IN',
		'kfz'	=> 'kfz_Latn_BF',
		'kga'	=> 'kga_Latn_CI',
		'kgb'	=> 'kgb_Latn_ID',
		'kgf'	=> 'kgf_Latn_PG',
		'kgj'	=> 'kgj_Deva_NP',
		'kgk'	=> 'kgk_Latn_BR',
		'kgl'	=> 'kgl_Latn_AU',
		'kgo'	=> 'kgo_Latn_SD',
		'kgq'	=> 'kgq_Latn_ID',
		'kgr'	=> 'kgr_Latn_ID',
		'kgs'	=> 'kgs_Latn_AU',
		'kgt'	=> 'kgt_Latn_NG',
		'kgu'	=> 'kgu_Latn_PG',
		'kgv'	=> 'kgv_Latn_ID',
		'kgw'	=> 'kgw_Latn_ID',
		'kgx'	=> 'kgx_Latn_ID',
		'kgy'	=> 'kgy_Deva_NP',
		'khc'	=> 'khc_Latn_ID',
		'khd'	=> 'khd_Latn_ID',
		'khe'	=> 'khe_Latn_ID',
		'khf'	=> 'khf_Thai_LA',
		'khg'	=> 'khg_Tibt_CN',
		'khh'	=> 'khh_Latn_ID',
		'khj'	=> 'khj_Latn_NG',
		'khl'	=> 'khl_Latn_PG',
		'kho'	=> 'kho_Brah_IR',
		'khp'	=> 'khp_Latn_ID',
		'khr'	=> 'khr_Latn_IN',
		'khs'	=> 'khs_Latn_PG',
		'khu'	=> 'khu_Latn_AO',
		'khv'	=> 'khv_Cyrl_RU',
		'khx'	=> 'khx_Latn_CD',
		'khy'	=> 'khy_Latn_CD',
		'khz'	=> 'khz_Latn_PG',
		'kia'	=> 'kia_Latn_TD',
		'kib'	=> 'kib_Latn_SD',
		'kic'	=> 'kic_Latn_US',
		'kid'	=> 'kid_Latn_CM',
		'kie'	=> 'kie_Latn_TD',
		'kif'	=> 'kif_Deva_NP',
		'kig'	=> 'kig_Latn_ID',
		'kih'	=> 'kih_Latn_PG',
		'kij'	=> 'kij_Latn_PG',
		'kil'	=> 'kil_Latn_NG',
		'kim'	=> 'kim_Cyrl_RU',
		'kio'	=> 'kio_Latn_US',
		'kip'	=> 'kip_Deva_NP',
		'kiq'	=> 'kiq_Latn_ID',
		'kis'	=> 'kis_Latn_PG',
		'kit'	=> 'kit_Latn_PG',
		'kiv'	=> 'kiv_Latn_TZ',
		'kiw'	=> 'kiw_Latn_PG',
		'kix'	=> 'kix_Latn_IN',
		'kiy'	=> 'kiy_Latn_ID',
		'kiz'	=> 'kiz_Latn_TZ',
		'kja'	=> 'kja_Latn_ID',
		'kjb'	=> 'kjb_Latn_GT',
		'kjc'	=> 'kjc_Latn_ID',
		'kjd'	=> 'kjd_Latn_PG',
		'kje'	=> 'kje_Latn_ID',
		'kjh'	=> 'kjh_Cyrl_RU',
		'kji'	=> 'kji_Latn_SB',
		'kjj'	=> 'kjj_Latn_AZ',
		'kjk'	=> 'kjk_Latn_ID',
		'kjl'	=> 'kjl_Deva_NP',
		'kjm'	=> 'kjm_Latn_VN',
		'kjn'	=> 'kjn_Latn_AU',
		'kjo'	=> 'kjo_Deva_IN',
		'kjp'	=> 'kjp_Mymr_MM',
		'kjq'	=> 'kjq_Latn_US',
		'kjr'	=> 'kjr_Latn_ID',
		'kjs'	=> 'kjs_Latn_PG',
		'kjt'	=> 'kjt_Thai_TH',
		'kju'	=> 'kju_Latn_US',
		'kjx'	=> 'kjx_Latn_PG',
		'kjy'	=> 'kjy_Latn_PG',
		'kjz'	=> 'kjz_Tibt_BT',
		'kka'	=> 'kka_Latn_NG',
		'kkb'	=> 'kkb_Latn_ID',
		'kkc'	=> 'kkc_Latn_PG',
		'kkd'	=> 'kkd_Latn_NG',
		'kke'	=> 'kke_Latn_GN',
		'kkf'	=> 'kkf_Tibt_IN',
		'kkg'	=> 'kkg_Latn_PH',
		'kkh'	=> 'kkh_Lana_MM',
		'kki'	=> 'kki_Latn_TZ',
		'kkk'	=> 'kkk_Latn_SB',
		'kkl'	=> 'kkl_Latn_ID',
		'kkm'	=> 'kkm_Latn_NG',
		'kko'	=> 'kko_Latn_SD',
		'kkp'	=> 'kkp_Latn_AU',
		'kkq'	=> 'kkq_Latn_CD',
		'kkr'	=> 'kkr_Latn_NG',
		'kks'	=> 'kks_Latn_NG',
		'kkt'	=> 'kkt_Deva_NP',
		'kku'	=> 'kku_Latn_NG',
		'kkv'	=> 'kkv_Latn_ID',
		'kkw'	=> 'kkw_Latn_CG',
		'kkx'	=> 'kkx_Latn_ID',
		'kky'	=> 'kky_Latn_AU',
		'kkz'	=> 'kkz_Latn_CA',
		'kla'	=> 'kla_Latn_US',
		'klb'	=> 'klb_Latn_MX',
		'klc'	=> 'klc_Latn_CM',
		'kld'	=> 'kld_Latn_AU',
		'kle'	=> 'kle_Deva_NP',
		'klf'	=> 'klf_Latn_TD',
		'klg'	=> 'klg_Latn_PH',
		'klh'	=> 'klh_Latn_PG',
		'kli'	=> 'kli_Latn_ID',
		'klj'	=> 'klj_Arab_IR',
		'klk'	=> 'klk_Latn_NG',
		'kll'	=> 'kll_Latn_PH',
		'klm'	=> 'klm_Latn_PG',
		'klo'	=> 'klo_Latn_NG',
		'klp'	=> 'klp_Latn_PG',
		'klq'	=> 'klq_Latn_PG',
		'klr'	=> 'klr_Deva_NP',
		'kls'	=> 'kls_Latn_PK',
		'klt'	=> 'klt_Latn_PG',
		'klu'	=> 'klu_Latn_LR',
		'klv'	=> 'klv_Latn_VU',
		'klw'	=> 'klw_Latn_ID',
		'klx'	=> 'klx_Latn_PG',
		'kly'	=> 'kly_Latn_ID',
		'klz'	=> 'klz_Latn_ID',
		'kma'	=> 'kma_Latn_GH',
		'kmc'	=> 'kmc_Latn_CN',
		'kmd'	=> 'kmd_Latn_PH',
		'kme'	=> 'kme_Latn_CM',
		'kmf'	=> 'kmf_Latn_PG',
		'kmg'	=> 'kmg_Latn_PG',
		'kmh'	=> 'kmh_Latn_PG',
		'kmi'	=> 'kmi_Latn_NG',
		'kmj'	=> 'kmj_Deva_IN',
		'kmk'	=> 'kmk_Latn_PH',
		'kml'	=> 'kml_Latn_PH',
		'kmm'	=> 'kmm_Latn_IN',
		'kmn'	=> 'kmn_Latn_PG',
		'kmo'	=> 'kmo_Latn_PG',
		'kmp'	=> 'kmp_Latn_CM',
		'kmq'	=> 'kmq_Latn_ET',
		'kms'	=> 'kms_Latn_PG',
		'kmt'	=> 'kmt_Latn_ID',
		'kmu'	=> 'kmu_Latn_PG',
		'kmv'	=> 'kmv_Latn_BR',
		'kmw'	=> 'kmw_Latn_CD',
		'kmx'	=> 'kmx_Latn_PG',
		'kmy'	=> 'kmy_Latn_NG',
		'kmz'	=> 'kmz_Arab_IR',
		'kna'	=> 'kna_Latn_NG',
		'knb'	=> 'knb_Latn_PH',
		'knd'	=> 'knd_Latn_ID',
		'kne'	=> 'kne_Latn_PH',
		'kni'	=> 'kni_Latn_NG',
		'knj'	=> 'knj_Latn_GT',
		'knk'	=> 'knk_Latn_SL',
		'knl'	=> 'knl_Latn_ID',
		'knm'	=> 'knm_Latn_BR',
		'kno'	=> 'kno_Latn_SL',
		'knp'	=> 'knp_Latn_CM',
		'knq'	=> 'knq_Latn_MY',
		'knr'	=> 'knr_Latn_PG',
		'kns'	=> 'kns_Latn_MY',
		'knt'	=> 'knt_Latn_BR',
		'knu'	=> 'knu_Latn_GN',
		'knv'	=> 'knv_Latn_PG',
		'knw'	=> 'knw_Latn_NA',
		'knx'	=> 'knx_Latn_ID',
		'kny'	=> 'kny_Latn_CD',
		'knz'	=> 'knz_Latn_BF',
		'koa'	=> 'koa_Latn_PG',
		'koc'	=> 'koc_Latn_NG',
		'kod'	=> 'kod_Latn_ID',
		'koe'	=> 'koe_Latn_SS',
		'kof'	=> 'kof_Latn_NG',
		'kog'	=> 'kog_Latn_CO',
		'koh'	=> 'koh_Latn_CG',
		'kol'	=> 'kol_Latn_PG',
		'koo'	=> 'koo_Latn_UG',
		'kop'	=> 'kop_Latn_PG',
		'koq'	=> 'koq_Latn_GA',
		'kot'	=> 'kot_Latn_CM',
		'kou'	=> 'kou_Latn_TD',
		'kov'	=> 'kov_Latn_NG',
		'kow'	=> 'kow_Latn_NG',
		'koy'	=> 'koy_Latn_US',
		'koz'	=> 'koz_Latn_PG',
		'kpa'	=> 'kpa_Latn_NG',
		'kpc'	=> 'kpc_Latn_CO',
		'kpd'	=> 'kpd_Latn_ID',
		'kpf'	=> 'kpf_Latn_PG',
		'kpg'	=> 'kpg_Latn_FM',
		'kph'	=> 'kph_Latn_GH',
		'kpi'	=> 'kpi_Latn_ID',
		'kpj'	=> 'kpj_Latn_BR',
		'kpk'	=> 'kpk_Latn_NG',
		'kpl'	=> 'kpl_Latn_CD',
		'kpm'	=> 'kpm_Latn_VN',
		'kpn'	=> 'kpn_Latn_BR',
		'kpo'	=> 'kpo_Latn_TG',
		'kpq'	=> 'kpq_Latn_ID',
		'kpr'	=> 'kpr_Latn_PG',
		'kps'	=> 'kps_Latn_ID',
		'kpt'	=> 'kpt_Cyrl_RU',
		'kpu'	=> 'kpu_Latn_ID',
		'kpw'	=> 'kpw_Latn_PG',
		'kpx'	=> 'kpx_Latn_PG',
		'kpy'	=> 'kpy_Cyrl_RU',
		'kpz'	=> 'kpz_Latn_UG',
		'kqa'	=> 'kqa_Latn_PG',
		'kqb'	=> 'kqb_Latn_PG',
		'kqc'	=> 'kqc_Latn_PG',
		'kqd'	=> 'kqd_Syrc_IQ',
		'kqe'	=> 'kqe_Latn_PH',
		'kqf'	=> 'kqf_Latn_PG',
		'kqg'	=> 'kqg_Latn_BF',
		'kqh'	=> 'kqh_Latn_TZ',
		'kqi'	=> 'kqi_Latn_PG',
		'kqj'	=> 'kqj_Latn_PG',
		'kqk'	=> 'kqk_Latn_BJ',
		'kql'	=> 'kql_Latn_PG',
		'kqm'	=> 'kqm_Latn_CI',
		'kqn'	=> 'kqn_Latn_ZM',
		'kqo'	=> 'kqo_Latn_LR',
		'kqp'	=> 'kqp_Latn_TD',
		'kqq'	=> 'kqq_Latn_BR',
		'kqr'	=> 'kqr_Latn_MY',
		'kqs'	=> 'kqs_Latn_GN',
		'kqt'	=> 'kqt_Latn_MY',
		'kqu'	=> 'kqu_Latn_ZA',
		'kqv'	=> 'kqv_Latn_ID',
		'kqw'	=> 'kqw_Latn_PG',
		'kqx'	=> 'kqx_Latn_CM',
		'kqy'	=> 'kqy_Ethi_ET',
		'kqz'	=> 'kqz_Latn_ZA',
		'kr'	=> 'kr_Latn_NG',
		'kra'	=> 'kra_Deva_NP',
		'krb'	=> 'krb_Latn_US',
		'krd'	=> 'krd_Latn_TL',
		'kre'	=> 'kre_Latn_BR',
		'krf'	=> 'krf_Latn_VU',
		'krh'	=> 'krh_Latn_NG',
		'krk'	=> 'krk_Cyrl_RU',
		'krn'	=> 'krn_Latn_LR',
		'krp'	=> 'krp_Latn_NG',
		'krr'	=> 'krr_Khmr_KH',
		'krs'	=> 'krs_Latn_SS',
		'krt'	=> 'krt_Latn_NE',
		'krv'	=> 'krv_Khmr_KH',
		'krw'	=> 'krw_Latn_LR',
		'krx'	=> 'krx_Latn_SN',
		'kry'	=> 'kry_Latn_AZ',
		'krz'	=> 'krz_Latn_ID',
		'ksc'	=> 'ksc_Latn_PH',
		'ksd'	=> 'ksd_Latn_PG',
		'kse'	=> 'kse_Latn_PG',
		'ksg'	=> 'ksg_Latn_SB',
		'ksi'	=> 'ksi_Latn_PG',
		'ksj'	=> 'ksj_Latn_PG',
		'ksk'	=> 'ksk_Latn_US',
		'ksl'	=> 'ksl_Latn_PG',
		'ksm'	=> 'ksm_Latn_NG',
		'ksn'	=> 'ksn_Latn_PH',
		'kso'	=> 'kso_Latn_NG',
		'ksp'	=> 'ksp_Latn_CF',
		'ksq'	=> 'ksq_Latn_NG',
		'ksr'	=> 'ksr_Latn_PG',
		'kss'	=> 'kss_Latn_LR',
		'kst'	=> 'kst_Latn_BF',
		'ksu'	=> 'ksu_Mymr_IN',
		'ksv'	=> 'ksv_Latn_CD',
		'ksw'	=> 'ksw_Mymr_MM',
		'ksx'	=> 'ksx_Latn_ID',
		'ksz'	=> 'ksz_Deva_IN',
		'kta'	=> 'kta_Latn_VN',
		'ktb'	=> 'ktb_Ethi_ET',
		'ktc'	=> 'ktc_Latn_NG',
		'ktd'	=> 'ktd_Latn_AU',
		'kte'	=> 'kte_Deva_NP',
		'ktf'	=> 'ktf_Latn_CD',
		'ktg'	=> 'ktg_Latn_AU',
		'kth'	=> 'kth_Latn_TD',
		'kti'	=> 'kti_Latn_ID',
		'ktj'	=> 'ktj_Latn_CI',
		'ktk'	=> 'ktk_Latn_PG',
		'ktl'	=> 'ktl_Arab_IR',
		'ktm'	=> 'ktm_Latn_PG',
		'ktn'	=> 'ktn_Latn_BR',
		'kto'	=> 'kto_Latn_PG',
		'ktp'	=> 'ktp_Plrd_CN',
		'ktq'	=> 'ktq_Latn_PH',
		'kts'	=> 'kts_Latn_ID',
		'ktt'	=> 'ktt_Latn_ID',
		'ktu'	=> 'ktu_Latn_CD',
		'ktv'	=> 'ktv_Latn_VN',
		'ktw'	=> 'ktw_Latn_US',
		'ktx'	=> 'ktx_Latn_BR',
		'kty'	=> 'kty_Latn_CD',
		'ktz'	=> 'ktz_Latn_NA',
		'kub'	=> 'kub_Latn_NG',
		'kuc'	=> 'kuc_Latn_ID',
		'kud'	=> 'kud_Latn_PG',
		'kue'	=> 'kue_Latn_PG',
		'kuf'	=> 'kuf_Laoo_LA',
		'kug'	=> 'kug_Latn_NG',
		'kuh'	=> 'kuh_Latn_NG',
		'kui'	=> 'kui_Latn_BR',
		'kuj'	=> 'kuj_Latn_TZ',
		'kuk'	=> 'kuk_Latn_ID',
		'kul'	=> 'kul_Latn_NG',
		'kun'	=> 'kun_Latn_ER',
		'kuo'	=> 'kuo_Latn_PG',
		'kup'	=> 'kup_Latn_PG',
		'kuq'	=> 'kuq_Latn_BR',
		'kus'	=> 'kus_Latn_GH',
		'kut'	=> 'kut_Latn_CA',
		'kuu'	=> 'kuu_Latn_US',
		'kuv'	=> 'kuv_Latn_ID',
		'kuw'	=> 'kuw_Latn_CF',
		'kux'	=> 'kux_Latn_AU',
		'kuy'	=> 'kuy_Latn_AU',
		'kuz'	=> 'kuz_Latn_CL',
		'kva'	=> 'kva_Cyrl_RU',
		'kvb'	=> 'kvb_Latn_ID',
		'kvc'	=> 'kvc_Latn_PG',
		'kvd'	=> 'kvd_Latn_ID',
		'kve'	=> 'kve_Latn_MY',
		'kvf'	=> 'kvf_Latn_TD',
		'kvg'	=> 'kvg_Latn_PG',
		'kvh'	=> 'kvh_Latn_ID',
		'kvi'	=> 'kvi_Latn_TD',
		'kvj'	=> 'kvj_Latn_CM',
		'kvl'	=> 'kvl_Latn_MM',
		'kvm'	=> 'kvm_Latn_CM',
		'kvn'	=> 'kvn_Latn_CO',
		'kvo'	=> 'kvo_Latn_ID',
		'kvp'	=> 'kvp_Latn_ID',
		'kvq'	=> 'kvq_Mymr_MM',
		'kvt'	=> 'kvt_Mymr_MM',
		'kvv'	=> 'kvv_Latn_ID',
		'kvw'	=> 'kvw_Latn_ID',
		'kvy'	=> 'kvy_Kali_MM',
		'kvz'	=> 'kvz_Latn_ID',
		'kwa'	=> 'kwa_Latn_BR',
		'kwb'	=> 'kwb_Latn_NG',
		'kwc'	=> 'kwc_Latn_CG',
		'kwd'	=> 'kwd_Latn_SB',
		'kwe'	=> 'kwe_Latn_ID',
		'kwf'	=> 'kwf_Latn_SB',
		'kwg'	=> 'kwg_Latn_TD',
		'kwh'	=> 'kwh_Latn_ID',
		'kwi'	=> 'kwi_Latn_CO',
		'kwj'	=> 'kwj_Latn_PG',
		'kwl'	=> 'kwl_Latn_NG',
		'kwm'	=> 'kwm_Latn_NA',
		'kwn'	=> 'kwn_Latn_NA',
		'kwo'	=> 'kwo_Latn_PG',
		'kwp'	=> 'kwp_Latn_CI',
		'kwr'	=> 'kwr_Latn_ID',
		'kws'	=> 'kws_Latn_CD',
		'kwt'	=> 'kwt_Latn_ID',
		'kwu'	=> 'kwu_Latn_CM',
		'kwv'	=> 'kwv_Latn_TD',
		'kww'	=> 'kww_Latn_SR',
		'kwy'	=> 'kwy_Latn_AO',
		'kwz'	=> 'kwz_Latn_AO',
		'kxa'	=> 'kxa_Latn_PG',
		'kxb'	=> 'kxb_Latn_CI',
		'kxc'	=> 'kxc_Latn_ET',
		'kxd'	=> 'kxd_Latn_BN',
		'kxf'	=> 'kxf_Mymr_MM',
		'kxi'	=> 'kxi_Latn_MY',
		'kxj'	=> 'kxj_Latn_TD',
		'kxk'	=> 'kxk_Mymr_MM',
		'kxn'	=> 'kxn_Latn_MY',
		'kxo'	=> 'kxo_Latn_BR',
		'kxq'	=> 'kxq_Latn_ID',
		'kxr'	=> 'kxr_Latn_PG',
		'kxt'	=> 'kxt_Latn_PG',
		'kxw'	=> 'kxw_Latn_PG',
		'kxx'	=> 'kxx_Latn_CG',
		'kxy'	=> 'kxy_Latn_VN',
		'kxz'	=> 'kxz_Latn_PG',
		'kya'	=> 'kya_Latn_TZ',
		'kyb'	=> 'kyb_Latn_PH',
		'kyc'	=> 'kyc_Latn_PG',
		'kyd'	=> 'kyd_Latn_ID',
		'kye'	=> 'kye_Latn_GH',
		'kyf'	=> 'kyf_Latn_CI',
		'kyg'	=> 'kyg_Latn_PG',
		'kyh'	=> 'kyh_Latn_US',
		'kyi'	=> 'kyi_Latn_MY',
		'kyj'	=> 'kyj_Latn_PH',
		'kyk'	=> 'kyk_Latn_PH',
		'kyl'	=> 'kyl_Latn_US',
		'kym'	=> 'kym_Latn_CF',
		'kyn'	=> 'kyn_Latn_PH',
		'kyo'	=> 'kyo_Latn_ID',
		'kyq'	=> 'kyq_Latn_TD',
		'kyr'	=> 'kyr_Latn_BR',
		'kys'	=> 'kys_Latn_MY',
		'kyt'	=> 'kyt_Latn_ID',
		'kyu'	=> 'kyu_Kali_MM',
		'kyv'	=> 'kyv_Deva_NP',
		'kyw'	=> 'kyw_Deva_IN',
		'kyx'	=> 'kyx_Latn_PG',
		'kyy'	=> 'kyy_Latn_PG',
		'kyz'	=> 'kyz_Latn_BR',
		'kza'	=> 'kza_Latn_BF',
		'kzb'	=> 'kzb_Latn_ID',
		'kzc'	=> 'kzc_Latn_CI',
		'kzd'	=> 'kzd_Latn_ID',
		'kze'	=> 'kze_Latn_PG',
		'kzf'	=> 'kzf_Latn_ID',
		'kzi'	=> 'kzi_Latn_MY',
		'kzk'	=> 'kzk_Latn_SB',
		'kzl'	=> 'kzl_Latn_ID',
		'kzm'	=> 'kzm_Latn_ID',
		'kzn'	=> 'kzn_Latn_MW',
		'kzo'	=> 'kzo_Latn_GA',
		'kzp'	=> 'kzp_Latn_ID',
		'kzr'	=> 'kzr_Latn_CM',
		'kzs'	=> 'kzs_Latn_MY',
		'kzu'	=> 'kzu_Latn_ID',
		'kzv'	=> 'kzv_Latn_ID',
		'kzw'	=> 'kzw_Latn_BR',
		'kzx'	=> 'kzx_Latn_ID',
		'kzy'	=> 'kzy_Latn_CD',
		'kzz'	=> 'kzz_Latn_ID',
		'laa'	=> 'laa_Latn_PH',
		'lac'	=> 'lac_Latn_MX',
		'lae'	=> 'lae_Deva_IN',
		'lai'	=> 'lai_Latn_MW',
		'lal'	=> 'lal_Latn_CD',
		'lam'	=> 'lam_Latn_ZM',
		'lan'	=> 'lan_Latn_NG',
		'lap'	=> 'lap_Latn_TD',
		'laq'	=> 'laq_Latn_VN',
		'lar'	=> 'lar_Latn_GH',
		'las'	=> 'las_Latn_TG',
		'lau'	=> 'lau_Latn_ID',
		'law'	=> 'law_Latn_ID',
		'lax'	=> 'lax_Latn_IN',
		'laz'	=> 'laz_Latn_PG',
		'lbb'	=> 'lbb_Latn_PG',
		'lbf'	=> 'lbf_Deva_IN',
		'lbi'	=> 'lbi_Latn_CM',
		'lbj'	=> 'lbj_Tibt_IN',
		'lbl'	=> 'lbl_Latn_PH',
		'lbm'	=> 'lbm_Deva_IN',
		'lbn'	=> 'lbn_Latn_LA',
		'lbo'	=> 'lbo_Laoo_LA',
		'lbq'	=> 'lbq_Latn_PG',
		'lbr'	=> 'lbr_Deva_NP',
		'lbt'	=> 'lbt_Latn_VN',
		'lbu'	=> 'lbu_Latn_PG',
		'lbv'	=> 'lbv_Latn_PG',
		'lbx'	=> 'lbx_Latn_ID',
		'lby'	=> 'lby_Latn_AU',
		'lbz'	=> 'lbz_Latn_AU',
		'lcc'	=> 'lcc_Latn_ID',
		'lcd'	=> 'lcd_Latn_ID',
		'lce'	=> 'lce_Latn_ID',
		'lcf'	=> 'lcf_Latn_ID',
		'lch'	=> 'lch_Latn_AO',
		'lcl'	=> 'lcl_Latn_ID',
		'lcm'	=> 'lcm_Latn_PG',
		'lcq'	=> 'lcq_Latn_ID',
		'lcs'	=> 'lcs_Latn_ID',
		'lda'	=> 'lda_Latn_CI',
		'ldb'	=> 'ldb_Latn_NG',
		'ldd'	=> 'ldd_Latn_NG',
		'ldg'	=> 'ldg_Latn_NG',
		'ldh'	=> 'ldh_Latn_NG',
		'ldi'	=> 'ldi_Latn_CG',
		'ldj'	=> 'ldj_Latn_NG',
		'ldk'	=> 'ldk_Latn_NG',
		'ldl'	=> 'ldl_Latn_NG',
		'ldm'	=> 'ldm_Latn_GN',
		'ldn'	=> 'ldn_Latn_001',
		'ldo'	=> 'ldo_Latn_NG',
		'ldp'	=> 'ldp_Latn_NG',
		'ldq'	=> 'ldq_Latn_NG',
		'lea'	=> 'lea_Latn_CD',
		'leb'	=> 'leb_Latn_ZM',
		'lec'	=> 'lec_Latn_BO',
		'led'	=> 'led_Latn_CD',
		'lee'	=> 'lee_Latn_BF',
		'lef'	=> 'lef_Latn_GH',
		'leh'	=> 'leh_Latn_ZM',
		'lei'	=> 'lei_Latn_PG',
		'lej'	=> 'lej_Latn_CD',
		'lek'	=> 'lek_Latn_PG',
		'lel'	=> 'lel_Latn_CD',
		'lem'	=> 'lem_Latn_CM',
		'len'	=> 'len_Latn_HN',
		'leo'	=> 'leo_Latn_CM',
		'leq'	=> 'leq_Latn_PG',
		'ler'	=> 'ler_Latn_PG',
		'les'	=> 'les_Latn_CD',
		'let'	=> 'let_Latn_PG',
		'leu'	=> 'leu_Latn_PG',
		'lev'	=> 'lev_Latn_ID',
		'lew'	=> 'lew_Latn_ID',
		'lex'	=> 'lex_Latn_ID',
		'ley'	=> 'ley_Latn_ID',
		'lfa'	=> 'lfa_Latn_CM',
		'lfn'	=> 'lfn_Latn_001',
		'lga'	=> 'lga_Latn_SB',
		'lgb'	=> 'lgb_Latn_SB',
		'lgg'	=> 'lgg_Latn_UG',
		'lgh'	=> 'lgh_Latn_VN',
		'lgi'	=> 'lgi_Latn_ID',
		'lgk'	=> 'lgk_Latn_VU',
		'lgl'	=> 'lgl_Latn_SB',
		'lgm'	=> 'lgm_Latn_CD',
		'lgn'	=> 'lgn_Latn_ET',
		'lgo'	=> 'lgo_Latn_SS',
		'lgq'	=> 'lgq_Latn_GH',
		'lgr'	=> 'lgr_Latn_SB',
		'lgt'	=> 'lgt_Latn_PG',
		'lgu'	=> 'lgu_Latn_SB',
		'lgz'	=> 'lgz_Latn_CD',
		'lha'	=> 'lha_Latn_VN',
		'lhh'	=> 'lhh_Latn_ID',
		'lhi'	=> 'lhi_Latn_CN',
		'lhm'	=> 'lhm_Deva_NP',
		'lhn'	=> 'lhn_Latn_MY',
		'lhs'	=> 'lhs_Syrc_SY',
		'lht'	=> 'lht_Latn_VU',
		'lhu'	=> 'lhu_Latn_CN',
		'lia'	=> 'lia_Latn_SL',
		'lib'	=> 'lib_Latn_PG',
		'lic'	=> 'lic_Latn_CN',
		'lid'	=> 'lid_Latn_PG',
		'lie'	=> 'lie_Latn_CD',
		'lig'	=> 'lig_Latn_GH',
		'lih'	=> 'lih_Latn_PG',
		'lik'	=> 'lik_Latn_CD',
		'lio'	=> 'lio_Latn_ID',
		'lip'	=> 'lip_Latn_GH',
		'liq'	=> 'liq_Latn_ET',
		'lir'	=> 'lir_Latn_LR',
		'liu'	=> 'liu_Latn_SD',
		'liv'	=> 'liv_Latn_LV',
		'liw'	=> 'liw_Latn_ID',
		'lix'	=> 'lix_Latn_ID',
		'liy'	=> 'liy_Latn_CF',
		'liz'	=> 'liz_Latn_CD',
		'lja'	=> 'lja_Latn_AU',
		'lje'	=> 'lje_Latn_ID',
		'lji'	=> 'lji_Latn_ID',
		'ljl'	=> 'ljl_Latn_ID',
		'ljw'	=> 'ljw_Latn_AU',
		'ljx'	=> 'ljx_Latn_AU',
		'lka'	=> 'lka_Latn_TL',
		'lkb'	=> 'lkb_Latn_KE',
		'lkc'	=> 'lkc_Latn_VN',
		'lkd'	=> 'lkd_Latn_BR',
		'lke'	=> 'lke_Latn_UG',
		'lkh'	=> 'lkh_Tibt_BT',
		'lkj'	=> 'lkj_Latn_MY',
		'lkl'	=> 'lkl_Latn_PG',
		'lkm'	=> 'lkm_Latn_AU',
		'lkn'	=> 'lkn_Latn_VU',
		'lko'	=> 'lko_Latn_KE',
		'lkr'	=> 'lkr_Latn_SS',
		'lks'	=> 'lks_Latn_KE',
		'lku'	=> 'lku_Latn_AU',
		'lky'	=> 'lky_Latn_SS',
		'lla'	=> 'lla_Latn_NG',
		'llb'	=> 'llb_Latn_MZ',
		'llc'	=> 'llc_Latn_GN',
		'lle'	=> 'lle_Latn_PG',
		'llf'	=> 'llf_Latn_PG',
		'llg'	=> 'llg_Latn_ID',
		'lli'	=> 'lli_Latn_CG',
		'llj'	=> 'llj_Latn_AU',
		'llk'	=> 'llk_Latn_MY',
		'lll'	=> 'lll_Latn_PG',
		'llm'	=> 'llm_Latn_ID',
		'lln'	=> 'lln_Latn_TD',
		'llp'	=> 'llp_Latn_VU',
		'llq'	=> 'llq_Latn_ID',
		'llu'	=> 'llu_Latn_SB',
		'llx'	=> 'llx_Latn_FJ',
		'lma'	=> 'lma_Latn_GN',
		'lmb'	=> 'lmb_Latn_VU',
		'lmc'	=> 'lmc_Latn_AU',
		'lmd'	=> 'lmd_Latn_SD',
		'lme'	=> 'lme_Latn_TD',
		'lmf'	=> 'lmf_Latn_ID',
		'lmg'	=> 'lmg_Latn_PG',
		'lmh'	=> 'lmh_Deva_NP',
		'lmi'	=> 'lmi_Latn_CD',
		'lmj'	=> 'lmj_Latn_ID',
		'lmk'	=> 'lmk_Latn_IN',
		'lml'	=> 'lml_Latn_VU',
		'lmp'	=> 'lmp_Latn_CM',
		'lmq'	=> 'lmq_Latn_ID',
		'lmr'	=> 'lmr_Latn_ID',
		'lmu'	=> 'lmu_Latn_VU',
		'lmv'	=> 'lmv_Latn_FJ',
		'lmw'	=> 'lmw_Latn_US',
		'lmx'	=> 'lmx_Latn_CM',
		'lmy'	=> 'lmy_Latn_ID',
		'lna'	=> 'lna_Latn_CF',
		'lnb'	=> 'lnb_Latn_NA',
		'lnd'	=> 'lnd_Latn_ID',
		'lng'	=> 'lng_Latn_HU',
		'lnh'	=> 'lnh_Latn_MY',
		'lni'	=> 'lni_Latn_PG',
		'lnj'	=> 'lnj_Latn_AU',
		'lnl'	=> 'lnl_Latn_CF',
		'lnm'	=> 'lnm_Latn_PG',
		'lnn'	=> 'lnn_Latn_VU',
		'lns'	=> 'lns_Latn_CM',
		'lnu'	=> 'lnu_Latn_NG',
		'lnw'	=> 'lnw_Latn_AU',
		'lnz'	=> 'lnz_Latn_CD',
		'loa'	=> 'loa_Latn_ID',
		'lob'	=> 'lob_Latn_BF',
		'loc'	=> 'loc_Latn_PH',
		'loe'	=> 'loe_Latn_ID',
		'log'	=> 'log_Latn_CD',
		'loh'	=> 'loh_Latn_SS',
		'loi'	=> 'loi_Latn_CI',
		'loj'	=> 'loj_Latn_PG',
		'lok'	=> 'lok_Latn_SL',
		'lom'	=> 'lom_Latn_LR',
		'lon'	=> 'lon_Latn_MW',
		'loo'	=> 'loo_Latn_CD',
		'lop'	=> 'lop_Latn_NG',
		'loq'	=> 'loq_Latn_CD',
		'lor'	=> 'lor_Latn_CI',
		'los'	=> 'los_Latn_PG',
		'lot'	=> 'lot_Latn_SS',
		'lou'	=> 'lou_Latn_US',
		'low'	=> 'low_Latn_MY',
		'lox'	=> 'lox_Latn_ID',
		'loy'	=> 'loy_Deva_NP',
		'lpa'	=> 'lpa_Latn_VU',
		'lpe'	=> 'lpe_Latn_ID',
		'lpn'	=> 'lpn_Latn_MM',
		'lpo'	=> 'lpo_Plrd_CN',
		'lpx'	=> 'lpx_Latn_SS',
		'lqr'	=> 'lqr_Latn_SS',
		'lra'	=> 'lra_Latn_MY',
		'lrg'	=> 'lrg_Latn_AU',
		'lri'	=> 'lri_Latn_KE',
		'lrk'	=> 'lrk_Arab_PK',
		'lrl'	=> 'lrl_Arab_IR',
		'lrm'	=> 'lrm_Latn_KE',
		'lrn'	=> 'lrn_Latn_ID',
		'lro'	=> 'lro_Latn_SD',
		'lrt'	=> 'lrt_Latn_ID',
		'lrv'	=> 'lrv_Latn_VU',
		'lrz'	=> 'lrz_Latn_VU',
		'lsa'	=> 'lsa_Arab_IR',
		'lsd'	=> 'lsd_Hebr_IL',
		'lse'	=> 'lse_Latn_CD',
		'lsi'	=> 'lsi_Latn_MM',
		'lsm'	=> 'lsm_Latn_UG',
		'lsr'	=> 'lsr_Latn_PG',
		'lss'	=> 'lss_Arab_PK',
		'ltc'	=> 'ltc_Hant_CN',
		'lth'	=> 'lth_Latn_UG',
		'lti'	=> 'lti_Latn_ID',
		'ltn'	=> 'ltn_Latn_BR',
		'lto'	=> 'lto_Latn_KE',
		'lts'	=> 'lts_Latn_KE',
		'ltu'	=> 'ltu_Latn_ID',
		'luc'	=> 'luc_Latn_UG',
		'lud'	=> 'lud_Latn_RU',
		'lue'	=> 'lue_Latn_ZM',
		'luf'	=> 'luf_Latn_PG',
		'lui'	=> 'lui_Latn_US',
		'luj'	=> 'luj_Latn_CD',
		'luk'	=> 'luk_Tibt_BT',
		'lul'	=> 'lul_Latn_SS',
		'lum'	=> 'lum_Latn_AO',
		'lun'	=> 'lun_Latn_ZM',
		'lup'	=> 'lup_Latn_GA',
		'luq'	=> 'luq_Latn_CU',
		'lur'	=> 'lur_Latn_ID',
		'lus'	=> 'lus_Latn_IN',
		'lut'	=> 'lut_Latn_US',
		'luu'	=> 'luu_Deva_NP',
		'luv'	=> 'luv_Arab_OM',
		'luw'	=> 'luw_Latn_CM',
		'lva'	=> 'lva_Latn_TL',
		'lvi'	=> 'lvi_Latn_LA',
		'lvk'	=> 'lvk_Latn_SB',
		'lvl'	=> 'lvl_Latn_CD',
		'lvu'	=> 'lvu_Latn_ID',
		'lwa'	=> 'lwa_Latn_CD',
		'lwe'	=> 'lwe_Latn_ID',
		'lwg'	=> 'lwg_Latn_KE',
		'lwh'	=> 'lwh_Latn_VN',
		'lwm'	=> 'lwm_Thai_CN',
		'lwo'	=> 'lwo_Latn_SS',
		'lwt'	=> 'lwt_Latn_ID',
		'lww'	=> 'lww_Latn_VU',
		'lxm'	=> 'lxm_Latn_PG',
		'lya'	=> 'lya_Tibt_BT',
		'lyn'	=> 'lyn_Latn_ZM',
		'lzl'	=> 'lzl_Latn_VU',
		'lzn'	=> 'lzn_Latn_MM',
		'maa'	=> 'maa_Latn_MX',
		'mab'	=> 'mab_Latn_MX',
		'mae'	=> 'mae_Latn_NG',
		'maj'	=> 'maj_Latn_MX',
		'mam'	=> 'mam_Latn_GT',
		'maq'	=> 'maq_Latn_MX',
		'mat'	=> 'mat_Latn_MX',
		'mau'	=> 'mau_Latn_MX',
		'mav'	=> 'mav_Latn_BR',
		'maw'	=> 'maw_Latn_GH',
		'max'	=> 'max_Latn_ID',
		'mba'	=> 'mba_Latn_PH',
		'mbb'	=> 'mbb_Latn_PH',
		'mbc'	=> 'mbc_Latn_BR',
		'mbd'	=> 'mbd_Latn_PH',
		'mbf'	=> 'mbf_Latn_SG',
		'mbh'	=> 'mbh_Latn_PG',
		'mbi'	=> 'mbi_Latn_PH',
		'mbj'	=> 'mbj_Latn_BR',
		'mbk'	=> 'mbk_Latn_PG',
		'mbl'	=> 'mbl_Latn_BR',
		'mbm'	=> 'mbm_Latn_CG',
		'mbn'	=> 'mbn_Latn_CO',
		'mbo'	=> 'mbo_Latn_CM',
		'mbp'	=> 'mbp_Latn_CO',
		'mbq'	=> 'mbq_Latn_PG',
		'mbr'	=> 'mbr_Latn_CO',
		'mbs'	=> 'mbs_Latn_PH',
		'mbt'	=> 'mbt_Latn_PH',
		'mbu'	=> 'mbu_Latn_NG',
		'mbv'	=> 'mbv_Latn_GN',
		'mbw'	=> 'mbw_Latn_PG',
		'mbx'	=> 'mbx_Latn_PG',
		'mby'	=> 'mby_Arab_PK',
		'mbz'	=> 'mbz_Latn_MX',
		'mca'	=> 'mca_Latn_PY',
		'mcb'	=> 'mcb_Latn_PE',
		'mcc'	=> 'mcc_Latn_PG',
		'mcd'	=> 'mcd_Latn_PE',
		'mce'	=> 'mce_Latn_MX',
		'mcf'	=> 'mcf_Latn_PE',
		'mcg'	=> 'mcg_Latn_VE',
		'mch'	=> 'mch_Latn_VE',
		'mci'	=> 'mci_Latn_PG',
		'mcj'	=> 'mcj_Latn_NG',
		'mck'	=> 'mck_Latn_AO',
		'mcl'	=> 'mcl_Latn_CO',
		'mcm'	=> 'mcm_Latn_MY',
		'mcn'	=> 'mcn_Latn_TD',
		'mco'	=> 'mco_Latn_MX',
		'mcp'	=> 'mcp_Latn_CM',
		'mcq'	=> 'mcq_Latn_PG',
		'mcr'	=> 'mcr_Latn_PG',
		'mcs'	=> 'mcs_Latn_CM',
		'mct'	=> 'mct_Latn_CM',
		'mcu'	=> 'mcu_Latn_CM',
		'mcv'	=> 'mcv_Latn_PG',
		'mcw'	=> 'mcw_Latn_TD',
		'mcx'	=> 'mcx_Latn_CF',
		'mcy'	=> 'mcy_Latn_PG',
		'mcz'	=> 'mcz_Latn_PG',
		'mda'	=> 'mda_Latn_NG',
		'mdb'	=> 'mdb_Latn_PG',
		'mdc'	=> 'mdc_Latn_PG',
		'mdd'	=> 'mdd_Latn_CM',
		'mde'	=> 'mde_Arab_TD',
		'mdg'	=> 'mdg_Latn_TD',
		'mdi'	=> 'mdi_Latn_CD',
		'mdj'	=> 'mdj_Latn_CD',
		'mdk'	=> 'mdk_Latn_CD',
		'mdm'	=> 'mdm_Latn_CD',
		'mdn'	=> 'mdn_Latn_CF',
		'mdp'	=> 'mdp_Latn_CD',
		'mdq'	=> 'mdq_Latn_CD',
		'mds'	=> 'mds_Latn_PG',
		'mdt'	=> 'mdt_Latn_CG',
		'mdu'	=> 'mdu_Latn_CG',
		'mdv'	=> 'mdv_Latn_MX',
		'mdw'	=> 'mdw_Latn_CG',
		'mdx'	=> 'mdx_Ethi_ET',
		'mdy'	=> 'mdy_Ethi_ET',
		'mdz'	=> 'mdz_Latn_BR',
		'mea'	=> 'mea_Latn_CM',
		'meb'	=> 'meb_Latn_PG',
		'mec'	=> 'mec_Latn_AU',
		'med'	=> 'med_Latn_PG',
		'mee'	=> 'mee_Latn_PG',
		'meh'	=> 'meh_Latn_MX',
		'mej'	=> 'mej_Latn_ID',
		'mek'	=> 'mek_Latn_PG',
		'mel'	=> 'mel_Latn_MY',
		'mem'	=> 'mem_Latn_AU',
		'meo'	=> 'meo_Latn_MY',
		'mep'	=> 'mep_Latn_AU',
		'meq'	=> 'meq_Latn_CM',
		'mes'	=> 'mes_Latn_TD',
		'met'	=> 'met_Latn_PG',
		'meu'	=> 'meu_Latn_PG',
		'mev'	=> 'mev_Latn_LR',
		'mew'	=> 'mew_Latn_NG',
		'mez'	=> 'mez_Latn_US',
		'mfb'	=> 'mfb_Latn_ID',
		'mfc'	=> 'mfc_Latn_CD',
		'mfd'	=> 'mfd_Latn_CM',
		'mff'	=> 'mff_Latn_CM',
		'mfg'	=> 'mfg_Latn_GN',
		'mfh'	=> 'mfh_Latn_CM',
		'mfi'	=> 'mfi_Arab_CM',
		'mfj'	=> 'mfj_Latn_CM',
		'mfk'	=> 'mfk_Latn_CM',
		'mfl'	=> 'mfl_Latn_NG',
		'mfm'	=> 'mfm_Latn_NG',
		'mfn'	=> 'mfn_Latn_NG',
		'mfo'	=> 'mfo_Latn_NG',
		'mfp'	=> 'mfp_Latn_ID',
		'mfq'	=> 'mfq_Latn_TG',
		'mfr'	=> 'mfr_Latn_AU',
		'mft'	=> 'mft_Latn_PG',
		'mfu'	=> 'mfu_Latn_AO',
		'mfw'	=> 'mfw_Latn_PG',
		'mfx'	=> 'mfx_Latn_ET',
		'mfy'	=> 'mfy_Latn_MX',
		'mfz'	=> 'mfz_Latn_SS',
		'mga'	=> 'mga_Latg_IE',
		'mgb'	=> 'mgb_Latn_TD',
		'mgc'	=> 'mgc_Latn_SS',
		'mgd'	=> 'mgd_Latn_SS',
		'mge'	=> 'mge_Latn_TD',
		'mgf'	=> 'mgf_Latn_ID',
		'mgg'	=> 'mgg_Latn_CM',
		'mgi'	=> 'mgi_Latn_NG',
		'mgj'	=> 'mgj_Latn_NG',
		'mgk'	=> 'mgk_Latn_ID',
		'mgl'	=> 'mgl_Latn_PG',
		'mgm'	=> 'mgm_Latn_TL',
		'mgn'	=> 'mgn_Latn_CF',
		'mgq'	=> 'mgq_Latn_TZ',
		'mgr'	=> 'mgr_Latn_ZM',
		'mgs'	=> 'mgs_Latn_TZ',
		'mgt'	=> 'mgt_Latn_PG',
		'mgu'	=> 'mgu_Latn_PG',
		'mgv'	=> 'mgv_Latn_TZ',
		'mgw'	=> 'mgw_Latn_TZ',
		'mgz'	=> 'mgz_Latn_TZ',
		'mhb'	=> 'mhb_Latn_GA',
		'mhc'	=> 'mhc_Latn_MX',
		'mhd'	=> 'mhd_Latn_TZ',
		'mhe'	=> 'mhe_Latn_MY',
		'mhf'	=> 'mhf_Latn_PG',
		'mhg'	=> 'mhg_Latn_AU',
		'mhi'	=> 'mhi_Latn_UG',
		'mhj'	=> 'mhj_Arab_AF',
		'mhk'	=> 'mhk_Latn_CM',
		'mhl'	=> 'mhl_Latn_PG',
		'mhm'	=> 'mhm_Latn_MZ',
		'mho'	=> 'mho_Latn_ZM',
		'mhp'	=> 'mhp_Latn_ID',
		'mhq'	=> 'mhq_Latn_US',
		'mhs'	=> 'mhs_Latn_ID',
		'mht'	=> 'mht_Latn_VE',
		'mhu'	=> 'mhu_Latn_IN',
		'mhw'	=> 'mhw_Latn_BW',
		'mhx'	=> 'mhx_Latn_MM',
		'mhy'	=> 'mhy_Latn_ID',
		'mhz'	=> 'mhz_Latn_ID',
		'mia'	=> 'mia_Latn_US',
		'mib'	=> 'mib_Latn_MX',
		'mid'	=> 'mid_Mand_IQ',
		'mie'	=> 'mie_Latn_MX',
		'mif'	=> 'mif_Latn_CM',
		'mig'	=> 'mig_Latn_MX',
		'mih'	=> 'mih_Latn_MX',
		'mii'	=> 'mii_Latn_MX',
		'mij'	=> 'mij_Latn_CM',
		'mik'	=> 'mik_Latn_US',
		'mil'	=> 'mil_Latn_MX',
		'mim'	=> 'mim_Latn_MX',
		'mio'	=> 'mio_Latn_MX',
		'mip'	=> 'mip_Latn_MX',
		'miq'	=> 'miq_Latn_NI',
		'mir'	=> 'mir_Latn_MX',
		'mit'	=> 'mit_Latn_MX',
		'miu'	=> 'miu_Latn_MX',
		'miw'	=> 'miw_Latn_PG',
		'mix'	=> 'mix_Latn_MX',
		'miy'	=> 'miy_Latn_MX',
		'miz'	=> 'miz_Latn_MX',
		'mjb'	=> 'mjb_Latn_TL',
		'mjc'	=> 'mjc_Latn_MX',
		'mjd'	=> 'mjd_Latn_US',
		'mje'	=> 'mje_Latn_TD',
		'mjg'	=> 'mjg_Latn_CN',
		'mjh'	=> 'mjh_Latn_TZ',
		'mji'	=> 'mji_Latn_CN',
		'mjj'	=> 'mjj_Latn_PG',
		'mjk'	=> 'mjk_Latn_PG',
		'mjl'	=> 'mjl_Deva_IN',
		'mjm'	=> 'mjm_Latn_PG',
		'mjn'	=> 'mjn_Latn_PG',
		'mjq'	=> 'mjq_Mlym_IN',
		'mjr'	=> 'mjr_Mlym_IN',
		'mjs'	=> 'mjs_Latn_NG',
		'mjt'	=> 'mjt_Deva_IN',
		'mju'	=> 'mju_Telu_IN',
		'mjv'	=> 'mjv_Mlym_IN',
		'mjw'	=> 'mjw_Latn_IN',
		'mjx'	=> 'mjx_Latn_BD',
		'mjy'	=> 'mjy_Latn_US',
		'mjz'	=> 'mjz_Deva_NP',
		'mka'	=> 'mka_Latn_CI',
		'mkb'	=> 'mkb_Deva_IN',
		'mkc'	=> 'mkc_Latn_PG',
		'mke'	=> 'mke_Deva_IN',
		'mkf'	=> 'mkf_Latn_NG',
		'mki'	=> 'mki_Arab_PK',
		'mkj'	=> 'mkj_Latn_FM',
		'mkk'	=> 'mkk_Latn_CM',
		'mkl'	=> 'mkl_Latn_BJ',
		'mkm'	=> 'mkm_Thai_TH',
		'mkn'	=> 'mkn_Latn_ID',
		'mko'	=> 'mko_Latn_NG',
		'mkp'	=> 'mkp_Latn_PG',
		'mkr'	=> 'mkr_Latn_PG',
		'mks'	=> 'mks_Latn_MX',
		'mkt'	=> 'mkt_Latn_NC',
		'mku'	=> 'mku_Latn_GN',
		'mkv'	=> 'mkv_Latn_VU',
		'mkw'	=> 'mkw_Latn_CG',
		'mkx'	=> 'mkx_Latn_PH',
		'mky'	=> 'mky_Latn_ID',
		'mkz'	=> 'mkz_Latn_TL',
		'mla'	=> 'mla_Latn_VU',
		'mlb'	=> 'mlb_Latn_CM',
		'mlc'	=> 'mlc_Latn_VN',
		'mle'	=> 'mle_Latn_PG',
		'mlf'	=> 'mlf_Thai_LA',
		'mlh'	=> 'mlh_Latn_PG',
		'mli'	=> 'mli_Latn_ID',
		'mlj'	=> 'mlj_Latn_TD',
		'mlk'	=> 'mlk_Latn_KE',
		'mll'	=> 'mll_Latn_VU',
		'mln'	=> 'mln_Latn_SB',
		'mlo'	=> 'mlo_Latn_SN',
		'mlp'	=> 'mlp_Latn_PG',
		'mlq'	=> 'mlq_Latn_SN',
		'mlr'	=> 'mlr_Latn_CM',
		'mlu'	=> 'mlu_Latn_SB',
		'mlv'	=> 'mlv_Latn_VU',
		'mlw'	=> 'mlw_Latn_CM',
		'mlx'	=> 'mlx_Latn_VU',
		'mlz'	=> 'mlz_Latn_PH',
		'mma'	=> 'mma_Latn_NG',
		'mmb'	=> 'mmb_Latn_ID',
		'mmc'	=> 'mmc_Latn_MX',
		'mmd'	=> 'mmd_Latn_CN',
		'mme'	=> 'mme_Latn_VU',
		'mmf'	=> 'mmf_Latn_NG',
		'mmg'	=> 'mmg_Latn_VU',
		'mmh'	=> 'mmh_Latn_BR',
		'mmi'	=> 'mmi_Latn_PG',
		'mmm'	=> 'mmm_Latn_VU',
		'mmn'	=> 'mmn_Latn_PH',
		'mmo'	=> 'mmo_Latn_PG',
		'mmp'	=> 'mmp_Latn_PG',
		'mmq'	=> 'mmq_Latn_PG',
		'mmr'	=> 'mmr_Latn_CN',
		'mmt'	=> 'mmt_Latn_PG',
		'mmu'	=> 'mmu_Latn_CM',
		'mmv'	=> 'mmv_Latn_BR',
		'mmw'	=> 'mmw_Latn_VU',
		'mmx'	=> 'mmx_Latn_PG',
		'mmy'	=> 'mmy_Latn_TD',
		'mmz'	=> 'mmz_Latn_CD',
		'mna'	=> 'mna_Latn_PG',
		'mnb'	=> 'mnb_Latn_ID',
		'mnc'	=> 'mnc_Mong_CN',
		'mnd'	=> 'mnd_Latn_BR',
		'mne'	=> 'mne_Latn_TD',
		'mnf'	=> 'mnf_Latn_CM',
		'mng'	=> 'mng_Latn_VN',
		'mnh'	=> 'mnh_Latn_CD',
		'mnj'	=> 'mnj_Arab_AF',
		'mnl'	=> 'mnl_Latn_VU',
		'mnm'	=> 'mnm_Latn_PG',
		'mnn'	=> 'mnn_Latn_VN',
		'mnp'	=> 'mnp_Latn_CN',
		'mnq'	=> 'mnq_Latn_MY',
		'mnr'	=> 'mnr_Latn_US',
		'mns'	=> 'mns_Cyrl_RU',
		'mnu'	=> 'mnu_Latn_ID',
		'mnv'	=> 'mnv_Latn_SB',
		'mnx'	=> 'mnx_Latn_ID',
		'mny'	=> 'mny_Latn_MZ',
		'mnz'	=> 'mnz_Latn_ID',
		'moa'	=> 'moa_Latn_CI',
		'moc'	=> 'moc_Latn_AR',
		'mod'	=> 'mod_Latn_US',
		'mog'	=> 'mog_Latn_ID',
		'moi'	=> 'moi_Latn_NG',
		'moj'	=> 'moj_Latn_CG',
		'mok'	=> 'mok_Latn_ID',
		'mom'	=> 'mom_Latn_NI',
		'moo'	=> 'moo_Latn_VN',
		'mop'	=> 'mop_Latn_BZ',
		'moq'	=> 'moq_Latn_ID',
		'mor'	=> 'mor_Latn_SD',
		'mot'	=> 'mot_Latn_CO',
		'mou'	=> 'mou_Latn_TD',
		'mov'	=> 'mov_Latn_US',
		'mow'	=> 'mow_Latn_CG',
		'mox'	=> 'mox_Latn_PG',
		'moy'	=> 'moy_Latn_ET',
		'moz'	=> 'moz_Latn_TD',
		'mpa'	=> 'mpa_Latn_TZ',
		'mpb'	=> 'mpb_Latn_AU',
		'mpc'	=> 'mpc_Latn_AU',
		'mpd'	=> 'mpd_Latn_BR',
		'mpe'	=> 'mpe_Latn_ET',
		'mpg'	=> 'mpg_Latn_TD',
		'mph'	=> 'mph_Latn_AU',
		'mpi'	=> 'mpi_Latn_CM',
		'mpj'	=> 'mpj_Latn_AU',
		'mpk'	=> 'mpk_Latn_TD',
		'mpl'	=> 'mpl_Latn_PG',
		'mpm'	=> 'mpm_Latn_MX',
		'mpn'	=> 'mpn_Latn_PG',
		'mpo'	=> 'mpo_Latn_PG',
		'mpp'	=> 'mpp_Latn_PG',
		'mpq'	=> 'mpq_Latn_BR',
		'mpr'	=> 'mpr_Latn_SB',
		'mps'	=> 'mps_Latn_PG',
		'mpt'	=> 'mpt_Latn_PG',
		'mpu'	=> 'mpu_Latn_BR',
		'mpv'	=> 'mpv_Latn_PG',
		'mpw'	=> 'mpw_Latn_BR',
		'mpx'	=> 'mpx_Latn_PG',
		'mpy'	=> 'mpy_Latn_ID',
		'mpz'	=> 'mpz_Thai_TH',
		'mqa'	=> 'mqa_Latn_ID',
		'mqb'	=> 'mqb_Latn_CM',
		'mqc'	=> 'mqc_Latn_ID',
		'mqe'	=> 'mqe_Latn_PG',
		'mqf'	=> 'mqf_Latn_ID',
		'mqg'	=> 'mqg_Latn_ID',
		'mqh'	=> 'mqh_Latn_MX',
		'mqi'	=> 'mqi_Latn_ID',
		'mqj'	=> 'mqj_Latn_ID',
		'mqk'	=> 'mqk_Latn_PH',
		'mql'	=> 'mql_Latn_BJ',
		'mqm'	=> 'mqm_Latn_PF',
		'mqn'	=> 'mqn_Latn_ID',
		'mqo'	=> 'mqo_Latn_ID',
		'mqp'	=> 'mqp_Latn_ID',
		'mqq'	=> 'mqq_Latn_MY',
		'mqr'	=> 'mqr_Latn_ID',
		'mqs'	=> 'mqs_Latn_ID',
		'mqu'	=> 'mqu_Latn_SS',
		'mqv'	=> 'mqv_Latn_PG',
		'mqw'	=> 'mqw_Latn_PG',
		'mqx'	=> 'mqx_Latn_ID',
		'mqy'	=> 'mqy_Latn_ID',
		'mqz'	=> 'mqz_Latn_PG',
		'mra'	=> 'mra_Thai_TH',
		'mrb'	=> 'mrb_Latn_VU',
		'mrc'	=> 'mrc_Latn_US',
		'mrf'	=> 'mrf_Latn_ID',
		'mrg'	=> 'mrg_Latn_IN',
		'mrh'	=> 'mrh_Latn_IN',
		'mrk'	=> 'mrk_Latn_NC',
		'mrl'	=> 'mrl_Latn_FM',
		'mrm'	=> 'mrm_Latn_VU',
		'mrn'	=> 'mrn_Latn_SB',
		'mrp'	=> 'mrp_Latn_VU',
		'mrq'	=> 'mrq_Latn_PF',
		'mrr'	=> 'mrr_Deva_IN',
		'mrs'	=> 'mrs_Latn_VU',
		'mrt'	=> 'mrt_Latn_NG',
		'mru'	=> 'mru_Latn_CM',
		'mrv'	=> 'mrv_Latn_PF',
		'mrw'	=> 'mrw_Latn_PH',
		'mrx'	=> 'mrx_Latn_ID',
		'mry'	=> 'mry_Latn_PH',
		'mrz'	=> 'mrz_Latn_ID',
		'msb'	=> 'msb_Latn_PH',
		'msc'	=> 'msc_Latn_GN',
		'mse'	=> 'mse_Latn_TD',
		'msf'	=> 'msf_Latn_ID',
		'msg'	=> 'msg_Latn_ID',
		'msh'	=> 'msh_Latn_MG',
		'msi'	=> 'msi_Latn_MY',
		'msj'	=> 'msj_Latn_CD',
		'msk'	=> 'msk_Latn_PH',
		'msl'	=> 'msl_Latn_ID',
		'msm'	=> 'msm_Latn_PH',
		'msn'	=> 'msn_Latn_VU',
		'mso'	=> 'mso_Latn_ID',
		'msp'	=> 'msp_Latn_BR',
		'msq'	=> 'msq_Latn_NC',
		'mss'	=> 'mss_Latn_ID',
		'msu'	=> 'msu_Latn_PG',
		'msv'	=> 'msv_Latn_CM',
		'msw'	=> 'msw_Latn_GW',
		'msx'	=> 'msx_Latn_PG',
		'msy'	=> 'msy_Latn_PG',
		'msz'	=> 'msz_Latn_PG',
		'mta'	=> 'mta_Latn_PH',
		'mtb'	=> 'mtb_Latn_CI',
		'mtc'	=> 'mtc_Latn_PG',
		'mtd'	=> 'mtd_Latn_ID',
		'mte'	=> 'mte_Latn_SB',
		'mtf'	=> 'mtf_Latn_PG',
		'mtg'	=> 'mtg_Latn_ID',
		'mth'	=> 'mth_Latn_ID',
		'mti'	=> 'mti_Latn_PG',
		'mtj'	=> 'mtj_Latn_ID',
		'mtk'	=> 'mtk_Latn_CM',
		'mtl'	=> 'mtl_Latn_NG',
		'mtm'	=> 'mtm_Cyrl_RU',
		'mtn'	=> 'mtn_Latn_NI',
		'mto'	=> 'mto_Latn_MX',
		'mtp'	=> 'mtp_Latn_BO',
		'mtq'	=> 'mtq_Latn_VN',
		'mts'	=> 'mts_Latn_PE',
		'mtt'	=> 'mtt_Latn_VU',
		'mtu'	=> 'mtu_Latn_MX',
		'mtv'	=> 'mtv_Latn_PG',
		'mtw'	=> 'mtw_Latn_PH',
		'mtx'	=> 'mtx_Latn_MX',
		'mty'	=> 'mty_Latn_PG',
		'mub'	=> 'mub_Latn_TD',
		'muc'	=> 'muc_Latn_CM',
		'mud'	=> 'mud_Cyrl_RU',
		'mue'	=> 'mue_Latn_EC',
		'mug'	=> 'mug_Latn_CM',
		'muh'	=> 'muh_Latn_SS',
		'mui'	=> 'mui_Latn_ID',
		'muj'	=> 'muj_Latn_TD',
		'muk'	=> 'muk_Tibt_NP',
		'mum'	=> 'mum_Latn_PG',
		'muo'	=> 'muo_Latn_CM',
		'muq'	=> 'muq_Latn_CN',
		'mur'	=> 'mur_Latn_SS',
		'mut'	=> 'mut_Deva_IN',
		'muu'	=> 'muu_Latn_KE',
		'muv'	=> 'muv_Taml_IN',
		'mux'	=> 'mux_Latn_PG',
		'muy'	=> 'muy_Latn_CM',
		'muz'	=> 'muz_Ethi_ET',
		'mva'	=> 'mva_Latn_PG',
		'mvd'	=> 'mvd_Latn_ID',
		'mve'	=> 'mve_Arab_PK',
		'mvf'	=> 'mvf_Mong_CN',
		'mvg'	=> 'mvg_Latn_MX',
		'mvh'	=> 'mvh_Latn_TD',
		'mvk'	=> 'mvk_Latn_PG',
		'mvl'	=> 'mvl_Latn_AU',
		'mvn'	=> 'mvn_Latn_PG',
		'mvo'	=> 'mvo_Latn_SB',
		'mvp'	=> 'mvp_Latn_ID',
		'mvq'	=> 'mvq_Latn_PG',
		'mvr'	=> 'mvr_Latn_ID',
		'mvs'	=> 'mvs_Latn_ID',
		'mvt'	=> 'mvt_Latn_VU',
		'mvu'	=> 'mvu_Latn_TD',
		'mvv'	=> 'mvv_Latn_MY',
		'mvw'	=> 'mvw_Latn_TZ',
		'mvx'	=> 'mvx_Latn_ID',
		'mvz'	=> 'mvz_Ethi_ET',
		'mwa'	=> 'mwa_Latn_PG',
		'mwb'	=> 'mwb_Latn_PG',
		'mwc'	=> 'mwc_Latn_PG',
		'mwe'	=> 'mwe_Latn_TZ',
		'mwf'	=> 'mwf_Latn_AU',
		'mwg'	=> 'mwg_Latn_PG',
		'mwh'	=> 'mwh_Latn_PG',
		'mwi'	=> 'mwi_Latn_VU',
		'mwl'	=> 'mwl_Latn_PT',
		'mwm'	=> 'mwm_Latn_TD',
		'mwn'	=> 'mwn_Latn_ZM',
		'mwo'	=> 'mwo_Latn_VU',
		'mwp'	=> 'mwp_Latn_AU',
		'mwq'	=> 'mwq_Latn_MM',
		'mws'	=> 'mws_Latn_KE',
		'mwt'	=> 'mwt_Mymr_MM',
		'mwu'	=> 'mwu_Latn_SS',
		'mwz'	=> 'mwz_Latn_CD',
		'mxa'	=> 'mxa_Latn_MX',
		'mxb'	=> 'mxb_Latn_MX',
		'mxd'	=> 'mxd_Latn_ID',
		'mxe'	=> 'mxe_Latn_VU',
		'mxf'	=> 'mxf_Latn_CM',
		'mxg'	=> 'mxg_Latn_AO',
		'mxh'	=> 'mxh_Latn_CD',
		'mxi'	=> 'mxi_Latn_ES',
		'mxj'	=> 'mxj_Latn_IN',
		'mxk'	=> 'mxk_Latn_PG',
		'mxl'	=> 'mxl_Latn_BJ',
		'mxm'	=> 'mxm_Latn_PG',
		'mxn'	=> 'mxn_Latn_ID',
		'mxo'	=> 'mxo_Latn_ZM',
		'mxp'	=> 'mxp_Latn_MX',
		'mxq'	=> 'mxq_Latn_MX',
		'mxr'	=> 'mxr_Latn_MY',
		'mxs'	=> 'mxs_Latn_MX',
		'mxt'	=> 'mxt_Latn_MX',
		'mxu'	=> 'mxu_Latn_CM',
		'mxv'	=> 'mxv_Latn_MX',
		'mxw'	=> 'mxw_Latn_PG',
		'mxx'	=> 'mxx_Latn_CI',
		'mxy'	=> 'mxy_Latn_MX',
		'mxz'	=> 'mxz_Latn_ID',
		'myb'	=> 'myb_Latn_TD',
		'myc'	=> 'myc_Latn_CD',
		'mye'	=> 'mye_Latn_GA',
		'myf'	=> 'myf_Latn_ET',
		'myg'	=> 'myg_Latn_CM',
		'myh'	=> 'myh_Latn_US',
		'myj'	=> 'myj_Latn_SS',
		'myk'	=> 'myk_Latn_ML',
		'myl'	=> 'myl_Latn_ID',
		'mym'	=> 'mym_Ethi_ET',
		'myp'	=> 'myp_Latn_BR',
		'myr'	=> 'myr_Latn_PE',
		'myu'	=> 'myu_Latn_BR',
		'myw'	=> 'myw_Latn_PG',
		'myy'	=> 'myy_Latn_CO',
		'mza'	=> 'mza_Latn_MX',
		'mzd'	=> 'mzd_Latn_CM',
		'mze'	=> 'mze_Latn_PG',
		'mzh'	=> 'mzh_Latn_AR',
		'mzi'	=> 'mzi_Latn_MX',
		'mzj'	=> 'mzj_Latn_LR',
		'mzk'	=> 'mzk_Latn_NG',
		'mzl'	=> 'mzl_Latn_MX',
		'mzm'	=> 'mzm_Latn_NG',
		'mzo'	=> 'mzo_Latn_BR',
		'mzp'	=> 'mzp_Latn_BO',
		'mzq'	=> 'mzq_Latn_ID',
		'mzr'	=> 'mzr_Latn_BR',
		'mzt'	=> 'mzt_Latn_MY',
		'mzu'	=> 'mzu_Latn_PG',
		'mzv'	=> 'mzv_Latn_CF',
		'mzw'	=> 'mzw_Latn_GH',
		'mzx'	=> 'mzx_Latn_GY',
		'mzz'	=> 'mzz_Latn_PG',
		'naa'	=> 'naa_Latn_ID',
		'nab'	=> 'nab_Latn_BR',
		'nac'	=> 'nac_Latn_PG',
		'nae'	=> 'nae_Latn_ID',
		'naf'	=> 'naf_Latn_PG',
		'nag'	=> 'nag_Latn_IN',
		'naj'	=> 'naj_Latn_GN',
		'nak'	=> 'nak_Latn_PG',
		'nal'	=> 'nal_Latn_PG',
		'nam'	=> 'nam_Latn_AU',
		'nao'	=> 'nao_Deva_NP',
		'nar'	=> 'nar_Latn_NG',
		'nas'	=> 'nas_Latn_PG',
		'nat'	=> 'nat_Latn_NG',
		'naw'	=> 'naw_Latn_GH',
		'nax'	=> 'nax_Latn_PG',
		'nay'	=> 'nay_Latn_AU',
		'naz'	=> 'naz_Latn_MX',
		'nba'	=> 'nba_Latn_AO',
		'nbb'	=> 'nbb_Latn_NG',
		'nbc'	=> 'nbc_Latn_IN',
		'nbd'	=> 'nbd_Latn_CD',
		'nbe'	=> 'nbe_Latn_IN',
		'nbh'	=> 'nbh_Latn_NG',
		'nbi'	=> 'nbi_Latn_IN',
		'nbj'	=> 'nbj_Latn_AU',
		'nbk'	=> 'nbk_Latn_PG',
		'nbm'	=> 'nbm_Latn_CF',
		'nbn'	=> 'nbn_Latn_ID',
		'nbo'	=> 'nbo_Latn_NG',
		'nbp'	=> 'nbp_Latn_NG',
		'nbq'	=> 'nbq_Latn_ID',
		'nbr'	=> 'nbr_Latn_NG',
		'nbt'	=> 'nbt_Latn_IN',
		'nbu'	=> 'nbu_Latn_IN',
		'nbv'	=> 'nbv_Latn_CM',
		'nbw'	=> 'nbw_Latn_CD',
		'nby'	=> 'nby_Latn_PG',
		'nca'	=> 'nca_Latn_PG',
		'ncb'	=> 'ncb_Latn_IN',
		'ncc'	=> 'ncc_Latn_PG',
		'ncd'	=> 'ncd_Deva_NP',
		'nce'	=> 'nce_Latn_PG',
		'ncf'	=> 'ncf_Latn_PG',
		'ncg'	=> 'ncg_Latn_CA',
		'nci'	=> 'nci_Latn_MX',
		'ncj'	=> 'ncj_Latn_MX',
		'nck'	=> 'nck_Latn_AU',
		'ncl'	=> 'ncl_Latn_MX',
		'ncm'	=> 'ncm_Latn_PG',
		'ncn'	=> 'ncn_Latn_PG',
		'nco'	=> 'nco_Latn_PG',
		'ncq'	=> 'ncq_Laoo_LA',
		'ncr'	=> 'ncr_Latn_CM',
		'nct'	=> 'nct_Latn_IN',
		'ncu'	=> 'ncu_Latn_GH',
		'ncx'	=> 'ncx_Latn_MX',
		'ncz'	=> 'ncz_Latn_US',
		'nda'	=> 'nda_Latn_CG',
		'ndb'	=> 'ndb_Latn_CM',
		'ndd'	=> 'ndd_Latn_NG',
		'ndf'	=> 'ndf_Cyrl_RU',
		'ndg'	=> 'ndg_Latn_TZ',
		'ndh'	=> 'ndh_Latn_TZ',
		'ndi'	=> 'ndi_Latn_NG',
		'ndj'	=> 'ndj_Latn_TZ',
		'ndk'	=> 'ndk_Latn_CD',
		'ndl'	=> 'ndl_Latn_CD',
		'ndm'	=> 'ndm_Latn_TD',
		'ndn'	=> 'ndn_Latn_CG',
		'ndp'	=> 'ndp_Latn_UG',
		'ndq'	=> 'ndq_Latn_AO',
		'ndr'	=> 'ndr_Latn_NG',
		'ndt'	=> 'ndt_Latn_CD',
		'ndu'	=> 'ndu_Latn_CM',
		'ndv'	=> 'ndv_Latn_SN',
		'ndw'	=> 'ndw_Latn_CD',
		'ndx'	=> 'ndx_Latn_ID',
		'ndy'	=> 'ndy_Latn_CF',
		'ndz'	=> 'ndz_Latn_SS',
		'nea'	=> 'nea_Latn_ID',
		'neb'	=> 'neb_Latn_CI',
		'nec'	=> 'nec_Latn_ID',
		'ned'	=> 'ned_Latn_NG',
		'nee'	=> 'nee_Latn_NC',
		'neg'	=> 'neg_Cyrl_RU',
		'neh'	=> 'neh_Tibt_BT',
		'nei'	=> 'nei_Xsux_TR',
		'nej'	=> 'nej_Latn_PG',
		'nek'	=> 'nek_Latn_NC',
		'nem'	=> 'nem_Latn_NC',
		'nen'	=> 'nen_Latn_NC',
		'neo'	=> 'neo_Latn_VN',
		'neq'	=> 'neq_Latn_MX',
		'ner'	=> 'ner_Latn_ID',
		'net'	=> 'net_Latn_PG',
		'neu'	=> 'neu_Latn_001',
		'nex'	=> 'nex_Latn_PG',
		'ney'	=> 'ney_Latn_CI',
		'nez'	=> 'nez_Latn_US',
		'nfa'	=> 'nfa_Latn_ID',
		'nfd'	=> 'nfd_Latn_NG',
		'nfl'	=> 'nfl_Latn_SB',
		'nfr'	=> 'nfr_Latn_GH',
		'nfu'	=> 'nfu_Latn_CM',
		'nga'	=> 'nga_Latn_CD',
		'ngb'	=> 'ngb_Latn_CD',
		'ngc'	=> 'ngc_Latn_CD',
		'ngd'	=> 'ngd_Latn_CF',
		'nge'	=> 'nge_Latn_CM',
		'ngg'	=> 'ngg_Latn_CF',
		'ngh'	=> 'ngh_Latn_ZA',
		'ngi'	=> 'ngi_Latn_NG',
		'ngj'	=> 'ngj_Latn_CM',
		'ngk'	=> 'ngk_Latn_AU',
		'ngm'	=> 'ngm_Latn_FM',
		'ngn'	=> 'ngn_Latn_CM',
		'ngp'	=> 'ngp_Latn_TZ',
		'ngq'	=> 'ngq_Latn_TZ',
		'ngr'	=> 'ngr_Latn_SB',
		'ngs'	=> 'ngs_Latn_NG',
		'ngt'	=> 'ngt_Laoo_LA',
		'ngu'	=> 'ngu_Latn_MX',
		'ngv'	=> 'ngv_Latn_CM',
		'ngw'	=> 'ngw_Latn_NG',
		'ngx'	=> 'ngx_Latn_NG',
		'ngy'	=> 'ngy_Latn_CM',
		'ngz'	=> 'ngz_Latn_CG',
		'nha'	=> 'nha_Latn_AU',
		'nhb'	=> 'nhb_Latn_CI',
		'nhc'	=> 'nhc_Latn_MX',
		'nhd'	=> 'nhd_Latn_PY',
		'nhf'	=> 'nhf_Latn_AU',
		'nhg'	=> 'nhg_Latn_MX',
		'nhi'	=> 'nhi_Latn_MX',
		'nhk'	=> 'nhk_Latn_MX',
		'nhm'	=> 'nhm_Latn_MX',
		'nhn'	=> 'nhn_Latn_MX',
		'nho'	=> 'nho_Latn_PG',
		'nhp'	=> 'nhp_Latn_MX',
		'nhq'	=> 'nhq_Latn_MX',
		'nhr'	=> 'nhr_Latn_BW',
		'nht'	=> 'nht_Latn_MX',
		'nhu'	=> 'nhu_Latn_CM',
		'nhv'	=> 'nhv_Latn_MX',
		'nhx'	=> 'nhx_Latn_MX',
		'nhy'	=> 'nhy_Latn_MX',
		'nhz'	=> 'nhz_Latn_MX',
		'nia'	=> 'nia_Latn_ID',
		'nib'	=> 'nib_Latn_PG',
		'nid'	=> 'nid_Latn_AU',
		'nie'	=> 'nie_Latn_TD',
		'nif'	=> 'nif_Latn_PG',
		'nig'	=> 'nig_Latn_AU',
		'nih'	=> 'nih_Latn_TZ',
		'nii'	=> 'nii_Latn_PG',
		'nil'	=> 'nil_Latn_ID',
		'nim'	=> 'nim_Latn_TZ',
		'nin'	=> 'nin_Latn_NG',
		'nio'	=> 'nio_Cyrl_RU',
		'niq'	=> 'niq_Latn_KE',
		'nir'	=> 'nir_Latn_ID',
		'nis'	=> 'nis_Latn_PG',
		'nit'	=> 'nit_Telu_IN',
		'niv'	=> 'niv_Cyrl_RU',
		'niw'	=> 'niw_Latn_PG',
		'nix'	=> 'nix_Latn_CD',
		'niy'	=> 'niy_Latn_CD',
		'niz'	=> 'niz_Latn_PG',
		'nja'	=> 'nja_Latn_NG',
		'njb'	=> 'njb_Latn_IN',
		'njd'	=> 'njd_Latn_TZ',
		'njh'	=> 'njh_Latn_IN',
		'nji'	=> 'nji_Latn_AU',
		'njj'	=> 'njj_Latn_CM',
		'njl'	=> 'njl_Latn_SS',
		'njm'	=> 'njm_Latn_IN',
		'njn'	=> 'njn_Latn_IN',
		'njr'	=> 'njr_Latn_NG',
		'njs'	=> 'njs_Latn_ID',
		'njt'	=> 'njt_Latn_SR',
		'nju'	=> 'nju_Latn_AU',
		'njx'	=> 'njx_Latn_CG',
		'njy'	=> 'njy_Latn_CM',
		'njz'	=> 'njz_Latn_IN',
		'nka'	=> 'nka_Latn_ZM',
		'nkb'	=> 'nkb_Latn_IN',
		'nkc'	=> 'nkc_Latn_CM',
		'nkd'	=> 'nkd_Latn_IN',
		'nke'	=> 'nke_Latn_SB',
		'nkf'	=> 'nkf_Latn_IN',
		'nkg'	=> 'nkg_Latn_PG',
		'nkh'	=> 'nkh_Latn_IN',
		'nki'	=> 'nki_Latn_IN',
		'nkj'	=> 'nkj_Latn_ID',
		'nkk'	=> 'nkk_Latn_VU',
		'nkm'	=> 'nkm_Latn_PG',
		'nkn'	=> 'nkn_Latn_AO',
		'nko'	=> 'nko_Latn_GH',
		'nkq'	=> 'nkq_Latn_GH',
		'nkr'	=> 'nkr_Latn_FM',
		'nks'	=> 'nks_Latn_ID',
		'nkt'	=> 'nkt_Latn_TZ',
		'nku'	=> 'nku_Latn_CI',
		'nkv'	=> 'nkv_Latn_MW',
		'nkw'	=> 'nkw_Latn_CD',
		'nkx'	=> 'nkx_Latn_NG',
		'nkz'	=> 'nkz_Latn_NG',
		'nla'	=> 'nla_Latn_CM',
		'nlc'	=> 'nlc_Latn_ID',
		'nle'	=> 'nle_Latn_KE',
		'nlg'	=> 'nlg_Latn_SB',
		'nli'	=> 'nli_Arab_AF',
		'nlj'	=> 'nlj_Latn_CD',
		'nlk'	=> 'nlk_Latn_ID',
		'nlm'	=> 'nlm_Arab_PK',
		'nlo'	=> 'nlo_Latn_CD',
		'nlq'	=> 'nlq_Latn_MM',
		'nlu'	=> 'nlu_Latn_GH',
		'nlv'	=> 'nlv_Latn_MX',
		'nlw'	=> 'nlw_Latn_AU',
		'nlx'	=> 'nlx_Deva_IN',
		'nly'	=> 'nly_Latn_AU',
		'nlz'	=> 'nlz_Latn_SB',
		'nma'	=> 'nma_Latn_IN',
		'nmb'	=> 'nmb_Latn_VU',
		'nmc'	=> 'nmc_Latn_TD',
		'nmd'	=> 'nmd_Latn_GA',
		'nme'	=> 'nme_Latn_IN',
		'nmf'	=> 'nmf_Latn_IN',
		'nmh'	=> 'nmh_Latn_IN',
		'nmi'	=> 'nmi_Latn_NG',
		'nmj'	=> 'nmj_Latn_CF',
		'nmk'	=> 'nmk_Latn_VU',
		'nml'	=> 'nml_Latn_CM',
		'nmm'	=> 'nmm_Deva_NP',
		'nmn'	=> 'nmn_Latn_BW',
		'nmo'	=> 'nmo_Latn_IN',
		'nmp'	=> 'nmp_Latn_AU',
		'nmq'	=> 'nmq_Latn_ZW',
		'nmr'	=> 'nmr_Latn_CM',
		'nms'	=> 'nms_Latn_VU',
		'nmt'	=> 'nmt_Latn_FM',
		'nmu'	=> 'nmu_Latn_US',
		'nmv'	=> 'nmv_Latn_AU',
		'nmw'	=> 'nmw_Latn_PG',
		'nmx'	=> 'nmx_Latn_PG',
		'nmz'	=> 'nmz_Latn_TG',
		'nna'	=> 'nna_Latn_AU',
		'nnb'	=> 'nnb_Latn_CD',
		'nnc'	=> 'nnc_Latn_TD',
		'nnd'	=> 'nnd_Latn_VU',
		'nne'	=> 'nne_Latn_AO',
		'nnf'	=> 'nnf_Latn_PG',
		'nng'	=> 'nng_Latn_IN',
		'nni'	=> 'nni_Latn_ID',
		'nnj'	=> 'nnj_Latn_ET',
		'nnk'	=> 'nnk_Latn_PG',
		'nnl'	=> 'nnl_Latn_IN',
		'nnm'	=> 'nnm_Latn_PG',
		'nnn'	=> 'nnn_Latn_TD',
		'nnq'	=> 'nnq_Latn_TZ',
		'nnr'	=> 'nnr_Latn_AU',
		'nnt'	=> 'nnt_Latn_US',
		'nnu'	=> 'nnu_Latn_GH',
		'nnv'	=> 'nnv_Latn_AU',
		'nnw'	=> 'nnw_Latn_BF',
		'nny'	=> 'nny_Latn_AU',
		'nnz'	=> 'nnz_Latn_CM',
		'noa'	=> 'noa_Latn_CO',
		'noc'	=> 'noc_Latn_PG',
		'nof'	=> 'nof_Latn_PG',
		'nog'	=> 'nog_Cyrl_RU',
		'noh'	=> 'noh_Latn_PG',
		'noi'	=> 'noi_Deva_IN',
		'noj'	=> 'noj_Latn_CO',
		'nok'	=> 'nok_Latn_US',
		'nop'	=> 'nop_Latn_PG',
		'noq'	=> 'noq_Latn_CD',
		'nos'	=> 'nos_Yiii_CN',
		'not'	=> 'not_Latn_PE',
		'nou'	=> 'nou_Latn_PG',
		'nov'	=> 'nov_Latn_001',
		'now'	=> 'now_Latn_TZ',
		'noy'	=> 'noy_Latn_TD',
		'npb'	=> 'npb_Tibt_BT',
		'npg'	=> 'npg_Latn_MM',
		'nph'	=> 'nph_Latn_IN',
		'npl'	=> 'npl_Latn_MX',
		'npn'	=> 'npn_Latn_PG',
		'npo'	=> 'npo_Latn_IN',
		'nps'	=> 'nps_Latn_ID',
		'npu'	=> 'npu_Latn_IN',
		'npx'	=> 'npx_Latn_SB',
		'npy'	=> 'npy_Latn_ID',
		'nqg'	=> 'nqg_Latn_BJ',
		'nqk'	=> 'nqk_Latn_BJ',
		'nql'	=> 'nql_Latn_AO',
		'nqm'	=> 'nqm_Latn_ID',
		'nqn'	=> 'nqn_Latn_PG',
		'nqq'	=> 'nqq_Latn_MM',
		'nqt'	=> 'nqt_Latn_NG',
		'nqy'	=> 'nqy_Latn_MM',
		'nra'	=> 'nra_Latn_GA',
		'nrb'	=> 'nrb_Latn_ER',
		'nre'	=> 'nre_Latn_IN',
		'nrf'	=> 'nrf_Latn_JE',
		'nrg'	=> 'nrg_Latn_VU',
		'nri'	=> 'nri_Latn_IN',
		'nrk'	=> 'nrk_Latn_AU',
		'nrl'	=> 'nrl_Latn_AU',
		'nrm'	=> 'nrm_Latn_MY',
		'nrn'	=> 'nrn_Runr_GB',
		'nrp'	=> 'nrp_Latn_IT',
		'nru'	=> 'nru_Latn_CN',
		'nrx'	=> 'nrx_Latn_AU',
		'nrz'	=> 'nrz_Latn_PG',
		'nsa'	=> 'nsa_Latn_IN',
		'nsb'	=> 'nsb_Latn_ZA',
		'nsc'	=> 'nsc_Latn_NG',
		'nsd'	=> 'nsd_Yiii_CN',
		'nse'	=> 'nse_Latn_ZM',
		'nsf'	=> 'nsf_Yiii_CN',
		'nsg'	=> 'nsg_Latn_TZ',
		'nsh'	=> 'nsh_Latn_CM',
		'nsm'	=> 'nsm_Latn_IN',
		'nsn'	=> 'nsn_Latn_PG',
		'nsq'	=> 'nsq_Latn_US',
		'nss'	=> 'nss_Latn_PG',
		'nsu'	=> 'nsu_Latn_MX',
		'nsv'	=> 'nsv_Yiii_CN',
		'nsw'	=> 'nsw_Latn_VU',
		'nsx'	=> 'nsx_Latn_AO',
		'nsy'	=> 'nsy_Latn_ID',
		'nsz'	=> 'nsz_Latn_US',
		'ntd'	=> 'ntd_Latn_MY',
		'nte'	=> 'nte_Latn_MZ',
		'ntg'	=> 'ntg_Latn_AU',
		'nti'	=> 'nti_Latn_BF',
		'ntj'	=> 'ntj_Latn_AU',
		'ntk'	=> 'ntk_Latn_TZ',
		'ntm'	=> 'ntm_Latn_BJ',
		'nto'	=> 'nto_Latn_CD',
		'ntp'	=> 'ntp_Latn_MX',
		'ntr'	=> 'ntr_Latn_GH',
		'ntu'	=> 'ntu_Latn_SB',
		'ntx'	=> 'ntx_Latn_MM',
		'nty'	=> 'nty_Yiii_VN',
		'ntz'	=> 'ntz_Arab_IR',
		'nua'	=> 'nua_Latn_NC',
		'nuc'	=> 'nuc_Latn_BR',
		'nud'	=> 'nud_Latn_PG',
		'nue'	=> 'nue_Latn_CD',
		'nuf'	=> 'nuf_Latn_CN',
		'nug'	=> 'nug_Latn_AU',
		'nuh'	=> 'nuh_Latn_NG',
		'nui'	=> 'nui_Latn_GQ',
		'nuj'	=> 'nuj_Latn_UG',
		'nuk'	=> 'nuk_Latn_CA',
		'num'	=> 'num_Latn_TO',
		'nun'	=> 'nun_Latn_MM',
		'nuo'	=> 'nuo_Latn_VN',
		'nup'	=> 'nup_Latn_NG',
		'nuq'	=> 'nuq_Latn_PG',
		'nur'	=> 'nur_Latn_PG',
		'nut'	=> 'nut_Latn_VN',
		'nuu'	=> 'nuu_Latn_CD',
		'nuv'	=> 'nuv_Latn_BF',
		'nuw'	=> 'nuw_Latn_FM',
		'nux'	=> 'nux_Latn_PG',
		'nuy'	=> 'nuy_Latn_AU',
		'nuz'	=> 'nuz_Latn_MX',
		'nvh'	=> 'nvh_Latn_VU',
		'nvm'	=> 'nvm_Latn_PG',
		'nvo'	=> 'nvo_Latn_CM',
		'nwb'	=> 'nwb_Latn_CI',
		'nwc'	=> 'nwc_Newa_NP',
		'nwe'	=> 'nwe_Latn_CM',
		'nwg'	=> 'nwg_Latn_AU',
		'nwi'	=> 'nwi_Latn_VU',
		'nwm'	=> 'nwm_Latn_SS',
		'nwo'	=> 'nwo_Latn_AU',
		'nwr'	=> 'nwr_Latn_PG',
		'nww'	=> 'nww_Latn_TZ',
		'nwx'	=> 'nwx_Deva_NP',
		'nxa'	=> 'nxa_Latn_TL',
		'nxd'	=> 'nxd_Latn_CD',
		'nxe'	=> 'nxe_Latn_ID',
		'nxg'	=> 'nxg_Latn_ID',
		'nxi'	=> 'nxi_Latn_TZ',
		'nxl'	=> 'nxl_Latn_ID',
		'nxn'	=> 'nxn_Latn_AU',
		'nxo'	=> 'nxo_Latn_GA',
		'nxr'	=> 'nxr_Latn_PG',
		'nxx'	=> 'nxx_Latn_ID',
		'nyb'	=> 'nyb_Latn_GH',
		'nyc'	=> 'nyc_Latn_CD',
		'nyd'	=> 'nyd_Latn_KE',
		'nye'	=> 'nye_Latn_AO',
		'nyf'	=> 'nyf_Latn_KE',
		'nyg'	=> 'nyg_Latn_CD',
		'nyh'	=> 'nyh_Latn_AU',
		'nyi'	=> 'nyi_Latn_SD',
		'nyj'	=> 'nyj_Latn_CD',
		'nyk'	=> 'nyk_Latn_AO',
		'nyl'	=> 'nyl_Thai_TH',
		'nyo'	=> 'nyo_Latn_UG',
		'nyp'	=> 'nyp_Latn_UG',
		'nyq'	=> 'nyq_Arab_IR',
		'nyr'	=> 'nyr_Latn_MW',
		'nys'	=> 'nys_Latn_AU',
		'nyt'	=> 'nyt_Latn_AU',
		'nyu'	=> 'nyu_Latn_MZ',
		'nyv'	=> 'nyv_Latn_AU',
		'nyw'	=> 'nyw_Thai_TH',
		'nyx'	=> 'nyx_Latn_AU',
		'nyy'	=> 'nyy_Latn_TZ',
		'nza'	=> 'nza_Latn_CM',
		'nzb'	=> 'nzb_Latn_GA',
		'nzd'	=> 'nzd_Latn_CD',
		'nzk'	=> 'nzk_Latn_CF',
		'nzm'	=> 'nzm_Latn_IN',
		'nzr'	=> 'nzr_Latn_NG',
		'nzu'	=> 'nzu_Latn_CG',
		'nzy'	=> 'nzy_Latn_TD',
		'nzz'	=> 'nzz_Latn_ML',
		'oaa'	=> 'oaa_Cyrl_RU',
		'oac'	=> 'oac_Cyrl_RU',
		'oar'	=> 'oar_Syrc_SY',
		'oav'	=> 'oav_Geor_GE',
		'obi'	=> 'obi_Latn_US',
		'obk'	=> 'obk_Latn_PH',
		'obl'	=> 'obl_Latn_CM',
		'obm'	=> 'obm_Phnx_JO',
		'obo'	=> 'obo_Latn_PH',
		'obr'	=> 'obr_Mymr_MM',
		'obt'	=> 'obt_Latn_FR',
		'obu'	=> 'obu_Latn_NG',
		'oca'	=> 'oca_Latn_PE',
		'oco'	=> 'oco_Latn_GB',
		'ocu'	=> 'ocu_Latn_MX',
		'oda'	=> 'oda_Latn_NG',
		'odk'	=> 'odk_Arab_PK',
		'odt'	=> 'odt_Latn_NL',
		'odu'	=> 'odu_Latn_NG',
		'ofs'	=> 'ofs_Latn_NL',
		'ofu'	=> 'ofu_Latn_NG',
		'ogb'	=> 'ogb_Latn_NG',
		'ogc'	=> 'ogc_Latn_NG',
		'ogg'	=> 'ogg_Latn_NG',
		'ogo'	=> 'ogo_Latn_NG',
		'ogu'	=> 'ogu_Latn_NG',
		'oht'	=> 'oht_Xsux_TR',
		'ohu'	=> 'ohu_Latn_HU',
		'oia'	=> 'oia_Latn_ID',
		'oie'	=> 'oie_Latn_SS',
		'oin'	=> 'oin_Latn_PG',
		'ojb'	=> 'ojb_Latn_CA',
		'ojc'	=> 'ojc_Latn_CA',
		'ojv'	=> 'ojv_Latn_SB',
		'ojw'	=> 'ojw_Latn_CA',
		'okb'	=> 'okb_Latn_NG',
		'okc'	=> 'okc_Latn_CD',
		'okd'	=> 'okd_Latn_NG',
		'oke'	=> 'oke_Latn_NG',
		'okg'	=> 'okg_Latn_AU',
		'oki'	=> 'oki_Latn_KE',
		'okk'	=> 'okk_Latn_PG',
		'okm'	=> 'okm_Hang_KR',
		'oko'	=> 'oko_Hani_KR',
		'okr'	=> 'okr_Latn_NG',
		'oks'	=> 'oks_Latn_NG',
		'oku'	=> 'oku_Latn_CM',
		'okv'	=> 'okv_Latn_PG',
		'okx'	=> 'okx_Latn_NG',
		'okz'	=> 'okz_Khmr_KH',
		'ola'	=> 'ola_Deva_NP',
		'old'	=> 'old_Latn_TZ',
		'ole'	=> 'ole_Tibt_BT',
		'olk'	=> 'olk_Latn_AU',
		'olm'	=> 'olm_Latn_NG',
		'olo'	=> 'olo_Latn_RU',
		'olr'	=> 'olr_Latn_VU',
		'olt'	=> 'olt_Latn_LT',
		'olu'	=> 'olu_Latn_AO',
		'oma'	=> 'oma_Latn_US',
		'omb'	=> 'omb_Latn_VU',
		'omc'	=> 'omc_Latn_PE',
		'omg'	=> 'omg_Latn_PE',
		'omi'	=> 'omi_Latn_CD',
		'omk'	=> 'omk_Cyrl_RU',
		'oml'	=> 'oml_Latn_CD',
		'omo'	=> 'omo_Latn_PG',
		'omp'	=> 'omp_Mtei_IN',
		'omr'	=> 'omr_Modi_IN',
		'omt'	=> 'omt_Latn_KE',
		'omu'	=> 'omu_Latn_PE',
		'omw'	=> 'omw_Latn_PG',
		'omx'	=> 'omx_Mymr_MM',
		'ona'	=> 'ona_Latn_AR',
		'one'	=> 'one_Latn_CA',
		'ong'	=> 'ong_Latn_PG',
		'oni'	=> 'oni_Latn_ID',
		'onj'	=> 'onj_Latn_PG',
		'onk'	=> 'onk_Latn_PG',
		'onn'	=> 'onn_Latn_PG',
		'ono'	=> 'ono_Latn_CA',
		'onp'	=> 'onp_Latn_IN',
		'onr'	=> 'onr_Latn_PG',
		'ons'	=> 'ons_Latn_PG',
		'ont'	=> 'ont_Latn_PG',
		'onu'	=> 'onu_Latn_VU',
		'onx'	=> 'onx_Latn_ID',
		'ood'	=> 'ood_Latn_US',
		'oon'	=> 'oon_Deva_IN',
		'oor'	=> 'oor_Latn_ZA',
		'opa'	=> 'opa_Latn_NG',
		'opk'	=> 'opk_Latn_ID',
		'opm'	=> 'opm_Latn_PG',
		'opo'	=> 'opo_Latn_PG',
		'opt'	=> 'opt_Latn_MX',
		'opy'	=> 'opy_Latn_BR',
		'ora'	=> 'ora_Latn_SB',
		'orc'	=> 'orc_Latn_KE',
		'ore'	=> 'ore_Latn_PE',
		'org'	=> 'org_Latn_NG',
		'orn'	=> 'orn_Latn_MY',
		'oro'	=> 'oro_Latn_PG',
		'orr'	=> 'orr_Latn_NG',
		'ors'	=> 'ors_Latn_MY',
		'ort'	=> 'ort_Telu_IN',
		'oru'	=> 'oru_Arab_PK',
		'orv'	=> 'orv_Cyrl_RU',
		'orw'	=> 'orw_Latn_BR',
		'orx'	=> 'orx_Latn_NG',
		'orz'	=> 'orz_Latn_ID',
		'osc'	=> 'osc_Ital_IT',
		'osi'	=> 'osi_Java_ID',
		'oso'	=> 'oso_Latn_NG',
		'osp'	=> 'osp_Latn_ES',
		'ost'	=> 'ost_Latn_CM',
		'osu'	=> 'osu_Latn_PG',
		'osx'	=> 'osx_Latn_DE',
		'ota'	=> 'ota_Arab_TR',
		'otb'	=> 'otb_Tibt_CN',
		'otd'	=> 'otd_Latn_ID',
		'ote'	=> 'ote_Latn_MX',
		'oti'	=> 'oti_Latn_BR',
		'otl'	=> 'otl_Latn_MX',
		'otm'	=> 'otm_Latn_MX',
		'otn'	=> 'otn_Latn_MX',
		'otq'	=> 'otq_Latn_MX',
		'otr'	=> 'otr_Latn_SD',
		'ots'	=> 'ots_Latn_MX',
		'ott'	=> 'ott_Latn_MX',
		'otu'	=> 'otu_Latn_BR',
		'otw'	=> 'otw_Latn_CA',
		'otx'	=> 'otx_Latn_MX',
		'oty'	=> 'oty_Gran_IN',
		'otz'	=> 'otz_Latn_MX',
		'oub'	=> 'oub_Latn_LR',
		'oue'	=> 'oue_Latn_PG',
		'oum'	=> 'oum_Latn_PG',
		'ovd'	=> 'ovd_Latn_SE',
		'owi'	=> 'owi_Latn_PG',
		'owl'	=> 'owl_Latn_GB',
		'oyd'	=> 'oyd_Latn_ET',
		'oym'	=> 'oym_Latn_BR',
		'oyy'	=> 'oyy_Latn_PG',
		'ozm'	=> 'ozm_Latn_CM',
		'pab'	=> 'pab_Latn_BR',
		'pac'	=> 'pac_Latn_VN',
		'pad'	=> 'pad_Latn_BR',
		'pae'	=> 'pae_Latn_CD',
		'paf'	=> 'paf_Latn_BR',
		'pah'	=> 'pah_Latn_BR',
		'pai'	=> 'pai_Latn_NG',
		'pak'	=> 'pak_Latn_BR',
		'pao'	=> 'pao_Latn_US',
		'paq'	=> 'paq_Cyrl_TJ',
		'par'	=> 'par_Latn_US',
		'pas'	=> 'pas_Latn_ID',
		'pav'	=> 'pav_Latn_BR',
		'paw'	=> 'paw_Latn_US',
		'pax'	=> 'pax_Latn_BR',
		'pay'	=> 'pay_Latn_HN',
		'paz'	=> 'paz_Latn_BR',
		'pbb'	=> 'pbb_Latn_CO',
		'pbc'	=> 'pbc_Latn_GY',
		'pbe'	=> 'pbe_Latn_MX',
		'pbf'	=> 'pbf_Latn_MX',
		'pbg'	=> 'pbg_Latn_VE',
		'pbh'	=> 'pbh_Latn_VE',
		'pbi'	=> 'pbi_Latn_CM',
		'pbl'	=> 'pbl_Latn_NG',
		'pbm'	=> 'pbm_Latn_MX',
		'pbn'	=> 'pbn_Latn_NG',
		'pbo'	=> 'pbo_Latn_GW',
		'pbp'	=> 'pbp_Latn_GN',
		'pbr'	=> 'pbr_Latn_TZ',
		'pbs'	=> 'pbs_Latn_MX',
		'pbt'	=> 'pbt_Arab_AF',
		'pbv'	=> 'pbv_Latn_IN',
		'pby'	=> 'pby_Latn_PG',
		'pca'	=> 'pca_Latn_MX',
		'pcb'	=> 'pcb_Khmr_KH',
		'pcc'	=> 'pcc_Latn_CN',
		'pce'	=> 'pce_Mymr_MM',
		'pcf'	=> 'pcf_Mlym_IN',
		'pcg'	=> 'pcg_Mlym_IN',
		'pch'	=> 'pch_Deva_IN',
		'pci'	=> 'pci_Deva_IN',
		'pcj'	=> 'pcj_Telu_IN',
		'pck'	=> 'pck_Latn_IN',
		'pcn'	=> 'pcn_Latn_NG',
		'pcp'	=> 'pcp_Latn_BO',
		'pcw'	=> 'pcw_Latn_NG',
		'pda'	=> 'pda_Latn_PG',
		'pdn'	=> 'pdn_Latn_ID',
		'pdo'	=> 'pdo_Latn_ID',
		'pdu'	=> 'pdu_Latn_MM',
		'pea'	=> 'pea_Latn_ID',
		'peb'	=> 'peb_Latn_US',
		'ped'	=> 'ped_Latn_PG',
		'pee'	=> 'pee_Latn_ID',
		'peg'	=> 'peg_Orya_IN',
		'pei'	=> 'pei_Latn_MX',
		'pek'	=> 'pek_Latn_PG',
		'pel'	=> 'pel_Latn_ID',
		'pem'	=> 'pem_Latn_CD',
		'pep'	=> 'pep_Latn_PG',
		'peq'	=> 'peq_Latn_US',
		'pev'	=> 'pev_Latn_VE',
		'pex'	=> 'pex_Latn_PG',
		'pey'	=> 'pey_Latn_ID',
		'pez'	=> 'pez_Latn_MY',
		'pfa'	=> 'pfa_Latn_FM',
		'pfe'	=> 'pfe_Latn_CM',
		'pga'	=> 'pga_Latn_SS',
		'pgd'	=> 'pgd_Khar_PK',
		'pgg'	=> 'pgg_Deva_IN',
		'pgi'	=> 'pgi_Latn_PG',
		'pgk'	=> 'pgk_Latn_VU',
		'pgl'	=> 'pgl_Ogam_IE',
		'pgn'	=> 'pgn_Ital_IT',
		'pgs'	=> 'pgs_Latn_NG',
		'pgu'	=> 'pgu_Latn_ID',
		'phd'	=> 'phd_Deva_IN',
		'phg'	=> 'phg_Latn_VN',
		'phh'	=> 'phh_Latn_VN',
		'phk'	=> 'phk_Mymr_IN',
		'phl'	=> 'phl_Arab_PK',
		'phm'	=> 'phm_Latn_MZ',
		'pho'	=> 'pho_Laoo_LA',
		'phr'	=> 'phr_Arab_PK',
		'pht'	=> 'pht_Thai_TH',
		'phu'	=> 'phu_Thai_TH',
		'phv'	=> 'phv_Arab_AF',
		'phw'	=> 'phw_Deva_NP',
		'pi'	=> 'pi_Sinh_IN',
		'pia'	=> 'pia_Latn_MX',
		'pib'	=> 'pib_Latn_PE',
		'pic'	=> 'pic_Latn_GA',
		'pid'	=> 'pid_Latn_VE',
		'pif'	=> 'pif_Latn_FM',
		'pig'	=> 'pig_Latn_PE',
		'pih'	=> 'pih_Latn_NF',
		'pij'	=> 'pij_Latn_CO',
		'pil'	=> 'pil_Latn_BJ',
		'pim'	=> 'pim_Latn_US',
		'pin'	=> 'pin_Latn_PG',
		'pio'	=> 'pio_Latn_CO',
		'pip'	=> 'pip_Latn_NG',
		'pir'	=> 'pir_Latn_BR',
		'pit'	=> 'pit_Latn_AU',
		'piu'	=> 'piu_Latn_AU',
		'piv'	=> 'piv_Latn_SB',
		'piw'	=> 'piw_Latn_TZ',
		'pix'	=> 'pix_Latn_PG',
		'piy'	=> 'piy_Latn_NG',
		'piz'	=> 'piz_Latn_NC',
		'pjt'	=> 'pjt_Latn_AU',
		'pkb'	=> 'pkb_Latn_KE',
		'pkg'	=> 'pkg_Latn_PG',
		'pkh'	=> 'pkh_Latn_BD',
		'pkn'	=> 'pkn_Latn_AU',
		'pkp'	=> 'pkp_Latn_CK',
		'pkr'	=> 'pkr_Mlym_IN',
		'pku'	=> 'pku_Latn_ID',
		'pla'	=> 'pla_Latn_PG',
		'plb'	=> 'plb_Latn_VU',
		'plc'	=> 'plc_Latn_PH',
		'pld'	=> 'pld_Latn_GB',
		'ple'	=> 'ple_Latn_ID',
		'plg'	=> 'plg_Latn_AR',
		'plh'	=> 'plh_Latn_ID',
		'plk'	=> 'plk_Arab_PK',
		'pll'	=> 'pll_Mymr_MM',
		'pln'	=> 'pln_Latn_CO',
		'plo'	=> 'plo_Latn_MX',
		'plr'	=> 'plr_Latn_CI',
		'pls'	=> 'pls_Latn_MX',
		'plu'	=> 'plu_Latn_BR',
		'plv'	=> 'plv_Latn_PH',
		'plw'	=> 'plw_Latn_PH',
		'plz'	=> 'plz_Latn_MY',
		'pma'	=> 'pma_Latn_VU',
		'pmb'	=> 'pmb_Latn_CD',
		'pmd'	=> 'pmd_Latn_AU',
		'pme'	=> 'pme_Latn_NC',
		'pmf'	=> 'pmf_Latn_ID',
		'pmh'	=> 'pmh_Brah_IN',
		'pmi'	=> 'pmi_Latn_CN',
		'pmj'	=> 'pmj_Latn_CN',
		'pml'	=> 'pml_Latn_TN',
		'pmm'	=> 'pmm_Latn_CM',
		'pmn'	=> 'pmn_Latn_CM',
		'pmo'	=> 'pmo_Latn_ID',
		'pmq'	=> 'pmq_Latn_MX',
		'pmr'	=> 'pmr_Latn_PG',
		'pmt'	=> 'pmt_Latn_PF',
		'pmw'	=> 'pmw_Latn_US',
		'pmx'	=> 'pmx_Latn_IN',
		'pmy'	=> 'pmy_Latn_ID',
		'pmz'	=> 'pmz_Latn_MX',
		'pna'	=> 'pna_Latn_MY',
		'pnc'	=> 'pnc_Latn_ID',
		'pnd'	=> 'pnd_Latn_AO',
		'pne'	=> 'pne_Latn_MY',
		'png'	=> 'png_Latn_NG',
		'pnh'	=> 'pnh_Latn_CK',
		'pni'	=> 'pni_Latn_ID',
		'pnj'	=> 'pnj_Latn_AU',
		'pnk'	=> 'pnk_Latn_BO',
		'pnl'	=> 'pnl_Latn_BF',
		'pnm'	=> 'pnm_Latn_MY',
		'pnn'	=> 'pnn_Latn_PG',
		'pno'	=> 'pno_Latn_PE',
		'pnp'	=> 'pnp_Latn_ID',
		'pnq'	=> 'pnq_Latn_BF',
		'pnr'	=> 'pnr_Latn_PG',
		'pns'	=> 'pns_Latn_ID',
		'pnv'	=> 'pnv_Latn_AU',
		'pnw'	=> 'pnw_Latn_AU',
		'pny'	=> 'pny_Latn_CM',
		'pnz'	=> 'pnz_Latn_CF',
		'poc'	=> 'poc_Latn_GT',
		'poe'	=> 'poe_Latn_MX',
		'pof'	=> 'pof_Latn_CD',
		'pog'	=> 'pog_Latn_BR',
		'poh'	=> 'poh_Latn_GT',
		'poi'	=> 'poi_Latn_MX',
		'pok'	=> 'pok_Latn_BR',
		'pom'	=> 'pom_Latn_US',
		'poo'	=> 'poo_Latn_US',
		'pop'	=> 'pop_Latn_NC',
		'poq'	=> 'poq_Latn_MX',
		'pos'	=> 'pos_Latn_MX',
		'pot'	=> 'pot_Latn_US',
		'pov'	=> 'pov_Latn_GW',
		'pow'	=> 'pow_Latn_MX',
		'poy'	=> 'poy_Latn_TZ',
		'ppe'	=> 'ppe_Latn_PG',
		'ppi'	=> 'ppi_Latn_MX',
		'ppk'	=> 'ppk_Latn_ID',
		'ppl'	=> 'ppl_Latn_SV',
		'ppm'	=> 'ppm_Latn_ID',
		'ppn'	=> 'ppn_Latn_PG',
		'ppo'	=> 'ppo_Latn_PG',
		'ppp'	=> 'ppp_Latn_CD',
		'ppq'	=> 'ppq_Latn_PG',
		'pps'	=> 'pps_Latn_MX',
		'ppt'	=> 'ppt_Latn_PG',
		'pqa'	=> 'pqa_Latn_NG',
		'prc'	=> 'prc_Arab_AF',
		'pre'	=> 'pre_Latn_ST',
		'prf'	=> 'prf_Latn_PH',
		'prh'	=> 'prh_Latn_PH',
		'pri'	=> 'pri_Latn_NC',
		'prk'	=> 'prk_Latn_MM',
		'prm'	=> 'prm_Latn_PG',
		'pro'	=> 'pro_Latn_FR',
		'prq'	=> 'prq_Latn_PE',
		'prr'	=> 'prr_Latn_BR',
		'prt'	=> 'prt_Thai_TH',
		'pru'	=> 'pru_Latn_ID',
		'prw'	=> 'prw_Latn_PG',
		'prx'	=> 'prx_Arab_IN',
		'psa'	=> 'psa_Latn_ID',
		'pse'	=> 'pse_Latn_ID',
		'psh'	=> 'psh_Arab_AF',
		'psi'	=> 'psi_Arab_AF',
		'psm'	=> 'psm_Latn_BO',
		'psn'	=> 'psn_Latn_ID',
		'psq'	=> 'psq_Latn_PG',
		'pss'	=> 'pss_Latn_PG',
		'pst'	=> 'pst_Arab_PK',
		'psu'	=> 'psu_Brah_IN',
		'psw'	=> 'psw_Latn_VU',
		'pta'	=> 'pta_Latn_PY',
		'pth'	=> 'pth_Latn_BR',
		'pti'	=> 'pti_Latn_AU',
		'ptn'	=> 'ptn_Latn_ID',
		'pto'	=> 'pto_Latn_BR',
		'ptp'	=> 'ptp_Latn_PG',
		'ptr'	=> 'ptr_Latn_VU',
		'ptt'	=> 'ptt_Latn_ID',
		'ptu'	=> 'ptu_Latn_ID',
		'ptv'	=> 'ptv_Latn_VU',
		'pua'	=> 'pua_Latn_MX',
		'pub'	=> 'pub_Latn_IN',
		'puc'	=> 'puc_Latn_ID',
		'pud'	=> 'pud_Latn_ID',
		'pue'	=> 'pue_Latn_AR',
		'puf'	=> 'puf_Latn_ID',
		'pug'	=> 'pug_Latn_BF',
		'pui'	=> 'pui_Latn_CO',
		'puj'	=> 'puj_Latn_ID',
		'pum'	=> 'pum_Deva_NP',
		'puo'	=> 'puo_Latn_VN',
		'pup'	=> 'pup_Latn_PG',
		'puq'	=> 'puq_Latn_BO',
		'pur'	=> 'pur_Latn_BR',
		'put'	=> 'put_Latn_ID',
		'puw'	=> 'puw_Latn_FM',
		'pux'	=> 'pux_Latn_PG',
		'puy'	=> 'puy_Latn_US',
		'pwa'	=> 'pwa_Latn_PG',
		'pwb'	=> 'pwb_Latn_NG',
		'pwg'	=> 'pwg_Latn_PG',
		'pwm'	=> 'pwm_Latn_PH',
		'pwn'	=> 'pwn_Latn_TW',
		'pwo'	=> 'pwo_Mymr_MM',
		'pwr'	=> 'pwr_Deva_IN',
		'pww'	=> 'pww_Thai_TH',
		'pxm'	=> 'pxm_Latn_MX',
		'pye'	=> 'pye_Latn_CI',
		'pym'	=> 'pym_Latn_NG',
		'pyn'	=> 'pyn_Latn_BR',
		'pyu'	=> 'pyu_Latn_TW',
		'pyx'	=> 'pyx_Mymr_MM',
		'pyy'	=> 'pyy_Latn_MM',
		'pze'	=> 'pze_Latn_NG',
		'pzh'	=> 'pzh_Latn_TW',
		'pzn'	=> 'pzn_Latn_MM',
		'qua'	=> 'qua_Latn_US',
		'qub'	=> 'qub_Latn_PE',
		'qud'	=> 'qud_Latn_EC',
		'quf'	=> 'quf_Latn_PE',
		'qui'	=> 'qui_Latn_US',
		'quk'	=> 'quk_Latn_PE',
		'qul'	=> 'qul_Latn_BO',
		'qum'	=> 'qum_Latn_GT',
		'qun'	=> 'qun_Latn_US',
		'qup'	=> 'qup_Latn_PE',
		'quq'	=> 'quq_Latn_ES',
		'qur'	=> 'qur_Latn_PE',
		'qus'	=> 'qus_Latn_AR',
		'quv'	=> 'quv_Latn_GT',
		'quw'	=> 'quw_Latn_EC',
		'qux'	=> 'qux_Latn_PE',
		'quy'	=> 'quy_Latn_PE',
		'qva'	=> 'qva_Latn_PE',
		'qvc'	=> 'qvc_Latn_PE',
		'qve'	=> 'qve_Latn_PE',
		'qvh'	=> 'qvh_Latn_PE',
		'qvi'	=> 'qvi_Latn_EC',
		'qvj'	=> 'qvj_Latn_EC',
		'qvl'	=> 'qvl_Latn_PE',
		'qvm'	=> 'qvm_Latn_PE',
		'qvn'	=> 'qvn_Latn_PE',
		'qvo'	=> 'qvo_Latn_PE',
		'qvp'	=> 'qvp_Latn_PE',
		'qvs'	=> 'qvs_Latn_PE',
		'qvw'	=> 'qvw_Latn_PE',
		'qvz'	=> 'qvz_Latn_EC',
		'qwa'	=> 'qwa_Latn_PE',
		'qwc'	=> 'qwc_Latn_PE',
		'qwh'	=> 'qwh_Latn_PE',
		'qwm'	=> 'qwm_Latn_HU',
		'qws'	=> 'qws_Latn_PE',
		'qwt'	=> 'qwt_Latn_US',
		'qxa'	=> 'qxa_Latn_PE',
		'qxc'	=> 'qxc_Latn_PE',
		'qxh'	=> 'qxh_Latn_PE',
		'qxl'	=> 'qxl_Latn_EC',
		'qxn'	=> 'qxn_Latn_PE',
		'qxo'	=> 'qxo_Latn_PE',
		'qxp'	=> 'qxp_Latn_PE',
		'qxq'	=> 'qxq_Arab_IR',
		'qxr'	=> 'qxr_Latn_EC',
		'qxt'	=> 'qxt_Latn_PE',
		'qxu'	=> 'qxu_Latn_PE',
		'qxw'	=> 'qxw_Latn_PE',
		'qya'	=> 'qya_Latn_001',
		'qyp'	=> 'qyp_Latn_US',
		'raa'	=> 'raa_Deva_NP',
		'rab'	=> 'rab_Deva_NP',
		'rac'	=> 'rac_Latn_ID',
		'rad'	=> 'rad_Latn_VN',
		'raf'	=> 'raf_Deva_NP',
		'rag'	=> 'rag_Latn_KE',
		'rah'	=> 'rah_Beng_IN',
		'rai'	=> 'rai_Latn_PG',
		'rak'	=> 'rak_Latn_PG',
		'ram'	=> 'ram_Latn_BR',
		'ran'	=> 'ran_Latn_ID',
		'rao'	=> 'rao_Latn_PG',
		'rap'	=> 'rap_Latn_CL',
		'rar'	=> 'rar_Latn_CK',
		'rav'	=> 'rav_Deva_NP',
		'raw'	=> 'raw_Latn_MM',
		'rax'	=> 'rax_Latn_NG',
		'ray'	=> 'ray_Latn_PF',
		'raz'	=> 'raz_Latn_ID',
		'rbb'	=> 'rbb_Mymr_MM',
		'rbk'	=> 'rbk_Latn_PH',
		'rbl'	=> 'rbl_Latn_PH',
		'rbp'	=> 'rbp_Latn_AU',
		'rdb'	=> 'rdb_Arab_IR',
		'rea'	=> 'rea_Latn_PG',
		'reb'	=> 'reb_Latn_ID',
		'ree'	=> 'ree_Latn_MY',
		'reg'	=> 'reg_Latn_TZ',
		'rei'	=> 'rei_Orya_IN',
		'rel'	=> 'rel_Latn_KE',
		'rem'	=> 'rem_Latn_PE',
		'ren'	=> 'ren_Latn_VN',
		'res'	=> 'res_Latn_NG',
		'ret'	=> 'ret_Latn_ID',
		'rey'	=> 'rey_Latn_BO',
		'rga'	=> 'rga_Latn_VU',
		'rgr'	=> 'rgr_Latn_PE',
		'rgs'	=> 'rgs_Latn_VN',
		'rgu'	=> 'rgu_Latn_ID',
		'rhp'	=> 'rhp_Latn_PG',
		'ril'	=> 'ril_Latn_MM',
		'rim'	=> 'rim_Latn_TZ',
		'rin'	=> 'rin_Latn_NG',
		'rir'	=> 'rir_Latn_ID',
		'rit'	=> 'rit_Latn_AU',
		'riu'	=> 'riu_Latn_ID',
		'rjg'	=> 'rjg_Latn_ID',
		'rji'	=> 'rji_Deva_NP',
		'rka'	=> 'rka_Khmr_KH',
		'rkb'	=> 'rkb_Latn_BR',
		'rkh'	=> 'rkh_Latn_CK',
		'rki'	=> 'rki_Mymr_MM',
		'rkm'	=> 'rkm_Latn_BF',
		'rkw'	=> 'rkw_Latn_AU',
		'rma'	=> 'rma_Latn_NI',
		'rmb'	=> 'rmb_Latn_AU',
		'rmc'	=> 'rmc_Latn_SK',
		'rmd'	=> 'rmd_Latn_DK',
		'rme'	=> 'rme_Latn_GB',
		'rmg'	=> 'rmg_Latn_NO',
		'rmh'	=> 'rmh_Latn_ID',
		'rmi'	=> 'rmi_Armn_AM',
		'rmk'	=> 'rmk_Latn_PG',
		'rml'	=> 'rml_Latn_PL',
		'rmm'	=> 'rmm_Latn_ID',
		'rmn'	=> 'rmn_Latn_RS',
		'rmp'	=> 'rmp_Latn_PG',
		'rmq'	=> 'rmq_Latn_ES',
		'rmw'	=> 'rmw_Latn_GB',
		'rmx'	=> 'rmx_Latn_VN',
		'rmz'	=> 'rmz_Mymr_IN',
		'rnd'	=> 'rnd_Latn_CD',
		'rnl'	=> 'rnl_Latn_IN',
		'rnn'	=> 'rnn_Latn_ID',
		'rnr'	=> 'rnr_Latn_AU',
		'rnw'	=> 'rnw_Latn_TZ',
		'roc'	=> 'roc_Latn_VN',
		'rod'	=> 'rod_Latn_NG',
		'roe'	=> 'roe_Latn_PG',
		'rog'	=> 'rog_Latn_VN',
		'rol'	=> 'rol_Latn_PH',
		'rom'	=> 'rom_Latn_RO',
		'roo'	=> 'roo_Latn_PG',
		'rop'	=> 'rop_Latn_AU',
		'ror'	=> 'ror_Latn_ID',
		'rou'	=> 'rou_Latn_TD',
		'row'	=> 'row_Latn_ID',
		'rpn'	=> 'rpn_Latn_VU',
		'rpt'	=> 'rpt_Latn_PG',
		'rri'	=> 'rri_Latn_SB',
		'rrm'	=> 'rrm_Latn_NZ',
		'rro'	=> 'rro_Latn_PG',
		'rrt'	=> 'rrt_Latn_AU',
		'rsk'	=> 'rsk_Cyrl_RS',
		'rsw'	=> 'rsw_Latn_NG',
		'rtc'	=> 'rtc_Latn_MM',
		'rth'	=> 'rth_Latn_ID',
		'rtw'	=> 'rtw_Deva_IN',
		'rub'	=> 'rub_Latn_UG',
		'ruc'	=> 'ruc_Latn_UG',
		'ruf'	=> 'ruf_Latn_TZ',
		'rui'	=> 'rui_Latn_TZ',
		'ruk'	=> 'ruk_Latn_NG',
		'ruo'	=> 'ruo_Latn_HR',
		'rup'	=> 'rup_Latn_RO',
		'ruq'	=> 'ruq_Latn_GR',
		'rut'	=> 'rut_Cyrl_RU',
		'ruu'	=> 'ruu_Latn_MY',
		'ruy'	=> 'ruy_Latn_NG',
		'ruz'	=> 'ruz_Latn_NG',
		'rwa'	=> 'rwa_Latn_PG',
		'rwl'	=> 'rwl_Latn_TZ',
		'rwm'	=> 'rwm_Latn_UG',
		'rwo'	=> 'rwo_Latn_PG',
		'rwr'	=> 'rwr_Deva_IN',
		'rxd'	=> 'rxd_Latn_AU',
		'rxw'	=> 'rxw_Latn_AU',
		'saa'	=> 'saa_Latn_TD',
		'sab'	=> 'sab_Latn_PA',
		'sac'	=> 'sac_Latn_US',
		'sad'	=> 'sad_Latn_TZ',
		'sae'	=> 'sae_Latn_BR',
		'saj'	=> 'saj_Latn_ID',
		'sak'	=> 'sak_Latn_GA',
		'sam'	=> 'sam_Samr_PS',
		'sao'	=> 'sao_Latn_ID',
		'sar'	=> 'sar_Latn_BO',
		'sau'	=> 'sau_Latn_ID',
		'saw'	=> 'saw_Latn_ID',
		'sax'	=> 'sax_Latn_VU',
		'say'	=> 'say_Latn_NG',
		'sba'	=> 'sba_Latn_TD',
		'sbb'	=> 'sbb_Latn_SB',
		'sbc'	=> 'sbc_Latn_PG',
		'sbd'	=> 'sbd_Latn_BF',
		'sbe'	=> 'sbe_Latn_PG',
		'sbg'	=> 'sbg_Latn_ID',
		'sbh'	=> 'sbh_Latn_PG',
		'sbi'	=> 'sbi_Latn_PG',
		'sbj'	=> 'sbj_Latn_TD',
		'sbk'	=> 'sbk_Latn_TZ',
		'sbl'	=> 'sbl_Latn_PH',
		'sbm'	=> 'sbm_Latn_TZ',
		'sbn'	=> 'sbn_Arab_PK',
		'sbo'	=> 'sbo_Latn_MY',
		'sbq'	=> 'sbq_Latn_PG',
		'sbr'	=> 'sbr_Latn_ID',
		'sbs'	=> 'sbs_Latn_NA',
		'sbt'	=> 'sbt_Latn_ID',
		'sbu'	=> 'sbu_Tibt_IN',
		'sbv'	=> 'sbv_Latn_IT',
		'sbw'	=> 'sbw_Latn_GA',
		'sbx'	=> 'sbx_Latn_ID',
		'sby'	=> 'sby_Latn_ZM',
		'sbz'	=> 'sbz_Latn_CF',
		'scb'	=> 'scb_Latn_VN',
		'sce'	=> 'sce_Latn_CN',
		'scf'	=> 'scf_Latn_PA',
		'scg'	=> 'scg_Latn_ID',
		'sch'	=> 'sch_Latn_IN',
		'sci'	=> 'sci_Latn_LK',
		'scl'	=> 'scl_Arab_PK',
		'scp'	=> 'scp_Deva_NP',
		'scs'	=> 'scs_Latn_CA',
		'sct'	=> 'sct_Laoo_LA',
		'scu'	=> 'scu_Takr_IN',
		'scv'	=> 'scv_Latn_NG',
		'scw'	=> 'scw_Latn_NG',
		'scx'	=> 'scx_Grek_IT',
		'sda'	=> 'sda_Latn_ID',
		'sdb'	=> 'sdb_Arab_IQ',
		'sde'	=> 'sde_Latn_NG',
		'sdf'	=> 'sdf_Arab_IQ',
		'sdg'	=> 'sdg_Arab_AF',
		'sdj'	=> 'sdj_Latn_CG',
		'sdk'	=> 'sdk_Latn_PG',
		'sdn'	=> 'sdn_Latn_IT',
		'sdo'	=> 'sdo_Latn_MY',
		'sdq'	=> 'sdq_Latn_ID',
		'sdr'	=> 'sdr_Beng_BD',
		'sds'	=> 'sds_Arab_TN',
		'sdu'	=> 'sdu_Latn_ID',
		'sdx'	=> 'sdx_Latn_MY',
		'sea'	=> 'sea_Latn_MY',
		'seb'	=> 'seb_Latn_CI',
		'sec'	=> 'sec_Latn_CA',
		'sed'	=> 'sed_Latn_VN',
		'see'	=> 'see_Latn_US',
		'seg'	=> 'seg_Latn_TZ',
		'sej'	=> 'sej_Latn_PG',
		'sek'	=> 'sek_Latn_CA',
		'sel'	=> 'sel_Cyrl_RU',
		'sen'	=> 'sen_Latn_BF',
		'seo'	=> 'seo_Latn_PG',
		'sep'	=> 'sep_Latn_BF',
		'seq'	=> 'seq_Latn_BF',
		'ser'	=> 'ser_Latn_US',
		'set'	=> 'set_Latn_ID',
		'seu'	=> 'seu_Latn_ID',
		'sev'	=> 'sev_Latn_CI',
		'sew'	=> 'sew_Latn_PG',
		'sey'	=> 'sey_Latn_EC',
		'sez'	=> 'sez_Latn_MM',
		'sfe'	=> 'sfe_Latn_PH',
		'sfm'	=> 'sfm_Plrd_CN',
		'sfw'	=> 'sfw_Latn_GH',
		'sgb'	=> 'sgb_Latn_PH',
		'sgc'	=> 'sgc_Latn_KE',
		'sgd'	=> 'sgd_Latn_PH',
		'sge'	=> 'sge_Latn_ID',
		'sgh'	=> 'sgh_Cyrl_TJ',
		'sgi'	=> 'sgi_Latn_CM',
		'sgj'	=> 'sgj_Deva_IN',
		'sgm'	=> 'sgm_Latn_KE',
		'sgp'	=> 'sgp_Latn_IN',
		'sgr'	=> 'sgr_Arab_IR',
		'sgt'	=> 'sgt_Tibt_BT',
		'sgu'	=> 'sgu_Latn_ID',
		'sgw'	=> 'sgw_Ethi_ET',
		'sgy'	=> 'sgy_Arab_AF',
		'sgz'	=> 'sgz_Latn_PG',
		'sha'	=> 'sha_Latn_NG',
		'shb'	=> 'shb_Latn_BR',
		'shc'	=> 'shc_Latn_CD',
		'shd'	=> 'shd_Arab_PK',
		'she'	=> 'she_Latn_ET',
		'shg'	=> 'shg_Latn_BW',
		'shh'	=> 'shh_Latn_US',
		'shj'	=> 'shj_Latn_SD',
		'shk'	=> 'shk_Latn_SS',
		'shm'	=> 'shm_Arab_IR',
		'sho'	=> 'sho_Latn_NG',
		'shp'	=> 'shp_Latn_PE',
		'shq'	=> 'shq_Latn_ZM',
		'shr'	=> 'shr_Latn_CD',
		'shs'	=> 'shs_Latn_CA',
		'sht'	=> 'sht_Latn_US',
		'shu'	=> 'shu_Arab_TD',
		'shv'	=> 'shv_Arab_OM',
		'shw'	=> 'shw_Latn_SD',
		'shy'	=> 'shy_Latn_DZ',
		'shz'	=> 'shz_Latn_ML',
		'sia'	=> 'sia_Cyrl_RU',
		'sib'	=> 'sib_Latn_MY',
		'sie'	=> 'sie_Latn_ZM',
		'sif'	=> 'sif_Latn_BF',
		'sig'	=> 'sig_Latn_GH',
		'sih'	=> 'sih_Latn_NC',
		'sii'	=> 'sii_Latn_IN',
		'sij'	=> 'sij_Latn_PG',
		'sik'	=> 'sik_Latn_BR',
		'sil'	=> 'sil_Latn_GH',
		'sim'	=> 'sim_Latn_PG',
		'sip'	=> 'sip_Tibt_IN',
		'siq'	=> 'siq_Latn_PG',
		'sir'	=> 'sir_Latn_NG',
		'sis'	=> 'sis_Latn_US',
		'siu'	=> 'siu_Latn_PG',
		'siv'	=> 'siv_Latn_PG',
		'siw'	=> 'siw_Latn_PG',
		'six'	=> 'six_Latn_PG',
		'siy'	=> 'siy_Arab_IR',
		'siz'	=> 'siz_Arab_EG',
		'sja'	=> 'sja_Latn_CO',
		'sjb'	=> 'sjb_Latn_ID',
		'sjd'	=> 'sjd_Cyrl_RU',
		'sje'	=> 'sje_Latn_SE',
		'sjg'	=> 'sjg_Latn_TD',
		'sjl'	=> 'sjl_Latn_IN',
		'sjm'	=> 'sjm_Latn_PH',
		'sjp'	=> 'sjp_Deva_IN',
		'sjr'	=> 'sjr_Latn_PG',
		'sjt'	=> 'sjt_Cyrl_RU',
		'sju'	=> 'sju_Latn_SE',
		'sjw'	=> 'sjw_Latn_US',
		'ska'	=> 'ska_Latn_US',
		'skb'	=> 'skb_Thai_TH',
		'skc'	=> 'skc_Latn_PG',
		'skd'	=> 'skd_Latn_US',
		'ske'	=> 'ske_Latn_VU',
		'skf'	=> 'skf_Latn_BR',
		'skg'	=> 'skg_Latn_MG',
		'skh'	=> 'skh_Latn_ID',
		'ski'	=> 'ski_Latn_ID',
		'skj'	=> 'skj_Deva_NP',
		'skm'	=> 'skm_Latn_PG',
		'skn'	=> 'skn_Latn_PH',
		'sko'	=> 'sko_Latn_ID',
		'skp'	=> 'skp_Latn_MY',
		'skq'	=> 'skq_Latn_BF',
		'sks'	=> 'sks_Latn_PG',
		'skt'	=> 'skt_Latn_CD',
		'sku'	=> 'sku_Latn_VU',
		'skv'	=> 'skv_Latn_ID',
		'skw'	=> 'skw_Latn_GY',
		'skx'	=> 'skx_Latn_ID',
		'sky'	=> 'sky_Latn_SB',
		'skz'	=> 'skz_Latn_ID',
		'slc'	=> 'slc_Latn_CO',
		'sld'	=> 'sld_Latn_BF',
		'slg'	=> 'slg_Latn_ID',
		'slh'	=> 'slh_Latn_US',
		'slj'	=> 'slj_Latn_BR',
		'sll'	=> 'sll_Latn_PG',
		'slm'	=> 'slm_Latn_PH',
		'sln'	=> 'sln_Latn_US',
		'slp'	=> 'slp_Latn_ID',
		'slr'	=> 'slr_Latn_CN',
		'slu'	=> 'slu_Latn_ID',
		'slw'	=> 'slw_Latn_PG',
		'slx'	=> 'slx_Latn_CD',
		'slz'	=> 'slz_Latn_ID',
		'smb'	=> 'smb_Latn_PG',
		'smc'	=> 'smc_Latn_PG',
		'smf'	=> 'smf_Latn_PG',
		'smg'	=> 'smg_Latn_PG',
		'smh'	=> 'smh_Yiii_CN',
		'smk'	=> 'smk_Latn_PH',
		'sml'	=> 'sml_Latn_PH',
		'smq'	=> 'smq_Latn_PG',
		'smr'	=> 'smr_Latn_ID',
		'smt'	=> 'smt_Latn_IN',
		'smu'	=> 'smu_Khmr_KH',
		'smw'	=> 'smw_Latn_ID',
		'smx'	=> 'smx_Latn_CD',
		'smy'	=> 'smy_Arab_IR',
		'smz'	=> 'smz_Latn_PG',
		'snc'	=> 'snc_Latn_PG',
		'sne'	=> 'sne_Latn_MY',
		'sng'	=> 'sng_Latn_CD',
		'sni'	=> 'sni_Latn_PE',
		'snj'	=> 'snj_Latn_CF',
		'snl'	=> 'snl_Latn_PH',
		'snm'	=> 'snm_Latn_UG',
		'snn'	=> 'snn_Latn_CO',
		'sno'	=> 'sno_Latn_US',
		'snp'	=> 'snp_Latn_PG',
		'snq'	=> 'snq_Latn_GA',
		'snr'	=> 'snr_Latn_PG',
		'sns'	=> 'sns_Latn_VU',
		'snu'	=> 'snu_Latn_ID',
		'snv'	=> 'snv_Latn_MY',
		'snw'	=> 'snw_Latn_GH',
		'snx'	=> 'snx_Latn_PG',
		'sny'	=> 'sny_Latn_PG',
		'snz'	=> 'snz_Latn_PG',
		'soa'	=> 'soa_Tavt_TH',
		'sob'	=> 'sob_Latn_ID',
		'soc'	=> 'soc_Latn_CD',
		'sod'	=> 'sod_Latn_CD',
		'soe'	=> 'soe_Latn_CD',
		'soi'	=> 'soi_Deva_NP',
		'sok'	=> 'sok_Latn_TD',
		'sol'	=> 'sol_Latn_PG',
		'soo'	=> 'soo_Latn_CD',
		'sop'	=> 'sop_Latn_CD',
		'soq'	=> 'soq_Latn_PG',
		'sor'	=> 'sor_Latn_TD',
		'sos'	=> 'sos_Latn_BF',
		'sov'	=> 'sov_Latn_PW',
		'sow'	=> 'sow_Latn_PG',
		'sox'	=> 'sox_Latn_CM',
		'soy'	=> 'soy_Latn_BJ',
		'soz'	=> 'soz_Latn_TZ',
		'spb'	=> 'spb_Latn_ID',
		'spc'	=> 'spc_Latn_VE',
		'spd'	=> 'spd_Latn_PG',
		'spe'	=> 'spe_Latn_PG',
		'spg'	=> 'spg_Latn_MY',
		'spi'	=> 'spi_Latn_ID',
		'spk'	=> 'spk_Latn_PG',
		'spl'	=> 'spl_Latn_PG',
		'spm'	=> 'spm_Latn_PG',
		'spn'	=> 'spn_Latn_PY',
		'spo'	=> 'spo_Latn_US',
		'spp'	=> 'spp_Latn_ML',
		'spq'	=> 'spq_Latn_PE',
		'spr'	=> 'spr_Latn_ID',
		'sps'	=> 'sps_Latn_PG',
		'spt'	=> 'spt_Tibt_IN',
		'spv'	=> 'spv_Orya_IN',
		'sqa'	=> 'sqa_Latn_NG',
		'sqh'	=> 'sqh_Latn_NG',
		'sqm'	=> 'sqm_Latn_CF',
		'sqo'	=> 'sqo_Arab_IR',
		'sqq'	=> 'sqq_Laoo_LA',
		'sqt'	=> 'sqt_Arab_YE',
		'squ'	=> 'squ_Latn_CA',
		'sra'	=> 'sra_Latn_PG',
		'sre'	=> 'sre_Latn_ID',
		'srf'	=> 'srf_Latn_PG',
		'srg'	=> 'srg_Latn_PH',
		'srh'	=> 'srh_Arab_CN',
		'sri'	=> 'sri_Latn_CO',
		'srk'	=> 'srk_Latn_MY',
		'srl'	=> 'srl_Latn_ID',
		'srm'	=> 'srm_Latn_SR',
		'sro'	=> 'sro_Latn_IT',
		'srq'	=> 'srq_Latn_BO',
		'srs'	=> 'srs_Latn_CA',
		'srt'	=> 'srt_Latn_ID',
		'sru'	=> 'sru_Latn_BR',
		'srv'	=> 'srv_Latn_PH',
		'srw'	=> 'srw_Latn_ID',
		'sry'	=> 'sry_Latn_PG',
		'srz'	=> 'srz_Arab_IR',
		'ssb'	=> 'ssb_Latn_PH',
		'ssc'	=> 'ssc_Latn_TZ',
		'ssd'	=> 'ssd_Latn_PG',
		'sse'	=> 'sse_Latn_PH',
		'ssf'	=> 'ssf_Latn_TW',
		'ssg'	=> 'ssg_Latn_PG',
		'ssh'	=> 'ssh_Arab_AE',
		'ssj'	=> 'ssj_Latn_PG',
		'ssl'	=> 'ssl_Latn_GH',
		'ssm'	=> 'ssm_Latn_MY',
		'ssn'	=> 'ssn_Latn_KE',
		'sso'	=> 'sso_Latn_PG',
		'ssq'	=> 'ssq_Latn_ID',
		'sss'	=> 'sss_Laoo_LA',
		'sst'	=> 'sst_Latn_PG',
		'ssu'	=> 'ssu_Latn_PG',
		'ssv'	=> 'ssv_Latn_VU',
		'ssx'	=> 'ssx_Latn_PG',
		'ssz'	=> 'ssz_Latn_PG',
		'sta'	=> 'sta_Latn_ZM',
		'stb'	=> 'stb_Latn_PH',
		'ste'	=> 'ste_Latn_ID',
		'stf'	=> 'stf_Latn_PG',
		'stg'	=> 'stg_Latn_VN',
		'sth'	=> 'sth_Latn_IE',
		'sti'	=> 'sti_Latn_VN',
		'stj'	=> 'stj_Latn_BF',
		'stk'	=> 'stk_Latn_PG',
		'stl'	=> 'stl_Latn_NL',
		'stm'	=> 'stm_Latn_PG',
		'stn'	=> 'stn_Latn_SB',
		'sto'	=> 'sto_Latn_CA',
		'stp'	=> 'stp_Latn_MX',
		'str'	=> 'str_Latn_CA',
		'sts'	=> 'sts_Arab_AF',
		'stt'	=> 'stt_Latn_VN',
		'stv'	=> 'stv_Ethi_ET',
		'stw'	=> 'stw_Latn_FM',
		'sty'	=> 'sty_Cyrl_RU',
		'sua'	=> 'sua_Latn_PG',
		'sub'	=> 'sub_Latn_CD',
		'suc'	=> 'suc_Latn_PH',
		'sue'	=> 'sue_Latn_PG',
		'sug'	=> 'sug_Latn_PG',
		'sui'	=> 'sui_Latn_PG',
		'suj'	=> 'suj_Latn_TZ',
		'suo'	=> 'suo_Latn_PG',
		'suq'	=> 'suq_Latn_ET',
		'sur'	=> 'sur_Latn_NG',
		'sut'	=> 'sut_Latn_NI',
		'suv'	=> 'suv_Latn_IN',
		'suw'	=> 'suw_Latn_TZ',
		'suy'	=> 'suy_Latn_BR',
		'sva'	=> 'sva_Geor_GE',
		'svb'	=> 'svb_Latn_PG',
		'svc'	=> 'svc_Latn_VC',
		'sve'	=> 'sve_Latn_ID',
		'svm'	=> 'svm_Latn_IT',
		'svs'	=> 'svs_Latn_SB',
		'swf'	=> 'swf_Latn_CD',
		'swi'	=> 'swi_Hani_CN',
		'swj'	=> 'swj_Latn_GA',
		'swk'	=> 'swk_Latn_MW',
		'swm'	=> 'swm_Latn_PG',
		'swo'	=> 'swo_Latn_BR',
		'swp'	=> 'swp_Latn_PG',
		'swq'	=> 'swq_Latn_CM',
		'swr'	=> 'swr_Latn_ID',
		'sws'	=> 'sws_Latn_ID',
		'swt'	=> 'swt_Latn_ID',
		'swu'	=> 'swu_Latn_ID',
		'sww'	=> 'sww_Latn_VU',
		'swx'	=> 'swx_Latn_BR',
		'swy'	=> 'swy_Latn_TD',
		'sxb'	=> 'sxb_Latn_KE',
		'sxe'	=> 'sxe_Latn_GA',
		'sxr'	=> 'sxr_Latn_TW',
		'sxs'	=> 'sxs_Latn_NG',
		'sxu'	=> 'sxu_Runr_DE',
		'sxw'	=> 'sxw_Latn_BJ',
		'sya'	=> 'sya_Latn_ID',
		'syb'	=> 'syb_Latn_PH',
		'syc'	=> 'syc_Syrc_TR',
		'syi'	=> 'syi_Latn_GA',
		'syk'	=> 'syk_Latn_NG',
		'sym'	=> 'sym_Latn_BF',
		'syn'	=> 'syn_Syrc_IR',
		'syo'	=> 'syo_Latn_KH',
		'sys'	=> 'sys_Latn_TD',
		'syw'	=> 'syw_Deva_NP',
		'syx'	=> 'syx_Latn_GA',
		'sza'	=> 'sza_Latn_MY',
		'szb'	=> 'szb_Latn_ID',
		'szc'	=> 'szc_Latn_MY',
		'szg'	=> 'szg_Latn_CD',
		'szn'	=> 'szn_Latn_ID',
		'szp'	=> 'szp_Latn_ID',
		'szv'	=> 'szv_Latn_CM',
		'szw'	=> 'szw_Latn_ID',
		'szy'	=> 'szy_Latn_TW',
		'taa'	=> 'taa_Latn_US',
		'tab'	=> 'tab_Cyrl_RU',
		'tac'	=> 'tac_Latn_MX',
		'tad'	=> 'tad_Latn_ID',
		'tae'	=> 'tae_Latn_BR',
		'taf'	=> 'taf_Latn_BR',
		'tag'	=> 'tag_Latn_SD',
		'tak'	=> 'tak_Latn_NG',
		'tal'	=> 'tal_Latn_NG',
		'tan'	=> 'tan_Latn_NG',
		'tao'	=> 'tao_Latn_TW',
		'tap'	=> 'tap_Latn_CD',
		'taq'	=> 'taq_Latn_ML',
		'tar'	=> 'tar_Latn_MX',
		'tas'	=> 'tas_Latn_VN',
		'tau'	=> 'tau_Latn_US',
		'tav'	=> 'tav_Latn_CO',
		'taw'	=> 'taw_Latn_PG',
		'tax'	=> 'tax_Latn_TD',
		'tay'	=> 'tay_Latn_TW',
		'taz'	=> 'taz_Latn_SD',
		'tba'	=> 'tba_Latn_BR',
		'tbc'	=> 'tbc_Latn_PG',
		'tbd'	=> 'tbd_Latn_PG',
		'tbe'	=> 'tbe_Latn_SB',
		'tbf'	=> 'tbf_Latn_PG',
		'tbg'	=> 'tbg_Latn_PG',
		'tbh'	=> 'tbh_Latn_AU',
		'tbi'	=> 'tbi_Latn_SD',
		'tbj'	=> 'tbj_Latn_PG',
		'tbk'	=> 'tbk_Tagb_PH',
		'tbl'	=> 'tbl_Latn_PH',
		'tbm'	=> 'tbm_Latn_CD',
		'tbn'	=> 'tbn_Latn_CO',
		'tbo'	=> 'tbo_Latn_PG',
		'tbp'	=> 'tbp_Latn_ID',
		'tbs'	=> 'tbs_Latn_PG',
		'tbt'	=> 'tbt_Latn_CD',
		'tbu'	=> 'tbu_Latn_MX',
		'tbv'	=> 'tbv_Latn_PG',
		'tbx'	=> 'tbx_Latn_PG',
		'tby'	=> 'tby_Latn_ID',
		'tbz'	=> 'tbz_Latn_BJ',
		'tca'	=> 'tca_Latn_BR',
		'tcb'	=> 'tcb_Latn_US',
		'tcc'	=> 'tcc_Latn_TZ',
		'tcd'	=> 'tcd_Latn_GH',
		'tce'	=> 'tce_Latn_CA',
		'tcf'	=> 'tcf_Latn_MX',
		'tcg'	=> 'tcg_Latn_ID',
		'tch'	=> 'tch_Latn_TC',
		'tci'	=> 'tci_Latn_PG',
		'tck'	=> 'tck_Latn_GA',
		'tcm'	=> 'tcm_Latn_ID',
		'tcn'	=> 'tcn_Tibt_NP',
		'tco'	=> 'tco_Mymr_MM',
		'tcp'	=> 'tcp_Latn_MM',
		'tcq'	=> 'tcq_Latn_ID',
		'tcs'	=> 'tcs_Latn_AU',
		'tcu'	=> 'tcu_Latn_MX',
		'tcw'	=> 'tcw_Latn_MX',
		'tcx'	=> 'tcx_Taml_IN',
		'tcz'	=> 'tcz_Latn_IN',
		'tda'	=> 'tda_Tfng_NE',
		'tdb'	=> 'tdb_Deva_IN',
		'tdc'	=> 'tdc_Latn_CO',
		'tde'	=> 'tde_Latn_ML',
		'tdi'	=> 'tdi_Latn_ID',
		'tdj'	=> 'tdj_Latn_ID',
		'tdk'	=> 'tdk_Latn_NG',
		'tdl'	=> 'tdl_Latn_NG',
		'tdm'	=> 'tdm_Latn_GY',
		'tdn'	=> 'tdn_Latn_ID',
		'tdo'	=> 'tdo_Latn_NG',
		'tdq'	=> 'tdq_Latn_NG',
		'tdr'	=> 'tdr_Latn_VN',
		'tds'	=> 'tds_Latn_ID',
		'tdt'	=> 'tdt_Latn_TL',
		'tdv'	=> 'tdv_Latn_NG',
		'tdx'	=> 'tdx_Latn_MG',
		'tdy'	=> 'tdy_Latn_PH',
		'tea'	=> 'tea_Latn_MY',
		'teb'	=> 'teb_Latn_EC',
		'tec'	=> 'tec_Latn_KE',
		'ted'	=> 'ted_Latn_CI',
		'tee'	=> 'tee_Latn_MX',
		'teg'	=> 'teg_Latn_GA',
		'teh'	=> 'teh_Latn_AR',
		'tei'	=> 'tei_Latn_PG',
		'tek'	=> 'tek_Latn_CD',
		'ten'	=> 'ten_Latn_CO',
		'tep'	=> 'tep_Latn_MX',
		'teq'	=> 'teq_Latn_SD',
		'ter'	=> 'ter_Latn_BR',
		'tes'	=> 'tes_Java_ID',
		'teu'	=> 'teu_Latn_UG',
		'tev'	=> 'tev_Latn_ID',
		'tew'	=> 'tew_Latn_US',
		'tex'	=> 'tex_Latn_SS',
		'tey'	=> 'tey_Latn_SD',
		'tez'	=> 'tez_Latn_NE',
		'tfi'	=> 'tfi_Latn_BJ',
		'tfn'	=> 'tfn_Latn_US',
		'tfo'	=> 'tfo_Latn_ID',
		'tfr'	=> 'tfr_Latn_PA',
		'tft'	=> 'tft_Latn_ID',
		'tga'	=> 'tga_Latn_KE',
		'tgb'	=> 'tgb_Latn_MY',
		'tgc'	=> 'tgc_Latn_PG',
		'tgd'	=> 'tgd_Latn_NG',
		'tge'	=> 'tge_Deva_NP',
		'tgf'	=> 'tgf_Tibt_BT',
		'tgh'	=> 'tgh_Latn_TT',
		'tgi'	=> 'tgi_Latn_PG',
		'tgj'	=> 'tgj_Latn_IN',
		'tgn'	=> 'tgn_Latn_PH',
		'tgo'	=> 'tgo_Latn_PG',
		'tgp'	=> 'tgp_Latn_VU',
		'tgq'	=> 'tgq_Latn_MY',
		'tgs'	=> 'tgs_Latn_VU',
		'tgt'	=> 'tgt_Latn_PH',
		'tgu'	=> 'tgu_Latn_PG',
		'tgv'	=> 'tgv_Latn_BR',
		'tgw'	=> 'tgw_Latn_CI',
		'tgx'	=> 'tgx_Latn_CA',
		'tgy'	=> 'tgy_Latn_SS',
		'tgz'	=> 'tgz_Latn_AU',
		'thd'	=> 'thd_Latn_AU',
		'the'	=> 'the_Deva_NP',
		'thf'	=> 'thf_Deva_NP',
		'thh'	=> 'thh_Latn_MX',
		'thi'	=> 'thi_Tale_LA',
		'thk'	=> 'thk_Latn_KE',
		'thm'	=> 'thm_Thai_TH',
		'thp'	=> 'thp_Latn_CA',
		'ths'	=> 'ths_Deva_NP',
		'tht'	=> 'tht_Latn_CA',
		'thu'	=> 'thu_Latn_SS',
		'thv'	=> 'thv_Latn_DZ',
		'thy'	=> 'thy_Latn_NG',
		'thz'	=> 'thz_Latn_NE',
		'tic'	=> 'tic_Latn_SD',
		'tif'	=> 'tif_Latn_PG',
		'tih'	=> 'tih_Latn_MY',
		'tii'	=> 'tii_Latn_CD',
		'tij'	=> 'tij_Deva_NP',
		'tik'	=> 'tik_Latn_CM',
		'til'	=> 'til_Latn_US',
		'tim'	=> 'tim_Latn_PG',
		'tin'	=> 'tin_Cyrl_RU',
		'tio'	=> 'tio_Latn_PG',
		'tip'	=> 'tip_Latn_ID',
		'tiq'	=> 'tiq_Latn_BF',
		'tis'	=> 'tis_Latn_PH',
		'tit'	=> 'tit_Latn_CO',
		'tiu'	=> 'tiu_Latn_PH',
		'tiw'	=> 'tiw_Latn_AU',
		'tix'	=> 'tix_Latn_US',
		'tiy'	=> 'tiy_Latn_PH',
		'tja'	=> 'tja_Latn_LR',
		'tjg'	=> 'tjg_Latn_ID',
		'tji'	=> 'tji_Latn_CN',
		'tjj'	=> 'tjj_Latn_AU',
		'tjl'	=> 'tjl_Mymr_MM',
		'tjn'	=> 'tjn_Latn_CI',
		'tjo'	=> 'tjo_Arab_DZ',
		'tjp'	=> 'tjp_Latn_AU',
		'tjs'	=> 'tjs_Latn_CN',
		'tju'	=> 'tju_Latn_AU',
		'tjw'	=> 'tjw_Latn_AU',
		'tka'	=> 'tka_Latn_BR',
		'tkb'	=> 'tkb_Deva_IN',
		'tkd'	=> 'tkd_Latn_TL',
		'tke'	=> 'tke_Latn_MZ',
		'tkf'	=> 'tkf_Latn_BR',
		'tkg'	=> 'tkg_Latn_MG',
		'tkp'	=> 'tkp_Latn_SB',
		'tkq'	=> 'tkq_Latn_NG',
		'tks'	=> 'tks_Arab_IR',
		'tku'	=> 'tku_Latn_MX',
		'tkv'	=> 'tkv_Latn_PG',
		'tkw'	=> 'tkw_Latn_SB',
		'tkx'	=> 'tkx_Latn_ID',
		'tkz'	=> 'tkz_Latn_VN',
		'tla'	=> 'tla_Latn_MX',
		'tlb'	=> 'tlb_Latn_ID',
		'tlc'	=> 'tlc_Latn_MX',
		'tld'	=> 'tld_Latn_ID',
		'tlf'	=> 'tlf_Latn_PG',
		'tlg'	=> 'tlg_Latn_ID',
		'tli'	=> 'tli_Latn_US',
		'tlj'	=> 'tlj_Latn_UG',
		'tlk'	=> 'tlk_Latn_ID',
		'tll'	=> 'tll_Latn_CD',
		'tlm'	=> 'tlm_Latn_VU',
		'tln'	=> 'tln_Latn_ID',
		'tlp'	=> 'tlp_Latn_MX',
		'tlq'	=> 'tlq_Latn_MM',
		'tlr'	=> 'tlr_Latn_SB',
		'tls'	=> 'tls_Latn_VU',
		'tlt'	=> 'tlt_Latn_ID',
		'tlu'	=> 'tlu_Latn_ID',
		'tlv'	=> 'tlv_Latn_ID',
		'tlx'	=> 'tlx_Latn_PG',
		'tma'	=> 'tma_Latn_TD',
		'tmb'	=> 'tmb_Latn_VU',
		'tmc'	=> 'tmc_Latn_TD',
		'tmd'	=> 'tmd_Latn_PG',
		'tme'	=> 'tme_Latn_BR',
		'tmf'	=> 'tmf_Latn_PY',
		'tmg'	=> 'tmg_Latn_ID',
		'tmi'	=> 'tmi_Latn_VU',
		'tmj'	=> 'tmj_Latn_ID',
		'tml'	=> 'tml_Latn_ID',
		'tmm'	=> 'tmm_Latn_VN',
		'tmn'	=> 'tmn_Latn_ID',
		'tmo'	=> 'tmo_Latn_MY',
		'tmq'	=> 'tmq_Latn_PG',
		'tmr'	=> 'tmr_Syrc_IL',
		'tmt'	=> 'tmt_Latn_VU',
		'tmu'	=> 'tmu_Latn_ID',
		'tmv'	=> 'tmv_Latn_CD',
		'tmw'	=> 'tmw_Latn_MY',
		'tmy'	=> 'tmy_Latn_PG',
		'tmz'	=> 'tmz_Latn_VE',
		'tna'	=> 'tna_Latn_BO',
		'tnb'	=> 'tnb_Latn_CO',
		'tnc'	=> 'tnc_Latn_CO',
		'tnd'	=> 'tnd_Latn_CO',
		'tng'	=> 'tng_Latn_TD',
		'tnh'	=> 'tnh_Latn_PG',
		'tni'	=> 'tni_Latn_ID',
		'tnk'	=> 'tnk_Latn_VU',
		'tnl'	=> 'tnl_Latn_VU',
		'tnm'	=> 'tnm_Latn_ID',
		'tnn'	=> 'tnn_Latn_VU',
		'tno'	=> 'tno_Latn_BO',
		'tnp'	=> 'tnp_Latn_VU',
		'tnq'	=> 'tnq_Latn_PR',
		'tns'	=> 'tns_Latn_PG',
		'tnt'	=> 'tnt_Latn_ID',
		'tnv'	=> 'tnv_Cakm_BD',
		'tnw'	=> 'tnw_Latn_ID',
		'tnx'	=> 'tnx_Latn_SB',
		'tny'	=> 'tny_Latn_TZ',
		'tob'	=> 'tob_Latn_AR',
		'toc'	=> 'toc_Latn_MX',
		'tod'	=> 'tod_Latn_GN',
		'tof'	=> 'tof_Latn_PG',
		'toh'	=> 'toh_Latn_MZ',
		'toi'	=> 'toi_Latn_ZM',
		'toj'	=> 'toj_Latn_MX',
		'tol'	=> 'tol_Latn_US',
		'tom'	=> 'tom_Latn_ID',
		'too'	=> 'too_Latn_MX',
		'top'	=> 'top_Latn_MX',
		'toq'	=> 'toq_Latn_SS',
		'tor'	=> 'tor_Latn_CD',
		'tos'	=> 'tos_Latn_MX',
		'tou'	=> 'tou_Latn_VN',
		'tov'	=> 'tov_Arab_IR',
		'tow'	=> 'tow_Latn_US',
		'tox'	=> 'tox_Latn_PW',
		'toy'	=> 'toy_Latn_ID',
		'toz'	=> 'toz_Latn_CM',
		'tpa'	=> 'tpa_Latn_PG',
		'tpc'	=> 'tpc_Latn_MX',
		'tpe'	=> 'tpe_Latn_BD',
		'tpf'	=> 'tpf_Latn_ID',
		'tpg'	=> 'tpg_Latn_ID',
		'tpj'	=> 'tpj_Latn_PY',
		'tpk'	=> 'tpk_Latn_BR',
		'tpl'	=> 'tpl_Latn_MX',
		'tpm'	=> 'tpm_Latn_GH',
		'tpn'	=> 'tpn_Latn_BR',
		'tpp'	=> 'tpp_Latn_MX',
		'tpr'	=> 'tpr_Latn_BR',
		'tpt'	=> 'tpt_Latn_MX',
		'tpu'	=> 'tpu_Khmr_KH',
		'tpv'	=> 'tpv_Latn_MP',
		'tpx'	=> 'tpx_Latn_MX',
		'tpy'	=> 'tpy_Latn_BR',
		'tpz'	=> 'tpz_Latn_PG',
		'tqb'	=> 'tqb_Latn_BR',
		'tql'	=> 'tql_Latn_VU',
		'tqm'	=> 'tqm_Latn_PG',
		'tqn'	=> 'tqn_Latn_US',
		'tqo'	=> 'tqo_Latn_PG',
		'tqp'	=> 'tqp_Latn_PG',
		'tqt'	=> 'tqt_Latn_MX',
		'tqu'	=> 'tqu_Latn_SB',
		'tqw'	=> 'tqw_Latn_US',
		'tra'	=> 'tra_Arab_AF',
		'trb'	=> 'trb_Latn_PG',
		'trc'	=> 'trc_Latn_MX',
		'tre'	=> 'tre_Latn_ID',
		'trf'	=> 'trf_Latn_TT',
		'trg'	=> 'trg_Hebr_IL',
		'trh'	=> 'trh_Latn_PG',
		'tri'	=> 'tri_Latn_SR',
		'trj'	=> 'trj_Latn_TD',
		'trl'	=> 'trl_Latn_GB',
		'trm'	=> 'trm_Arab_AF',
		'trn'	=> 'trn_Latn_BO',
		'tro'	=> 'tro_Latn_IN',
		'trp'	=> 'trp_Latn_IN',
		'trq'	=> 'trq_Latn_MX',
		'trr'	=> 'trr_Latn_PE',
		'trs'	=> 'trs_Latn_MX',
		'trt'	=> 'trt_Latn_ID',
		'trx'	=> 'trx_Latn_MY',
		'try'	=> 'try_Latn_IN',
		'trz'	=> 'trz_Latn_BR',
		'tsa'	=> 'tsa_Latn_CG',
		'tsb'	=> 'tsb_Latn_ET',
		'tsc'	=> 'tsc_Latn_MZ',
		'tsh'	=> 'tsh_Latn_CM',
		'tsi'	=> 'tsi_Latn_CA',
		'tsl'	=> 'tsl_Latn_VN',
		'tsp'	=> 'tsp_Latn_BF',
		'tsr'	=> 'tsr_Latn_VU',
		'tst'	=> 'tst_Latn_ML',
		'tsu'	=> 'tsu_Latn_TW',
		'tsv'	=> 'tsv_Latn_GA',
		'tsw'	=> 'tsw_Latn_NG',
		'tsx'	=> 'tsx_Latn_PG',
		'tsz'	=> 'tsz_Latn_MX',
		'ttb'	=> 'ttb_Latn_NG',
		'ttc'	=> 'ttc_Latn_GT',
		'ttd'	=> 'ttd_Latn_PG',
		'tte'	=> 'tte_Latn_PG',
		'ttf'	=> 'ttf_Latn_CM',
		'tth'	=> 'tth_Laoo_LA',
		'tti'	=> 'tti_Latn_ID',
		'ttk'	=> 'ttk_Latn_CO',
		'ttl'	=> 'ttl_Latn_ZM',
		'ttm'	=> 'ttm_Latn_CA',
		'ttn'	=> 'ttn_Latn_ID',
		'tto'	=> 'tto_Laoo_LA',
		'ttp'	=> 'ttp_Latn_ID',
		'ttr'	=> 'ttr_Latn_NG',
		'ttu'	=> 'ttu_Latn_PG',
		'ttv'	=> 'ttv_Latn_PG',
		'ttw'	=> 'ttw_Latn_MY',
		'tty'	=> 'tty_Latn_ID',
		'ttz'	=> 'ttz_Deva_NP',
		'tua'	=> 'tua_Latn_PG',
		'tub'	=> 'tub_Latn_US',
		'tuc'	=> 'tuc_Latn_PG',
		'tud'	=> 'tud_Latn_BR',
		'tue'	=> 'tue_Latn_CO',
		'tuf'	=> 'tuf_Latn_CO',
		'tug'	=> 'tug_Latn_TD',
		'tuh'	=> 'tuh_Latn_PG',
		'tui'	=> 'tui_Latn_CM',
		'tuj'	=> 'tuj_Latn_ID',
		'tul'	=> 'tul_Latn_NG',
		'tun'	=> 'tun_Latn_US',
		'tuo'	=> 'tuo_Latn_BR',
		'tuq'	=> 'tuq_Latn_TD',
		'tus'	=> 'tus_Latn_CA',
		'tuu'	=> 'tuu_Latn_US',
		'tuv'	=> 'tuv_Latn_KE',
		'tux'	=> 'tux_Latn_BR',
		'tuy'	=> 'tuy_Latn_KE',
		'tuz'	=> 'tuz_Latn_BF',
		'tva'	=> 'tva_Latn_SB',
		'tvd'	=> 'tvd_Latn_NG',
		'tve'	=> 'tve_Latn_ID',
		'tvi'	=> 'tvi_Latn_NG',
		'tvk'	=> 'tvk_Latn_VU',
		'tvm'	=> 'tvm_Latn_ID',
		'tvn'	=> 'tvn_Mymr_MM',
		'tvo'	=> 'tvo_Latn_ID',
		'tvs'	=> 'tvs_Latn_KE',
		'tvt'	=> 'tvt_Latn_IN',
		'tvu'	=> 'tvu_Latn_CM',
		'tvw'	=> 'tvw_Latn_ID',
		'tvx'	=> 'tvx_Latn_TW',
		'twa'	=> 'twa_Latn_US',
		'twb'	=> 'twb_Latn_PH',
		'twd'	=> 'twd_Latn_NL',
		'twe'	=> 'twe_Latn_ID',
		'twf'	=> 'twf_Latn_US',
		'twg'	=> 'twg_Latn_ID',
		'twh'	=> 'twh_Latn_VN',
		'twl'	=> 'twl_Latn_MZ',
		'twm'	=> 'twm_Deva_IN',
		'twn'	=> 'twn_Latn_CM',
		'two'	=> 'two_Latn_BW',
		'twp'	=> 'twp_Latn_PG',
		'twr'	=> 'twr_Latn_MX',
		'twt'	=> 'twt_Latn_BR',
		'twu'	=> 'twu_Latn_ID',
		'tww'	=> 'tww_Latn_PG',
		'twx'	=> 'twx_Latn_MZ',
		'twy'	=> 'twy_Latn_ID',
		'txa'	=> 'txa_Latn_MY',
		'txe'	=> 'txe_Latn_ID',
		'txi'	=> 'txi_Latn_BR',
		'txj'	=> 'txj_Latn_NG',
		'txm'	=> 'txm_Latn_ID',
		'txn'	=> 'txn_Latn_ID',
		'txq'	=> 'txq_Latn_ID',
		'txs'	=> 'txs_Latn_ID',
		'txt'	=> 'txt_Latn_ID',
		'txu'	=> 'txu_Latn_BR',
		'txx'	=> 'txx_Latn_MY',
		'txy'	=> 'txy_Latn_MG',
		'tya'	=> 'tya_Latn_PG',
		'tye'	=> 'tye_Latn_NG',
		'tyh'	=> 'tyh_Latn_VN',
		'tyi'	=> 'tyi_Latn_CG',
		'tyj'	=> 'tyj_Latn_VN',
		'tyl'	=> 'tyl_Latn_VN',
		'tyn'	=> 'tyn_Latn_ID',
		'typ'	=> 'typ_Latn_AU',
		'tyr'	=> 'tyr_Tavt_VN',
		'tys'	=> 'tys_Latn_VN',
		'tyt'	=> 'tyt_Latn_VN',
		'tyu'	=> 'tyu_Latn_BW',
		'tyx'	=> 'tyx_Latn_CG',
		'tyy'	=> 'tyy_Latn_NG',
		'tyz'	=> 'tyz_Latn_VN',
		'tzh'	=> 'tzh_Latn_MX',
		'tzj'	=> 'tzj_Latn_GT',
		'tzl'	=> 'tzl_Latn_001',
		'tzn'	=> 'tzn_Latn_ID',
		'tzo'	=> 'tzo_Latn_MX',
		'tzx'	=> 'tzx_Latn_PG',
		'uam'	=> 'uam_Latn_BR',
		'uar'	=> 'uar_Latn_PG',
		'uba'	=> 'uba_Latn_NG',
		'ubi'	=> 'ubi_Latn_TD',
		'ubl'	=> 'ubl_Latn_PH',
		'ubr'	=> 'ubr_Latn_PG',
		'ubu'	=> 'ubu_Latn_PG',
		'uby'	=> 'uby_Latn_TR',
		'uda'	=> 'uda_Latn_NG',
		'ude'	=> 'ude_Cyrl_RU',
		'udg'	=> 'udg_Mlym_IN',
		'udi'	=> 'udi_Cyrl_RU',
		'udj'	=> 'udj_Latn_ID',
		'udl'	=> 'udl_Latn_CM',
		'udu'	=> 'udu_Latn_SD',
		'ues'	=> 'ues_Latn_ID',
		'ufi'	=> 'ufi_Latn_PG',
		'ugb'	=> 'ugb_Latn_AU',
		'uge'	=> 'uge_Latn_SB',
		'ugh'	=> 'ugh_Cyrl_RU',
		'ugo'	=> 'ugo_Thai_TH',
		'uha'	=> 'uha_Latn_NG',
		'uhn'	=> 'uhn_Latn_ID',
		'uis'	=> 'uis_Latn_PG',
		'uiv'	=> 'uiv_Latn_CM',
		'uji'	=> 'uji_Latn_NG',
		'uka'	=> 'uka_Latn_ID',
		'ukg'	=> 'ukg_Latn_PG',
		'ukh'	=> 'ukh_Latn_CF',
		'uki'	=> 'uki_Orya_IN',
		'ukk'	=> 'ukk_Latn_MM',
		'ukp'	=> 'ukp_Latn_NG',
		'ukq'	=> 'ukq_Latn_NG',
		'uku'	=> 'uku_Latn_NG',
		'ukv'	=> 'ukv_Latn_SS',
		'ukw'	=> 'ukw_Latn_NG',
		'uky'	=> 'uky_Latn_AU',
		'ula'	=> 'ula_Latn_NG',
		'ulb'	=> 'ulb_Latn_NG',
		'ulc'	=> 'ulc_Cyrl_RU',
		'ule'	=> 'ule_Latn_AR',
		'ulf'	=> 'ulf_Latn_ID',
		'ulk'	=> 'ulk_Latn_AU',
		'ulm'	=> 'ulm_Latn_ID',
		'uln'	=> 'uln_Latn_PG',
		'ulu'	=> 'ulu_Latn_ID',
		'ulw'	=> 'ulw_Latn_NI',
		'uly'	=> 'uly_Latn_NG',
		'uma'	=> 'uma_Latn_US',
		'umd'	=> 'umd_Latn_AU',
		'umg'	=> 'umg_Latn_AU',
		'umi'	=> 'umi_Latn_MY',
		'umm'	=> 'umm_Latn_NG',
		'umn'	=> 'umn_Latn_MM',
		'umo'	=> 'umo_Latn_BR',
		'ump'	=> 'ump_Latn_AU',
		'umr'	=> 'umr_Latn_AU',
		'ums'	=> 'ums_Latn_ID',
		'una'	=> 'una_Latn_PG',
		'une'	=> 'une_Latn_NG',
		'ung'	=> 'ung_Latn_AU',
		'uni'	=> 'uni_Latn_PG',
		'unk'	=> 'unk_Latn_BR',
		'unm'	=> 'unm_Latn_US',
		'unn'	=> 'unn_Latn_AU',
		'unu'	=> 'unu_Latn_PG',
		'unz'	=> 'unz_Latn_ID',
		'uon'	=> 'uon_Latn_TW',
		'upi'	=> 'upi_Latn_PG',
		'upv'	=> 'upv_Latn_VU',
		'ura'	=> 'ura_Latn_PE',
		'urb'	=> 'urb_Latn_BR',
		'urc'	=> 'urc_Latn_AU',
		'ure'	=> 'ure_Latn_BO',
		'urf'	=> 'urf_Latn_AU',
		'urg'	=> 'urg_Latn_PG',
		'urh'	=> 'urh_Latn_NG',
		'uri'	=> 'uri_Latn_PG',
		'urk'	=> 'urk_Thai_TH',
		'urm'	=> 'urm_Latn_PG',
		'urn'	=> 'urn_Latn_ID',
		'uro'	=> 'uro_Latn_PG',
		'urp'	=> 'urp_Latn_BR',
		'urr'	=> 'urr_Latn_VU',
		'urt'	=> 'urt_Latn_PG',
		'uru'	=> 'uru_Latn_BR',
		'urv'	=> 'urv_Latn_PG',
		'urw'	=> 'urw_Latn_PG',
		'urx'	=> 'urx_Latn_PG',
		'ury'	=> 'ury_Latn_ID',
		'urz'	=> 'urz_Latn_BR',
		'usa'	=> 'usa_Latn_PG',
		'ush'	=> 'ush_Arab_PK',
		'usi'	=> 'usi_Latn_BD',
		'usk'	=> 'usk_Latn_CM',
		'usp'	=> 'usp_Latn_GT',
		'uss'	=> 'uss_Latn_NG',
		'usu'	=> 'usu_Latn_PG',
		'uta'	=> 'uta_Latn_NG',
		'ute'	=> 'ute_Latn_US',
		'uth'	=> 'uth_Latn_NG',
		'utp'	=> 'utp_Latn_SB',
		'utr'	=> 'utr_Latn_NG',
		'utu'	=> 'utu_Latn_PG',
		'uum'	=> 'uum_Grek_GE',
		'uur'	=> 'uur_Latn_VU',
		'uve'	=> 'uve_Latn_NC',
		'uvh'	=> 'uvh_Latn_PG',
		'uvl'	=> 'uvl_Latn_PG',
		'uwa'	=> 'uwa_Latn_AU',
		'uya'	=> 'uya_Latn_NG',
		'uzs'	=> 'uzs_Arab_AF',
		'vaa'	=> 'vaa_Taml_IN',
		'vae'	=> 'vae_Latn_CF',
		'vaf'	=> 'vaf_Arab_IR',
		'vag'	=> 'vag_Latn_GH',
		'vah'	=> 'vah_Deva_IN',
		'vaj'	=> 'vaj_Latn_NA',
		'val'	=> 'val_Latn_PG',
		'vam'	=> 'vam_Latn_PG',
		'van'	=> 'van_Latn_PG',
		'vao'	=> 'vao_Latn_VU',
		'vap'	=> 'vap_Latn_IN',
		'var'	=> 'var_Latn_MX',
		'vas'	=> 'vas_Deva_IN',
		'vau'	=> 'vau_Latn_CD',
		'vav'	=> 'vav_Deva_IN',
		'vay'	=> 'vay_Deva_NP',
		'vbb'	=> 'vbb_Latn_ID',
		'vbk'	=> 'vbk_Latn_PH',
		'vem'	=> 'vem_Latn_NG',
		'veo'	=> 'veo_Latn_US',
		'ver'	=> 'ver_Latn_NG',
		'vgr'	=> 'vgr_Arab_PK',
		'vid'	=> 'vid_Latn_TZ',
		'vif'	=> 'vif_Latn_CG',
		'vig'	=> 'vig_Latn_BF',
		'vil'	=> 'vil_Latn_AR',
		'vin'	=> 'vin_Latn_TZ',
		'vit'	=> 'vit_Latn_NG',
		'viv'	=> 'viv_Latn_PG',
		'vjk'	=> 'vjk_Deva_IN',
		'vka'	=> 'vka_Latn_AU',
		'vkj'	=> 'vkj_Latn_TD',
		'vkk'	=> 'vkk_Latn_ID',
		'vkl'	=> 'vkl_Latn_ID',
		'vkm'	=> 'vkm_Latn_BR',
		'vkn'	=> 'vkn_Latn_NG',
		'vko'	=> 'vko_Latn_ID',
		'vkp'	=> 'vkp_Latn_IN',
		'vkt'	=> 'vkt_Latn_ID',
		'vku'	=> 'vku_Latn_AU',
		'vkz'	=> 'vkz_Latn_NG',
		'vlp'	=> 'vlp_Latn_VU',
		'vma'	=> 'vma_Latn_AU',
		'vmb'	=> 'vmb_Latn_AU',
		'vmc'	=> 'vmc_Latn_MX',
		'vmd'	=> 'vmd_Knda_IN',
		'vme'	=> 'vme_Latn_ID',
		'vmg'	=> 'vmg_Latn_PG',
		'vmh'	=> 'vmh_Arab_IR',
		'vmi'	=> 'vmi_Latn_AU',
		'vmj'	=> 'vmj_Latn_MX',
		'vmk'	=> 'vmk_Latn_MZ',
		'vml'	=> 'vml_Latn_AU',
		'vmm'	=> 'vmm_Latn_MX',
		'vmp'	=> 'vmp_Latn_MX',
		'vmq'	=> 'vmq_Latn_MX',
		'vmr'	=> 'vmr_Latn_MZ',
		'vms'	=> 'vms_Latn_ID',
		'vmu'	=> 'vmu_Latn_AU',
		'vmx'	=> 'vmx_Latn_MX',
		'vmy'	=> 'vmy_Latn_MX',
		'vmz'	=> 'vmz_Latn_MX',
		'vnk'	=> 'vnk_Latn_SB',
		'vnm'	=> 'vnm_Latn_VU',
		'vnp'	=> 'vnp_Latn_VU',
		'vor'	=> 'vor_Latn_NG',
		'vra'	=> 'vra_Latn_VU',
		'vrs'	=> 'vrs_Latn_SB',
		'vrt'	=> 'vrt_Latn_VU',
		'vto'	=> 'vto_Latn_ID',
		'vum'	=> 'vum_Latn_GA',
		'vut'	=> 'vut_Latn_CM',
		'vwa'	=> 'vwa_Latn_CN',
		'waa'	=> 'waa_Latn_US',
		'wab'	=> 'wab_Latn_PG',
		'wac'	=> 'wac_Latn_US',
		'wad'	=> 'wad_Latn_ID',
		'waf'	=> 'waf_Latn_BR',
		'wag'	=> 'wag_Latn_PG',
		'wah'	=> 'wah_Latn_ID',
		'wai'	=> 'wai_Latn_ID',
		'waj'	=> 'waj_Latn_PG',
		'wam'	=> 'wam_Latn_US',
		'wan'	=> 'wan_Latn_CI',
		'wap'	=> 'wap_Latn_GY',
		'waq'	=> 'waq_Latn_AU',
		'was'	=> 'was_Latn_US',
		'wat'	=> 'wat_Latn_PG',
		'wau'	=> 'wau_Latn_BR',
		'wav'	=> 'wav_Latn_NG',
		'waw'	=> 'waw_Latn_BR',
		'wax'	=> 'wax_Latn_PG',
		'way'	=> 'way_Latn_SR',
		'waz'	=> 'waz_Latn_PG',
		'wba'	=> 'wba_Latn_VE',
		'wbb'	=> 'wbb_Latn_ID',
		'wbe'	=> 'wbe_Latn_ID',
		'wbf'	=> 'wbf_Latn_BF',
		'wbh'	=> 'wbh_Latn_TZ',
		'wbi'	=> 'wbi_Latn_TZ',
		'wbj'	=> 'wbj_Latn_TZ',
		'wbk'	=> 'wbk_Arab_AF',
		'wbl'	=> 'wbl_Latn_PK',
		'wbm'	=> 'wbm_Latn_CN',
		'wbt'	=> 'wbt_Latn_AU',
		'wbv'	=> 'wbv_Latn_AU',
		'wbw'	=> 'wbw_Latn_ID',
		'wca'	=> 'wca_Latn_BR',
		'wci'	=> 'wci_Latn_TG',
		'wdd'	=> 'wdd_Latn_GA',
		'wdg'	=> 'wdg_Latn_PG',
		'wdj'	=> 'wdj_Latn_AU',
		'wdk'	=> 'wdk_Latn_AU',
		'wdt'	=> 'wdt_Latn_CA',
		'wdu'	=> 'wdu_Latn_AU',
		'wdy'	=> 'wdy_Latn_AU',
		'wec'	=> 'wec_Latn_CI',
		'wed'	=> 'wed_Latn_PG',
		'weg'	=> 'weg_Latn_AU',
		'weh'	=> 'weh_Latn_CM',
		'wei'	=> 'wei_Latn_PG',
		'wem'	=> 'wem_Latn_BJ',
		'weo'	=> 'weo_Latn_ID',
		'wep'	=> 'wep_Latn_DE',
		'wer'	=> 'wer_Latn_PG',
		'wes'	=> 'wes_Latn_CM',
		'wet'	=> 'wet_Latn_ID',
		'weu'	=> 'weu_Latn_MM',
		'wew'	=> 'wew_Latn_ID',
		'wfg'	=> 'wfg_Latn_ID',
		'wga'	=> 'wga_Latn_AU',
		'wgb'	=> 'wgb_Latn_PG',
		'wgg'	=> 'wgg_Latn_AU',
		'wgi'	=> 'wgi_Latn_PG',
		'wgo'	=> 'wgo_Latn_ID',
		'wgu'	=> 'wgu_Latn_AU',
		'wgy'	=> 'wgy_Latn_AU',
		'wha'	=> 'wha_Latn_ID',
		'whg'	=> 'whg_Latn_PG',
		'whk'	=> 'whk_Latn_ID',
		'whu'	=> 'whu_Latn_ID',
		'wib'	=> 'wib_Latn_BF',
		'wic'	=> 'wic_Latn_US',
		'wie'	=> 'wie_Latn_AU',
		'wif'	=> 'wif_Latn_AU',
		'wig'	=> 'wig_Latn_AU',
		'wih'	=> 'wih_Latn_AU',
		'wii'	=> 'wii_Latn_PG',
		'wij'	=> 'wij_Latn_AU',
		'wik'	=> 'wik_Latn_AU',
		'wil'	=> 'wil_Latn_AU',
		'wim'	=> 'wim_Latn_AU',
		'win'	=> 'win_Latn_US',
		'wir'	=> 'wir_Latn_BR',
		'wiu'	=> 'wiu_Latn_PG',
		'wiv'	=> 'wiv_Latn_PG',
		'wiy'	=> 'wiy_Latn_US',
		'wja'	=> 'wja_Latn_NG',
		'wji'	=> 'wji_Latn_NG',
		'wka'	=> 'wka_Latn_TZ',
		'wkd'	=> 'wkd_Latn_ID',
		'wkr'	=> 'wkr_Latn_AU',
		'wkw'	=> 'wkw_Latn_AU',
		'wky'	=> 'wky_Latn_AU',
		'wla'	=> 'wla_Latn_PG',
		'wle'	=> 'wle_Ethi_ET',
		'wlg'	=> 'wlg_Latn_AU',
		'wlh'	=> 'wlh_Latn_TL',
		'wli'	=> 'wli_Latn_ID',
		'wlm'	=> 'wlm_Latn_GB',
		'wlo'	=> 'wlo_Arab_ID',
		'wlr'	=> 'wlr_Latn_VU',
		'wlu'	=> 'wlu_Latn_AU',
		'wlv'	=> 'wlv_Latn_AR',
		'wlw'	=> 'wlw_Latn_ID',
		'wlx'	=> 'wlx_Latn_GH',
		'wma'	=> 'wma_Latn_NG',
		'wmb'	=> 'wmb_Latn_AU',
		'wmc'	=> 'wmc_Latn_PG',
		'wmd'	=> 'wmd_Latn_BR',
		'wme'	=> 'wme_Deva_NP',
		'wmh'	=> 'wmh_Latn_TL',
		'wmi'	=> 'wmi_Latn_AU',
		'wmm'	=> 'wmm_Latn_ID',
		'wmn'	=> 'wmn_Latn_NC',
		'wmo'	=> 'wmo_Latn_PG',
		'wms'	=> 'wms_Latn_ID',
		'wmt'	=> 'wmt_Latn_AU',
		'wmw'	=> 'wmw_Latn_MZ',
		'wmx'	=> 'wmx_Latn_PG',
		'wnb'	=> 'wnb_Latn_PG',
		'wnc'	=> 'wnc_Latn_PG',
		'wnd'	=> 'wnd_Latn_AU',
		'wne'	=> 'wne_Arab_PK',
		'wng'	=> 'wng_Latn_ID',
		'wnk'	=> 'wnk_Latn_ID',
		'wnm'	=> 'wnm_Latn_AU',
		'wnn'	=> 'wnn_Latn_AU',
		'wno'	=> 'wno_Latn_ID',
		'wnp'	=> 'wnp_Latn_PG',
		'wnu'	=> 'wnu_Latn_PG',
		'wnw'	=> 'wnw_Latn_US',
		'wny'	=> 'wny_Latn_AU',
		'woa'	=> 'woa_Latn_AU',
		'wob'	=> 'wob_Latn_CI',
		'woc'	=> 'woc_Latn_PG',
		'wod'	=> 'wod_Latn_ID',
		'woe'	=> 'woe_Latn_FM',
		'wof'	=> 'wof_Latn_GM',
		'wog'	=> 'wog_Latn_PG',
		'woi'	=> 'woi_Latn_ID',
		'wok'	=> 'wok_Latn_CM',
		'wom'	=> 'wom_Latn_NG',
		'won'	=> 'won_Latn_CD',
		'woo'	=> 'woo_Latn_ID',
		'wor'	=> 'wor_Latn_ID',
		'wos'	=> 'wos_Latn_PG',
		'wow'	=> 'wow_Latn_ID',
		'wpc'	=> 'wpc_Latn_VE',
		'wrb'	=> 'wrb_Latn_AU',
		'wrg'	=> 'wrg_Latn_AU',
		'wrh'	=> 'wrh_Latn_AU',
		'wri'	=> 'wri_Latn_AU',
		'wrk'	=> 'wrk_Latn_AU',
		'wrl'	=> 'wrl_Latn_AU',
		'wrm'	=> 'wrm_Latn_AU',
		'wro'	=> 'wro_Latn_AU',
		'wrp'	=> 'wrp_Latn_ID',
		'wrr'	=> 'wrr_Latn_AU',
		'wrs'	=> 'wrs_Latn_PG',
		'wru'	=> 'wru_Latn_ID',
		'wrv'	=> 'wrv_Latn_PG',
		'wrw'	=> 'wrw_Latn_AU',
		'wrx'	=> 'wrx_Latn_ID',
		'wrz'	=> 'wrz_Latn_AU',
		'wsa'	=> 'wsa_Latn_ID',
		'wsi'	=> 'wsi_Latn_VU',
		'wsk'	=> 'wsk_Latn_PG',
		'wsr'	=> 'wsr_Latn_PG',
		'wss'	=> 'wss_Latn_GH',
		'wsu'	=> 'wsu_Latn_BR',
		'wsv'	=> 'wsv_Arab_AF',
		'wtb'	=> 'wtb_Latn_TZ',
		'wtf'	=> 'wtf_Latn_PG',
		'wth'	=> 'wth_Latn_AU',
		'wti'	=> 'wti_Latn_ET',
		'wtk'	=> 'wtk_Latn_PG',
		'wtw'	=> 'wtw_Latn_ID',
		'wua'	=> 'wua_Latn_AU',
		'wub'	=> 'wub_Latn_AU',
		'wud'	=> 'wud_Latn_TG',
		'wul'	=> 'wul_Latn_ID',
		'wum'	=> 'wum_Latn_GA',
		'wun'	=> 'wun_Latn_TZ',
		'wur'	=> 'wur_Latn_AU',
		'wut'	=> 'wut_Latn_PG',
		'wuv'	=> 'wuv_Latn_PG',
		'wux'	=> 'wux_Latn_AU',
		'wuy'	=> 'wuy_Latn_ID',
		'wwa'	=> 'wwa_Latn_BJ',
		'wwb'	=> 'wwb_Latn_AU',
		'wwo'	=> 'wwo_Latn_VU',
		'wwr'	=> 'wwr_Latn_AU',
		'www'	=> 'www_Latn_CM',
		'wxw'	=> 'wxw_Latn_AU',
		'wyb'	=> 'wyb_Latn_AU',
		'wyi'	=> 'wyi_Latn_AU',
		'wym'	=> 'wym_Latn_PL',
		'wyn'	=> 'wyn_Latn_US',
		'wyr'	=> 'wyr_Latn_BR',
		'wyy'	=> 'wyy_Latn_FJ',
		'xaa'	=> 'xaa_Latn_ES',
		'xab'	=> 'xab_Latn_NG',
		'xai'	=> 'xai_Latn_BR',
		'xaj'	=> 'xaj_Latn_BR',
		'xak'	=> 'xak_Latn_VE',
		'xal'	=> 'xal_Cyrl_RU',
		'xam'	=> 'xam_Latn_ZA',
		'xan'	=> 'xan_Ethi_ET',
		'xao'	=> 'xao_Latn_VN',
		'xar'	=> 'xar_Latn_PG',
		'xas'	=> 'xas_Cyrl_RU',
		'xat'	=> 'xat_Latn_BR',
		'xau'	=> 'xau_Latn_ID',
		'xaw'	=> 'xaw_Latn_US',
		'xay'	=> 'xay_Latn_ID',
		'xbb'	=> 'xbb_Latn_AU',
		'xbd'	=> 'xbd_Latn_AU',
		'xbe'	=> 'xbe_Latn_AU',
		'xbg'	=> 'xbg_Latn_AU',
		'xbi'	=> 'xbi_Latn_PG',
		'xbj'	=> 'xbj_Latn_AU',
		'xbm'	=> 'xbm_Latn_FR',
		'xbn'	=> 'xbn_Latn_MY',
		'xbp'	=> 'xbp_Latn_AU',
		'xbr'	=> 'xbr_Latn_ID',
		'xbw'	=> 'xbw_Latn_BR',
		'xby'	=> 'xby_Latn_AU',
		'xch'	=> 'xch_Latn_US',
		'xda'	=> 'xda_Latn_AU',
		'xdk'	=> 'xdk_Latn_AU',
		'xdo'	=> 'xdo_Latn_AO',
		'xdq'	=> 'xdq_Cyrl_RU',
		'xdy'	=> 'xdy_Latn_ID',
		'xed'	=> 'xed_Latn_CM',
		'xeg'	=> 'xeg_Latn_ZA',
		'xem'	=> 'xem_Latn_ID',
		'xer'	=> 'xer_Latn_BR',
		'xes'	=> 'xes_Latn_PG',
		'xet'	=> 'xet_Latn_BR',
		'xeu'	=> 'xeu_Latn_PG',
		'xgb'	=> 'xgb_Latn_CI',
		'xgd'	=> 'xgd_Latn_AU',
		'xgg'	=> 'xgg_Latn_AU',
		'xgi'	=> 'xgi_Latn_AU',
		'xgm'	=> 'xgm_Latn_AU',
		'xgu'	=> 'xgu_Latn_AU',
		'xgw'	=> 'xgw_Latn_AU',
		'xhe'	=> 'xhe_Arab_PK',
		'xhm'	=> 'xhm_Khmr_KH',
		'xhv'	=> 'xhv_Latn_VN',
		'xii'	=> 'xii_Latn_ZA',
		'xin'	=> 'xin_Latn_GT',
		'xir'	=> 'xir_Latn_BR',
		'xis'	=> 'xis_Orya_IN',
		'xiy'	=> 'xiy_Latn_BR',
		'xjb'	=> 'xjb_Latn_AU',
		'xjt'	=> 'xjt_Latn_AU',
		'xka'	=> 'xka_Arab_PK',
		'xkb'	=> 'xkb_Latn_BJ',
		'xkc'	=> 'xkc_Arab_IR',
		'xkd'	=> 'xkd_Latn_ID',
		'xke'	=> 'xke_Latn_ID',
		'xkf'	=> 'xkf_Tibt_BT',
		'xkg'	=> 'xkg_Latn_ML',
		'xkj'	=> 'xkj_Arab_IR',
		'xkl'	=> 'xkl_Latn_ID',
		'xkn'	=> 'xkn_Latn_ID',
		'xkp'	=> 'xkp_Arab_IR',
		'xkq'	=> 'xkq_Latn_ID',
		'xkr'	=> 'xkr_Latn_BR',
		'xks'	=> 'xks_Latn_ID',
		'xkt'	=> 'xkt_Latn_GH',
		'xku'	=> 'xku_Latn_CG',
		'xkv'	=> 'xkv_Latn_BW',
		'xkw'	=> 'xkw_Latn_ID',
		'xkx'	=> 'xkx_Latn_PG',
		'xky'	=> 'xky_Latn_MY',
		'xkz'	=> 'xkz_Latn_BT',
		'xla'	=> 'xla_Latn_PG',
		'xly'	=> 'xly_Elym_IR',
		'xma'	=> 'xma_Latn_SO',
		'xmb'	=> 'xmb_Latn_CM',
		'xmc'	=> 'xmc_Latn_MZ',
		'xmd'	=> 'xmd_Latn_CM',
		'xmg'	=> 'xmg_Latn_CM',
		'xmh'	=> 'xmh_Latn_AU',
		'xmj'	=> 'xmj_Latn_CM',
		'xmm'	=> 'xmm_Latn_ID',
		'xmo'	=> 'xmo_Latn_BR',
		'xmp'	=> 'xmp_Latn_AU',
		'xmq'	=> 'xmq_Latn_AU',
		'xmt'	=> 'xmt_Latn_ID',
		'xmu'	=> 'xmu_Latn_AU',
		'xmv'	=> 'xmv_Latn_MG',
		'xmw'	=> 'xmw_Latn_MG',
		'xmx'	=> 'xmx_Latn_ID',
		'xmy'	=> 'xmy_Latn_AU',
		'xmz'	=> 'xmz_Latn_ID',
		'xnb'	=> 'xnb_Latn_TW',
		'xni'	=> 'xni_Latn_AU',
		'xnj'	=> 'xnj_Latn_TZ',
		'xnk'	=> 'xnk_Latn_AU',
		'xnm'	=> 'xnm_Latn_AU',
		'xnn'	=> 'xnn_Latn_PH',
		'xnq'	=> 'xnq_Latn_MZ',
		'xnt'	=> 'xnt_Latn_US',
		'xnu'	=> 'xnu_Latn_AU',
		'xny'	=> 'xny_Latn_AU',
		'xnz'	=> 'xnz_Latn_EG',
		'xoc'	=> 'xoc_Latn_NG',
		'xod'	=> 'xod_Latn_ID',
		'xoi'	=> 'xoi_Latn_PG',
		'xok'	=> 'xok_Latn_BR',
		'xom'	=> 'xom_Latn_SD',
		'xon'	=> 'xon_Latn_GH',
		'xoo'	=> 'xoo_Latn_BR',
		'xop'	=> 'xop_Latn_PG',
		'xor'	=> 'xor_Latn_BR',
		'xow'	=> 'xow_Latn_PG',
		'xpa'	=> 'xpa_Latn_AU',
		'xpb'	=> 'xpb_Latn_AU',
		'xpd'	=> 'xpd_Latn_AU',
		'xpf'	=> 'xpf_Latn_AU',
		'xpg'	=> 'xpg_Grek_TR',
		'xph'	=> 'xph_Latn_AU',
		'xpi'	=> 'xpi_Ogam_GB',
		'xpj'	=> 'xpj_Latn_AU',
		'xpk'	=> 'xpk_Latn_BR',
		'xpl'	=> 'xpl_Latn_AU',
		'xpm'	=> 'xpm_Cyrl_RU',
		'xpn'	=> 'xpn_Latn_BR',
		'xpo'	=> 'xpo_Latn_MX',
		'xpq'	=> 'xpq_Latn_US',
		'xpt'	=> 'xpt_Latn_AU',
		'xpv'	=> 'xpv_Latn_AU',
		'xpw'	=> 'xpw_Latn_AU',
		'xpx'	=> 'xpx_Latn_AU',
		'xpz'	=> 'xpz_Latn_AU',
		'xra'	=> 'xra_Latn_BR',
		'xrb'	=> 'xrb_Latn_BF',
		'xrd'	=> 'xrd_Latn_AU',
		'xre'	=> 'xre_Latn_BR',
		'xrg'	=> 'xrg_Latn_AU',
		'xri'	=> 'xri_Latn_BR',
		'xrm'	=> 'xrm_Cyrl_RU',
		'xrn'	=> 'xrn_Cyrl_RU',
		'xrr'	=> 'xrr_Latn_IT',
		'xru'	=> 'xru_Latn_AU',
		'xrw'	=> 'xrw_Latn_PG',
		'xsb'	=> 'xsb_Latn_PH',
		'xse'	=> 'xse_Latn_ID',
		'xsh'	=> 'xsh_Latn_NG',
		'xsi'	=> 'xsi_Latn_PG',
		'xsm'	=> 'xsm_Latn_GH',
		'xsn'	=> 'xsn_Latn_NG',
		'xsp'	=> 'xsp_Latn_PG',
		'xsq'	=> 'xsq_Latn_MZ',
		'xsu'	=> 'xsu_Latn_VE',
		'xsy'	=> 'xsy_Latn_TW',
		'xta'	=> 'xta_Latn_MX',
		'xtb'	=> 'xtb_Latn_MX',
		'xtc'	=> 'xtc_Latn_SD',
		'xtd'	=> 'xtd_Latn_MX',
		'xte'	=> 'xte_Latn_ID',
		'xth'	=> 'xth_Latn_AU',
		'xti'	=> 'xti_Latn_MX',
		'xtj'	=> 'xtj_Latn_MX',
		'xtl'	=> 'xtl_Latn_MX',
		'xtm'	=> 'xtm_Latn_MX',
		'xtn'	=> 'xtn_Latn_MX',
		'xtp'	=> 'xtp_Latn_MX',
		'xtq'	=> 'xtq_Brah_IR',
		'xts'	=> 'xts_Latn_MX',
		'xtt'	=> 'xtt_Latn_MX',
		'xtu'	=> 'xtu_Latn_MX',
		'xtv'	=> 'xtv_Latn_AU',
		'xtw'	=> 'xtw_Latn_BR',
		'xty'	=> 'xty_Latn_MX',
		'xub'	=> 'xub_Taml_IN',
		'xud'	=> 'xud_Latn_AU',
		'xuj'	=> 'xuj_Taml_IN',
		'xul'	=> 'xul_Latn_AU',
		'xum'	=> 'xum_Latn_IT',
		'xun'	=> 'xun_Latn_AU',
		'xuo'	=> 'xuo_Latn_TD',
		'xut'	=> 'xut_Latn_AU',
		'xuu'	=> 'xuu_Latn_NA',
		'xve'	=> 'xve_Ital_IT',
		'xvi'	=> 'xvi_Arab_AF',
		'xvn'	=> 'xvn_Latn_ES',
		'xvo'	=> 'xvo_Latn_IT',
		'xvs'	=> 'xvs_Latn_IT',
		'xwa'	=> 'xwa_Latn_BR',
		'xwd'	=> 'xwd_Latn_AU',
		'xwe'	=> 'xwe_Latn_BJ',
		'xwj'	=> 'xwj_Latn_AU',
		'xwk'	=> 'xwk_Latn_AU',
		'xwl'	=> 'xwl_Latn_BJ',
		'xwo'	=> 'xwo_Cyrl_RU',
		'xwr'	=> 'xwr_Latn_ID',
		'xwt'	=> 'xwt_Latn_AU',
		'xww'	=> 'xww_Latn_AU',
		'xxb'	=> 'xxb_Latn_GH',
		'xxk'	=> 'xxk_Latn_ID',
		'xxm'	=> 'xxm_Latn_AU',
		'xxr'	=> 'xxr_Latn_BR',
		'xxt'	=> 'xxt_Latn_ID',
		'xya'	=> 'xya_Latn_AU',
		'xyb'	=> 'xyb_Latn_AU',
		'xyj'	=> 'xyj_Latn_AU',
		'xyk'	=> 'xyk_Latn_AU',
		'xyl'	=> 'xyl_Latn_BR',
		'xyt'	=> 'xyt_Latn_AU',
		'xyy'	=> 'xyy_Latn_AU',
		'xzh'	=> 'xzh_Marc_CN',
		'xzp'	=> 'xzp_Latn_MX',
		'yaa'	=> 'yaa_Latn_PE',
		'yab'	=> 'yab_Latn_BR',
		'yac'	=> 'yac_Latn_ID',
		'yad'	=> 'yad_Latn_PE',
		'yae'	=> 'yae_Latn_VE',
		'yaf'	=> 'yaf_Latn_CD',
		'yag'	=> 'yag_Latn_CL',
		'yah'	=> 'yah_Latn_TJ',
		'yai'	=> 'yai_Cyrl_TJ',
		'yaj'	=> 'yaj_Latn_CF',
		'yak'	=> 'yak_Latn_US',
		'yal'	=> 'yal_Latn_GN',
		'yam'	=> 'yam_Latn_CM',
		'yan'	=> 'yan_Latn_NI',
		'yaq'	=> 'yaq_Latn_MX',
		'yar'	=> 'yar_Latn_VE',
		'yas'	=> 'yas_Latn_CM',
		'yat'	=> 'yat_Latn_CM',
		'yau'	=> 'yau_Latn_VE',
		'yaw'	=> 'yaw_Latn_BR',
		'yax'	=> 'yax_Latn_AO',
		'yay'	=> 'yay_Latn_NG',
		'yaz'	=> 'yaz_Latn_NG',
		'yba'	=> 'yba_Latn_NG',
		'ybe'	=> 'ybe_Latn_CN',
		'ybh'	=> 'ybh_Deva_NP',
		'ybi'	=> 'ybi_Deva_NP',
		'ybj'	=> 'ybj_Latn_NG',
		'ybl'	=> 'ybl_Latn_NG',
		'ybm'	=> 'ybm_Latn_PG',
		'ybn'	=> 'ybn_Latn_BR',
		'ybo'	=> 'ybo_Latn_PG',
		'ybx'	=> 'ybx_Latn_PG',
		'yby'	=> 'yby_Latn_PG',
		'ycl'	=> 'ycl_Latn_CN',
		'ycn'	=> 'ycn_Latn_CO',
		'ycr'	=> 'ycr_Latn_TW',
		'yda'	=> 'yda_Latn_AU',
		'yde'	=> 'yde_Latn_PG',
		'ydg'	=> 'ydg_Arab_PK',
		'ydk'	=> 'ydk_Latn_PG',
		'yea'	=> 'yea_Mlym_IN',
		'yec'	=> 'yec_Latn_DE',
		'yee'	=> 'yee_Latn_PG',
		'yei'	=> 'yei_Latn_CM',
		'yej'	=> 'yej_Grek_GR',
		'yel'	=> 'yel_Latn_CD',
		'yer'	=> 'yer_Latn_NG',
		'yes'	=> 'yes_Latn_NG',
		'yet'	=> 'yet_Latn_ID',
		'yeu'	=> 'yeu_Telu_IN',
		'yev'	=> 'yev_Latn_PG',
		'yey'	=> 'yey_Latn_BW',
		'yga'	=> 'yga_Latn_AU',
		'ygi'	=> 'ygi_Latn_AU',
		'ygl'	=> 'ygl_Latn_PG',
		'ygm'	=> 'ygm_Latn_PG',
		'ygp'	=> 'ygp_Plrd_CN',
		'ygr'	=> 'ygr_Latn_PG',
		'ygu'	=> 'ygu_Latn_AU',
		'ygw'	=> 'ygw_Latn_PG',
		'yhd'	=> 'yhd_Hebr_IL',
		'yia'	=> 'yia_Latn_AU',
		'yig'	=> 'yig_Yiii_CN',
		'yih'	=> 'yih_Hebr_DE',
		'yii'	=> 'yii_Latn_AU',
		'yij'	=> 'yij_Latn_AU',
		'yil'	=> 'yil_Latn_AU',
		'yim'	=> 'yim_Latn_IN',
		'yir'	=> 'yir_Latn_ID',
		'yis'	=> 'yis_Latn_PG',
		'yiv'	=> 'yiv_Yiii_CN',
		'yka'	=> 'yka_Latn_PH',
		'ykg'	=> 'ykg_Cyrl_RU',
		'ykh'	=> 'ykh_Cyrl_MN',
		'yki'	=> 'yki_Latn_ID',
		'ykk'	=> 'ykk_Latn_PG',
		'ykm'	=> 'ykm_Latn_PG',
		'yko'	=> 'yko_Latn_CM',
		'ykr'	=> 'ykr_Latn_PG',
		'yky'	=> 'yky_Latn_CF',
		'yla'	=> 'yla_Latn_PG',
		'ylb'	=> 'ylb_Latn_PG',
		'yle'	=> 'yle_Latn_PG',
		'ylg'	=> 'ylg_Latn_PG',
		'yli'	=> 'yli_Latn_ID',
		'yll'	=> 'yll_Latn_PG',
		'ylr'	=> 'ylr_Latn_AU',
		'ylu'	=> 'ylu_Latn_PG',
		'yly'	=> 'yly_Latn_NC',
		'ymb'	=> 'ymb_Latn_PG',
		'yme'	=> 'yme_Latn_PE',
		'ymg'	=> 'ymg_Latn_CD',
		'ymk'	=> 'ymk_Latn_MZ',
		'yml'	=> 'yml_Latn_PG',
		'ymm'	=> 'ymm_Latn_SO',
		'ymn'	=> 'ymn_Latn_ID',
		'ymo'	=> 'ymo_Latn_PG',
		'ymp'	=> 'ymp_Latn_PG',
		'yna'	=> 'yna_Plrd_CN',
		'ynd'	=> 'ynd_Latn_AU',
		'yng'	=> 'yng_Latn_CD',
		'ynk'	=> 'ynk_Cyrl_RU',
		'ynl'	=> 'ynl_Latn_PG',
		'ynq'	=> 'ynq_Latn_NG',
		'yns'	=> 'yns_Latn_CD',
		'ynu'	=> 'ynu_Latn_CO',
		'yob'	=> 'yob_Latn_PG',
		'yog'	=> 'yog_Latn_PH',
		'yoi'	=> 'yoi_Jpan_JP',
		'yok'	=> 'yok_Latn_US',
		'yol'	=> 'yol_Latn_IE',
		'yom'	=> 'yom_Latn_CD',
		'yon'	=> 'yon_Latn_PG',
		'yot'	=> 'yot_Latn_NG',
		'yoy'	=> 'yoy_Thai_TH',
		'yra'	=> 'yra_Latn_PG',
		'yrb'	=> 'yrb_Latn_PG',
		'yre'	=> 'yre_Latn_CI',
		'yrk'	=> 'yrk_Cyrl_RU',
		'yrm'	=> 'yrm_Latn_AU',
		'yro'	=> 'yro_Latn_BR',
		'yrs'	=> 'yrs_Latn_ID',
		'yrw'	=> 'yrw_Latn_PG',
		'yry'	=> 'yry_Latn_AU',
		'ysd'	=> 'ysd_Yiii_CN',
		'ysn'	=> 'ysn_Yiii_CN',
		'ysp'	=> 'ysp_Yiii_CN',
		'ysr'	=> 'ysr_Cyrl_RU',
		'yss'	=> 'yss_Latn_PG',
		'ysy'	=> 'ysy_Plrd_CN',
		'ytw'	=> 'ytw_Latn_PG',
		'yty'	=> 'yty_Latn_AU',
		'yub'	=> 'yub_Latn_AU',
		'yuc'	=> 'yuc_Latn_US',
		'yud'	=> 'yud_Hebr_IL',
		'yuf'	=> 'yuf_Latn_US',
		'yug'	=> 'yug_Cyrl_RU',
		'yui'	=> 'yui_Latn_CO',
		'yuj'	=> 'yuj_Latn_PG',
		'yul'	=> 'yul_Latn_CF',
		'yum'	=> 'yum_Latn_US',
		'yun'	=> 'yun_Latn_NG',
		'yup'	=> 'yup_Latn_CO',
		'yuq'	=> 'yuq_Latn_BO',
		'yur'	=> 'yur_Latn_US',
		'yut'	=> 'yut_Latn_PG',
		'yuw'	=> 'yuw_Latn_PG',
		'yux'	=> 'yux_Cyrl_RU',
		'yuz'	=> 'yuz_Latn_BO',
		'yva'	=> 'yva_Latn_ID',
		'yvt'	=> 'yvt_Latn_VE',
		'ywa'	=> 'ywa_Latn_PG',
		'ywg'	=> 'ywg_Latn_AU',
		'ywn'	=> 'ywn_Latn_BR',
		'ywq'	=> 'ywq_Plrd_CN',
		'ywr'	=> 'ywr_Latn_AU',
		'ywu'	=> 'ywu_Plrd_CN',
		'yww'	=> 'yww_Latn_AU',
		'yxa'	=> 'yxa_Latn_AU',
		'yxg'	=> 'yxg_Latn_AU',
		'yxl'	=> 'yxl_Latn_AU',
		'yxm'	=> 'yxm_Latn_AU',
		'yxu'	=> 'yxu_Latn_AU',
		'yxy'	=> 'yxy_Latn_AU',
		'yyr'	=> 'yyr_Latn_AU',
		'yyu'	=> 'yyu_Latn_PG',
		'zaa'	=> 'zaa_Latn_MX',
		'zab'	=> 'zab_Latn_MX',
		'zac'	=> 'zac_Latn_MX',
		'zad'	=> 'zad_Latn_MX',
		'zae'	=> 'zae_Latn_MX',
		'zaf'	=> 'zaf_Latn_MX',
		'zah'	=> 'zah_Latn_NG',
		'zaj'	=> 'zaj_Latn_TZ',
		'zak'	=> 'zak_Latn_TZ',
		'zam'	=> 'zam_Latn_MX',
		'zao'	=> 'zao_Latn_MX',
		'zap'	=> 'zap_Latn_MX',
		'zaq'	=> 'zaq_Latn_MX',
		'zar'	=> 'zar_Latn_MX',
		'zas'	=> 'zas_Latn_MX',
		'zat'	=> 'zat_Latn_MX',
		'zau'	=> 'zau_Tibt_IN',
		'zav'	=> 'zav_Latn_MX',
		'zaw'	=> 'zaw_Latn_MX',
		'zax'	=> 'zax_Latn_MX',
		'zay'	=> 'zay_Latn_ET',
		'zaz'	=> 'zaz_Latn_NG',
		'zba'	=> 'zba_Arab_001',
		'zbc'	=> 'zbc_Latn_MY',
		'zbe'	=> 'zbe_Latn_MY',
		'zbt'	=> 'zbt_Latn_ID',
		'zbu'	=> 'zbu_Latn_NG',
		'zbw'	=> 'zbw_Latn_MY',
		'zca'	=> 'zca_Latn_MX',
		'zch'	=> 'zch_Hani_CN',
		'zeg'	=> 'zeg_Latn_PG',
		'zeh'	=> 'zeh_Hani_CN',
		'zem'	=> 'zem_Latn_NG',
		'zen'	=> 'zen_Tfng_MR',
		'zga'	=> 'zga_Latn_TZ',
		'zgb'	=> 'zgb_Hani_CN',
		'zgm'	=> 'zgm_Hani_CN',
		'zgn'	=> 'zgn_Hani_CN',
		'zgr'	=> 'zgr_Latn_PG',
		'zhd'	=> 'zhd_Hani_CN',
		'zhi'	=> 'zhi_Latn_NG',
		'zhn'	=> 'zhn_Latn_CN',
		'zhw'	=> 'zhw_Latn_CM',
		'zia'	=> 'zia_Latn_PG',
		'zik'	=> 'zik_Latn_PG',
		'zil'	=> 'zil_Latn_GN',
		'zim'	=> 'zim_Latn_TD',
		'zin'	=> 'zin_Latn_TZ',
		'ziw'	=> 'ziw_Latn_TZ',
		'ziz'	=> 'ziz_Latn_NG',
		'zka'	=> 'zka_Latn_ID',
		'zkd'	=> 'zkd_Latn_MM',
		'zko'	=> 'zko_Cyrl_RU',
		'zkp'	=> 'zkp_Latn_BR',
		'zku'	=> 'zku_Latn_AU',
		'zkz'	=> 'zkz_Cyrl_RU',
		'zla'	=> 'zla_Latn_CD',
		'zlj'	=> 'zlj_Hani_CN',
		'zln'	=> 'zln_Hani_CN',
		'zlq'	=> 'zlq_Hani_CN',
		'zlu'	=> 'zlu_Latn_NG',
		'zma'	=> 'zma_Latn_AU',
		'zmb'	=> 'zmb_Latn_CD',
		'zmc'	=> 'zmc_Latn_AU',
		'zmd'	=> 'zmd_Latn_AU',
		'zme'	=> 'zme_Latn_AU',
		'zmf'	=> 'zmf_Latn_CD',
		'zmg'	=> 'zmg_Latn_AU',
		'zmh'	=> 'zmh_Latn_PG',
		'zmj'	=> 'zmj_Latn_AU',
		'zmk'	=> 'zmk_Latn_AU',
		'zml'	=> 'zml_Latn_AU',
		'zmm'	=> 'zmm_Latn_AU',
		'zmn'	=> 'zmn_Latn_GA',
		'zmo'	=> 'zmo_Latn_SD',
		'zmp'	=> 'zmp_Latn_CD',
		'zmq'	=> 'zmq_Latn_CD',
		'zmr'	=> 'zmr_Latn_AU',
		'zms'	=> 'zms_Latn_CD',
		'zmt'	=> 'zmt_Latn_AU',
		'zmu'	=> 'zmu_Latn_AU',
		'zmv'	=> 'zmv_Latn_AU',
		'zmw'	=> 'zmw_Latn_CD',
		'zmx'	=> 'zmx_Latn_CG',
		'zmy'	=> 'zmy_Latn_AU',
		'zmz'	=> 'zmz_Latn_CD',
		'zna'	=> 'zna_Latn_TD',
		'zne'	=> 'zne_Latn_CD',
		'zng'	=> 'zng_Latn_VN',
		'znk'	=> 'znk_Latn_AU',
		'zns'	=> 'zns_Latn_NG',
		'zoc'	=> 'zoc_Latn_MX',
		'zoh'	=> 'zoh_Latn_MX',
		'zom'	=> 'zom_Latn_IN',
		'zoo'	=> 'zoo_Latn_MX',
		'zoq'	=> 'zoq_Latn_MX',
		'zor'	=> 'zor_Latn_MX',
		'zos'	=> 'zos_Latn_MX',
		'zpa'	=> 'zpa_Latn_MX',
		'zpb'	=> 'zpb_Latn_MX',
		'zpc'	=> 'zpc_Latn_MX',
		'zpd'	=> 'zpd_Latn_MX',
		'zpe'	=> 'zpe_Latn_MX',
		'zpf'	=> 'zpf_Latn_MX',
		'zpg'	=> 'zpg_Latn_MX',
		'zph'	=> 'zph_Latn_MX',
		'zpi'	=> 'zpi_Latn_MX',
		'zpj'	=> 'zpj_Latn_MX',
		'zpk'	=> 'zpk_Latn_MX',
		'zpl'	=> 'zpl_Latn_MX',
		'zpm'	=> 'zpm_Latn_MX',
		'zpn'	=> 'zpn_Latn_MX',
		'zpo'	=> 'zpo_Latn_MX',
		'zpp'	=> 'zpp_Latn_MX',
		'zpq'	=> 'zpq_Latn_MX',
		'zpr'	=> 'zpr_Latn_MX',
		'zps'	=> 'zps_Latn_MX',
		'zpt'	=> 'zpt_Latn_MX',
		'zpu'	=> 'zpu_Latn_MX',
		'zpv'	=> 'zpv_Latn_MX',
		'zpw'	=> 'zpw_Latn_MX',
		'zpx'	=> 'zpx_Latn_MX',
		'zpy'	=> 'zpy_Latn_MX',
		'zpz'	=> 'zpz_Latn_MX',
		'zqe'	=> 'zqe_Hani_CN',
		'zrg'	=> 'zrg_Orya_IN',
		'zrn'	=> 'zrn_Latn_TD',
		'zro'	=> 'zro_Latn_EC',
		'zrp'	=> 'zrp_Hebr_FR',
		'zrs'	=> 'zrs_Latn_ID',
		'zsa'	=> 'zsa_Latn_PG',
		'zsr'	=> 'zsr_Latn_MX',
		'zsu'	=> 'zsu_Latn_PG',
		'zte'	=> 'zte_Latn_MX',
		'ztg'	=> 'ztg_Latn_MX',
		'ztl'	=> 'ztl_Latn_MX',
		'ztm'	=> 'ztm_Latn_MX',
		'ztn'	=> 'ztn_Latn_MX',
		'ztp'	=> 'ztp_Latn_MX',
		'ztq'	=> 'ztq_Latn_MX',
		'zts'	=> 'zts_Latn_MX',
		'ztt'	=> 'ztt_Latn_MX',
		'ztu'	=> 'ztu_Latn_MX',
		'ztx'	=> 'ztx_Latn_MX',
		'zty'	=> 'zty_Latn_MX',
		'zuh'	=> 'zuh_Latn_PG',
		'zum'	=> 'zum_Arab_OM',
		'zun'	=> 'zun_Latn_US',
		'zuy'	=> 'zuy_Latn_CM',
		'zwa'	=> 'zwa_Ethi_ET',
		'zyg'	=> 'zyg_Hani_CN',
		'zyj'	=> 'zyj_Latn_CN',
		'zyn'	=> 'zyn_Hani_CN',
		'zyp'	=> 'zyp_Latn_MM',
		'zzj'	=> 'zzj_Hani_CN',
	}},
);

no Moo::Role;

1;

# vim: tabstop=4
