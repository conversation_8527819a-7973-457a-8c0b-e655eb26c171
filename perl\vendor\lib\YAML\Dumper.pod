=pod

=for comment
DO NOT EDIT. This Pod was generated by Swim v0.1.48.
See http://github.com/ingydotnet/swim-pm#readme

=encoding utf8

=head1 NAME

YAML::Dumper - YAML class for dumping Perl objects to YAML

=head1 SYNOPSIS

    use YAML::Dumper;
    my $dumper = YAML::Dumper->new;
    $dumper->indent_width(4);
    print $dumper->dump({foo => 'bar'});

=head1 DESCRIPTION

YAML::Dumper is the module that YAML.pm used to serialize Perl objects to
YAML. It is fully object oriented and usable on its own.

=head1 AUTHOR

Ingy döt Net <<EMAIL>>

=head1 COPYRIGHT

Copyright 2001-2014. Ingy döt Net

This program is free software; you can redistribute it and/or modify it under
the same terms as Perl itself.

See L<http://www.perl.com/perl/misc/Artistic.html>

=cut
