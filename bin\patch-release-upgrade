#!/usr/bin/perl

use strict;
use warnings;
use Getopt::Long;

use LedgerSMB;
use LedgerSMB::Database;
use LedgerSMB::Database::SchemaChecks::JSON qw( json_formatter_context );

my $user;
my $schema = 'public';
my $script_name = `basename $0`; chomp $script_name;
my $data_dir = '.';
my $usage = qq|
usage: $script_name [ OPTION... ] database

This script upgrades your database when moving from one patchlevel
to another, e.g. 1.3.15 to 1.3.18.


Options:
  --user <user>     This option names the user to be used for logging in.
                     The name of the current user is the default value.
  --data-dir <dir>  Uses directory <dir> to store upgrade failure feedback
                     as well as pre-defined responses for upgrades.
  --help            Prints this help screen.

Note:
  If the user (implied or explicitly specified) requires a password, it
will be asked for explicitly unless it's specified in an environment variable.
This is to prevent passwords being saved into the history file.

|;

sub rebuild_modules {

    my $database = LedgerSMB::Database->new(
        connect_data => {
            user   => $ENV{PGUSER},
            dbname => $ENV{PGDATABASE},
            password => $ENV{PGPASSWORD},
        },
        schema => $schema)
        or die "No database connection.";

    my $out = json_formatter_context {
        return ! $database->apply_changes( checks => 1 );
    } $data_dir;

    if ($out) {
        die "Upgrading database schema failed.\nResults stored in: $out\n";
    }
    $database->upgrade_modules('LOADORDER', $LedgerSMB::VERSION)
        or die "Upgrading modules failed.";

    return 1;
};


sub usage { print $usage; exit; }


GetOptions(
    'help'      => \&usage,
    'user=s'    => \$user,
    'schema=s'  => \$schema,
    'data-dir=s'=> \$data_dir,
    );

if (scalar(@ARGV) != 1) {
    my $argnum = scalar(@ARGV);
    print STDERR "$script_name: Incorrect number of arguments ($argnum found; 1 expected)";
    &usage;
}

$ENV{PGUSER} = $user if $user;
$ENV{PGDATABASE} = $ARGV[0];
&rebuild_modules;


print "Database upgraded to $LedgerSMB::VERSION.\n";
exit 0;
