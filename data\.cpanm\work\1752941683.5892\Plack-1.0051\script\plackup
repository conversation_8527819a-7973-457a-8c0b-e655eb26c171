#!perl
use strict;
use Plack::Runner;

my $runner = Plack::Runner->new;
$runner->parse_options(@ARGV);
$runner->run;

__END__

=head1 NAME

plackup - Run PSGI application with Plack handlers

=head1 SYNOPSIS

  # read your app from app.psgi file
  plackup

  # choose .psgi file from ARGV[0] (or with -a option)
  plackup hello.psgi

  # switch server implementation with --server (or -s)
  plackup --server HTTP::Server::Simple --port 9090 --host 127.0.0.1 test.psgi

  # use UNIX socket to run FCGI daemon
  plackup -s FCGI --listen /tmp/fcgi.sock myapp.psgi

  # launch FCGI external server on port 9090
  plackup -s FCGI --port 9090

=head1 DESCRIPTION

plackup is a command line utility to run PSGI applications from the command
line.

plackup automatically figures out the environment it is run in, and
runs your application in that environment. FastCGI, CGI, AnyEvent and
others can all be detected. See L<Plack::Loader> for the authoritative
list.

C<plackup> assumes you have an C<app.psgi> script in your current directory.
The last statement of C<app.psgi> should be a code reference that is a PSGI
application:

  #!/usr/bin/perl
  use MyApp;
  my $application = MyApp->new;
  my $app = sub { $application->run_psgi(@_) };

=head1 ARGUMENTS

=over 4

=item .psgi

  plackup --host 127.0.0.1 --port 9090 /path/to/app.psgi

The first non-option argument is used as a C<.psgi> file path. You can
also set this path with C<-a> or C<--app>. If omitted, the
default file path is C<app.psgi> in the current directory.

=back

=head1 OPTIONS

=over 4

=item -a, --app

Specifies the full path to a C<.psgi> script. You may alternately provide this
path as the first argument to C<plackup>.

=item -e

Evaluates the given perl code as a PSGI app, much like perl's C<-e>
option:

  plackup -e 'sub { my $env = shift; return [ ... ] }'

It is also handy when you want to run a custom application like Plack::App::*.

  plackup -MPlack::App::File -e 'Plack::App::File->new(...)->to_app'

You can also specify C<-e> option with C<.psgi> file path to wrap the
application with middleware configuration from the command line. You
can also use L<Plack::Builder> DSL syntax inside C<-e> code. For example:

  plackup -e 'enable "Auth::Basic", authenticator => ...;' myapp.psgi

is equivalent to the PSGI application:

  use Plack::Builder;
  use Plack::Util;
  
  builder {
      enable "Auth::Basic", authenticator => ...;
      Plack::Util::load_psgi("myapp.psgi");
  };

Note that when you use C<-e> option to enable middleware, plackup
doesn't assume the implicit C<app.psgi> path. You must either pass the
path to your C<.psgi> file in the command line arguments or load the
application inside C<-e> after the C<enable>.

  plackup                                # Runs app.psgi
  plackup -e 'enable "Foo"'              # Doesn't work!
  plackup -e 'enable "Foo"' app.psgi     # Works
  plackup -e 'enable "Foo"; sub { ... }' # Works

=item -o, --host

Binds to a TCP interface. Defaults to undef, which lets most server backends
bind to the any (*) interface. This option is only valid for servers which support
TCP sockets.

=item -p, --port

Binds to a TCP port. Defaults to 5000. This option is only valid for servers
which support TCP sockets.

Note: default port 5000 may conflict with AirPlay server on MacOS 12 (Monterey)
or later.

=item -s, --server, the C<PLACK_SERVER> environment variable

Selects a specific server implementation to run on. When provided, the C<-s> or
C<--server> flag will be preferred over the environment variable.

If no option is given, plackup will try to detect the I<best> server
implementation based on the environment variables as well as modules loaded by
your application in C<%INC>. See L<Plack::Loader> for details.

=item -S, --socket

Listens on a UNIX domain socket path. Defaults to undef. This option is only
valid for servers which support UNIX sockets.

=item -l, --listen

Listens on one or more addresses, whether "HOST:PORT", ":PORT", or "PATH"
(without colons). You may use this option multiple times to listen on multiple
addresses, but the server will decide whether it supports multiple interfaces.

=item -D, --daemonize

Makes the process run in the background. It's up to the backend server/handler
implementation whether this option is respected or not.

=item -I

Specifies Perl library include paths, like C<perl>'s -I option. You may add
multiple paths by using this option multiple times.

=item -M

Loads the named modules before loading the app's code. You may load multiple
modules by using this option multiple times.

In combination with C<-r> or C<-R> may not have the desired restart effect
when the loaded module is changed in the development directory. To avoid
this problem you need to load the module with the app code using C<-e>.

=item -E, --env, the C<PLACK_ENV> environment variable.

Specifies the environment option. Setting this value with C<-E> or C<--env>
also writes to the C<PLACK_ENV> environment variable. This allows applications
or frameworks to tell which environment setting the application is running on.

  # These two are the same
  plackup -E deployment
  env PLACK_ENV=deployment plackup

Common values are C<development>, C<deployment>, and C<test>. The default value
is C<development>, which causes C<plackup> to load the middleware components:
I<AccessLog>, I<StackTrace>, and I<Lint> unless C<--no-default-middleware>
is set.

=item --no-default-middleware

This prevents loading the default middleware stack even when Plack
environment (i.e. C<-E> or C<PLACK_ENV>) is set to C<development>.

=item -r, --reload

Makes plackup restart the server whenever a file in your development directory
changes. This option by default watches the C<lib> directory and the base
directory where I<.psgi> file is located. Use C<-R> to watch other
directories.

Reloading will delay the compilation of your application. Automatic server
detection (see C<-s> above) may not behave as you expect, if plackup needs to
scan your application for the modules it uses. Avoid problems by specifying
C<-s> explicitly when using C<-r> or C<-R>.

To avoid problems with changes to preloaded modules see documentation for C<-M>.

=item -R, --Reload

Makes plackup restart the server whenever a file in any of the given
directories changes. C<-R> and C<--Reload> take a comma-separated list of
paths:

  plackup -R /path/to/project/lib,/path/to/project/templates

To avoid problems with changes to preloaded modules see documentation for C<-M>.

=item -L, --loader

Specifies the server loading subclass that implements how to run the server.
Available options are I<Plack::Loader> (default), I<Restarter> (automatically
set when C<-r> or C<-R> is used), I<Delayed>, and I<Shotgun>.

See L<Plack::Loader::Delayed> and L<Plack::Loader::Shotgun> for more details.

=item --access-log

Specifies the pathname of a file where the access log should be written.  By
default, in the development environment access logs will go to STDERR.

=item --path

Specify the root path of your app (C<SCRIPT_NAME> in PSGI env) to
run. The following two commands are roughly the same.

  plackup --path /foo app.psgi
  plackup -e 'mount "/foo" => Plack::Util::load_psgi("app.psgi")'

=back

Other options that starts with C<--> are passed through to the backend server.
See each Plack::Handler backend's documentation for more details on their
available options.

=head1 SEE ALSO

L<Plack::Runner> L<Plack::Loader>

=cut
