{"abstract": "PSGI compliant HTTP Entity Parser", "author": ["<PERSON><PERSON><PERSON> <<EMAIL>>"], "dynamic_config": 0, "generated_by": "Minilla/v3.1.10", "license": ["perl_5"], "meta-spec": {"url": "http://search.cpan.org/perldoc?CPAN::Meta::Spec", "version": "2"}, "name": "HTTP-Entity-Parser", "no_index": {"directory": ["t", "xt", "inc", "share", "eg", "examples", "author", "builder"]}, "prereqs": {"configure": {"requires": {"Module::Build::Tiny": "0.035"}}, "develop": {"requires": {"Test::CPAN::Meta": "0", "Test::MinimumVersion::Fast": "0.04", "Test::PAUSE::Permissions": "0.07", "Test::Pod": "1.41", "Test::Spellunker": "v0.2.7"}}, "runtime": {"requires": {"Encode": "0", "File::Temp": "0", "HTTP::MultiPartParser": "0", "Hash::MultiValue": "0", "JSON::MaybeXS": "1.003007", "Module::Load": "0", "Stream::Buffered": "0", "WWW::Form::UrlEncoded": "0.23", "perl": "5.008001"}, "suggests": {"WWW::Form::UrlEncoded::XS": "0.23"}}, "test": {"requires": {"Cwd": "0", "File::Spec::Functions": "0", "HTTP::Message": "6", "Test::More": "0.98"}}}, "provides": {"HTTP::Entity::Parser": {"file": "lib/HTTP/Entity/Parser.pm", "version": "0.25"}, "HTTP::Entity::Parser::JSON": {"file": "lib/HTTP/Entity/Parser/JSON.pm"}, "HTTP::Entity::Parser::MultiPart": {"file": "lib/HTTP/Entity/Parser/MultiPart.pm"}, "HTTP::Entity::Parser::OctetStream": {"file": "lib/HTTP/Entity/Parser/OctetStream.pm"}, "HTTP::Entity::Parser::UrlEncoded": {"file": "lib/HTTP/Entity/Parser/UrlEncoded.pm"}}, "release_status": "stable", "resources": {"bugtracker": {"web": "https://github.com/kazeburo/HTTP-Entity-Parser/issues"}, "homepage": "https://github.com/kazeburo/HTTP-Entity-Parser", "repository": {"url": "git://github.com/kazeburo/HTTP-Entity-Parser.git", "web": "https://github.com/kazeburo/HTTP-Entity-Parser"}}, "version": "0.25", "x_contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "commojun <<EMAIL>>", "jrubinator <<EMAIL>>", "moznion <<EMAIL>>"], "x_serialization_backend": "JSON::PP version 4.04", "x_static_install": 1}