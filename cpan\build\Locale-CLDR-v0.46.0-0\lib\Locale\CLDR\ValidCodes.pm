package Locale::CLDR::ValidCodes;
# This file auto generated from Data\common\supplemental\supplementalMetadata.xml
#	on Fri 17 Jan 12:03:31 pm GMT

use strict;
use warnings;
use version;

our $VERSION = version->declare('v0.46.0');

use v5.12.0;
use mro 'c3';
use utf8;
use feature 'unicode_strings';
use Types::Standard qw( Str Int HashRef ArrayRef CodeRef RegexpRef );
use Moo::Role;

has 'valid_languages' => (
	is			=> 'ro',
	isa			=> ArrayRef,
	init_arg	=> undef,
	default	=> sub {[qw( aa
 aaa
 aab
 aac
 aad
 aae
 aaf
 aag
 aah
 aai
 aak
 aal
 aan
 aao
 aap
 aaq
 aas
 aat
 aau
 aav
 aaw
 aax
 aaz
 ab
 aba
 abb
 abc
 abd
 abe
 abf
 abg
 abh
 abi
 abj
 abl
 abm
 abn
 abo
 abp
 abq
 abr
 abs
 abt
 abu
 abv
 abw
 abx
 aby
 abz
 aca
 acb
 acd
 ace
 acf
 ach
 aci
 ack
 acl
 acm
 acn
 acp
 acq
 acr
 acs
 act
 acu
 acv
 acw
 acx
 acy
 acz
 ada
 adb
 add
 ade
 adf
 adg
 adh
 adi
 adj
 adl
 adn
 ado
 adq
 adr
 ads
 adt
 adu
 adw
 adx
 ady
 adz
 ae
 aea
 aeb
 aec
 aed
 aee
 aek
 ael
 aem
 aen
 aeq
 aer
 aes
 aeu
 aew
 aey
 aez
 af
 afa
 afb
 afd
 afe
 afg
 afh
 afi
 afk
 afn
 afo
 afp
 afs
 aft
 afu
 afz
 aga
 agb
 agc
 agd
 age
 agf
 agg
 agh
 agi
 agj
 agk
 agl
 agm
 agn
 ago
 agq
 agr
 ags
 agt
 agu
 agv
 agw
 agx
 agy
 agz
 aha
 ahb
 ahg
 ahh
 ahi
 ahk
 ahl
 ahm
 ahn
 aho
 ahp
 ahr
 ahs
 aht
 aia
 aib
 aic
 aid
 aie
 aif
 aig
 aih
 aii
 aij
 aik
 ail
 aim
 ain
 aio
 aip
 aiq
 air
 ait
 aiw
 aix
 aiy
 aja
 ajg
 aji
 ajn
 ajs
 ajw
 ajz
 ak
 akb
 akc
 akd
 ake
 akf
 akg
 akh
 aki
 akj
 akk
 akl
 akm
 ako
 akp
 akq
 akr
 aks
 akt
 aku
 akv
 akw
 akx
 aky
 akz
 ala
 alc
 ald
 ale
 alf
 alg
 alh
 ali
 alj
 alk
 all
 alm
 aln
 alo
 alp
 alq
 alr
 alt
 alu
 alv
 alw
 alx
 aly
 alz
 am
 ama
 amb
 amc
 ame
 amf
 amg
 ami
 amj
 amk
 aml
 amm
 amn
 amo
 amp
 amq
 amr
 ams
 amt
 amu
 amv
 amw
 amx
 amy
 amz
 an
 ana
 anb
 anc
 and
 ane
 anf
 ang
 anh
 ani
 anj
 ank
 anl
 anm
 ann
 ano
 anp
 anq
 anr
 ans
 ant
 anu
 anv
 anw
 anx
 any
 anz
 aoa
 aob
 aoc
 aod
 aoe
 aof
 aog
 aoi
 aoj
 aok
 aol
 aom
 aon
 aor
 aos
 aot
 aou
 aox
 aoz
 apa
 apb
 apc
 apd
 ape
 apf
 apg
 aph
 api
 apj
 apk
 apl
 apm
 apn
 apo
 app
 apq
 apr
 aps
 apt
 apu
 apv
 apw
 apx
 apy
 apz
 aqa
 aqc
 aqd
 aqg
 aqk
 aql
 aqm
 aqn
 aqp
 aqr
 aqt
 aqz
 ar
 arc
 ard
 are
 arh
 ari
 arj
 ark
 arl
 arn
 aro
 arp
 arq
 arr
 ars
 art
 aru
 arv
 arw
 arx
 ary
 arz
 as
 asa
 asb
 asc
 ase
 asf
 asg
 ash
 asi
 asj
 ask
 asl
 asn
 aso
 asp
 asq
 asr
 ass
 ast
 asu
 asv
 asw
 asx
 asy
 asz
 ata
 atb
 atc
 atd
 ate
 atg
 ath
 ati
 atj
 atk
 atl
 atm
 atn
 ato
 atp
 atq
 atr
 ats
 att
 atu
 atv
 atw
 atx
 aty
 atz
 aua
 aub
 auc
 aud
 auf
 aug
 auh
 aui
 auj
 auk
 aul
 aum
 aun
 auo
 aup
 auq
 aur
 aus
 aut
 auu
 auw
 aux
 auy
 auz
 av
 avb
 avd
 avi
 avk
 avl
 avm
 avn
 avo
 avs
 avt
 avu
 avv
 awa
 awb
 awc
 awd
 awe
 awg
 awh
 awi
 awk
 awm
 awn
 awo
 awr
 aws
 awt
 awu
 awv
 aww
 awx
 awy
 axb
 axe
 axg
 axk
 axl
 axm
 axx
 ay
 aya
 ayb
 ayc
 ayd
 aye
 ayg
 ayh
 ayi
 ayk
 ayl
 ayn
 ayo
 ayp
 ayq
 ays
 ayt
 ayu
 ayz
 az
 aza
 azb
 azc
 azd
 azg
 azm
 azn
 azo
 azt
 azz
 ba
 baa
 bab
 bac
 bad
 bae
 baf
 bag
 bah
 bai
 baj
 bal
 ban
 bao
 bap
 bar
 bas
 bat
 bau
 bav
 baw
 bax
 bay
 bba
 bbb
 bbc
 bbd
 bbe
 bbf
 bbg
 bbh
 bbi
 bbj
 bbk
 bbl
 bbm
 bbn
 bbo
 bbp
 bbq
 bbr
 bbs
 bbt
 bbu
 bbv
 bbw
 bbx
 bby
 bca
 bcb
 bcd
 bce
 bcf
 bcg
 bch
 bci
 bcj
 bck
 bcm
 bcn
 bco
 bcp
 bcq
 bcr
 bcs
 bct
 bcu
 bcv
 bcw
 bcy
 bcz
 bda
 bdb
 bdc
 bdd
 bde
 bdf
 bdg
 bdh
 bdi
 bdj
 bdk
 bdl
 bdm
 bdn
 bdo
 bdp
 bdq
 bdr
 bds
 bdt
 bdu
 bdv
 bdw
 bdx
 bdy
 bdz
 be
 bea
 beb
 bec
 bed
 bee
 bef
 beg
 beh
 bei
 bej
 bek
 bem
 beo
 bep
 beq
 ber
 bes
 bet
 beu
 bev
 bew
 bex
 bey
 bez
 bfa
 bfb
 bfc
 bfd
 bfe
 bff
 bfg
 bfh
 bfi
 bfj
 bfk
 bfl
 bfm
 bfn
 bfo
 bfp
 bfq
 bfr
 bfs
 bft
 bfu
 bfw
 bfx
 bfy
 bfz
 bg
 bga
 bgb
 bgc
 bgd
 bge
 bgf
 bgg
 bgi
 bgj
 bgk
 bgl
 bgn
 bgo
 bgp
 bgq
 bgr
 bgs
 bgt
 bgu
 bgv
 bgw
 bgx
 bgy
 bgz
 bha
 bhb
 bhc
 bhd
 bhe
 bhf
 bhg
 bhh
 bhi
 bhj
 bhl
 bhm
 bhn
 bho
 bhp
 bhq
 bhr
 bhs
 bht
 bhu
 bhv
 bhw
 bhx
 bhy
 bhz
 bi
 bia
 bib
 bid
 bie
 bif
 big
 bik
 bil
 bim
 bin
 bio
 bip
 biq
 bir
 bit
 biu
 biv
 biw
 bix
 biy
 biz
 bja
 bjb
 bjc
 bje
 bjf
 bjg
 bjh
 bji
 bjj
 bjk
 bjl
 bjm
 bjn
 bjo
 bjp
 bjr
 bjs
 bjt
 bju
 bjv
 bjw
 bjx
 bjy
 bjz
 bka
 bkc
 bkd
 bkf
 bkg
 bkh
 bki
 bkj
 bkk
 bkl
 bkm
 bkn
 bko
 bkp
 bkq
 bkr
 bks
 bkt
 bku
 bkv
 bkw
 bkx
 bky
 bkz
 bla
 blb
 blc
 bld
 ble
 blf
 blh
 bli
 blj
 blk
 bll
 blm
 bln
 blo
 blp
 blq
 blr
 bls
 blt
 blv
 blw
 blx
 bly
 blz
 bm
 bma
 bmb
 bmc
 bmd
 bme
 bmf
 bmg
 bmh
 bmi
 bmj
 bmk
 bml
 bmm
 bmn
 bmo
 bmp
 bmq
 bmr
 bms
 bmt
 bmu
 bmv
 bmw
 bmx
 bmz
 bn
 bna
 bnb
 bnc
 bnd
 bne
 bnf
 bng
 bni
 bnj
 bnk
 bnl
 bnm
 bnn
 bno
 bnp
 bnq
 bnr
 bns
 bnt
 bnu
 bnv
 bnw
 bnx
 bny
 bnz
 bo
 boa
 bob
 boe
 bof
 bog
 boh
 boi
 boj
 bok
 bol
 bom
 bon
 boo
 bop
 boq
 bor
 bot
 bou
 bov
 bow
 box
 boy
 boz
 bpa
 bpc
 bpd
 bpe
 bpg
 bph
 bpi
 bpj
 bpk
 bpl
 bpm
 bpn
 bpo
 bpp
 bpq
 bpr
 bps
 bpt
 bpu
 bpv
 bpw
 bpx
 bpy
 bpz
 bqa
 bqb
 bqc
 bqd
 bqf
 bqg
 bqh
 bqi
 bqj
 bqk
 bql
 bqm
 bqn
 bqo
 bqp
 bqq
 bqr
 bqs
 bqt
 bqu
 bqv
 bqw
 bqx
 bqy
 bqz
 br
 bra
 brb
 brc
 brd
 brf
 brg
 brh
 bri
 brj
 brk
 brl
 brm
 brn
 bro
 brp
 brq
 brr
 brs
 brt
 bru
 brv
 brw
 brx
 bry
 brz
 bs
 bsa
 bsb
 bsc
 bse
 bsf
 bsg
 bsh
 bsi
 bsj
 bsk
 bsl
 bsm
 bsn
 bso
 bsp
 bsq
 bsr
 bss
 bst
 bsu
 bsv
 bsw
 bsx
 bsy
 bta
 btc
 btd
 bte
 btf
 btg
 bth
 bti
 btj
 btk
 btm
 btn
 bto
 btp
 btq
 btr
 bts
 btt
 btu
 btv
 btw
 btx
 bty
 btz
 bua
 bub
 buc
 bud
 bue
 buf
 bug
 buh
 bui
 buj
 buk
 bum
 bun
 buo
 bup
 buq
 bus
 but
 buu
 buv
 buw
 bux
 buy
 buz
 bva
 bvb
 bvc
 bvd
 bve
 bvf
 bvg
 bvh
 bvi
 bvj
 bvk
 bvl
 bvm
 bvn
 bvo
 bvp
 bvq
 bvr
 bvt
 bvu
 bvv
 bvw
 bvx
 bvy
 bvz
 bwa
 bwb
 bwc
 bwd
 bwe
 bwf
 bwg
 bwh
 bwi
 bwj
 bwk
 bwl
 bwm
 bwn
 bwo
 bwp
 bwq
 bwr
 bws
 bwt
 bwu
 bww
 bwx
 bwy
 bwz
 bxa
 bxb
 bxc
 bxd
 bxe
 bxf
 bxg
 bxh
 bxi
 bxj
 bxl
 bxm
 bxn
 bxo
 bxp
 bxq
 bxs
 bxu
 bxv
 bxw
 bxz
 bya
 byb
 byc
 byd
 bye
 byf
 byg
 byh
 byi
 byj
 byk
 byl
 bym
 byn
 byo
 byp
 byq
 byr
 bys
 byt
 byv
 byw
 byx
 byz
 bza
 bzb
 bzc
 bzd
 bze
 bzf
 bzg
 bzh
 bzi
 bzj
 bzk
 bzl
 bzm
 bzn
 bzo
 bzp
 bzq
 bzr
 bzs
 bzt
 bzu
 bzv
 bzw
 bzx
 bzy
 bzz
 ca
 caa
 cab
 cac
 cad
 cae
 caf
 cag
 cah
 cai
 caj
 cak
 cal
 cam
 can
 cao
 cap
 caq
 car
 cas
 cau
 cav
 caw
 cax
 cay
 caz
 cba
 cbb
 cbc
 cbd
 cbg
 cbi
 cbj
 cbk
 cbl
 cbn
 cbo
 cbq
 cbr
 cbs
 cbt
 cbu
 cbv
 cbw
 cby
 ccc
 ccd
 cce
 ccg
 cch
 ccj
 ccl
 ccm
 ccn
 cco
 ccp
 ccr
 ccs
 cda
 cdc
 cdd
 cde
 cdf
 cdh
 cdi
 cdj
 cdm
 cdn
 cdo
 cdr
 cds
 cdy
 cdz
 ce
 cea
 ceb
 ceg
 cek
 cel
 cen
 cet
 cey
 cfa
 cfd
 cfg
 cfm
 cga
 cgc
 cgg
 cgk
 ch
 chb
 chc
 chd
 chf
 chg
 chh
 chj
 chk
 chl
 chm
 chn
 cho
 chp
 chq
 chr
 cht
 chw
 chx
 chy
 chz
 cia
 cib
 cic
 cid
 cie
 cih
 cik
 cim
 cin
 cip
 cir
 ciw
 ciy
 cja
 cje
 cjh
 cji
 cjk
 cjm
 cjn
 cjo
 cjp
 cjs
 cjv
 cjy
 ckb
 ckh
 ckl
 ckm
 ckn
 cko
 ckq
 ckr
 cks
 ckt
 cku
 ckv
 ckx
 cky
 ckz
 cla
 clc
 cle
 clh
 cli
 clj
 clk
 cll
 clm
 clo
 cls
 clt
 clu
 clw
 cly
 cma
 cmc
 cme
 cmg
 cmi
 cml
 cmm
 cmo
 cmr
 cms
 cmt
 cna
 cnb
 cnc
 cng
 cnh
 cni
 cnk
 cnl
 cno
 cnp
 cnq
 cns
 cnt
 cnu
 cnw
 cnx
 co
 coa
 cob
 coc
 cod
 coe
 cof
 cog
 coh
 coj
 cok
 col
 com
 con
 coo
 cop
 coq
 cot
 cou
 cov
 cow
 cox
 coz
 cpa
 cpb
 cpc
 cpe
 cpf
 cpg
 cpi
 cpn
 cpo
 cpp
 cps
 cpu
 cpx
 cpy
 cqd
 cr
 cra
 crb
 crc
 crd
 crf
 crg
 crh
 cri
 crj
 crk
 crl
 crm
 crn
 cro
 crp
 crq
 crr
 crs
 crt
 crv
 crw
 crx
 cry
 crz
 cs
 csa
 csb
 csc
 csd
 cse
 csf
 csg
 csh
 csi
 csj
 csk
 csl
 csm
 csn
 cso
 csp
 csq
 csr
 css
 cst
 csu
 csv
 csw
 csx
 csy
 csz
 cta
 ctc
 ctd
 cte
 ctg
 cth
 ctl
 ctm
 ctn
 cto
 ctp
 cts
 ctt
 ctu
 cty
 ctz
 cu
 cua
 cub
 cuc
 cuh
 cui
 cuj
 cuk
 cul
 cuo
 cup
 cuq
 cur
 cus
 cut
 cuu
 cuv
 cuw
 cux
 cuy
 cv
 cvg
 cvn
 cwa
 cwb
 cwe
 cwg
 cwt
 cxh
 cy
 cya
 cyb
 cyo
 czh
 czk
 czn
 czo
 czt
 da
 daa
 dac
 dad
 dae
 dag
 dah
 dai
 daj
 dak
 dal
 dam
 dao
 daq
 dar
 das
 dau
 dav
 daw
 dax
 day
 daz
 dba
 dbb
 dbd
 dbe
 dbf
 dbg
 dbi
 dbj
 dbl
 dbm
 dbn
 dbo
 dbp
 dbq
 dbr
 dbt
 dbu
 dbv
 dbw
 dby
 dcc
 dcr
 dda
 ddd
 dde
 ddg
 ddi
 ddj
 ddn
 ddo
 ddr
 dds
 ddw
 de
 dec
 ded
 dee
 def
 deg
 deh
 dei
 dek
 del
 dem
 den
 dep
 deq
 der
 des
 dev
 dez
 dga
 dgb
 dgc
 dgd
 dge
 dgg
 dgh
 dgi
 dgk
 dgl
 dgn
 dgr
 dgs
 dgt
 dgw
 dgx
 dgz
 dhg
 dhi
 dhl
 dhm
 dhn
 dho
 dhr
 dhs
 dhu
 dhv
 dhw
 dhx
 dia
 dib
 dic
 did
 dif
 dig
 dih
 dii
 dij
 dil
 dim
 din
 dio
 dip
 dir
 dis
 diu
 diw
 dix
 diy
 diz
 dja
 djb
 djc
 djd
 dje
 djf
 dji
 djj
 djk
 djm
 djn
 djo
 djr
 dju
 djw
 dka
 dkg
 dkk
 dkr
 dks
 dkx
 dlg
 dlk
 dlm
 dln
 dma
 dmb
 dmc
 dmd
 dme
 dmf
 dmg
 dmk
 dml
 dmm
 dmn
 dmo
 dmr
 dms
 dmu
 dmv
 dmw
 dmx
 dmy
 dna
 dnd
 dne
 dng
 dni
 dnj
 dnk
 dnn
 dno
 dnr
 dnt
 dnu
 dnv
 dnw
 dny
 doa
 dob
 doc
 doe
 dof
 doh
 doi
 dok
 dol
 don
 doo
 dop
 doq
 dor
 dos
 dot
 dov
 dow
 dox
 doy
 doz
 dpp
 dra
 drb
 drc
 drd
 dre
 drg
 dri
 drl
 drn
 dro
 drq
 drs
 drt
 dru
 dry
 dsb
 dse
 dsh
 dsi
 dsk
 dsl
 dsn
 dso
 dsq
 dsz
 dta
 dtb
 dtd
 dth
 dti
 dtk
 dtm
 dtn
 dto
 dtp
 dtr
 dts
 dtt
 dtu
 dty
 dua
 dub
 duc
 due
 duf
 dug
 duh
 dui
 duk
 dul
 dum
 dun
 duo
 dup
 duq
 dur
 dus
 duu
 duv
 duw
 dux
 duy
 duz
 dv
 dva
 dwa
 dwk
 dwr
 dws
 dwu
 dww
 dwy
 dwz
 dya
 dyb
 dyd
 dyg
 dyi
 dym
 dyn
 dyo
 dyr
 dyu
 dyy
 dz
 dza
 dzd
 dze
 dzg
 dzl
 dzn
 eaa
 ebc
 ebg
 ebk
 ebo
 ebr
 ebu
 ecr
 ecs
 ecy
 ee
 eee
 efa
 efe
 efi
 ega
 egl
 egm
 ego
 egx
 egy
 ehs
 ehu
 eip
 eit
 eiv
 eja
 eka
 eke
 ekg
 eki
 ekl
 ekm
 eko
 ekp
 ekr
 eky
 el
 ele
 elh
 eli
 elk
 elm
 elo
 elu
 elx
 ema
 emb
 eme
 emg
 emi
 emm
 emn
 emp
 emq
 ems
 emu
 emw
 emx
 emy
 emz
 en
 ena
 enb
 enc
 end
 enf
 enh
 enl
 enm
 enn
 eno
 enq
 enr
 enu
 env
 enw
 enx
 eo
 eot
 epi
 era
 erg
 erh
 eri
 erk
 ero
 err
 ers
 ert
 erw
 es
 ese
 esg
 esh
 esi
 esl
 esm
 esn
 eso
 esq
 ess
 esu
 esx
 esy
 et
 etb
 etc
 eth
 etn
 eto
 etr
 ets
 ett
 etu
 etx
 etz
 eu
 eud
 euq
 eve
 evh
 evn
 ewo
 ext
 eya
 eyo
 eza
 eze
 fa
 faa
 fab
 fad
 faf
 fag
 fah
 fai
 faj
 fak
 fal
 fam
 fan
 fap
 far
 fau
 fax
 fay
 faz
 fbl
 fcs
 fer
 ff
 ffi
 ffm
 fgr
 fi
 fia
 fie
 fif
 fil
 fip
 fir
 fit
 fiu
 fiw
 fj
 fkk
 fkv
 fla
 flh
 fli
 fll
 fln
 flr
 fly
 fmp
 fmu
 fnb
 fng
 fni
 fo
 fod
 foi
 fom
 fon
 for
 fos
 fox
 fpe
 fqs
 fr
 frc
 frd
 frk
 frm
 fro
 frp
 frq
 frr
 frs
 frt
 fse
 fsl
 fss
 fub
 fud
 fue
 fuf
 fuh
 fui
 fuj
 fum
 fun
 fuq
 fur
 fut
 fuu
 fuv
 fuy
 fvr
 fwa
 fwe
 fy
 ga
 gaa
 gab
 gac
 gad
 gae
 gaf
 gag
 gah
 gai
 gaj
 gak
 gal
 gam
 gan
 gao
 gap
 gaq
 gar
 gas
 gat
 gau
 gaw
 gax
 gay
 gba
 gbb
 gbd
 gbe
 gbf
 gbg
 gbh
 gbi
 gbj
 gbk
 gbl
 gbm
 gbn
 gbp
 gbq
 gbr
 gbs
 gbu
 gbv
 gbw
 gbx
 gby
 gbz
 gcc
 gcd
 gce
 gcf
 gcl
 gcn
 gcr
 gct
 gd
 gda
 gdb
 gdc
 gdd
 gde
 gdf
 gdg
 gdh
 gdi
 gdj
 gdk
 gdl
 gdm
 gdn
 gdo
 gdq
 gdr
 gds
 gdt
 gdu
 gdx
 gea
 geb
 gec
 ged
 gef
 geg
 geh
 gei
 gej
 gek
 gel
 gem
 geq
 ges
 gev
 gew
 gex
 gey
 gez
 gfk
 gft
 gga
 ggb
 ggd
 gge
 ggg
 ggk
 ggl
 ggt
 ggu
 ggw
 gha
 ghc
 ghe
 ghh
 ghk
 ghl
 ghn
 gho
 ghr
 ghs
 ght
 gia
 gib
 gic
 gid
 gie
 gig
 gih
 gii
 gil
 gim
 gin
 gip
 giq
 gir
 gis
 git
 giu
 giw
 gix
 giy
 giz
 gjk
 gjm
 gjn
 gjr
 gju
 gka
 gkd
 gke
 gkn
 gko
 gkp
 gku
 gl
 glb
 glc
 gld
 glh
 glj
 glk
 gll
 glo
 glr
 glu
 glw
 gly
 gma
 gmb
 gmd
 gme
 gmg
 gmh
 gml
 gmm
 gmn
 gmq
 gmr
 gmu
 gmv
 gmw
 gmx
 gmy
 gmz
 gn
 gna
 gnb
 gnc
 gnd
 gne
 gng
 gnh
 gni
 gnj
 gnk
 gnl
 gnm
 gnn
 gnq
 gnr
 gnt
 gnu
 gnw
 gnz
 goa
 gob
 goc
 god
 goe
 gof
 gog
 goh
 goi
 goj
 gok
 gol
 gon
 goo
 gop
 goq
 gor
 gos
 got
 gou
 gov
 gow
 gox
 goy
 goz
 gpa
 gpe
 gpn
 gqa
 gqi
 gqn
 gqr
 gqu
 gra
 grb
 grc
 grd
 grg
 grh
 gri
 grj
 grk
 grm
 gro
 grq
 grr
 grs
 grt
 gru
 grv
 grw
 grx
 gry
 grz
 gse
 gsg
 gsl
 gsm
 gsn
 gso
 gsp
 gss
 gsw
 gta
 gtu
 gu
 gua
 gub
 guc
 gud
 gue
 guf
 guh
 gui
 guk
 gul
 gum
 gun
 guo
 gup
 guq
 gur
 gus
 gut
 guu
 guw
 gux
 guz
 gv
 gva
 gvc
 gve
 gvf
 gvj
 gvl
 gvm
 gvn
 gvo
 gvp
 gvr
 gvs
 gvy
 gwa
 gwb
 gwc
 gwd
 gwe
 gwf
 gwg
 gwi
 gwj
 gwm
 gwn
 gwr
 gwt
 gwu
 gww
 gwx
 gxx
 gyb
 gyd
 gye
 gyf
 gyg
 gyi
 gyl
 gym
 gyn
 gyo
 gyr
 gyy
 gyz
 gza
 gzi
 gzn
 ha
 haa
 hab
 hac
 had
 hae
 haf
 hag
 hah
 hai
 haj
 hak
 hal
 ham
 han
 hao
 hap
 haq
 har
 has
 hav
 haw
 hax
 hay
 haz
 hba
 hbb
 hbn
 hbo
 hbu
 hca
 hch
 hds
 hdy
 he
 hed
 heg
 heh
 hei
 hem
 hgm
 hgw
 hhi
 hhr
 hhy
 hi
 hia
 hib
 hid
 hif
 hig
 hih
 hii
 hij
 hik
 hil
 hio
 hir
 hit
 hiw
 hix
 hji
 hka
 hke
 hkh
 hkk
 hkn
 hks
 hla
 hlb
 hld
 hle
 hlt
 hlu
 hma
 hmb
 hmc
 hmd
 hme
 hmf
 hmg
 hmh
 hmi
 hmj
 hmk
 hml
 hmm
 hmn
 hmp
 hmq
 hmr
 hms
 hmt
 hmu
 hmv
 hmw
 hmx
 hmy
 hmz
 hna
 hnd
 hne
 hng
 hnh
 hni
 hnj
 hnn
 hno
 hns
 hnu
 ho
 hoa
 hob
 hoc
 hod
 hoe
 hoh
 hoi
 hoj
 hok
 hol
 hom
 hoo
 hop
 hor
 hos
 hot
 hov
 how
 hoy
 hoz
 hpo
 hps
 hr
 hra
 hrc
 hre
 hrk
 hrm
 hro
 hrp
 hrt
 hru
 hrw
 hrx
 hrz
 hsb
 hsh
 hsl
 hsn
 hss
 ht
 hti
 hto
 hts
 htu
 htx
 hu
 hub
 huc
 hud
 hue
 huf
 hug
 huh
 hui
 huj
 huk
 hul
 hum
 huo
 hup
 huq
 hur
 hus
 hut
 huu
 huv
 huw
 hux
 huy
 huz
 hvc
 hve
 hvk
 hvn
 hvv
 hwa
 hwc
 hwo
 hy
 hya
 hyw
 hyx
 hz
 ia
 iai
 ian
 iar
 iba
 ibb
 ibd
 ibe
 ibg
 ibh
 ibl
 ibm
 ibn
 ibr
 ibu
 iby
 ica
 ich
 icl
 icr
 id
 ida
 idb
 idc
 idd
 ide
 idi
 idr
 ids
 idt
 idu
 ie
 ifa
 ifb
 ife
 iff
 ifk
 ifm
 ifu
 ify
 ig
 igb
 ige
 igg
 igl
 igm
 ign
 igo
 igs
 igw
 ihb
 ihi
 ihp
 ihw
 ii
 iin
 iir
 ijc
 ije
 ijj
 ijn
 ijo
 ijs
 ik
 ikh
 iki
 ikk
 ikl
 iko
 ikp
 ikr
 iks
 ikt
 ikv
 ikw
 ikx
 ikz
 ila
 ilb
 ilg
 ili
 ilk
 ilm
 ilo
 ilp
 ils
 ilu
 ilv
 ima
 imi
 iml
 imn
 imo
 imr
 ims
 imt
 imy
 inb
 inc
 ine
 ing
 inh
 inj
 inl
 inm
 inn
 ino
 inp
 ins
 int
 inz
 io
 ior
 iou
 iow
 ipi
 ipo
 iqu
 iqw
 ira
 ire
 irh
 iri
 irk
 irn
 iro
 irr
 iru
 irx
 iry
 is
 isa
 isc
 isd
 ise
 isg
 ish
 isi
 isk
 ism
 isn
 iso
 isr
 ist
 isu
 isv
 it
 itb
 itc
 itd
 ite
 iti
 itk
 itl
 itm
 ito
 itr
 its
 itt
 itv
 itw
 itx
 ity
 itz
 iu
 ium
 ivb
 ivv
 iwk
 iwm
 iwo
 iws
 ixc
 ixl
 iya
 iyo
 iyx
 izh
 izm
 izr
 izz
 ja
 jaa
 jab
 jac
 jad
 jae
 jaf
 jah
 jaj
 jak
 jal
 jam
 jan
 jao
 jaq
 jas
 jat
 jau
 jax
 jay
 jaz
 jbe
 jbi
 jbj
 jbk
 jbm
 jbn
 jbo
 jbr
 jbt
 jbu
 jbw
 jcs
 jct
 jda
 jdg
 jdt
 jeb
 jee
 jeh
 jei
 jek
 jel
 jen
 jer
 jet
 jeu
 jgb
 jge
 jgk
 jgo
 jhi
 jhs
 jia
 jib
 jic
 jid
 jie
 jig
 jih
 jii
 jil
 jim
 jio
 jiq
 jit
 jiu
 jiv
 jiy
 jje
 jjr
 jka
 jkm
 jko
 jkp
 jkr
 jks
 jku
 jle
 jls
 jma
 jmb
 jmc
 jmd
 jmi
 jml
 jmn
 jmr
 jms
 jmw
 jmx
 jna
 jnd
 jng
 jni
 jnj
 jnl
 jns
 job
 jod
 jog
 jor
 jos
 jow
 jpa
 jpr
 jpx
 jqr
 jra
 jrb
 jrr
 jrt
 jru
 jsl
 jua
 jub
 juc
 jud
 juh
 jui
 juk
 jul
 jum
 jun
 juo
 jup
 jur
 jus
 jut
 juu
 juw
 juy
 jv
 jvd
 jvn
 jwi
 jya
 jye
 jyy
 ka
 kaa
 kab
 kac
 kad
 kae
 kaf
 kag
 kah
 kai
 kaj
 kak
 kam
 kao
 kap
 kaq
 kar
 kav
 kaw
 kax
 kay
 kba
 kbb
 kbc
 kbd
 kbe
 kbg
 kbh
 kbi
 kbj
 kbk
 kbl
 kbm
 kbn
 kbo
 kbp
 kbq
 kbr
 kbs
 kbt
 kbu
 kbv
 kbw
 kbx
 kby
 kbz
 kca
 kcb
 kcc
 kcd
 kce
 kcf
 kcg
 kch
 kci
 kcj
 kck
 kcl
 kcm
 kcn
 kco
 kcp
 kcq
 kcr
 kcs
 kct
 kcu
 kcv
 kcw
 kcx
 kcy
 kcz
 kda
 kdc
 kdd
 kde
 kdf
 kdg
 kdh
 kdi
 kdj
 kdk
 kdl
 kdm
 kdn
 kdo
 kdp
 kdq
 kdr
 kdt
 kdu
 kdw
 kdx
 kdy
 kdz
 kea
 keb
 kec
 ked
 kee
 kef
 keg
 keh
 kei
 kej
 kek
 kel
 kem
 ken
 keo
 kep
 keq
 ker
 kes
 ket
 keu
 kev
 kew
 kex
 key
 kez
 kfa
 kfb
 kfc
 kfd
 kfe
 kff
 kfg
 kfh
 kfi
 kfj
 kfk
 kfl
 kfm
 kfn
 kfo
 kfp
 kfq
 kfr
 kfs
 kft
 kfu
 kfv
 kfw
 kfx
 kfy
 kfz
 kg
 kga
 kgb
 kge
 kgf
 kgg
 kgi
 kgj
 kgk
 kgl
 kgn
 kgo
 kgp
 kgq
 kgr
 kgs
 kgt
 kgu
 kgv
 kgw
 kgx
 kgy
 kha
 khb
 khc
 khd
 khe
 khf
 khg
 khh
 khi
 khj
 khl
 khn
 kho
 khp
 khq
 khr
 khs
 kht
 khu
 khv
 khw
 khx
 khy
 khz
 ki
 kia
 kib
 kic
 kid
 kie
 kif
 kig
 kih
 kii
 kij
 kil
 kim
 kio
 kip
 kiq
 kis
 kit
 kiu
 kiv
 kiw
 kix
 kiy
 kiz
 kj
 kja
 kjb
 kjc
 kjd
 kje
 kjg
 kjh
 kji
 kjj
 kjk
 kjl
 kjm
 kjn
 kjo
 kjp
 kjq
 kjr
 kjs
 kjt
 kju
 kjv
 kjx
 kjy
 kjz
 kk
 kka
 kkb
 kkc
 kkd
 kke
 kkf
 kkg
 kkh
 kki
 kkj
 kkk
 kkl
 kkm
 kkn
 kko
 kkp
 kkq
 kkr
 kks
 kkt
 kku
 kkv
 kkw
 kkx
 kky
 kkz
 kl
 kla
 klb
 klc
 kld
 kle
 klf
 klg
 klh
 kli
 klj
 klk
 kll
 klm
 kln
 klo
 klp
 klq
 klr
 kls
 klt
 klu
 klv
 klw
 klx
 kly
 klz
 km
 kma
 kmb
 kmc
 kmd
 kme
 kmf
 kmg
 kmh
 kmi
 kmj
 kmk
 kml
 kmm
 kmn
 kmo
 kmp
 kmq
 kms
 kmt
 kmu
 kmv
 kmw
 kmx
 kmy
 kmz
 kn
 kna
 knb
 knd
 kne
 knf
 kni
 knj
 knk
 knl
 knm
 knn
 kno
 knp
 knq
 knr
 kns
 knt
 knu
 knv
 knw
 knx
 kny
 knz
 ko
 koa
 koc
 kod
 koe
 kof
 kog
 koh
 koi
 kok
 kol
 koo
 kop
 koq
 kos
 kot
 kou
 kov
 kow
 koy
 koz
 kpa
 kpb
 kpc
 kpd
 kpe
 kpf
 kpg
 kph
 kpi
 kpj
 kpk
 kpl
 kpm
 kpn
 kpo
 kpq
 kpr
 kps
 kpt
 kpu
 kpw
 kpx
 kpy
 kpz
 kqa
 kqb
 kqc
 kqd
 kqe
 kqf
 kqg
 kqh
 kqi
 kqj
 kqk
 kql
 kqm
 kqn
 kqo
 kqp
 kqq
 kqr
 kqs
 kqt
 kqu
 kqv
 kqw
 kqx
 kqy
 kqz
 kr
 kra
 krb
 krc
 krd
 kre
 krf
 krh
 kri
 krj
 krk
 krl
 krn
 kro
 krp
 krr
 krs
 krt
 kru
 krv
 krw
 krx
 kry
 krz
 ks
 ksb
 ksc
 ksd
 kse
 ksf
 ksg
 ksh
 ksi
 ksj
 ksk
 ksl
 ksm
 ksn
 kso
 ksp
 ksq
 ksr
 kss
 kst
 ksu
 ksv
 ksw
 ksx
 ksy
 ksz
 kta
 ktb
 ktc
 ktd
 kte
 ktf
 ktg
 kth
 kti
 ktj
 ktk
 ktl
 ktm
 ktn
 kto
 ktp
 ktq
 kts
 ktt
 ktu
 ktv
 ktw
 ktx
 kty
 ktz
 ku
 kub
 kuc
 kud
 kue
 kuf
 kug
 kuh
 kui
 kuj
 kuk
 kul
 kum
 kun
 kuo
 kup
 kuq
 kus
 kut
 kuu
 kuv
 kuw
 kux
 kuy
 kuz
 kv
 kva
 kvb
 kvc
 kvd
 kve
 kvf
 kvg
 kvh
 kvi
 kvj
 kvk
 kvl
 kvm
 kvn
 kvo
 kvp
 kvq
 kvr
 kvt
 kvu
 kvv
 kvw
 kvx
 kvy
 kvz
 kw
 kwa
 kwb
 kwc
 kwd
 kwe
 kwf
 kwg
 kwh
 kwi
 kwj
 kwk
 kwl
 kwm
 kwn
 kwo
 kwp
 kwr
 kws
 kwt
 kwu
 kwv
 kww
 kwx
 kwy
 kwz
 kxa
 kxb
 kxc
 kxd
 kxf
 kxh
 kxi
 kxj
 kxk
 kxm
 kxn
 kxo
 kxp
 kxq
 kxr
 kxs
 kxt
 kxv
 kxw
 kxx
 kxy
 kxz
 ky
 kya
 kyb
 kyc
 kyd
 kye
 kyf
 kyg
 kyh
 kyi
 kyj
 kyk
 kyl
 kym
 kyn
 kyo
 kyp
 kyq
 kyr
 kys
 kyt
 kyu
 kyv
 kyw
 kyx
 kyy
 kyz
 kza
 kzb
 kzc
 kzd
 kze
 kzf
 kzg
 kzi
 kzk
 kzl
 kzm
 kzn
 kzo
 kzp
 kzq
 kzr
 kzs
 kzu
 kzv
 kzw
 kzx
 kzy
 kzz
 la
 laa
 lab
 lac
 lad
 lae
 laf
 lag
 lah
 lai
 laj
 lal
 lam
 lan
 lap
 laq
 lar
 las
 lau
 law
 lax
 lay
 laz
 lb
 lbb
 lbc
 lbe
 lbf
 lbg
 lbi
 lbj
 lbl
 lbm
 lbn
 lbo
 lbq
 lbr
 lbs
 lbt
 lbu
 lbv
 lbw
 lbx
 lby
 lbz
 lcc
 lcd
 lce
 lcf
 lch
 lcl
 lcm
 lcp
 lcq
 lcs
 lda
 ldb
 ldd
 ldg
 ldh
 ldi
 ldj
 ldk
 ldl
 ldm
 ldn
 ldo
 ldp
 ldq
 lea
 leb
 lec
 led
 lee
 lef
 leh
 lei
 lej
 lek
 lel
 lem
 len
 leo
 lep
 leq
 ler
 les
 let
 leu
 lev
 lew
 lex
 ley
 lez
 lfa
 lfn
 lg
 lga
 lgb
 lgg
 lgh
 lgi
 lgk
 lgl
 lgm
 lgn
 lgo
 lgq
 lgr
 lgs
 lgt
 lgu
 lgz
 lha
 lhh
 lhi
 lhl
 lhm
 lhn
 lhp
 lhs
 lht
 lhu
 li
 lia
 lib
 lic
 lid
 lie
 lif
 lig
 lih
 lij
 lik
 lil
 lio
 lip
 liq
 lir
 lis
 liu
 liv
 liw
 lix
 liy
 liz
 lja
 lje
 lji
 ljl
 ljp
 ljw
 ljx
 lka
 lkb
 lkc
 lkd
 lke
 lkh
 lki
 lkj
 lkl
 lkm
 lkn
 lko
 lkr
 lks
 lkt
 lku
 lky
 lla
 llb
 llc
 lld
 lle
 llf
 llg
 llh
 lli
 llj
 llk
 lll
 llm
 lln
 llp
 llq
 lls
 llu
 llx
 lma
 lmb
 lmc
 lmd
 lme
 lmf
 lmg
 lmh
 lmi
 lmj
 lmk
 lml
 lmn
 lmo
 lmp
 lmq
 lmr
 lmu
 lmv
 lmw
 lmx
 lmy
 ln
 lna
 lnb
 lnd
 lng
 lnh
 lni
 lnj
 lnl
 lnm
 lnn
 lns
 lnu
 lnw
 lnz
 lo
 loa
 lob
 loc
 loe
 lof
 log
 loh
 loi
 loj
 lok
 lol
 lom
 lon
 loo
 lop
 loq
 lor
 los
 lot
 lou
 lov
 low
 lox
 loy
 loz
 lpa
 lpe
 lpn
 lpo
 lpx
 lqr
 lra
 lrc
 lre
 lrg
 lri
 lrk
 lrl
 lrm
 lrn
 lro
 lrr
 lrt
 lrv
 lrz
 lsa
 lsb
 lsc
 lsd
 lse
 lsh
 lsi
 lsl
 lsm
 lsn
 lso
 lsp
 lsr
 lss
 lst
 lsv
 lsw
 lsy
 lt
 ltc
 ltg
 lth
 lti
 ltn
 lto
 lts
 ltu
 lu
 lua
 luc
 lud
 lue
 luf
 lui
 luj
 luk
 lul
 lum
 lun
 luo
 lup
 luq
 lur
 lus
 lut
 luu
 luv
 luw
 luy
 luz
 lv
 lva
 lvi
 lvk
 lvl
 lvu
 lwa
 lwe
 lwg
 lwh
 lwl
 lwm
 lwo
 lws
 lwt
 lwu
 lww
 lxm
 lya
 lyg
 lyn
 lzh
 lzl
 lzn
 lzz
 maa
 mab
 mad
 mae
 maf
 mag
 mai
 maj
 mak
 mam
 man
 map
 maq
 mas
 mat
 mau
 mav
 maw
 max
 maz
 mba
 mbb
 mbc
 mbd
 mbe
 mbf
 mbh
 mbi
 mbj
 mbk
 mbl
 mbm
 mbn
 mbo
 mbp
 mbq
 mbr
 mbs
 mbt
 mbu
 mbv
 mbw
 mbx
 mby
 mbz
 mca
 mcb
 mcc
 mcd
 mce
 mcf
 mcg
 mch
 mci
 mcj
 mck
 mcl
 mcm
 mcn
 mco
 mcp
 mcq
 mcr
 mcs
 mct
 mcu
 mcv
 mcw
 mcx
 mcy
 mcz
 mda
 mdb
 mdc
 mdd
 mde
 mdf
 mdg
 mdh
 mdi
 mdj
 mdk
 mdl
 mdm
 mdn
 mdp
 mdq
 mdr
 mds
 mdt
 mdu
 mdv
 mdw
 mdx
 mdy
 mdz
 mea
 meb
 mec
 med
 mee
 mef
 meh
 mei
 mej
 mek
 mel
 mem
 men
 meo
 mep
 meq
 mer
 mes
 met
 meu
 mev
 mew
 mey
 mez
 mfa
 mfb
 mfc
 mfd
 mfe
 mff
 mfg
 mfh
 mfi
 mfj
 mfk
 mfl
 mfm
 mfn
 mfo
 mfp
 mfq
 mfr
 mfs
 mft
 mfu
 mfv
 mfw
 mfx
 mfy
 mfz
 mg
 mga
 mgb
 mgc
 mgd
 mge
 mgf
 mgg
 mgh
 mgi
 mgj
 mgk
 mgl
 mgm
 mgn
 mgo
 mgp
 mgq
 mgr
 mgs
 mgt
 mgu
 mgv
 mgw
 mgy
 mgz
 mh
 mha
 mhb
 mhc
 mhd
 mhe
 mhf
 mhg
 mhi
 mhj
 mhk
 mhl
 mhm
 mhn
 mho
 mhp
 mhq
 mhs
 mht
 mhu
 mhw
 mhx
 mhy
 mhz
 mi
 mia
 mib
 mic
 mid
 mie
 mif
 mig
 mih
 mii
 mij
 mik
 mil
 mim
 min
 mio
 mip
 miq
 mir
 mit
 miu
 miw
 mix
 miy
 miz
 mjb
 mjc
 mjd
 mje
 mjg
 mjh
 mji
 mjj
 mjk
 mjl
 mjm
 mjn
 mjo
 mjp
 mjq
 mjr
 mjs
 mjt
 mju
 mjv
 mjw
 mjx
 mjy
 mjz
 mk
 mka
 mkb
 mkc
 mke
 mkf
 mkg
 mkh
 mki
 mkj
 mkk
 mkl
 mkm
 mkn
 mko
 mkp
 mkq
 mkr
 mks
 mkt
 mku
 mkv
 mkw
 mkx
 mky
 mkz
 ml
 mla
 mlb
 mlc
 mle
 mlf
 mlh
 mli
 mlj
 mlk
 mll
 mlm
 mln
 mlo
 mlp
 mlq
 mlr
 mls
 mlu
 mlv
 mlw
 mlx
 mlz
 mma
 mmb
 mmc
 mmd
 mme
 mmf
 mmg
 mmh
 mmi
 mmj
 mmk
 mml
 mmm
 mmn
 mmo
 mmp
 mmq
 mmr
 mmt
 mmu
 mmv
 mmw
 mmx
 mmy
 mmz
 mn
 mna
 mnb
 mnc
 mnd
 mne
 mnf
 mng
 mnh
 mni
 mnj
 mnl
 mnm
 mnn
 mno
 mnp
 mnq
 mnr
 mns
 mnu
 mnv
 mnw
 mnx
 mny
 mnz
 moa
 moc
 mod
 moe
 mog
 moh
 moi
 moj
 mok
 mom
 moo
 mop
 moq
 mor
 mos
 mot
 mou
 mov
 mow
 mox
 moy
 moz
 mpa
 mpb
 mpc
 mpd
 mpe
 mpg
 mph
 mpi
 mpj
 mpk
 mpl
 mpm
 mpn
 mpo
 mpp
 mpq
 mpr
 mps
 mpt
 mpu
 mpv
 mpw
 mpx
 mpy
 mpz
 mqa
 mqb
 mqc
 mqe
 mqf
 mqg
 mqh
 mqi
 mqj
 mqk
 mql
 mqm
 mqn
 mqo
 mqp
 mqq
 mqr
 mqs
 mqt
 mqu
 mqv
 mqw
 mqx
 mqy
 mqz
 mr
 mra
 mrb
 mrc
 mrd
 mre
 mrf
 mrg
 mrh
 mrj
 mrk
 mrl
 mrm
 mrn
 mro
 mrp
 mrq
 mrr
 mrs
 mrt
 mru
 mrv
 mrw
 mrx
 mry
 mrz
 ms
 msb
 msc
 msd
 mse
 msf
 msg
 msh
 msi
 msj
 msk
 msl
 msm
 msn
 mso
 msp
 msq
 msr
 mss
 msu
 msv
 msw
 msx
 msy
 msz
 mt
 mta
 mtb
 mtc
 mtd
 mte
 mtf
 mtg
 mth
 mti
 mtj
 mtk
 mtl
 mtm
 mtn
 mto
 mtp
 mtq
 mtr
 mts
 mtt
 mtu
 mtv
 mtw
 mtx
 mty
 mua
 mub
 muc
 mud
 mue
 mug
 muh
 mui
 muj
 muk
 mum
 mun
 muo
 muq
 mur
 mus
 mut
 muu
 muv
 mux
 muy
 muz
 mva
 mvb
 mvd
 mve
 mvf
 mvg
 mvh
 mvi
 mvk
 mvl
 mvn
 mvo
 mvp
 mvq
 mvr
 mvs
 mvt
 mvu
 mvv
 mvw
 mvx
 mvy
 mvz
 mwa
 mwb
 mwc
 mwe
 mwf
 mwg
 mwh
 mwi
 mwk
 mwl
 mwm
 mwn
 mwo
 mwp
 mwq
 mwr
 mws
 mwt
 mwu
 mwv
 mww
 mwz
 mxa
 mxb
 mxc
 mxd
 mxe
 mxf
 mxg
 mxh
 mxi
 mxj
 mxk
 mxl
 mxm
 mxn
 mxo
 mxp
 mxq
 mxr
 mxs
 mxt
 mxu
 mxv
 mxw
 mxx
 mxy
 mxz
 my
 myb
 myc
 mye
 myf
 myg
 myh
 myj
 myk
 myl
 mym
 myn
 myo
 myp
 myr
 mys
 myu
 myv
 myw
 myx
 myy
 myz
 mza
 mzb
 mzc
 mzd
 mze
 mzg
 mzh
 mzi
 mzj
 mzk
 mzl
 mzm
 mzn
 mzo
 mzp
 mzq
 mzr
 mzs
 mzt
 mzu
 mzv
 mzw
 mzx
 mzy
 mzz
 na
 naa
 nab
 nac
 nae
 naf
 nag
 nah
 nai
 naj
 nak
 nal
 nam
 nan
 nao
 nap
 naq
 nar
 nas
 nat
 naw
 nax
 nay
 naz
 nb
 nba
 nbb
 nbc
 nbd
 nbe
 nbg
 nbh
 nbi
 nbj
 nbk
 nbm
 nbn
 nbo
 nbp
 nbq
 nbr
 nbs
 nbt
 nbu
 nbv
 nbw
 nby
 nca
 ncb
 ncc
 ncd
 nce
 ncf
 ncg
 nch
 nci
 ncj
 nck
 ncl
 ncm
 ncn
 nco
 ncq
 ncr
 ncs
 nct
 ncu
 ncx
 ncz
 nd
 nda
 ndb
 ndc
 ndd
 ndf
 ndg
 ndh
 ndi
 ndj
 ndk
 ndl
 ndm
 ndn
 ndp
 ndq
 ndr
 nds
 ndt
 ndu
 ndv
 ndw
 ndx
 ndy
 ndz
 ne
 nea
 neb
 nec
 ned
 nee
 nef
 neg
 neh
 nei
 nej
 nek
 nem
 nen
 neo
 neq
 ner
 nes
 net
 neu
 nev
 new
 nex
 ney
 nez
 nfa
 nfd
 nfl
 nfr
 nfu
 ng
 nga
 ngb
 ngc
 ngd
 nge
 ngf
 ngg
 ngh
 ngi
 ngj
 ngk
 ngl
 ngm
 ngn
 ngp
 ngq
 ngr
 ngs
 ngt
 ngu
 ngv
 ngw
 ngx
 ngy
 ngz
 nha
 nhb
 nhc
 nhd
 nhe
 nhf
 nhg
 nhh
 nhi
 nhk
 nhm
 nhn
 nho
 nhp
 nhq
 nhr
 nht
 nhu
 nhv
 nhw
 nhx
 nhy
 nhz
 nia
 nib
 nic
 nid
 nie
 nif
 nig
 nih
 nii
 nij
 nik
 nil
 nim
 nin
 nio
 niq
 nir
 nis
 nit
 niu
 niv
 niw
 nix
 niy
 niz
 nja
 njb
 njd
 njh
 nji
 njj
 njl
 njm
 njn
 njo
 njr
 njs
 njt
 nju
 njx
 njy
 njz
 nka
 nkb
 nkc
 nkd
 nke
 nkf
 nkg
 nkh
 nki
 nkj
 nkk
 nkm
 nkn
 nko
 nkp
 nkq
 nkr
 nks
 nkt
 nku
 nkv
 nkw
 nkx
 nkz
 nl
 nla
 nlc
 nle
 nlg
 nli
 nlj
 nlk
 nll
 nlm
 nlo
 nlq
 nlu
 nlv
 nlw
 nlx
 nly
 nlz
 nma
 nmb
 nmc
 nmd
 nme
 nmf
 nmg
 nmh
 nmi
 nmj
 nmk
 nml
 nmm
 nmn
 nmo
 nmp
 nmq
 nmr
 nms
 nmt
 nmu
 nmv
 nmw
 nmx
 nmy
 nmz
 nn
 nna
 nnb
 nnc
 nnd
 nne
 nnf
 nng
 nnh
 nni
 nnj
 nnk
 nnl
 nnm
 nnn
 nnp
 nnq
 nnr
 nnt
 nnu
 nnv
 nnw
 nny
 nnz
 no
 noa
 noc
 nod
 noe
 nof
 nog
 noh
 noi
 noj
 nok
 nol
 non
 nop
 noq
 nos
 not
 nou
 nov
 now
 noy
 noz
 npa
 npb
 npg
 nph
 npl
 npn
 npo
 nps
 npu
 npx
 npy
 nqg
 nqk
 nql
 nqm
 nqn
 nqo
 nqq
 nqt
 nqy
 nr
 nra
 nrb
 nrc
 nre
 nrf
 nrg
 nri
 nrk
 nrl
 nrm
 nrn
 nrp
 nrr
 nrt
 nru
 nrx
 nrz
 nsa
 nsb
 nsc
 nsd
 nse
 nsf
 nsg
 nsh
 nsi
 nsk
 nsl
 nsm
 nsn
 nso
 nsp
 nsq
 nsr
 nss
 nst
 nsu
 nsv
 nsw
 nsx
 nsy
 nsz
 ntd
 nte
 ntg
 nti
 ntj
 ntk
 ntm
 nto
 ntp
 ntr
 ntu
 ntw
 ntx
 nty
 ntz
 nua
 nub
 nuc
 nud
 nue
 nuf
 nug
 nuh
 nui
 nuj
 nuk
 nul
 num
 nun
 nuo
 nup
 nuq
 nur
 nus
 nut
 nuu
 nuv
 nuw
 nux
 nuy
 nuz
 nv
 nvh
 nvm
 nvo
 nwa
 nwb
 nwc
 nwe
 nwg
 nwi
 nwm
 nwo
 nwr
 nww
 nwx
 nwy
 nxa
 nxd
 nxe
 nxg
 nxi
 nxk
 nxl
 nxm
 nxn
 nxo
 nxq
 nxr
 nxx
 ny
 nyb
 nyc
 nyd
 nye
 nyf
 nyg
 nyh
 nyi
 nyj
 nyk
 nyl
 nym
 nyn
 nyo
 nyp
 nyq
 nyr
 nys
 nyt
 nyu
 nyv
 nyw
 nyx
 nyy
 nza
 nzb
 nzd
 nzi
 nzk
 nzm
 nzr
 nzs
 nzu
 nzy
 nzz
 oaa
 oac
 oar
 oav
 obi
 obk
 obl
 obm
 obo
 obr
 obt
 obu
 oc
 oca
 och
 ocm
 oco
 ocu
 oda
 odk
 odt
 odu
 ofo
 ofs
 ofu
 ogb
 ogc
 oge
 ogg
 ogo
 ogu
 oht
 ohu
 oia
 oie
 oin
 oj
 ojb
 ojc
 ojp
 ojs
 ojv
 ojw
 oka
 okb
 okc
 okd
 oke
 okg
 okh
 oki
 okj
 okk
 okl
 okm
 okn
 oko
 okr
 oks
 oku
 okv
 okx
 okz
 ola
 old
 ole
 olk
 olm
 olo
 olr
 olt
 olu
 om
 oma
 omb
 omc
 omg
 omi
 omk
 oml
 omn
 omo
 omp
 omq
 omr
 omt
 omu
 omv
 omw
 omx
 omy
 ona
 onb
 one
 ong
 oni
 onj
 onk
 onn
 ono
 onp
 onr
 ons
 ont
 onu
 onw
 onx
 ood
 oog
 oon
 oor
 oos
 opa
 opk
 opm
 opo
 opt
 opy
 or
 ora
 orc
 ore
 org
 orh
 orn
 oro
 orr
 ors
 ort
 oru
 orv
 orw
 orx
 orz
 os
 osa
 osc
 osi
 osn
 oso
 osp
 ost
 osu
 osx
 ota
 otb
 otd
 ote
 oti
 otk
 otl
 otm
 otn
 oto
 otq
 otr
 ots
 ott
 otu
 otw
 otx
 oty
 otz
 oua
 oub
 oue
 oui
 oum
 ovd
 owi
 owl
 oyb
 oyd
 oym
 oyy
 ozm
 pa
 paa
 pab
 pac
 pad
 pae
 paf
 pag
 pah
 pai
 pak
 pal
 pam
 pao
 pap
 paq
 par
 pas
 pau
 pav
 paw
 pax
 pay
 paz
 pbb
 pbc
 pbe
 pbf
 pbg
 pbh
 pbi
 pbl
 pbm
 pbn
 pbo
 pbp
 pbr
 pbs
 pbt
 pbv
 pby
 pca
 pcb
 pcc
 pcd
 pce
 pcf
 pcg
 pch
 pci
 pcj
 pck
 pcl
 pcm
 pcn
 pcp
 pcw
 pda
 pdc
 pdi
 pdn
 pdo
 pdt
 pdu
 pea
 peb
 ped
 pee
 pef
 peg
 peh
 pei
 pej
 pek
 pel
 pem
 peo
 pep
 peq
 pev
 pex
 pey
 pez
 pfa
 pfe
 pfl
 pga
 pgd
 pgg
 pgi
 pgk
 pgl
 pgn
 pgs
 pgu
 pgz
 pha
 phd
 phg
 phh
 phi
 phj
 phk
 phl
 phm
 phn
 pho
 phq
 phr
 pht
 phu
 phv
 phw
 pi
 pia
 pib
 pic
 pid
 pie
 pif
 pig
 pih
 pij
 pil
 pim
 pin
 pio
 pip
 pir
 pis
 pit
 piu
 piv
 piw
 pix
 piy
 piz
 pjt
 pka
 pkb
 pkc
 pkg
 pkh
 pkn
 pko
 pkp
 pkr
 pks
 pkt
 pku
 pl
 pla
 plb
 plc
 pld
 ple
 plf
 plg
 plh
 plk
 pll
 pln
 plo
 plq
 plr
 pls
 plu
 plv
 plw
 ply
 plz
 pma
 pmb
 pmd
 pme
 pmf
 pmh
 pmi
 pmj
 pml
 pmm
 pmn
 pmo
 pmq
 pmr
 pms
 pmt
 pmw
 pmx
 pmy
 pmz
 pna
 pnc
 pnd
 pne
 png
 pnh
 pni
 pnj
 pnk
 pnl
 pnm
 pnn
 pno
 pnp
 pnq
 pnr
 pns
 pnt
 pnu
 pnv
 pnw
 pnx
 pny
 pnz
 poc
 poe
 pof
 pog
 poh
 poi
 pok
 pom
 pon
 poo
 pop
 poq
 pos
 pot
 pov
 pow
 pox
 poy
 poz
 ppe
 ppi
 ppk
 ppl
 ppm
 ppn
 ppo
 ppp
 ppq
 pps
 ppt
 ppu
 pqa
 pqe
 pqm
 pqw
 pra
 prc
 prd
 pre
 prf
 prg
 prh
 pri
 prk
 prl
 prm
 prn
 pro
 prq
 prr
 prt
 pru
 prw
 prx
 prz
 ps
 psa
 psc
 psd
 pse
 psg
 psh
 psi
 psl
 psm
 psn
 pso
 psp
 psq
 psr
 pss
 pst
 psu
 psw
 psy
 pt
 pta
 pth
 pti
 ptn
 pto
 ptp
 ptq
 ptr
 ptt
 ptu
 ptv
 ptw
 pty
 pua
 pub
 puc
 pud
 pue
 puf
 pug
 pui
 puj
 pum
 puo
 pup
 puq
 pur
 put
 puu
 puw
 pux
 puy
 pwa
 pwb
 pwg
 pwi
 pwm
 pwn
 pwo
 pwr
 pww
 pxm
 pye
 pym
 pyn
 pys
 pyu
 pyx
 pyy
 pze
 pzh
 pzn
 qu
 qua
 qub
 quc
 qud
 quf
 qug
 quh
 qui
 quk
 qul
 qum
 qun
 qup
 quq
 qur
 qus
 quv
 quw
 qux
 quy
 qva
 qvc
 qve
 qvh
 qvi
 qvj
 qvl
 qvm
 qvn
 qvo
 qvp
 qvs
 qvw
 qvy
 qvz
 qwa
 qwc
 qwe
 qwh
 qwm
 qws
 qwt
 qxa
 qxc
 qxh
 qxl
 qxn
 qxo
 qxp
 qxq
 qxr
 qxs
 qxt
 qxu
 qxw
 qya
 qyp
 raa
 rab
 rac
 rad
 raf
 rag
 rah
 rai
 raj
 rak
 ral
 ram
 ran
 rao
 rap
 raq
 rar
 ras
 rat
 rau
 rav
 raw
 rax
 ray
 raz
 rbb
 rbk
 rbl
 rbp
 rcf
 rdb
 rea
 reb
 ree
 reg
 rei
 rej
 rel
 rem
 ren
 rer
 res
 ret
 rey
 rga
 rge
 rgk
 rgn
 rgr
 rgs
 rgu
 rhg
 rhp
 ria
 rib
 rif
 ril
 rim
 rin
 rir
 rit
 riu
 rjg
 rji
 rjs
 rka
 rkb
 rkh
 rki
 rkm
 rkt
 rkw
 rm
 rma
 rmb
 rmc
 rmd
 rme
 rmf
 rmg
 rmh
 rmi
 rmk
 rml
 rmm
 rmn
 rmo
 rmp
 rmq
 rms
 rmt
 rmu
 rmv
 rmw
 rmx
 rmz
 rn
 rnb
 rnd
 rng
 rnl
 rnn
 rnp
 rnr
 rnw
 ro
 roa
 rob
 roc
 rod
 roe
 rof
 rog
 rol
 rom
 roo
 rop
 ror
 rou
 row
 rpn
 rpt
 rri
 rrm
 rro
 rrt
 rsb
 rsk
 rsl
 rsm
 rsn
 rsw
 rtc
 rth
 rtm
 rts
 rtw
 ru
 rub
 ruc
 rue
 ruf
 rug
 ruh
 rui
 ruk
 ruo
 rup
 ruq
 rut
 ruu
 ruy
 ruz
 rw
 rwa
 rwk
 rwl
 rwm
 rwo
 rwr
 rxd
 rxw
 ryn
 rys
 ryu
 rzh
 sa
 saa
 sab
 sac
 sad
 sae
 saf
 sah
 sai
 saj
 sak
 sal
 sam
 sao
 saq
 sar
 sas
 sat
 sau
 sav
 saw
 sax
 say
 saz
 sba
 sbb
 sbc
 sbd
 sbe
 sbf
 sbg
 sbh
 sbi
 sbj
 sbk
 sbl
 sbm
 sbn
 sbo
 sbp
 sbq
 sbr
 sbs
 sbt
 sbu
 sbv
 sbw
 sbx
 sby
 sbz
 sc
 scb
 sce
 scf
 scg
 sch
 sci
 sck
 scl
 scn
 sco
 scp
 scq
 scs
 sct
 scu
 scv
 scw
 scx
 sd
 sda
 sdb
 sdc
 sde
 sdf
 sdg
 sdh
 sdj
 sdk
 sdl
 sdn
 sdo
 sdp
 sdq
 sdr
 sds
 sdt
 sdu
 sdv
 sdx
 sdz
 se
 sea
 seb
 sec
 sed
 see
 sef
 seg
 seh
 sei
 sej
 sek
 sel
 sem
 sen
 seo
 sep
 seq
 ser
 ses
 set
 seu
 sev
 sew
 sey
 sez
 sfb
 sfe
 sfm
 sfs
 sfw
 sg
 sga
 sgb
 sgc
 sgd
 sge
 sgg
 sgh
 sgi
 sgj
 sgk
 sgm
 sgn
 sgp
 sgr
 sgs
 sgt
 sgu
 sgw
 sgx
 sgy
 sgz
 sha
 shb
 shc
 shd
 she
 shg
 shh
 shi
 shj
 shk
 shl
 shm
 shn
 sho
 shp
 shq
 shr
 shs
 sht
 shu
 shv
 shw
 shx
 shy
 shz
 si
 sia
 sib
 sid
 sie
 sif
 sig
 sih
 sii
 sij
 sik
 sil
 sim
 sio
 sip
 siq
 sir
 sis
 sit
 siu
 siv
 siw
 six
 siy
 siz
 sja
 sjb
 sjd
 sje
 sjg
 sjk
 sjl
 sjm
 sjn
 sjo
 sjp
 sjr
 sjs
 sjt
 sju
 sjw
 sk
 ska
 skb
 skc
 skd
 ske
 skf
 skg
 skh
 ski
 skj
 skm
 skn
 sko
 skp
 skq
 skr
 sks
 skt
 sku
 skv
 skw
 skx
 sky
 skz
 sl
 sla
 slc
 sld
 sle
 slf
 slg
 slh
 sli
 slj
 sll
 slm
 sln
 slp
 slr
 sls
 slt
 slu
 slw
 slx
 sly
 slz
 sm
 sma
 smb
 smc
 smf
 smg
 smh
 smi
 smj
 smk
 sml
 smm
 smn
 smp
 smq
 smr
 sms
 smt
 smu
 smv
 smw
 smx
 smy
 smz
 sn
 snc
 sne
 snf
 sng
 sni
 snj
 snk
 snl
 snm
 snn
 sno
 snp
 snq
 snr
 sns
 snu
 snv
 snw
 snx
 sny
 snz
 so
 soa
 sob
 soc
 sod
 soe
 sog
 soh
 soi
 soj
 sok
 sol
 son
 soo
 sop
 soq
 sor
 sos
 sou
 sov
 sow
 sox
 soy
 soz
 spb
 spc
 spd
 spe
 spg
 spi
 spk
 spl
 spm
 spn
 spo
 spp
 spq
 spr
 sps
 spt
 spu
 spv
 spx
 sq
 sqa
 sqh
 sqj
 sqk
 sqm
 sqn
 sqo
 sqq
 sqr
 sqs
 sqt
 squ
 sqx
 sr
 sra
 srb
 sre
 srf
 srg
 srh
 sri
 srk
 srl
 srm
 srn
 sro
 srq
 srr
 srs
 srt
 sru
 srv
 srw
 srx
 sry
 srz
 ss
 ssa
 ssb
 ssc
 ssd
 sse
 ssf
 ssg
 ssh
 ssi
 ssj
 ssk
 ssl
 ssm
 ssn
 sso
 ssp
 ssq
 ssr
 sss
 sst
 ssu
 ssv
 ssx
 ssy
 ssz
 st
 sta
 stb
 std
 ste
 stf
 stg
 sth
 sti
 stj
 stk
 stl
 stm
 stn
 sto
 stp
 stq
 str
 sts
 stt
 stu
 stv
 stw
 sty
 su
 sua
 sub
 suc
 sue
 sug
 sui
 suj
 suk
 suo
 suq
 sur
 sus
 sut
 suv
 suw
 sux
 suy
 suz
 sv
 sva
 svb
 svc
 sve
 svk
 svm
 svs
 svx
 sw
 swb
 swf
 swg
 swi
 swj
 swk
 swl
 swm
 swn
 swo
 swp
 swq
 swr
 sws
 swt
 swu
 swv
 sww
 swx
 swy
 sxb
 sxc
 sxe
 sxg
 sxk
 sxl
 sxm
 sxn
 sxo
 sxr
 sxs
 sxu
 sxw
 sya
 syb
 syc
 syd
 syi
 syk
 syl
 sym
 syn
 syo
 syr
 sys
 syw
 syx
 syy
 sza
 szb
 szc
 sze
 szg
 szl
 szn
 szp
 szs
 szv
 szw
 szy
 ta
 taa
 tab
 tac
 tad
 tae
 taf
 tag
 tai
 taj
 tak
 tal
 tan
 tao
 tap
 taq
 tar
 tas
 tau
 tav
 taw
 tax
 tay
 taz
 tba
 tbc
 tbd
 tbe
 tbf
 tbg
 tbh
 tbi
 tbj
 tbk
 tbl
 tbm
 tbn
 tbo
 tbp
 tbq
 tbr
 tbs
 tbt
 tbu
 tbv
 tbw
 tbx
 tby
 tbz
 tca
 tcb
 tcc
 tcd
 tce
 tcf
 tcg
 tch
 tci
 tck
 tcl
 tcm
 tcn
 tco
 tcp
 tcq
 tcs
 tct
 tcu
 tcw
 tcx
 tcy
 tcz
 tda
 tdb
 tdc
 tdd
 tde
 tdf
 tdg
 tdh
 tdi
 tdj
 tdk
 tdl
 tdm
 tdn
 tdo
 tdq
 tdr
 tds
 tdt
 tdv
 tdx
 tdy
 te
 tea
 teb
 tec
 ted
 tee
 tef
 teg
 teh
 tei
 tek
 tem
 ten
 teo
 tep
 teq
 ter
 tes
 tet
 teu
 tev
 tew
 tex
 tey
 tez
 tfi
 tfn
 tfo
 tfr
 tft
 tg
 tga
 tgb
 tgc
 tgd
 tge
 tgf
 tgh
 tgi
 tgj
 tgn
 tgo
 tgp
 tgq
 tgr
 tgs
 tgt
 tgu
 tgv
 tgw
 tgx
 tgy
 tgz
 th
 thd
 the
 thf
 thh
 thi
 thk
 thl
 thm
 thn
 thp
 thq
 thr
 ths
 tht
 thu
 thv
 thy
 thz
 ti
 tia
 tic
 tif
 tig
 tih
 tii
 tij
 tik
 til
 tim
 tin
 tio
 tip
 tiq
 tis
 tit
 tiu
 tiv
 tiw
 tix
 tiy
 tiz
 tja
 tjg
 tji
 tjj
 tjl
 tjm
 tjn
 tjo
 tjp
 tjs
 tju
 tjw
 tk
 tka
 tkb
 tkd
 tke
 tkf
 tkg
 tkl
 tkm
 tkn
 tkp
 tkq
 tkr
 tks
 tkt
 tku
 tkv
 tkw
 tkx
 tkz
 tla
 tlb
 tlc
 tld
 tlf
 tlg
 tlh
 tli
 tlj
 tlk
 tll
 tlm
 tln
 tlo
 tlp
 tlq
 tlr
 tls
 tlt
 tlu
 tlv
 tlx
 tly
 tma
 tmb
 tmc
 tmd
 tme
 tmf
 tmg
 tmh
 tmi
 tmj
 tml
 tmm
 tmn
 tmo
 tmq
 tmr
 tms
 tmt
 tmu
 tmv
 tmw
 tmy
 tmz
 tn
 tna
 tnb
 tnc
 tnd
 tng
 tnh
 tni
 tnk
 tnl
 tnm
 tnn
 tno
 tnp
 tnq
 tnr
 tns
 tnt
 tnu
 tnv
 tnw
 tnx
 tny
 tnz
 to
 tob
 toc
 tod
 tof
 tog
 toh
 toi
 toj
 tok
 tol
 tom
 too
 top
 toq
 tor
 tos
 tou
 tov
 tow
 tox
 toy
 toz
 tpa
 tpc
 tpe
 tpf
 tpg
 tpi
 tpj
 tpk
 tpl
 tpm
 tpn
 tpo
 tpp
 tpq
 tpr
 tpt
 tpu
 tpv
 tpx
 tpy
 tpz
 tqb
 tql
 tqm
 tqn
 tqo
 tqp
 tqq
 tqr
 tqt
 tqu
 tqw
 tr
 tra
 trb
 trc
 trd
 tre
 trf
 trg
 trh
 tri
 trj
 trk
 trl
 trm
 trn
 tro
 trp
 trq
 trr
 trs
 trt
 tru
 trv
 trw
 trx
 try
 trz
 ts
 tsa
 tsb
 tsc
 tsd
 tse
 tsg
 tsh
 tsi
 tsj
 tsk
 tsl
 tsm
 tsp
 tsq
 tsr
 tss
 tst
 tsu
 tsv
 tsw
 tsx
 tsy
 tsz
 tt
 tta
 ttb
 ttc
 ttd
 tte
 ttf
 ttg
 tth
 tti
 ttj
 ttk
 ttl
 ttm
 ttn
 tto
 ttp
 ttr
 tts
 ttt
 ttu
 ttv
 ttw
 tty
 ttz
 tua
 tub
 tuc
 tud
 tue
 tuf
 tug
 tuh
 tui
 tuj
 tul
 tum
 tun
 tuo
 tup
 tuq
 tus
 tut
 tuu
 tuv
 tuw
 tux
 tuy
 tuz
 tva
 tvd
 tve
 tvi
 tvk
 tvl
 tvm
 tvn
 tvo
 tvs
 tvt
 tvu
 tvw
 tvx
 tvy
 twa
 twb
 twc
 twd
 twe
 twf
 twg
 twh
 twl
 twm
 twn
 two
 twp
 twq
 twr
 twt
 twu
 tww
 twx
 twy
 txa
 txb
 txc
 txe
 txg
 txh
 txi
 txj
 txm
 txn
 txo
 txq
 txr
 txs
 txt
 txu
 txx
 txy
 ty
 tya
 tye
 tyh
 tyi
 tyj
 tyl
 tyn
 typ
 tyr
 tys
 tyt
 tyu
 tyv
 tyx
 tyy
 tyz
 tza
 tzh
 tzj
 tzl
 tzm
 tzn
 tzo
 tzx
 uam
 uan
 uar
 uba
 ubi
 ubl
 ubr
 ubu
 uby
 uda
 ude
 udg
 udi
 udj
 udl
 udm
 udu
 ues
 ufi
 ug
 uga
 ugb
 uge
 ugh
 ugn
 ugo
 ugy
 uha
 uhn
 uis
 uiv
 uji
 uk
 uka
 ukg
 ukh
 uki
 ukk
 ukl
 ukp
 ukq
 uks
 uku
 ukv
 ukw
 uky
 ula
 ulb
 ulc
 ule
 ulf
 uli
 ulk
 ull
 ulm
 uln
 ulu
 ulw
 uly
 uma
 umb
 umc
 umd
 umg
 umi
 umm
 umn
 umo
 ump
 umr
 ums
 una
 une
 ung
 uni
 unk
 unm
 unn
 unr
 unu
 unx
 unz
 uon
 upi
 upv
 ur
 ura
 urb
 urc
 ure
 urf
 urg
 urh
 uri
 urj
 urk
 url
 urm
 urn
 uro
 urp
 urr
 urt
 uru
 urv
 urw
 urx
 ury
 urz
 usa
 ush
 usi
 usk
 usp
 uss
 usu
 uta
 ute
 uth
 utp
 utr
 utu
 uum
 uur
 uuu
 uve
 uvh
 uvl
 uwa
 uya
 uz
 uzs
 vaa
 vae
 vaf
 vag
 vah
 vai
 vaj
 val
 vam
 van
 vao
 vap
 var
 vas
 vau
 vav
 vay
 vbb
 vbk
 ve
 vec
 ved
 vel
 vem
 veo
 vep
 ver
 vgr
 vgt
 vi
 vic
 vid
 vif
 vig
 vil
 vin
 vis
 vit
 viv
 vjk
 vka
 vkj
 vkk
 vkl
 vkm
 vkn
 vko
 vkp
 vkt
 vku
 vkz
 vlp
 vls
 vma
 vmb
 vmc
 vmd
 vme
 vmf
 vmg
 vmh
 vmi
 vmj
 vmk
 vml
 vmm
 vmp
 vmq
 vmr
 vms
 vmu
 vmv
 vmw
 vmx
 vmy
 vmz
 vnk
 vnm
 vnp
 vo
 vor
 vot
 vra
 vro
 vrs
 vrt
 vsi
 vsl
 vsn
 vsv
 vto
 vum
 vun
 vut
 vwa
 wa
 waa
 wab
 wac
 wad
 wae
 waf
 wag
 wah
 wai
 waj
 wak
 wal
 wam
 wan
 wao
 wap
 waq
 war
 was
 wat
 wau
 wav
 waw
 wax
 way
 waz
 wba
 wbb
 wbe
 wbf
 wbh
 wbi
 wbj
 wbk
 wbl
 wbm
 wbp
 wbq
 wbr
 wbs
 wbt
 wbv
 wbw
 wca
 wci
 wdd
 wdg
 wdj
 wdk
 wdt
 wdu
 wdy
 wea
 wec
 wed
 weg
 weh
 wei
 wem
 wen
 weo
 wep
 wer
 wes
 wet
 weu
 wew
 wfg
 wga
 wgb
 wgg
 wgi
 wgo
 wgu
 wgy
 wha
 whg
 whk
 whu
 wib
 wic
 wie
 wif
 wig
 wih
 wii
 wij
 wik
 wil
 wim
 win
 wir
 wiu
 wiv
 wiy
 wja
 wji
 wka
 wkb
 wkd
 wkl
 wkr
 wku
 wkw
 wky
 wla
 wlc
 wle
 wlg
 wlh
 wli
 wlk
 wll
 wlm
 wlo
 wlr
 wls
 wlu
 wlv
 wlw
 wlx
 wly
 wma
 wmb
 wmc
 wmd
 wme
 wmg
 wmh
 wmi
 wmm
 wmn
 wmo
 wms
 wmt
 wmw
 wmx
 wnb
 wnc
 wnd
 wne
 wng
 wni
 wnk
 wnm
 wnn
 wno
 wnp
 wnu
 wnw
 wny
 wo
 woa
 wob
 woc
 wod
 woe
 wof
 wog
 woi
 wok
 wom
 won
 woo
 wor
 wos
 wow
 woy
 wpc
 wrb
 wrg
 wrh
 wri
 wrk
 wrl
 wrm
 wrn
 wro
 wrp
 wrr
 wrs
 wru
 wrv
 wrw
 wrx
 wry
 wrz
 wsa
 wsg
 wsi
 wsk
 wsr
 wss
 wsu
 wsv
 wtb
 wtf
 wth
 wti
 wtk
 wtm
 wtw
 wua
 wub
 wud
 wuh
 wul
 wum
 wun
 wur
 wut
 wuu
 wuv
 wux
 wuy
 wwa
 wwb
 wwo
 wwr
 www
 wxa
 wxw
 wyb
 wyi
 wym
 wyn
 wyr
 wyy
 xaa
 xab
 xac
 xad
 xae
 xag
 xai
 xaj
 xak
 xal
 xam
 xan
 xao
 xap
 xaq
 xar
 xas
 xat
 xau
 xav
 xaw
 xay
 xbb
 xbc
 xbd
 xbe
 xbg
 xbi
 xbj
 xbm
 xbn
 xbo
 xbp
 xbr
 xbw
 xby
 xcb
 xcc
 xce
 xcg
 xch
 xcl
 xcm
 xcn
 xco
 xcr
 xct
 xcu
 xcv
 xcw
 xcy
 xda
 xdc
 xdk
 xdm
 xdo
 xdq
 xdy
 xeb
 xed
 xeg
 xel
 xem
 xep
 xer
 xes
 xet
 xeu
 xfa
 xga
 xgb
 xgd
 xgf
 xgg
 xgi
 xgl
 xgm
 xgn
 xgr
 xgu
 xgw
 xh
 xha
 xhc
 xhd
 xhe
 xhm
 xhr
 xht
 xhu
 xhv
 xib
 xii
 xil
 xin
 xir
 xis
 xiv
 xiy
 xjb
 xjt
 xka
 xkb
 xkc
 xkd
 xke
 xkf
 xkg
 xki
 xkj
 xkk
 xkl
 xkn
 xko
 xkp
 xkq
 xkr
 xks
 xkt
 xku
 xkv
 xkw
 xkx
 xky
 xkz
 xla
 xlb
 xlc
 xld
 xle
 xlg
 xli
 xln
 xlo
 xlp
 xls
 xlu
 xly
 xma
 xmb
 xmc
 xmd
 xme
 xmf
 xmg
 xmh
 xmj
 xmk
 xml
 xmm
 xmn
 xmo
 xmp
 xmq
 xmr
 xms
 xmt
 xmu
 xmv
 xmw
 xmx
 xmy
 xmz
 xna
 xnb
 xnd
 xng
 xnh
 xni
 xnj
 xnk
 xnm
 xnn
 xno
 xnq
 xnr
 xns
 xnt
 xnu
 xny
 xnz
 xoc
 xod
 xog
 xoi
 xok
 xom
 xon
 xoo
 xop
 xor
 xow
 xpa
 xpb
 xpc
 xpd
 xpf
 xpg
 xph
 xpi
 xpj
 xpk
 xpl
 xpm
 xpn
 xpo
 xpp
 xpq
 xpr
 xps
 xpt
 xpu
 xpv
 xpw
 xpx
 xpy
 xpz
 xqa
 xqt
 xra
 xrb
 xrd
 xre
 xrg
 xri
 xrm
 xrn
 xrr
 xrt
 xru
 xrw
 xsa
 xsb
 xsc
 xsd
 xse
 xsh
 xsi
 xsm
 xsn
 xso
 xsp
 xsq
 xsr
 xsu
 xsv
 xsy
 xta
 xtb
 xtc
 xtd
 xte
 xtg
 xth
 xti
 xtj
 xtl
 xtm
 xtn
 xto
 xtp
 xtq
 xtr
 xts
 xtt
 xtu
 xtv
 xtw
 xty
 xua
 xub
 xud
 xug
 xuj
 xul
 xum
 xun
 xuo
 xup
 xur
 xut
 xuu
 xve
 xvi
 xvn
 xvo
 xvs
 xwa
 xwc
 xwd
 xwe
 xwg
 xwj
 xwk
 xwl
 xwo
 xwr
 xwt
 xww
 xxb
 xxk
 xxm
 xxr
 xxt
 xya
 xyb
 xyj
 xyk
 xyl
 xyt
 xyy
 xzh
 xzm
 xzp
 yaa
 yab
 yac
 yad
 yae
 yaf
 yag
 yah
 yai
 yaj
 yak
 yal
 yam
 yan
 yao
 yap
 yaq
 yar
 yas
 yat
 yau
 yav
 yaw
 yax
 yay
 yaz
 yba
 ybb
 ybe
 ybh
 ybi
 ybj
 ybk
 ybl
 ybm
 ybn
 ybo
 ybx
 yby
 ych
 ycl
 ycn
 ycp
 ycr
 yda
 yde
 ydg
 ydk
 yea
 yec
 yee
 yei
 yej
 yel
 yer
 yes
 yet
 yeu
 yev
 yey
 yga
 ygi
 ygl
 ygm
 ygp
 ygr
 ygs
 ygu
 ygw
 yha
 yhd
 yhl
 yhs
 yi
 yia
 yif
 yig
 yih
 yii
 yij
 yik
 yil
 yim
 yin
 yip
 yiq
 yir
 yis
 yit
 yiu
 yiv
 yix
 yiz
 yka
 ykg
 ykh
 yki
 ykk
 ykl
 ykm
 ykn
 yko
 ykr
 ykt
 yku
 yky
 yla
 ylb
 yle
 ylg
 yli
 yll
 ylm
 yln
 ylo
 ylr
 ylu
 yly
 ymb
 ymc
 ymd
 yme
 ymg
 ymh
 ymi
 ymk
 yml
 ymm
 ymn
 ymo
 ymp
 ymq
 ymr
 yms
 ymx
 ymz
 yna
 ynd
 yne
 yng
 ynk
 ynl
 ynn
 yno
 ynq
 yns
 ynu
 yo
 yob
 yog
 yoi
 yok
 yol
 yom
 yon
 yot
 yox
 yoy
 ypa
 ypb
 ypg
 yph
 ypk
 ypm
 ypn
 ypo
 ypp
 ypz
 yra
 yrb
 yre
 yrk
 yrl
 yrm
 yrn
 yro
 yrs
 yrw
 yry
 ysc
 ysd
 ysg
 ysl
 ysm
 ysn
 yso
 ysp
 ysr
 yss
 ysy
 yta
 ytl
 ytp
 ytw
 yty
 yua
 yub
 yuc
 yud
 yue
 yuf
 yug
 yui
 yuj
 yuk
 yul
 yum
 yun
 yup
 yuq
 yur
 yut
 yuw
 yux
 yuy
 yuz
 yva
 yvt
 ywa
 ywg
 ywl
 ywn
 ywq
 ywr
 ywt
 ywu
 yww
 yxa
 yxg
 yxl
 yxm
 yxu
 yxy
 yyr
 yyu
 yyz
 yzg
 yzk
 za
 zaa
 zab
 zac
 zad
 zae
 zaf
 zag
 zah
 zaj
 zak
 zal
 zam
 zao
 zap
 zaq
 zar
 zas
 zat
 zau
 zav
 zaw
 zax
 zay
 zaz
 zba
 zbc
 zbe
 zbl
 zbt
 zbu
 zbw
 zca
 zcd
 zch
 zdj
 zea
 zeg
 zeh
 zem
 zen
 zga
 zgb
 zgh
 zgm
 zgn
 zgr
 zh
 zhb
 zhd
 zhi
 zhn
 zhw
 zhx
 zia
 zib
 zik
 zil
 zim
 zin
 ziw
 ziz
 zka
 zkd
 zkg
 zkh
 zkk
 zkn
 zko
 zkp
 zkr
 zkt
 zku
 zkv
 zkz
 zla
 zle
 zlj
 zlm
 zln
 zlq
 zls
 zlu
 zlw
 zma
 zmb
 zmc
 zmd
 zme
 zmf
 zmg
 zmh
 zmi
 zmj
 zmk
 zml
 zmm
 zmn
 zmo
 zmp
 zmq
 zmr
 zms
 zmt
 zmu
 zmv
 zmw
 zmx
 zmy
 zmz
 zna
 znd
 zne
 zng
 znk
 zns
 zoc
 zoh
 zom
 zoo
 zoq
 zor
 zos
 zpa
 zpb
 zpc
 zpd
 zpe
 zpf
 zpg
 zph
 zpi
 zpj
 zpk
 zpl
 zpm
 zpn
 zpo
 zpp
 zpq
 zpr
 zps
 zpt
 zpu
 zpv
 zpw
 zpx
 zpy
 zpz
 zqe
 zra
 zrg
 zrn
 zro
 zrp
 zrs
 zsa
 zsk
 zsl
 zsr
 zsu
 zte
 ztg
 ztl
 ztm
 ztn
 ztp
 ztq
 zts
 ztt
 ztu
 ztx
 zty
 zu
 zuh
 zum
 zun
 zuy
 zwa
 zyg
 zyj
 zyn
 zyp
 zza
 zzj
 mis
 mul
 zxx
 qaa
 qab
 qac
 qad
 qae
 qaf
 qag
 qah
 qai
 qaj
 qak
 qal
 qam
 qan
 qao
 qap
 qaq
 qar
 qas
 qat
 qau
 qav
 qaw
 qax
 qay
 qaz
 qba
 qbb
 qbc
 qbd
 qbe
 qbf
 qbg
 qbh
 qbi
 qbj
 qbk
 qbl
 qbm
 qbn
 qbo
 qbp
 qbq
 qbr
 qbs
 qbt
 qbu
 qbv
 qbw
 qbx
 qby
 qbz
 qca
 qcb
 qcc
 qcd
 qce
 qcf
 qcg
 qch
 qci
 qcj
 qck
 qcl
 qcm
 qcn
 qco
 qcp
 qcq
 qcr
 qcs
 qct
 qcu
 qcv
 qcw
 qcx
 qcy
 qcz
 qda
 qdb
 qdc
 qdd
 qde
 qdf
 qdg
 qdh
 qdi
 qdj
 qdk
 qdl
 qdm
 qdn
 qdo
 qdp
 qdq
 qdr
 qds
 qdt
 qdu
 qdv
 qdw
 qdx
 qdy
 qdz
 qea
 qeb
 qec
 qed
 qee
 qef
 qeg
 qeh
 qei
 qej
 qek
 qel
 qem
 qen
 qeo
 qep
 qeq
 qer
 qes
 qet
 qeu
 qev
 qew
 qex
 qey
 qez
 qfa
 qfb
 qfc
 qfd
 qfe
 qff
 qfg
 qfh
 qfi
 qfj
 qfk
 qfl
 qfm
 qfn
 qfo
 qfp
 qfq
 qfr
 qfs
 qft
 qfu
 qfv
 qfw
 qfx
 qfy
 qfz
 qga
 qgb
 qgc
 qgd
 qge
 qgf
 qgg
 qgh
 qgi
 qgj
 qgk
 qgl
 qgm
 qgn
 qgo
 qgp
 qgq
 qgr
 qgs
 qgt
 qgu
 qgv
 qgw
 qgx
 qgy
 qgz
 qha
 qhb
 qhc
 qhd
 qhe
 qhf
 qhg
 qhh
 qhi
 qhj
 qhk
 qhl
 qhm
 qhn
 qho
 qhp
 qhq
 qhr
 qhs
 qht
 qhu
 qhv
 qhw
 qhx
 qhy
 qhz
 qia
 qib
 qic
 qid
 qie
 qif
 qig
 qih
 qii
 qij
 qik
 qil
 qim
 qin
 qio
 qip
 qiq
 qir
 qis
 qit
 qiu
 qiv
 qiw
 qix
 qiy
 qiz
 qja
 qjb
 qjc
 qjd
 qje
 qjf
 qjg
 qjh
 qji
 qjj
 qjk
 qjl
 qjm
 qjn
 qjo
 qjp
 qjq
 qjr
 qjs
 qjt
 qju
 qjv
 qjw
 qjx
 qjy
 qjz
 qka
 qkb
 qkc
 qkd
 qke
 qkf
 qkg
 qkh
 qki
 qkj
 qkk
 qkl
 qkm
 qkn
 qko
 qkp
 qkq
 qkr
 qks
 qkt
 qku
 qkv
 qkw
 qkx
 qky
 qkz
 qla
 qlb
 qlc
 qld
 qle
 qlf
 qlg
 qlh
 qli
 qlj
 qlk
 qll
 qlm
 qln
 qlo
 qlp
 qlq
 qlr
 qls
 qlt
 qlu
 qlv
 qlw
 qlx
 qly
 qlz
 qma
 qmb
 qmc
 qmd
 qme
 qmf
 qmg
 qmh
 qmi
 qmj
 qmk
 qml
 qmm
 qmn
 qmo
 qmp
 qmq
 qmr
 qms
 qmt
 qmu
 qmv
 qmw
 qmx
 qmy
 qmz
 qna
 qnb
 qnc
 qnd
 qne
 qnf
 qng
 qnh
 qni
 qnj
 qnk
 qnl
 qnm
 qnn
 qno
 qnp
 qnq
 qnr
 qns
 qnt
 qnu
 qnv
 qnw
 qnx
 qny
 qnz
 qoa
 qob
 qoc
 qod
 qoe
 qof
 qog
 qoh
 qoi
 qoj
 qok
 qol
 qom
 qon
 qoo
 qop
 qoq
 qor
 qos
 qot
 qou
 qov
 qow
 qox
 qoy
 qoz
 qpa
 qpb
 qpc
 qpd
 qpe
 qpf
 qpg
 qph
 qpi
 qpj
 qpk
 qpl
 qpm
 qpn
 qpo
 qpp
 qpq
 qpr
 qps
 qpt
 qpu
 qpv
 qpw
 qpx
 qpy
 qpz
 qqa
 qqb
 qqc
 qqd
 qqe
 qqf
 qqg
 qqh
 qqi
 qqj
 qqk
 qql
 qqm
 qqn
 qqo
 qqp
 qqq
 qqr
 qqs
 qqt
 qqu
 qqv
 qqw
 qqx
 qqy
 qqz
 qra
 qrb
 qrc
 qrd
 qre
 qrf
 qrg
 qrh
 qri
 qrj
 qrk
 qrl
 qrm
 qrn
 qro
 qrp
 qrq
 qrr
 qrs
 qrt
 qru
 qrv
 qrw
 qrx
 qry
 qrz
 qsa
 qsb
 qsc
 qsd
 qse
 qsf
 qsg
 qsh
 qsi
 qsj
 qsk
 qsl
 qsm
 qsn
 qso
 qsp
 qsq
 qsr
 qss
 qst
 qsu
 qsv
 qsw
 qsx
 qsy
 qsz
 qta
 qtb
 qtc
 qtd
 qte
 qtf
 qtg
 qth
 qti
 qtj
 qtk
 qtl
 qtm
 qtn
 qto
 qtp
 qtq
 qtr
 qts
 qtt
 qtu
 qtv
 qtw
 qtx
 qty
 qtz
 und
 	)]},
);

around valid_languages => sub {
    my ($orig, $self) = @_;

    my $languages = $self->$orig;
    return @{$languages};
};

has 'valid_scripts' => (
	is			=> 'ro',
	isa			=> ArrayRef,
	init_arg	=> undef,
	default	=> sub {[qw( Adlm
 Aghb
 Ahom
 Arab
 Aran
 Armi
 Armn
 Avst
 Bali
 Bamu
 Bass
 Batk
 Beng
 Bhks
 Bopo
 Brah
 Brai
 Bugi
 Buhd
 Cakm
 Cans
 Cari
 Cham
 Cher
 Chrs
 Copt
 Cpmn
 Cprt
 Cyrl
 Cyrs
 Deva
 Diak
 Dogr
 Dsrt
 Dupl
 Egyp
 Elba
 Elym
 Ethi
 Gara
 Geor
 Glag
 Gong
 Gonm
 Goth
 Gran
 Grek
 Gujr
 Gukh
 Guru
 Hanb
 Hang
 Hani
 Hano
 Hans
 Hant
 Hatr
 Hebr
 Hira
 Hluw
 Hmng
 Hmnp
 Hrkt
 Hung
 Ital
 Jamo
 Java
 Jpan
 Kali
 Kana
 Kawi
 Khar
 Khmr
 Khoj
 Kits
 Knda
 Kore
 Krai
 Kthi
 Lana
 Laoo
 Latf
 Latg
 Latn
 Lepc
 Limb
 Lina
 Linb
 Lisu
 Lyci
 Lydi
 Mahj
 Maka
 Mand
 Mani
 Marc
 Medf
 Mend
 Merc
 Mero
 Mlym
 Modi
 Mong
 Mroo
 Mtei
 Mult
 Mymr
 Nagm
 Nand
 Narb
 Nbat
 Newa
 Nkoo
 Nshu
 Ogam
 Olck
 Onao
 Orkh
 Orya
 Osge
 Osma
 Ougr
 Palm
 Pauc
 Perm
 Phag
 Phli
 Phlp
 Phnx
 Plrd
 Prti
 Rjng
 Rohg
 Runr
 Samr
 Sarb
 Saur
 Sgnw
 Shaw
 Shrd
 Sidd
 Sind
 Sinh
 Sogd
 Sogo
 Sora
 Soyo
 Sund
 Sunu
 Sylo
 Syrc
 Syre
 Syrj
 Syrn
 Tagb
 Takr
 Tale
 Talu
 Taml
 Tang
 Tavt
 Telu
 Tfng
 Tglg
 Thaa
 Thai
 Tibt
 Tirh
 Tnsa
 Todr
 Toto
 Tutg
 Ugar
 Vaii
 Vith
 Wara
 Wcho
 Xpeo
 Xsux
 Yezi
 Yiii
 Zanb
 Qaag
 Zinh
 Zmth
 Zsye
 Zsym
 Zxxx
 Zyyy
 Qaaa
 Qaab
 Qaac
 Qaad
 Qaae
 Qaaf
 Qaah
 Qaaj
 Qaak
 Qaal
 Qaam
 Qaan
 Qaao
 Qaap
 Qaaq
 Qaar
 Qaas
 Qaat
 Qaau
 Qaav
 Qaaw
 Qaax
 Qaay
 Qaaz
 Qaba
 Qabb
 Qabc
 Qabd
 Qabe
 Qabf
 Qabg
 Qabh
 Qabi
 Qabj
 Qabk
 Qabl
 Qabm
 Qabn
 Qabo
 Qabp
 Qabq
 Qabr
 Qabs
 Qabt
 Qabu
 Qabv
 Qabw
 Qabx
 Zzzz
 	)]},
);

around valid_scripts => sub {
    my ($orig, $self) = @_;

    my $scripts = $self->$orig;
    return @{$scripts};
};

has 'valid_regions' => (
	is			=> 'ro',
	isa			=> ArrayRef,
	init_arg	=> undef,
	default	=> sub {[qw( AC
 AD
 AE
 AF
 AG
 AI
 AL
 AM
 AO
 AQ
 AR
 AS
 AT
 AU
 AW
 AX
 AZ
 BA
 BB
 BD
 BE
 BF
 BG
 BH
 BI
 BJ
 BL
 BM
 BN
 BO
 BQ
 BR
 BS
 BT
 BV
 BW
 BY
 BZ
 CA
 CC
 CD
 CF
 CG
 CH
 CI
 CK
 CL
 CM
 CN
 CO
 CP
 CQ
 CR
 CU
 CV
 CW
 CX
 CY
 CZ
 DE
 DG
 DJ
 DK
 DM
 DO
 DZ
 EA
 EC
 EE
 EG
 EH
 ER
 ES
 ET
 FI
 FJ
 FK
 FM
 FO
 FR
 GA
 GB
 GD
 GE
 GF
 GG
 GH
 GI
 GL
 GM
 GN
 GP
 GQ
 GR
 GS
 GT
 GU
 GW
 GY
 HK
 HM
 HN
 HR
 HT
 HU
 IC
 ID
 IE
 IL
 IM
 IN
 IO
 IQ
 IR
 IS
 IT
 JE
 JM
 JO
 JP
 KE
 KG
 KH
 KI
 KM
 KN
 KP
 KR
 KW
 KY
 KZ
 LA
 LB
 LC
 LI
 LK
 LR
 LS
 LT
 LU
 LV
 LY
 MA
 MC
 MD
 ME
 MF
 MG
 MH
 MK
 ML
 MM
 MN
 MO
 MP
 MQ
 MR
 MS
 MT
 MU
 MV
 MW
 MX
 MY
 MZ
 NA
 NC
 NE
 NF
 NG
 NI
 NL
 NO
 NP
 NR
 NU
 NZ
 OM
 PA
 PE
 PF
 PG
 PH
 PK
 PL
 PM
 PN
 PR
 PS
 PT
 PW
 PY
 QA
 RE
 RO
 RS
 RU
 RW
 SA
 SB
 SC
 SD
 SE
 SG
 SH
 SI
 SJ
 SK
 SL
 SM
 SN
 SO
 SR
 SS
 ST
 SV
 SX
 SY
 SZ
 TA
 TC
 TD
 TF
 TG
 TH
 TJ
 TK
 TL
 TM
 TN
 TO
 TR
 TT
 TV
 TW
 TZ
 UA
 UG
 UM
 US
 UY
 UZ
 VA
 VC
 VE
 VG
 VI
 VN
 VU
 WF
 WS
 XK
 YE
 YT
 ZA
 ZM
 ZW
 XA
 XB
 001
 002
 003
 005
 009
 011
 013
 014
 015
 017
 018
 019
 021
 029
 030
 034
 035
 039
 053
 054
 057
 061
 142
 143
 145
 150
 151
 154
 155
 202
 419
 EU
 EZ
 QO
 UN
 AA
 QM
 QN
 QP
 QQ
 QR
 QS
 QT
 QV
 QW
 QX
 QY
 QZ
 XC
 XD
 XE
 XF
 XG
 XH
 XI
 XJ
 XL
 XM
 XN
 XO
 XP
 XQ
 XR
 XS
 XT
 XU
 XV
 XW
 XX
 XY
 XZ
 ZZ
 	)]},
);

around valid_regions => sub {
    my ($orig, $self) = @_;

    my $regions = $self->$orig;
    return @{$regions};
};

has 'valid_variants' => (
	is			=> 'ro',
	isa			=> ArrayRef,
	init_arg	=> undef,
	default	=> sub {[qw( 1606nict
 1694acad
 1901
 1959acad
 1994
 1996
 abl1943
 akuapem
 alalc97
 aluku
 anpezo
 ao1990
 aranes
 arkaika
 asante
 auvern
 baku1926
 balanka
 barla
 basiceng
 bauddha
 bciav
 bcizbl
 biscayan
 biske
 blasl
 bohoric
 boont
 bornholm
 cisaup
 colb1945
 cornu
 creiss
 dajnko
 ekavsk
 emodeng
 fascia
 fodom
 fonipa
 fonkirsh
 fonnapa
 fonupa
 fonxsamp
 gallo
 gascon
 gherd
 grclass
 grital
 grmistr
 hepburn
 hognorsk
 hsistemo
 ijekavsk
 itihasa
 ivanchov
 jauer
 jyutping
 kkcor
 kociewie
 kscor
 lemosin
 lengadoc
 lipaw
 ltg1929
 ltg2007
 luna1918
 metelko
 monoton
 ndyuka
 nedis
 newfound
 nicard
 njiva
 nulik
 osojs
 oxendict
 pahawh2
 pahawh3
 pahawh4
 pamaka
 peano
 pehoeji
 petr1708
 pinyin
 polyton
 provenc
 puter
 rigik
 rozaj
 rumgr
 scotland
 scouse
 simple
 solba
 sotav
 spanglis
 surmiran
 sursilv
 sutsilv
 synnejyl
 tailo
 tarask
 tongyong
 tunumiit
 uccor
 ucrcor
 ulster
 unifon
 valbadia
 valencia
 vallader
 vecdruka
 vivaraup
 wadegile
 xsistemo
 arevela
 arevmda
 heploc
 laukika
 vaidika
 	)]},
);

around valid_variants => sub {
    my ($orig, $self) = @_;

    my $variants = $self->$orig;
    return @{$variants};
};

has 'valid_currencies' => (
	is			=> 'ro',
	isa			=> ArrayRef,
	init_arg	=> undef,
	default	=> sub {[qw( AED
 AFN
 ALL
 AMD
 ANG
 AOA
 ARS
 AUD
 AWG
 AZN
 BAM
 BBD
 BDT
 BGN
 BHD
 BIF
 BMD
 BND
 BOB
 BRL
 BSD
 BTN
 BWP
 BYN
 BZD
 CAD
 CDF
 CHF
 CLP
 CNY
 COP
 CRC
 CUP
 CVE
 CZK
 DJF
 DKK
 DOP
 DZD
 EGP
 ERN
 ETB
 EUR
 FJD
 FKP
 GBP
 GEL
 GHS
 GIP
 GMD
 GNF
 GTQ
 GYD
 HKD
 HNL
 HTG
 HUF
 IDR
 ILS
 INR
 IQD
 IRR
 ISK
 JMD
 JOD
 JPY
 KES
 KGS
 KHR
 KMF
 KPW
 KRW
 KWD
 KYD
 KZT
 LAK
 LBP
 LKR
 LRD
 LSL
 LYD
 MAD
 MDL
 MGA
 MKD
 MMK
 MNT
 MOP
 MRU
 MUR
 MVR
 MWK
 MXN
 MYR
 MZN
 NAD
 NGN
 NIO
 NOK
 NPR
 NZD
 OMR
 PAB
 PEN
 PGK
 PHP
 PKR
 PLN
 PYG
 QAR
 RON
 RSD
 RUB
 RWF
 SAR
 SBD
 SCR
 SDG
 SEK
 SGD
 SHP
 SLE
 SOS
 SRD
 SSP
 STN
 SYP
 SZL
 THB
 TJS
 TMT
 TND
 TOP
 TRY
 TTD
 TWD
 TZS
 UAH
 UGX
 USD
 UYU
 UZS
 VES
 VND
 VUV
 WST
 XAF
 XCD
 XCG
 XOF
 XPF
 YER
 ZAR
 ZMW
 ZWG
 XXX
 	)]},
);

around valid_currencies => sub {
    my ($orig, $self) = @_;

    my $currencies = $self->$orig;
    return @{$currencies};
};

has 'valid_subdivisions' => (
	is			=> 'ro',
	isa			=> ArrayRef,
	init_arg	=> undef,
	default	=> sub {[qw( ad02
 ad03
 ad04
 ad05
 ad06
 ad07
 ad08
 aeaj
 aeaz
 aedu
 aefu
 aerk
 aesh
 aeuq
 afbal
 afbam
 afbdg
 afbds
 afbgl
 afday
 affra
 affyb
 afgha
 afgho
 afhel
 afher
 afjow
 afkab
 afkan
 afkap
 afkdz
 afkho
 afknr
 aflag
 aflog
 afnan
 afnim
 afnur
 afpan
 afpar
 afpia
 afpka
 afsam
 afsar
 aftak
 afuru
 afwar
 afzab
 ag03
 ag04
 ag05
 ag06
 ag07
 ag08
 ag10
 ag11
 al01
 al02
 al03
 al04
 al05
 al06
 al07
 al08
 al09
 al10
 al11
 al12
 amag
 amar
 amav
 amer
 amgr
 amkt
 amlo
 amsh
 amsu
 amtv
 amvd
 aobgo
 aobgu
 aobie
 aocab
 aoccu
 aocnn
 aocno
 aocus
 aohua
 aohui
 aolno
 aolsu
 aolua
 aomal
 aomox
 aonam
 aouig
 aozai
 ara
 arb
 arc
 ard
 are
 arf
 arg
 arh
 arj
 ark
 arl
 arm
 arn
 arp
 arq
 arr
 ars
 art
 aru
 arv
 arw
 arx
 ary
 arz
 at1
 at2
 at3
 at4
 at5
 at6
 at7
 at8
 at9
 auact
 aunsw
 aunt
 auqld
 ausa
 autas
 auvic
 auwa
 azabs
 azaga
 azagc
 azagm
 azags
 azagu
 azast
 azba
 azbab
 azbal
 azbar
 azbey
 azbil
 azcab
 azcal
 azcul
 azdas
 azfuz
 azga
 azgad
 azgor
 azgoy
 azgyg
 azhac
 azimi
 azism
 azkal
 azkan
 azkur
 azla
 azlac
 azlan
 azler
 azmas
 azmi
 azna
 aznef
 aznv
 aznx
 azogu
 azord
 azqab
 azqax
 azqaz
 azqba
 azqbi
 azqob
 azqus
 azsa
 azsab
 azsad
 azsah
 azsak
 azsal
 azsar
 azsat
 azsbn
 azsiy
 azskr
 azsm
 azsmi
 azsmx
 azsr
 azsus
 aztar
 aztov
 azuca
 azxa
 azxac
 azxci
 azxiz
 azxvd
 azyar
 azye
 azyev
 azzan
 azzaq
 azzar
 babih
 babrc
 basrp
 bb01
 bb02
 bb03
 bb04
 bb05
 bb06
 bb07
 bb08
 bb09
 bb10
 bb11
 bd01
 bd02
 bd03
 bd04
 bd05
 bd06
 bd07
 bd08
 bd09
 bd10
 bd11
 bd12
 bd13
 bd14
 bd15
 bd16
 bd17
 bd18
 bd19
 bd20
 bd21
 bd22
 bd23
 bd24
 bd25
 bd26
 bd27
 bd28
 bd29
 bd30
 bd31
 bd32
 bd33
 bd34
 bd35
 bd36
 bd37
 bd38
 bd39
 bd40
 bd41
 bd42
 bd43
 bd44
 bd45
 bd46
 bd47
 bd48
 bd49
 bd50
 bd51
 bd52
 bd53
 bd54
 bd55
 bd56
 bd57
 bd58
 bd59
 bd60
 bd61
 bd62
 bd63
 bd64
 bda
 bdb
 bdc
 bdd
 bde
 bdf
 bdg
 bdh
 bebru
 bevan
 bevbr
 bevlg
 bevli
 bevov
 bevwv
 bewal
 bewbr
 bewht
 bewlg
 bewlx
 bewna
 bf01
 bf02
 bf03
 bf04
 bf05
 bf06
 bf07
 bf08
 bf09
 bf10
 bf11
 bf12
 bf13
 bfbal
 bfbam
 bfban
 bfbaz
 bfbgr
 bfblg
 bfblk
 bfcom
 bfgan
 bfgna
 bfgou
 bfhou
 bfiob
 bfkad
 bfken
 bfkmd
 bfkmp
 bfkop
 bfkos
 bfkot
 bfkow
 bfler
 bflor
 bfmou
 bfnam
 bfnao
 bfnay
 bfnou
 bfoub
 bfoud
 bfpas
 bfpon
 bfsen
 bfsis
 bfsmt
 bfsng
 bfsom
 bfsor
 bftap
 bftui
 bfyag
 bfyat
 bfzir
 bfzon
 bfzou
 bg01
 bg02
 bg03
 bg04
 bg05
 bg06
 bg07
 bg08
 bg09
 bg10
 bg11
 bg12
 bg13
 bg14
 bg15
 bg16
 bg17
 bg18
 bg19
 bg20
 bg21
 bg22
 bg23
 bg24
 bg25
 bg26
 bg27
 bg28
 bh13
 bh14
 bh15
 bh17
 bibb
 bibl
 bibm
 bibr
 bica
 bici
 bigi
 biki
 bikr
 biky
 bima
 bimu
 bimw
 bimy
 bing
 birm
 birt
 biry
 bjak
 bjal
 bjaq
 bjbo
 bjco
 bjdo
 bjko
 bjli
 bjmo
 bjou
 bjpl
 bjzo
 bnbe
 bnbm
 bnte
 bntu
 bob
 boc
 boh
 bol
 bon
 boo
 bop
 bos
 bot
 bqbo
 bqsa
 bqse
 brac
 bral
 bram
 brap
 brba
 brce
 brdf
 bres
 brgo
 brma
 brmg
 brms
 brmt
 brpa
 brpb
 brpe
 brpi
 brpr
 brrj
 brrn
 brro
 brrr
 brrs
 brsc
 brse
 brsp
 brto
 bsak
 bsbi
 bsbp
 bsby
 bsce
 bsci
 bsck
 bsco
 bscs
 bseg
 bsex
 bsfp
 bsgc
 bshi
 bsht
 bsin
 bsli
 bsmc
 bsmg
 bsmi
 bsne
 bsno
 bsnp
 bsns
 bsrc
 bsri
 bssa
 bsse
 bsso
 bsss
 bssw
 bswg
 bt11
 bt12
 bt13
 bt14
 bt15
 bt21
 bt22
 bt23
 bt24
 bt31
 bt32
 bt33
 bt34
 bt41
 bt42
 bt43
 bt44
 bt45
 btga
 btty
 bwce
 bwch
 bwfr
 bwga
 bwgh
 bwjw
 bwkg
 bwkl
 bwkw
 bwlo
 bwne
 bwnw
 bwse
 bwso
 bwsp
 bwst
 bybr
 byhm
 byho
 byhr
 byma
 bymi
 byvi
 bzbz
 bzcy
 bzczl
 bzow
 bzsc
 bztol
 caab
 cabc
 camb
 canb
 canl
 cans
 cant
 canu
 caon
 cape
 caqc
 cask
 cayt
 cdbc
 cdbu
 cdeq
 cdhk
 cdhl
 cdhu
 cdit
 cdkc
 cdke
 cdkg
 cdkl
 cdkn
 cdks
 cdlo
 cdlu
 cdma
 cdmn
 cdmo
 cdnk
 cdnu
 cdsa
 cdsk
 cdsu
 cdta
 cdto
 cdtu
 cfac
 cfbb
 cfbgf
 cfbk
 cfhk
 cfhm
 cfhs
 cfkb
 cfkg
 cflb
 cfmb
 cfmp
 cfnm
 cfop
 cfse
 cfuk
 cfvk
 cg11
 cg12
 cg13
 cg14
 cg15
 cg16
 cg2
 cg5
 cg7
 cg8
 cg9
 cgbzv
 chag
 chai
 char
 chbe
 chbl
 chbs
 chfr
 chge
 chgl
 chgr
 chju
 chlu
 chne
 chnw
 chow
 chsg
 chsh
 chso
 chsz
 chtg
 chti
 chur
 chvd
 chvs
 chzg
 chzh
 ciab
 cibs
 cicm
 cidn
 cigd
 cilc
 cilg
 cimg
 cism
 cisv
 civb
 ciwr
 ciym
 cizz
 clai
 clan
 clap
 clar
 clat
 clbi
 clco
 clli
 clll
 cllr
 clma
 clml
 clnb
 clrm
 clta
 clvs
 cmad
 cmce
 cmen
 cmes
 cmlt
 cmno
 cmnw
 cmou
 cmsu
 cmsw
 cnah
 cnbj
 cncq
 cnfj
 cngd
 cngs
 cngx
 cngz
 cnha
 cnhb
 cnhe
 cnhi
 cnhk
 cnhl
 cnhn
 cnjl
 cnjs
 cnjx
 cnln
 cnmo
 cnnm
 cnnx
 cnqh
 cnsc
 cnsd
 cnsh
 cnsn
 cnsx
 cntj
 cntw
 cnxj
 cnxz
 cnyn
 cnzj
 coama
 coant
 coara
 coatl
 cobol
 coboy
 cocal
 cocaq
 cocas
 cocau
 coces
 cocho
 cocor
 cocun
 codc
 cogua
 coguv
 cohui
 colag
 comag
 comet
 conar
 consa
 coput
 coqui
 coris
 cosan
 cosap
 cosuc
 cotol
 covac
 covau
 covid
 cra
 crc
 crg
 crh
 crl
 crp
 crsj
 cu01
 cu03
 cu04
 cu05
 cu06
 cu07
 cu08
 cu09
 cu10
 cu11
 cu12
 cu13
 cu14
 cu15
 cu16
 cu99
 cvb
 cvbr
 cvbv
 cvca
 cvcf
 cvcr
 cvma
 cvmo
 cvpa
 cvpn
 cvpr
 cvrb
 cvrg
 cvrs
 cvs
 cvsd
 cvsf
 cvsl
 cvsm
 cvso
 cvss
 cvsv
 cvta
 cvts
 cy01
 cy02
 cy03
 cy04
 cy05
 cy06
 cz10
 cz20
 cz201
 cz202
 cz203
 cz204
 cz205
 cz206
 cz207
 cz208
 cz209
 cz20a
 cz20b
 cz20c
 cz31
 cz311
 cz312
 cz313
 cz314
 cz315
 cz316
 cz317
 cz32
 cz321
 cz322
 cz323
 cz324
 cz325
 cz326
 cz327
 cz41
 cz411
 cz412
 cz413
 cz42
 cz421
 cz422
 cz423
 cz424
 cz425
 cz426
 cz427
 cz51
 cz511
 cz512
 cz513
 cz514
 cz52
 cz521
 cz522
 cz523
 cz524
 cz525
 cz53
 cz531
 cz532
 cz533
 cz534
 cz63
 cz631
 cz632
 cz633
 cz634
 cz635
 cz64
 cz641
 cz642
 cz643
 cz644
 cz645
 cz646
 cz647
 cz71
 cz711
 cz712
 cz713
 cz714
 cz715
 cz72
 cz721
 cz722
 cz723
 cz724
 cz80
 cz801
 cz802
 cz803
 cz804
 cz805
 cz806
 debb
 debe
 debw
 deby
 dehb
 dehe
 dehh
 demv
 deni
 denw
 derp
 desh
 desl
 desn
 dest
 deth
 djar
 djas
 djdi
 djdj
 djob
 djta
 dk81
 dk82
 dk83
 dk84
 dk85
 dm02
 dm03
 dm04
 dm05
 dm06
 dm07
 dm08
 dm09
 dm10
 dm11
 do01
 do02
 do03
 do04
 do05
 do06
 do07
 do08
 do09
 do10
 do11
 do12
 do13
 do14
 do15
 do16
 do17
 do18
 do19
 do20
 do21
 do22
 do23
 do24
 do25
 do26
 do27
 do28
 do29
 do30
 do31
 do32
 do33
 do34
 do35
 do36
 do37
 do38
 do39
 do40
 do41
 do42
 dz01
 dz02
 dz03
 dz04
 dz05
 dz06
 dz07
 dz08
 dz09
 dz10
 dz11
 dz12
 dz13
 dz14
 dz15
 dz16
 dz17
 dz18
 dz19
 dz20
 dz21
 dz22
 dz23
 dz24
 dz25
 dz26
 dz27
 dz28
 dz29
 dz30
 dz31
 dz32
 dz33
 dz34
 dz35
 dz36
 dz37
 dz38
 dz39
 dz40
 dz41
 dz42
 dz43
 dz44
 dz45
 dz46
 dz47
 dz48
 dz49
 dz50
 dz51
 dz52
 dz53
 dz54
 dz55
 dz56
 dz57
 dz58
 eca
 ecb
 ecc
 ecd
 ece
 ecf
 ecg
 ech
 eci
 ecl
 ecm
 ecn
 eco
 ecp
 ecr
 ecs
 ecsd
 ecse
 ect
 ecu
 ecw
 ecx
 ecy
 ecz
 ee130
 ee141
 ee142
 ee171
 ee184
 ee191
 ee198
 ee205
 ee214
 ee245
 ee247
 ee251
 ee255
 ee272
 ee283
 ee284
 ee291
 ee293
 ee296
 ee303
 ee305
 ee317
 ee321
 ee338
 ee353
 ee37
 ee39
 ee424
 ee430
 ee431
 ee432
 ee441
 ee442
 ee446
 ee45
 ee478
 ee480
 ee486
 ee50
 ee503
 ee511
 ee514
 ee52
 ee528
 ee557
 ee56
 ee567
 ee586
 ee60
 ee615
 ee618
 ee622
 ee624
 ee638
 ee64
 ee651
 ee653
 ee661
 ee663
 ee668
 ee68
 ee689
 ee698
 ee708
 ee71
 ee712
 ee714
 ee719
 ee726
 ee732
 ee735
 ee74
 ee784
 ee79
 ee792
 ee793
 ee796
 ee803
 ee809
 ee81
 ee824
 ee834
 ee84
 ee855
 ee87
 ee890
 ee897
 ee899
 ee901
 ee903
 ee907
 ee917
 ee919
 ee928
 egalx
 egasn
 egast
 egba
 egbh
 egbns
 egc
 egdk
 egdt
 egfym
 eggh
 eggz
 egis
 egjs
 egkb
 egkfs
 egkn
 eglx
 egmn
 egmnf
 egmt
 egpts
 egshg
 egshr
 egsin
 egsuz
 egwad
 eran
 erdk
 erdu
 ergb
 erma
 ersk
 esa
 esab
 esal
 esan
 esar
 esas
 esav
 esb
 esba
 esbi
 esbu
 esc
 esca
 escb
 escc
 esce
 escl
 escm
 escn
 esco
 escr
 escs
 esct
 escu
 esex
 esga
 esgc
 esgi
 esgr
 esgu
 esh
 eshu
 esib
 esj
 esl
 esle
 eslo
 eslu
 esm
 esma
 esmc
 esmd
 esml
 esmu
 esna
 esnc
 eso
 esor
 esp
 espm
 espo
 espv
 esri
 ess
 essa
 esse
 essg
 esso
 esss
 est
 este
 estf
 esto
 esv
 esva
 esvc
 esvi
 esz
 esza
 etaa
 etaf
 etam
 etbe
 etdd
 etga
 etha
 etor
 etsi
 etsn
 etso
 etsw
 etti
 fi02
 fi03
 fi04
 fi05
 fi06
 fi07
 fi08
 fi09
 fi10
 fi11
 fi12
 fi13
 fi14
 fi15
 fi16
 fi17
 fi18
 fi19
 fj01
 fj02
 fj03
 fj04
 fj05
 fj06
 fj07
 fj08
 fj09
 fj10
 fj11
 fj12
 fj13
 fj14
 fjc
 fje
 fjn
 fjr
 fjw
 fmksa
 fmpni
 fmtrk
 fmyap
 fr01
 fr02
 fr03
 fr04
 fr05
 fr06
 fr07
 fr08
 fr09
 fr10
 fr11
 fr12
 fr13
 fr14
 fr15
 fr16
 fr17
 fr18
 fr19
 fr20r
 fr21
 fr22
 fr23
 fr24
 fr25
 fr26
 fr27
 fr28
 fr29
 fr2a
 fr2b
 fr30
 fr31
 fr32
 fr33
 fr34
 fr35
 fr36
 fr37
 fr38
 fr39
 fr40
 fr41
 fr42
 fr43
 fr44
 fr45
 fr46
 fr47
 fr48
 fr49
 fr50
 fr51
 fr52
 fr53
 fr54
 fr55
 fr56
 fr57
 fr58
 fr59
 fr60
 fr61
 fr62
 fr63
 fr64
 fr65
 fr66
 fr67
 fr68
 fr69
 fr69m
 fr6ae
 fr70
 fr71
 fr72
 fr73
 fr74
 fr75c
 fr76
 fr77
 fr78
 fr79
 fr80
 fr81
 fr82
 fr83
 fr84
 fr85
 fr86
 fr87
 fr88
 fr89
 fr90
 fr91
 fr92
 fr93
 fr94
 fr95
 fr971
 fr972
 fr973
 fr974
 fr976
 frara
 frbfc
 frbre
 frcvl
 frges
 frhdf
 fridf
 frnaq
 frnor
 frocc
 frpac
 frpdl
 ga1
 ga2
 ga3
 ga4
 ga5
 ga6
 ga7
 ga8
 ga9
 gbabc
 gbabd
 gbabe
 gbagb
 gbagy
 gband
 gbann
 gbans
 gbbas
 gbbbd
 gbbcp
 gbbdf
 gbbdg
 gbben
 gbbex
 gbbfs
 gbbge
 gbbgw
 gbbir
 gbbkm
 gbbne
 gbbnh
 gbbns
 gbbol
 gbbpl
 gbbrc
 gbbrd
 gbbry
 gbbst
 gbbur
 gbcam
 gbcay
 gbcbf
 gbccg
 gbcgn
 gbche
 gbchw
 gbcld
 gbclk
 gbcma
 gbcmd
 gbcmn
 gbcon
 gbcov
 gbcrf
 gbcry
 gbcwy
 gbdal
 gbdby
 gbden
 gbder
 gbdev
 gbdgy
 gbdnc
 gbdnd
 gbdor
 gbdrs
 gbdud
 gbdur
 gbeal
 gbeay
 gbedh
 gbedu
 gbeln
 gbels
 gbenf
 gbeng
 gberw
 gbery
 gbess
 gbesx
 gbfal
 gbfif
 gbfln
 gbfmo
 gbgat
 gbglg
 gbgls
 gbgre
 gbgwn
 gbhal
 gbham
 gbhav
 gbhck
 gbhef
 gbhil
 gbhld
 gbhmf
 gbhns
 gbhpl
 gbhrt
 gbhrw
 gbhry
 gbios
 gbiow
 gbisl
 gbivc
 gbkec
 gbken
 gbkhl
 gbkir
 gbktt
 gbkwl
 gblan
 gblbc
 gblbh
 gblce
 gblds
 gblec
 gblew
 gblin
 gbliv
 gblnd
 gblut
 gbman
 gbmdb
 gbmdw
 gbmea
 gbmik
 gbmln
 gbmon
 gbmrt
 gbmry
 gbmty
 gbmul
 gbnay
 gbnbl
 gbnel
 gbnet
 gbnfk
 gbngm
 gbnir
 gbnlk
 gbnln
 gbnmd
 gbnnh
 gbnsm
 gbntl
 gbntt
 gbnty
 gbnwm
 gbnwp
 gbnyk
 gbold
 gbork
 gboxf
 gbpem
 gbpkn
 gbply
 gbpor
 gbpow
 gbpte
 gbrcc
 gbrch
 gbrct
 gbrdb
 gbrdg
 gbrfw
 gbric
 gbrot
 gbrut
 gbsaw
 gbsay
 gbscb
 gbsct
 gbsfk
 gbsft
 gbsgc
 gbshf
 gbshn
 gbshr
 gbskp
 gbslf
 gbslg
 gbslk
 gbsnd
 gbsol
 gbsom
 gbsos
 gbsry
 gbste
 gbstg
 gbsth
 gbstn
 gbsts
 gbstt
 gbsty
 gbswa
 gbswd
 gbswk
 gbtam
 gbtfw
 gbthr
 gbtob
 gbtof
 gbtrf
 gbtwh
 gbvgl
 gbwar
 gbwbk
 gbwdu
 gbwft
 gbwgn
 gbwil
 gbwkf
 gbwll
 gbwln
 gbwls
 gbwlv
 gbwnd
 gbwnh
 gbwnm
 gbwok
 gbwor
 gbwrl
 gbwrt
 gbwrx
 gbwsm
 gbwsx
 gbyor
 gbzet
 gd01
 gd02
 gd03
 gd04
 gd05
 gd06
 gd10
 geab
 geaj
 gegu
 geim
 geka
 gekk
 gemm
 gerl
 gesj
 gesk
 gesz
 getb
 ghaa
 ghaf
 ghah
 ghbe
 ghbo
 ghcp
 ghep
 ghne
 ghnp
 ghot
 ghsv
 ghtv
 ghue
 ghuw
 ghwn
 ghwp
 glav
 glku
 glqe
 glqt
 glsm
 gmb
 gml
 gmm
 gmn
 gmu
 gmw
 gnb
 gnbe
 gnbf
 gnbk
 gnc
 gnco
 gnd
 gndb
 gndi
 gndl
 gndu
 gnf
 gnfa
 gnfo
 gnfr
 gnga
 gngu
 gnk
 gnka
 gnkb
 gnkd
 gnke
 gnkn
 gnko
 gnks
 gnl
 gnla
 gnle
 gnlo
 gnm
 gnmc
 gnmd
 gnml
 gnmm
 gnn
 gnnz
 gnpi
 gnsi
 gnte
 gnto
 gnyo
 gqan
 gqbn
 gqbs
 gqc
 gqcs
 gqdj
 gqi
 gqkn
 gqli
 gqwn
 gr69
 gra
 grb
 grc
 grd
 gre
 grf
 grg
 grh
 gri
 grj
 grk
 grl
 grm
 gt01
 gt02
 gt03
 gt04
 gt05
 gt06
 gt07
 gt08
 gt09
 gt10
 gt11
 gt12
 gt13
 gt14
 gt15
 gt16
 gt17
 gt18
 gt19
 gt20
 gt21
 gt22
 gwba
 gwbl
 gwbm
 gwbs
 gwca
 gwga
 gwl
 gwn
 gwoi
 gwqu
 gws
 gwto
 gyba
 gycu
 gyde
 gyeb
 gyes
 gyma
 gypm
 gypt
 gyud
 gyut
 hnat
 hnch
 hncl
 hncm
 hncp
 hncr
 hnep
 hnfm
 hngd
 hnib
 hnin
 hnle
 hnlp
 hnoc
 hnol
 hnsb
 hnva
 hnyo
 hr01
 hr02
 hr03
 hr04
 hr05
 hr06
 hr07
 hr08
 hr09
 hr10
 hr11
 hr12
 hr13
 hr14
 hr15
 hr16
 hr17
 hr18
 hr19
 hr20
 hr21
 htar
 htce
 htga
 htnd
 htne
 htni
 htno
 htou
 htsd
 htse
 huba
 hubc
 hube
 hubk
 hubu
 hubz
 hucs
 hude
 hudu
 hueg
 huer
 hufe
 hugs
 hugy
 huhb
 huhe
 huhv
 hujn
 huke
 hukm
 hukv
 humi
 hunk
 huno
 huny
 hupe
 hups
 husd
 husf
 hush
 husk
 husn
 huso
 huss
 hust
 husz
 hutb
 huto
 huva
 huve
 huvm
 huza
 huze
 idac
 idba
 idbb
 idbe
 idbt
 idgo
 idja
 idjb
 idji
 idjk
 idjt
 idjw
 idka
 idkb
 idki
 idkr
 idks
 idkt
 idku
 idla
 idma
 idml
 idmu
 idnb
 idnt
 idnu
 idpa
 idpb
 idpd
 idpe
 idpp
 idps
 idpt
 idri
 idsa
 idsb
 idsg
 idsl
 idsm
 idsn
 idsr
 idss
 idst
 idsu
 idyo
 iec
 iece
 iecn
 ieco
 iecw
 ied
 iedl
 ieg
 ieke
 iekk
 ieky
 iel
 ield
 ielh
 ielk
 ielm
 iels
 iem
 iemh
 iemn
 iemo
 ieoy
 iern
 ieso
 ieta
 ieu
 iewd
 iewh
 ieww
 iewx
 ild
 ilha
 iljm
 ilm
 ilta
 ilz
 inan
 inap
 inar
 inas
 inbr
 incg
 inch
 indh
 indl
 inga
 ingj
 inhp
 inhr
 injh
 injk
 inka
 inkl
 inla
 inld
 inmh
 inml
 inmn
 inmp
 inmz
 innl
 inod
 inpb
 inpy
 inrj
 insk
 intn
 intr
 ints
 inuk
 inup
 inwb
 iqan
 iqar
 iqba
 iqbb
 iqbg
 iqda
 iqdi
 iqdq
 iqka
 iqki
 iqkr
 iqma
 iqmu
 iqna
 iqni
 iqqa
 iqsd
 iqsu
 iqwa
 ir00
 ir01
 ir02
 ir03
 ir04
 ir05
 ir06
 ir07
 ir08
 ir09
 ir10
 ir11
 ir12
 ir13
 ir14
 ir15
 ir16
 ir17
 ir18
 ir19
 ir20
 ir21
 ir22
 ir23
 ir24
 ir25
 ir26
 ir27
 ir28
 ir29
 ir30
 is1
 is2
 is3
 is4
 is5
 is6
 is7
 is8
 isakn
 isaku
 isarn
 isasa
 isbla
 isbog
 isbol
 isdab
 isdav
 iseom
 iseyf
 isfjd
 isfjl
 isfla
 isflr
 isgar
 isgog
 isgrn
 isgru
 isgry
 ishaf
 ishrg
 ishru
 ishug
 ishuv
 ishva
 ishve
 isisa
 iskal
 iskjo
 iskop
 islan
 ismos
 ismul
 ismyr
 isnor
 isrge
 isrgy
 isrhh
 isrkn
 isrkv
 issbt
 issdn
 issdv
 issel
 issfa
 isshf
 isskf
 isskg
 issko
 isskr
 issnf
 issog
 issol
 issss
 isstr
 issty
 issvg
 istal
 isthg
 istjo
 isvem
 isver
 isvop
 it21
 it23
 it25
 it32
 it34
 it36
 it42
 it45
 it52
 it55
 it57
 it62
 it65
 it67
 it72
 it75
 it77
 it78
 it82
 it88
 itag
 ital
 itan
 itap
 itaq
 itar
 itat
 itav
 itba
 itbg
 itbi
 itbl
 itbn
 itbo
 itbr
 itbs
 itbt
 itbz
 itca
 itcb
 itce
 itch
 itcl
 itcn
 itco
 itcr
 itcs
 itct
 itcz
 iten
 itfc
 itfe
 itfg
 itfi
 itfm
 itfr
 itge
 itgo
 itgr
 itim
 itis
 itkr
 itlc
 itle
 itli
 itlo
 itlt
 itlu
 itmb
 itmc
 itme
 itmi
 itmn
 itmo
 itms
 itmt
 itna
 itno
 itnu
 itor
 itpa
 itpc
 itpd
 itpe
 itpg
 itpi
 itpn
 itpo
 itpr
 itpt
 itpu
 itpv
 itpz
 itra
 itrc
 itre
 itrg
 itri
 itrm
 itrn
 itro
 itsa
 itsi
 itso
 itsp
 itsr
 itss
 itsu
 itsv
 itta
 itte
 ittn
 itto
 ittp
 ittr
 itts
 ittv
 itud
 itva
 itvb
 itvc
 itve
 itvi
 itvr
 itvt
 itvv
 jm01
 jm02
 jm03
 jm04
 jm05
 jm06
 jm07
 jm08
 jm09
 jm10
 jm11
 jm12
 jm13
 jm14
 joaj
 joam
 joaq
 joat
 joaz
 joba
 joir
 joja
 joka
 joma
 jomd
 jomn
 jp01
 jp02
 jp03
 jp04
 jp05
 jp06
 jp07
 jp08
 jp09
 jp10
 jp11
 jp12
 jp13
 jp14
 jp15
 jp16
 jp17
 jp18
 jp19
 jp20
 jp21
 jp22
 jp23
 jp24
 jp25
 jp26
 jp27
 jp28
 jp29
 jp30
 jp31
 jp32
 jp33
 jp34
 jp35
 jp36
 jp37
 jp38
 jp39
 jp40
 jp41
 jp42
 jp43
 jp44
 jp45
 jp46
 jp47
 ke01
 ke02
 ke03
 ke04
 ke05
 ke06
 ke07
 ke08
 ke09
 ke10
 ke11
 ke12
 ke13
 ke14
 ke15
 ke16
 ke17
 ke18
 ke19
 ke20
 ke21
 ke22
 ke23
 ke24
 ke25
 ke26
 ke27
 ke28
 ke29
 ke30
 ke31
 ke32
 ke33
 ke34
 ke35
 ke36
 ke37
 ke38
 ke39
 ke40
 ke41
 ke42
 ke43
 ke44
 ke45
 ke46
 ke47
 kgb
 kgc
 kggb
 kggo
 kgj
 kgn
 kgo
 kgt
 kgy
 kh1
 kh10
 kh11
 kh12
 kh13
 kh14
 kh15
 kh16
 kh17
 kh18
 kh19
 kh2
 kh20
 kh21
 kh22
 kh23
 kh24
 kh25
 kh3
 kh4
 kh5
 kh6
 kh7
 kh8
 kh9
 kig
 kil
 kip
 kma
 kmg
 kmm
 kn01
 kn02
 kn03
 kn04
 kn05
 kn06
 kn07
 kn08
 kn09
 kn10
 kn11
 kn12
 kn13
 kn15
 knk
 knn
 kp01
 kp02
 kp03
 kp04
 kp05
 kp06
 kp07
 kp08
 kp09
 kp10
 kp13
 kp14
 kp15
 kr11
 kr26
 kr27
 kr28
 kr29
 kr30
 kr31
 kr41
 kr42
 kr43
 kr44
 kr45
 kr46
 kr47
 kr48
 kr49
 kr50
 kwah
 kwfa
 kwha
 kwja
 kwku
 kwmu
 kz10
 kz11
 kz15
 kz19
 kz23
 kz27
 kz31
 kz33
 kz35
 kz39
 kz43
 kz47
 kz55
 kz59
 kz61
 kz62
 kz63
 kz71
 kz75
 kz79
 laat
 labk
 labl
 lach
 laho
 lakh
 lalm
 lalp
 laou
 laph
 lasl
 lasv
 lavi
 lavt
 laxa
 laxe
 laxi
 laxs
 lbak
 lbas
 lbba
 lbbh
 lbbi
 lbja
 lbjl
 lbna
 lc01
 lc02
 lc03
 lc05
 lc06
 lc07
 lc08
 lc10
 lc11
 lc12
 li01
 li02
 li03
 li04
 li05
 li06
 li07
 li08
 li09
 li10
 li11
 lk1
 lk11
 lk12
 lk13
 lk2
 lk21
 lk22
 lk23
 lk3
 lk31
 lk32
 lk33
 lk4
 lk41
 lk42
 lk43
 lk44
 lk45
 lk5
 lk51
 lk52
 lk53
 lk6
 lk61
 lk62
 lk7
 lk71
 lk72
 lk8
 lk81
 lk82
 lk9
 lk91
 lk92
 lrbg
 lrbm
 lrcm
 lrgb
 lrgg
 lrgk
 lrgp
 lrlo
 lrmg
 lrmo
 lrmy
 lrni
 lrrg
 lrri
 lrsi
 lsa
 lsb
 lsc
 lsd
 lse
 lsf
 lsg
 lsh
 lsj
 lsk
 lt01
 lt02
 lt03
 lt04
 lt05
 lt06
 lt07
 lt08
 lt09
 lt10
 lt11
 lt12
 lt13
 lt14
 lt15
 lt16
 lt17
 lt18
 lt19
 lt20
 lt21
 lt22
 lt23
 lt24
 lt25
 lt26
 lt27
 lt28
 lt29
 lt30
 lt31
 lt32
 lt33
 lt34
 lt35
 lt36
 lt37
 lt38
 lt39
 lt40
 lt41
 lt42
 lt43
 lt44
 lt45
 lt46
 lt47
 lt48
 lt49
 lt50
 lt51
 lt52
 lt53
 lt54
 lt55
 lt56
 lt57
 lt58
 lt59
 lt60
 ltal
 ltkl
 ltku
 ltmr
 ltpn
 ltsa
 ltta
 ltte
 ltut
 ltvl
 luca
 lucl
 ludi
 luec
 lues
 lugr
 lulu
 lume
 lurd
 lurm
 luvd
 luwi
 lv002
 lv007
 lv011
 lv015
 lv016
 lv022
 lv026
 lv033
 lv041
 lv042
 lv047
 lv050
 lv052
 lv054
 lv056
 lv058
 lv059
 lv062
 lv067
 lv068
 lv073
 lv077
 lv080
 lv087
 lv088
 lv089
 lv091
 lv094
 lv097
 lv099
 lv101
 lv102
 lv106
 lv111
 lv112
 lv113
 lvdgv
 lvjel
 lvjur
 lvlpx
 lvrez
 lvrix
 lvven
 lyba
 lybu
 lydr
 lygt
 lyja
 lyjg
 lyji
 lyju
 lykf
 lymb
 lymi
 lymj
 lymq
 lynl
 lynq
 lysb
 lysr
 lytb
 lywa
 lywd
 lyws
 lyza
 ma01
 ma02
 ma03
 ma04
 ma05
 ma06
 ma07
 ma08
 ma09
 ma10
 ma11
 ma12
 maagd
 maaou
 maasz
 maazi
 mabem
 maber
 mabes
 mabod
 mabom
 mabrr
 macas
 mache
 machi
 macht
 madri
 maerr
 maesi
 maesm
 mafah
 mafes
 mafig
 mafqh
 mague
 maguf
 mahaj
 mahao
 mahoc
 maifr
 maine
 majdi
 majra
 maken
 makes
 makhe
 makhn
 makho
 malaa
 malar
 mamar
 mamdf
 mamed
 mamek
 mamid
 mamoh
 mamou
 manad
 manou
 maoua
 maoud
 maouj
 maouz
 marab
 mareh
 masaf
 masal
 masef
 maset
 masib
 masif
 masik
 masil
 maskh
 mataf
 matai
 matao
 matar
 matat
 mataz
 matet
 matin
 matiz
 matng
 matnt
 mayus
 mazag
 mccl
 mcco
 mcfo
 mcga
 mcje
 mcla
 mcma
 mcmc
 mcmg
 mcmo
 mcmu
 mcph
 mcsd
 mcso
 mcsp
 mcsr
 mcvr
 mdan
 mdba
 mdbd
 mdbr
 mdbs
 mdca
 mdcl
 mdcm
 mdcr
 mdcs
 mdct
 mdcu
 mddo
 mddr
 mddu
 mded
 mdfa
 mdfl
 mdga
 mdgl
 mdhi
 mdia
 mdle
 mdni
 mdoc
 mdor
 mdre
 mdri
 mdsd
 mdsi
 mdsn
 mdso
 mdst
 mdsv
 mdta
 mdte
 mdun
 me01
 me02
 me03
 me04
 me05
 me06
 me07
 me08
 me09
 me10
 me11
 me12
 me13
 me14
 me15
 me16
 me17
 me18
 me19
 me20
 me21
 me22
 me23
 me24
 me25
 mga
 mgd
 mgf
 mgm
 mgt
 mgu
 mhalk
 mhall
 mharn
 mhaur
 mhebo
 mheni
 mhjab
 mhjal
 mhkil
 mhkwa
 mhl
 mhlae
 mhlib
 mhlik
 mhmaj
 mhmal
 mhmej
 mhmil
 mhnmk
 mhnmu
 mhron
 mht
 mhuja
 mhuti
 mhwth
 mhwtj
 mk101
 mk102
 mk103
 mk104
 mk105
 mk106
 mk107
 mk108
 mk109
 mk201
 mk202
 mk203
 mk204
 mk205
 mk206
 mk207
 mk208
 mk209
 mk210
 mk211
 mk301
 mk303
 mk304
 mk307
 mk308
 mk310
 mk311
 mk312
 mk313
 mk401
 mk402
 mk403
 mk404
 mk405
 mk406
 mk407
 mk408
 mk409
 mk410
 mk501
 mk502
 mk503
 mk504
 mk505
 mk506
 mk507
 mk508
 mk509
 mk601
 mk602
 mk603
 mk604
 mk605
 mk606
 mk607
 mk608
 mk609
 mk701
 mk702
 mk703
 mk704
 mk705
 mk706
 mk801
 mk802
 mk803
 mk804
 mk805
 mk806
 mk807
 mk808
 mk809
 mk810
 mk811
 mk812
 mk813
 mk814
 mk815
 mk816
 mk817
 ml1
 ml10
 ml2
 ml3
 ml4
 ml5
 ml6
 ml7
 ml8
 ml9
 mlbko
 mm01
 mm02
 mm03
 mm04
 mm05
 mm06
 mm07
 mm11
 mm12
 mm13
 mm14
 mm15
 mm16
 mm17
 mm18
 mn035
 mn037
 mn039
 mn041
 mn043
 mn046
 mn047
 mn049
 mn051
 mn053
 mn055
 mn057
 mn059
 mn061
 mn063
 mn064
 mn065
 mn067
 mn069
 mn071
 mn073
 mn1
 mr01
 mr02
 mr03
 mr04
 mr05
 mr06
 mr07
 mr08
 mr09
 mr10
 mr11
 mr12
 mr13
 mr14
 mr15
 mt01
 mt02
 mt03
 mt04
 mt05
 mt06
 mt07
 mt08
 mt09
 mt10
 mt11
 mt12
 mt13
 mt14
 mt15
 mt16
 mt17
 mt18
 mt19
 mt20
 mt21
 mt22
 mt23
 mt24
 mt25
 mt26
 mt27
 mt28
 mt29
 mt30
 mt31
 mt32
 mt33
 mt34
 mt35
 mt36
 mt37
 mt38
 mt39
 mt40
 mt41
 mt42
 mt43
 mt44
 mt45
 mt46
 mt47
 mt48
 mt49
 mt50
 mt51
 mt52
 mt53
 mt54
 mt55
 mt56
 mt57
 mt58
 mt59
 mt60
 mt61
 mt62
 mt63
 mt64
 mt65
 mt66
 mt67
 mt68
 muag
 mubl
 mucc
 mufl
 mugp
 mumo
 mupa
 mupl
 mupw
 muro
 murr
 musa
 mv00
 mv01
 mv02
 mv03
 mv04
 mv05
 mv07
 mv08
 mv12
 mv13
 mv14
 mv17
 mv20
 mv23
 mv24
 mv25
 mv26
 mv27
 mv28
 mv29
 mvmle
 mwba
 mwbl
 mwc
 mwck
 mwcr
 mwct
 mwde
 mwdo
 mwkr
 mwks
 mwli
 mwlk
 mwmc
 mwmg
 mwmh
 mwmu
 mwmw
 mwmz
 mwn
 mwnb
 mwne
 mwni
 mwnk
 mwns
 mwnu
 mwph
 mwru
 mws
 mwsa
 mwth
 mwzo
 mxagu
 mxbcn
 mxbcs
 mxcam
 mxchh
 mxchp
 mxcmx
 mxcoa
 mxcol
 mxdur
 mxgro
 mxgua
 mxhid
 mxjal
 mxmex
 mxmic
 mxmor
 mxnay
 mxnle
 mxoax
 mxpue
 mxque
 mxroo
 mxsin
 mxslp
 mxson
 mxtab
 mxtam
 mxtla
 mxver
 mxyuc
 mxzac
 my01
 my02
 my03
 my04
 my05
 my06
 my07
 my08
 my09
 my10
 my11
 my12
 my13
 my14
 my15
 my16
 mza
 mzb
 mzg
 mzi
 mzl
 mzmpm
 mzn
 mzp
 mzq
 mzs
 mzt
 naca
 naer
 naha
 naka
 nake
 nakh
 naku
 nakw
 naod
 naoh
 naon
 naos
 naot
 naow
 ne1
 ne2
 ne3
 ne4
 ne5
 ne6
 ne7
 ne8
 ngab
 ngad
 ngak
 ngan
 ngba
 ngbe
 ngbo
 ngby
 ngcr
 ngde
 ngeb
 nged
 ngek
 ngen
 ngfc
 nggo
 ngim
 ngji
 ngkd
 ngke
 ngkn
 ngko
 ngkt
 ngkw
 ngla
 ngna
 ngni
 ngog
 ngon
 ngos
 ngoy
 ngpl
 ngri
 ngso
 ngta
 ngyo
 ngza
 nian
 nias
 nibo
 nica
 nici
 nico
 nies
 nigr
 niji
 nile
 nimd
 nimn
 nims
 nimt
 nins
 niri
 nisj
 nlbq1
 nlbq2
 nlbq3
 nldr
 nlfl
 nlfr
 nlge
 nlgr
 nlli
 nlnb
 nlnh
 nlov
 nlut
 nlze
 nlzh
 no03
 no11
 no15
 no18
 no21
 no22
 no30
 no34
 no38
 no42
 no46
 no50
 no54
 npp1
 npp2
 npp3
 npp4
 npp5
 npp6
 npp7
 nr01
 nr02
 nr03
 nr04
 nr05
 nr06
 nr07
 nr08
 nr09
 nr10
 nr11
 nr12
 nr13
 nr14
 nzauk
 nzbop
 nzcan
 nzcit
 nzgis
 nzhkb
 nzmbh
 nzmwt
 nznsn
 nzntl
 nzota
 nzstl
 nztas
 nztki
 nzwgn
 nzwko
 nzwtc
 ombj
 ombs
 ombu
 omda
 omma
 ommu
 omsj
 omss
 omwu
 omza
 omzu
 pa1
 pa10
 pa2
 pa3
 pa4
 pa5
 pa6
 pa7
 pa8
 pa9
 paem
 paky
 panb
 pant
 peama
 peanc
 peapu
 peare
 peaya
 pecaj
 pecal
 pecus
 pehuc
 pehuv
 peica
 pejun
 pelal
 pelam
 pelim
 pelma
 pelor
 pemdd
 pemoq
 pepas
 pepiu
 pepun
 pesam
 petac
 petum
 peuca
 pgcpk
 pgcpm
 pgebr
 pgehg
 pgepw
 pgesw
 pggpk
 pghla
 pgjwk
 pgmba
 pgmpl
 pgmpm
 pgmrl
 pgncd
 pgnik
 pgnpp
 pgnsb
 pgsan
 pgshm
 pgwbk
 pgwhm
 pgwpd
 ph00
 ph01
 ph02
 ph03
 ph05
 ph06
 ph07
 ph08
 ph09
 ph10
 ph11
 ph12
 ph13
 ph14
 ph15
 ph40
 ph41
 phabr
 phagn
 phags
 phakl
 phalb
 phant
 phapa
 phaur
 phban
 phbas
 phben
 phbil
 phboh
 phbtg
 phbtn
 phbuk
 phbul
 phcag
 phcam
 phcan
 phcap
 phcas
 phcat
 phcav
 phceb
 phcom
 phdao
 phdas
 phdav
 phdin
 phdvo
 pheas
 phgui
 phifu
 phili
 philn
 phils
 phisa
 phkal
 phlag
 phlan
 phlas
 phley
 phlun
 phmad
 phmas
 phmdc
 phmdr
 phmgn
 phmgs
 phmou
 phmsc
 phmsr
 phnco
 phnec
 phner
 phnsa
 phnue
 phnuv
 phpam
 phpan
 phplw
 phque
 phqui
 phriz
 phrom
 phsar
 phsco
 phsig
 phsle
 phslu
 phsor
 phsuk
 phsun
 phsur
 phtar
 phtaw
 phwsa
 phzan
 phzas
 phzmb
 phzsi
 pkba
 pkgb
 pkis
 pkjk
 pkkp
 pkpb
 pksd
 pl02
 pl04
 pl06
 pl08
 pl10
 pl12
 pl14
 pl16
 pl18
 pl20
 pl22
 pl24
 pl26
 pl28
 pl30
 pl32
 psbth
 psdeb
 psgza
 pshbn
 psjem
 psjen
 psjrh
 pskys
 psnbs
 psngz
 psqqa
 psrbh
 psrfh
 psslt
 pstbs
 pstkm
 pt01
 pt02
 pt03
 pt04
 pt05
 pt06
 pt07
 pt08
 pt09
 pt10
 pt11
 pt12
 pt13
 pt14
 pt15
 pt16
 pt17
 pt18
 pt20
 pt30
 pw002
 pw004
 pw010
 pw050
 pw100
 pw150
 pw212
 pw214
 pw218
 pw222
 pw224
 pw226
 pw227
 pw228
 pw350
 pw370
 py1
 py10
 py11
 py12
 py13
 py14
 py15
 py16
 py19
 py2
 py3
 py4
 py5
 py6
 py7
 py8
 py9
 pyasu
 qada
 qakh
 qams
 qara
 qash
 qaus
 qawa
 qaza
 roab
 roag
 roar
 rob
 robc
 robh
 robn
 robr
 robt
 robv
 robz
 rocj
 rocl
 rocs
 roct
 rocv
 rodb
 rodj
 rogj
 rogl
 rogr
 rohd
 rohr
 roif
 roil
 rois
 romh
 romm
 roms
 ront
 root
 roph
 rosb
 rosj
 rosm
 rosv
 rotl
 rotm
 rotr
 rovl
 rovn
 rovs
 rs00
 rs01
 rs02
 rs03
 rs04
 rs05
 rs06
 rs07
 rs08
 rs09
 rs10
 rs11
 rs12
 rs13
 rs14
 rs15
 rs16
 rs17
 rs18
 rs19
 rs20
 rs21
 rs22
 rs23
 rs24
 rs25
 rs26
 rs27
 rs28
 rs29
 rskm
 rsvo
 ruad
 rual
 rualt
 ruamu
 ruark
 ruast
 ruba
 rubel
 rubry
 rubu
 ruce
 ruche
 ruchu
 rucu
 ruda
 ruin
 ruirk
 ruiva
 rukam
 rukb
 rukc
 rukda
 rukem
 rukgd
 rukgn
 rukha
 rukhm
 rukir
 rukk
 rukl
 ruklu
 ruko
 rukos
 rukr
 rukrs
 rukya
 rulen
 rulip
 rumag
 rume
 rumo
 rumos
 rumow
 rumur
 runen
 rungr
 runiz
 runvs
 ruoms
 ruore
 ruorl
 ruper
 rupnz
 rupri
 rupsk
 ruros
 rurya
 rusa
 rusak
 rusam
 rusar
 ruse
 rusmo
 ruspe
 rusta
 rusve
 ruta
 rutam
 rutom
 rutul
 rutve
 ruty
 rutyu
 ruud
 ruuly
 ruvgg
 ruvla
 ruvlg
 ruvor
 ruyan
 ruyar
 ruyev
 ruzab
 rw01
 rw02
 rw03
 rw04
 rw05
 sa01
 sa02
 sa03
 sa04
 sa05
 sa06
 sa07
 sa08
 sa09
 sa10
 sa11
 sa12
 sa14
 sbce
 sbch
 sbct
 sbgu
 sbis
 sbmk
 sbml
 sbrb
 sbte
 sbwe
 sc01
 sc02
 sc03
 sc04
 sc05
 sc06
 sc07
 sc08
 sc09
 sc10
 sc11
 sc12
 sc13
 sc14
 sc15
 sc16
 sc17
 sc18
 sc19
 sc20
 sc21
 sc22
 sc23
 sc24
 sc25
 sc26
 sc27
 sddc
 sdde
 sddn
 sdds
 sddw
 sdgd
 sdgk
 sdgz
 sdka
 sdkh
 sdkn
 sdks
 sdnb
 sdno
 sdnr
 sdnw
 sdrs
 sdsi
 seab
 seac
 sebd
 sec
 sed
 see
 sef
 seg
 seh
 sei
 sek
 sem
 sen
 seo
 ses
 set
 seu
 sew
 sex
 sey
 sez
 sg01
 sg02
 sg03
 sg04
 sg05
 shac
 shhl
 si001
 si002
 si003
 si004
 si005
 si006
 si007
 si008
 si009
 si010
 si011
 si012
 si013
 si014
 si015
 si016
 si017
 si018
 si019
 si020
 si021
 si022
 si023
 si024
 si025
 si026
 si027
 si028
 si029
 si030
 si031
 si032
 si033
 si034
 si035
 si036
 si037
 si038
 si039
 si040
 si041
 si042
 si043
 si044
 si045
 si046
 si047
 si048
 si049
 si050
 si051
 si052
 si053
 si054
 si055
 si056
 si057
 si058
 si059
 si060
 si061
 si062
 si063
 si064
 si065
 si066
 si067
 si068
 si069
 si070
 si071
 si072
 si073
 si074
 si075
 si076
 si077
 si078
 si079
 si080
 si081
 si082
 si083
 si084
 si085
 si086
 si087
 si088
 si089
 si090
 si091
 si092
 si093
 si094
 si095
 si096
 si097
 si098
 si099
 si100
 si101
 si102
 si103
 si104
 si105
 si106
 si107
 si108
 si109
 si110
 si111
 si112
 si113
 si114
 si115
 si116
 si117
 si118
 si119
 si120
 si121
 si122
 si123
 si124
 si125
 si126
 si127
 si128
 si129
 si130
 si131
 si132
 si133
 si134
 si135
 si136
 si137
 si138
 si139
 si140
 si141
 si142
 si143
 si144
 si146
 si147
 si148
 si149
 si150
 si151
 si152
 si153
 si154
 si155
 si156
 si157
 si158
 si159
 si160
 si161
 si162
 si163
 si164
 si165
 si166
 si167
 si168
 si169
 si170
 si171
 si172
 si173
 si174
 si175
 si176
 si177
 si178
 si179
 si180
 si181
 si182
 si183
 si184
 si185
 si186
 si187
 si188
 si189
 si190
 si191
 si192
 si193
 si194
 si195
 si196
 si197
 si198
 si199
 si200
 si201
 si202
 si203
 si204
 si205
 si206
 si207
 si208
 si209
 si210
 si211
 si212
 si213
 skbc
 skbl
 skki
 skni
 skpv
 skta
 sktc
 skzi
 sle
 sln
 slnw
 sls
 slw
 sm01
 sm02
 sm03
 sm04
 sm05
 sm06
 sm07
 sm08
 sm09
 sndb
 sndk
 snfk
 snka
 snkd
 snke
 snkl
 snlg
 snmt
 snse
 snsl
 sntc
 snth
 snzg
 soaw
 sobk
 sobn
 sobr
 soby
 soga
 soge
 sohi
 sojd
 sojh
 somu
 sonu
 sosa
 sosd
 sosh
 soso
 soto
 sowo
 srbr
 srcm
 srcr
 srma
 srni
 srpm
 srpr
 srsa
 srsi
 srwa
 ssbn
 ssbw
 ssec
 ssee
 ssew
 ssjg
 sslk
 ssnu
 ssuy
 sswr
 st01
 st02
 st03
 st04
 st05
 st06
 stp
 svah
 svca
 svch
 svcu
 svli
 svmo
 svpa
 svsa
 svsm
 svso
 svss
 svsv
 svun
 svus
 sydi
 sydr
 sydy
 syha
 syhi
 syhl
 syhm
 syid
 syla
 syqu
 syra
 syrd
 sysu
 syta
 szhh
 szlu
 szma
 szsh
 tdba
 tdbg
 tdbo
 tdcb
 tdee
 tdeo
 tdgr
 tdhl
 tdka
 tdlc
 tdlo
 tdlr
 tdma
 tdmc
 tdme
 tdmo
 tdnd
 tdod
 tdsa
 tdsi
 tdta
 tdti
 tdwf
 tgc
 tgk
 tgm
 tgp
 tgs
 th10
 th11
 th12
 th13
 th14
 th15
 th16
 th17
 th18
 th19
 th20
 th21
 th22
 th23
 th24
 th25
 th26
 th27
 th30
 th31
 th32
 th33
 th34
 th35
 th36
 th37
 th38
 th39
 th40
 th41
 th42
 th43
 th44
 th45
 th46
 th47
 th48
 th49
 th50
 th51
 th52
 th53
 th54
 th55
 th56
 th57
 th58
 th60
 th61
 th62
 th63
 th64
 th65
 th66
 th67
 th70
 th71
 th72
 th73
 th74
 th75
 th76
 th77
 th80
 th81
 th82
 th83
 th84
 th85
 th86
 th90
 th91
 th92
 th93
 th94
 th95
 th96
 ths
 tjdu
 tjgb
 tjkt
 tjra
 tjsu
 tlal
 tlan
 tlba
 tlbo
 tlco
 tldi
 tler
 tlla
 tlli
 tlmf
 tlmt
 tloe
 tlvi
 tma
 tmb
 tmd
 tml
 tmm
 tms
 tn11
 tn12
 tn13
 tn14
 tn21
 tn22
 tn23
 tn31
 tn32
 tn33
 tn34
 tn41
 tn42
 tn43
 tn51
 tn52
 tn53
 tn61
 tn71
 tn72
 tn73
 tn81
 tn82
 tn83
 to01
 to02
 to03
 to04
 to05
 tr01
 tr02
 tr03
 tr04
 tr05
 tr06
 tr07
 tr08
 tr09
 tr10
 tr11
 tr12
 tr13
 tr14
 tr15
 tr16
 tr17
 tr18
 tr19
 tr20
 tr21
 tr22
 tr23
 tr24
 tr25
 tr26
 tr27
 tr28
 tr29
 tr30
 tr31
 tr32
 tr33
 tr34
 tr35
 tr36
 tr37
 tr38
 tr39
 tr40
 tr41
 tr42
 tr43
 tr44
 tr45
 tr46
 tr47
 tr48
 tr49
 tr50
 tr51
 tr52
 tr53
 tr54
 tr55
 tr56
 tr57
 tr58
 tr59
 tr60
 tr61
 tr62
 tr63
 tr64
 tr65
 tr66
 tr67
 tr68
 tr69
 tr70
 tr71
 tr72
 tr73
 tr74
 tr75
 tr76
 tr77
 tr78
 tr79
 tr80
 tr81
 ttari
 ttcha
 ttctt
 ttdmn
 ttmrc
 ttped
 ttpos
 ttprt
 ttptf
 ttsfo
 ttsge
 ttsip
 ttsjl
 tttob
 tttup
 tvfun
 tvnit
 tvnkf
 tvnkl
 tvnma
 tvnmg
 tvnui
 tvvai
 twcha
 twcyi
 twcyq
 twhsq
 twhsz
 twhua
 twila
 twkee
 twkhh
 twkin
 twlie
 twmia
 twnan
 twnwt
 twpen
 twpif
 twtao
 twtnn
 twtpe
 twttt
 twtxg
 twyun
 tz01
 tz02
 tz03
 tz04
 tz05
 tz06
 tz07
 tz08
 tz09
 tz10
 tz11
 tz12
 tz13
 tz14
 tz15
 tz16
 tz17
 tz18
 tz19
 tz20
 tz21
 tz22
 tz23
 tz24
 tz25
 tz26
 tz27
 tz28
 tz29
 tz30
 tz31
 ua05
 ua07
 ua09
 ua12
 ua14
 ua18
 ua21
 ua23
 ua26
 ua30
 ua32
 ua35
 ua40
 ua43
 ua46
 ua48
 ua51
 ua53
 ua56
 ua59
 ua61
 ua63
 ua65
 ua68
 ua71
 ua74
 ua77
 ug101
 ug102
 ug103
 ug104
 ug105
 ug106
 ug107
 ug108
 ug109
 ug110
 ug111
 ug112
 ug113
 ug114
 ug115
 ug116
 ug117
 ug118
 ug119
 ug120
 ug121
 ug122
 ug123
 ug124
 ug125
 ug126
 ug201
 ug202
 ug203
 ug204
 ug205
 ug206
 ug207
 ug208
 ug209
 ug210
 ug211
 ug212
 ug213
 ug214
 ug215
 ug216
 ug217
 ug218
 ug219
 ug220
 ug221
 ug222
 ug223
 ug224
 ug225
 ug226
 ug227
 ug228
 ug229
 ug230
 ug231
 ug232
 ug233
 ug234
 ug235
 ug236
 ug237
 ug301
 ug302
 ug303
 ug304
 ug305
 ug306
 ug307
 ug308
 ug309
 ug310
 ug311
 ug312
 ug313
 ug314
 ug315
 ug316
 ug317
 ug318
 ug319
 ug320
 ug321
 ug322
 ug323
 ug324
 ug325
 ug326
 ug327
 ug328
 ug329
 ug330
 ug331
 ug332
 ug333
 ug334
 ug335
 ug336
 ug337
 ug401
 ug402
 ug403
 ug404
 ug405
 ug406
 ug407
 ug408
 ug409
 ug410
 ug411
 ug412
 ug413
 ug414
 ug415
 ug416
 ug417
 ug418
 ug419
 ug420
 ug421
 ug422
 ug423
 ug424
 ug425
 ug426
 ug427
 ug428
 ug429
 ug430
 ug431
 ug432
 ug433
 ug434
 ug435
 ugc
 uge
 ugn
 ugw
 um67
 um71
 um76
 um79
 um81
 um84
 um86
 um89
 um95
 usak
 usal
 usar
 usaz
 usca
 usco
 usct
 usdc
 usde
 usfl
 usga
 ushi
 usia
 usid
 usil
 usin
 usks
 usky
 usla
 usma
 usmd
 usme
 usmi
 usmn
 usmo
 usms
 usmt
 usnc
 usnd
 usne
 usnh
 usnj
 usnm
 usnv
 usny
 usoh
 usok
 usor
 uspa
 usri
 ussc
 ussd
 ustn
 ustx
 usut
 usva
 usvt
 uswa
 uswi
 uswv
 uswy
 uyar
 uyca
 uycl
 uyco
 uydu
 uyfd
 uyfs
 uyla
 uyma
 uymo
 uypa
 uyrn
 uyro
 uyrv
 uysa
 uysj
 uyso
 uyta
 uytt
 uzan
 uzbu
 uzfa
 uzji
 uzng
 uznw
 uzqa
 uzqr
 uzsa
 uzsi
 uzsu
 uztk
 uzto
 uzxo
 vc01
 vc02
 vc03
 vc04
 vc05
 vc06
 vea
 veb
 vec
 ved
 vee
 vef
 veg
 veh
 vei
 vej
 vek
 vel
 vem
 ven
 veo
 vep
 ver
 ves
 vet
 veu
 vev
 vew
 vex
 vey
 vez
 vn01
 vn02
 vn03
 vn04
 vn05
 vn06
 vn07
 vn09
 vn13
 vn14
 vn18
 vn20
 vn21
 vn22
 vn23
 vn24
 vn25
 vn26
 vn27
 vn28
 vn29
 vn30
 vn31
 vn32
 vn33
 vn34
 vn35
 vn36
 vn37
 vn39
 vn40
 vn41
 vn43
 vn44
 vn45
 vn46
 vn47
 vn49
 vn50
 vn51
 vn52
 vn53
 vn54
 vn55
 vn56
 vn57
 vn58
 vn59
 vn61
 vn63
 vn66
 vn67
 vn68
 vn69
 vn70
 vn71
 vn72
 vn73
 vnct
 vndn
 vnhn
 vnhp
 vnsg
 vumap
 vupam
 vusam
 vusee
 vutae
 vutob
 wfal
 wfsg
 wfuv
 wsaa
 wsal
 wsat
 wsfa
 wsge
 wsgi
 wspa
 wssa
 wstu
 wsvf
 wsvs
 yeab
 yead
 yeam
 yeba
 yeda
 yedh
 yehd
 yehj
 yehu
 yeib
 yeja
 yela
 yema
 yemr
 yemw
 yera
 yesa
 yesd
 yesh
 yesn
 yesu
 yeta
 zaec
 zafs
 zagp
 zakzn
 zalp
 zamp
 zanc
 zanw
 zawc
 zm01
 zm02
 zm03
 zm04
 zm05
 zm06
 zm07
 zm08
 zm09
 zm10
 zwbu
 zwha
 zwma
 zwmc
 zwme
 zwmi
 zwmn
 zwms
 zwmv
 zwmw
 aczzzz
 adzzzz
 aezzzz
 afzzzz
 agzzzz
 aizzzz
 alzzzz
 amzzzz
 aozzzz
 aqzzzz
 arzzzz
 aszzzz
 atzzzz
 auzzzz
 awzzzz
 axzzzz
 azzzzz
 bazzzz
 bbzzzz
 bdzzzz
 bezzzz
 bfzzzz
 bgzzzz
 bhzzzz
 bizzzz
 bjzzzz
 blzzzz
 bmzzzz
 bnzzzz
 bozzzz
 bqzzzz
 brzzzz
 bszzzz
 btzzzz
 bvzzzz
 bwzzzz
 byzzzz
 bzzzzz
 cazzzz
 cczzzz
 cdzzzz
 cfzzzz
 cgzzzz
 chzzzz
 cizzzz
 ckzzzz
 clzzzz
 cmzzzz
 cnzzzz
 cozzzz
 cpzzzz
 cqzzzz
 crzzzz
 cuzzzz
 cvzzzz
 cwzzzz
 cxzzzz
 cyzzzz
 czzzzz
 dezzzz
 dgzzzz
 djzzzz
 dkzzzz
 dmzzzz
 dozzzz
 dzzzzz
 eazzzz
 eczzzz
 eezzzz
 egzzzz
 ehzzzz
 erzzzz
 eszzzz
 etzzzz
 fizzzz
 fjzzzz
 fkzzzz
 fmzzzz
 fozzzz
 frzzzz
 gazzzz
 gbzzzz
 gdzzzz
 gezzzz
 gfzzzz
 ggzzzz
 ghzzzz
 gizzzz
 glzzzz
 gmzzzz
 gnzzzz
 gpzzzz
 gqzzzz
 grzzzz
 gszzzz
 gtzzzz
 guzzzz
 gwzzzz
 gyzzzz
 hkzzzz
 hmzzzz
 hnzzzz
 hrzzzz
 htzzzz
 huzzzz
 iczzzz
 idzzzz
 iezzzz
 ilzzzz
 imzzzz
 inzzzz
 iozzzz
 iqzzzz
 irzzzz
 iszzzz
 itzzzz
 jezzzz
 jmzzzz
 jozzzz
 jpzzzz
 kezzzz
 kgzzzz
 khzzzz
 kizzzz
 kmzzzz
 knzzzz
 kpzzzz
 krzzzz
 kwzzzz
 kyzzzz
 kzzzzz
 lazzzz
 lbzzzz
 lczzzz
 lizzzz
 lkzzzz
 lrzzzz
 lszzzz
 ltzzzz
 luzzzz
 lvzzzz
 lyzzzz
 mazzzz
 mczzzz
 mdzzzz
 mezzzz
 mfzzzz
 mgzzzz
 mhzzzz
 mkzzzz
 mlzzzz
 mmzzzz
 mnzzzz
 mozzzz
 mpzzzz
 mqzzzz
 mrzzzz
 mszzzz
 mtzzzz
 muzzzz
 mvzzzz
 mwzzzz
 mxzzzz
 myzzzz
 mzzzzz
 nazzzz
 nczzzz
 nezzzz
 nfzzzz
 ngzzzz
 nizzzz
 nlzzzz
 nozzzz
 npzzzz
 nrzzzz
 nuzzzz
 nzzzzz
 omzzzz
 pazzzz
 pezzzz
 pfzzzz
 pgzzzz
 phzzzz
 pkzzzz
 plzzzz
 pmzzzz
 pnzzzz
 przzzz
 pszzzz
 ptzzzz
 pwzzzz
 pyzzzz
 qazzzz
 rezzzz
 rozzzz
 rszzzz
 ruzzzz
 rwzzzz
 sazzzz
 sbzzzz
 sczzzz
 sdzzzz
 sezzzz
 sgzzzz
 shzzzz
 sizzzz
 sjzzzz
 skzzzz
 slzzzz
 smzzzz
 snzzzz
 sozzzz
 srzzzz
 sszzzz
 stzzzz
 svzzzz
 sxzzzz
 syzzzz
 szzzzz
 tazzzz
 tczzzz
 tdzzzz
 tfzzzz
 tgzzzz
 thzzzz
 tjzzzz
 tkzzzz
 tlzzzz
 tmzzzz
 tnzzzz
 tozzzz
 trzzzz
 ttzzzz
 tvzzzz
 twzzzz
 tzzzzz
 uazzzz
 ugzzzz
 umzzzz
 uszzzz
 uyzzzz
 uzzzzz
 vazzzz
 vczzzz
 vezzzz
 vgzzzz
 vizzzz
 vnzzzz
 vuzzzz
 wfzzzz
 wszzzz
 xkzzzz
 yezzzz
 ytzzzz
 zazzzz
 zmzzzz
 zwzzzz
 	)]},
);

around valid_subdivisions => sub {
    my ($orig, $self) = @_;

    my $subdivisions = $self->$orig;
    return @{$subdivisions};
};

has 'valid_units' => (
	is			=> 'ro',
	isa			=> ArrayRef,
	init_arg	=> undef,
	default	=> sub {[qw( acceleration-g-force
 acceleration-meter-per-square-second
 angle-revolution
 angle-radian
 angle-degree
 angle-arc-minute
 angle-arc-second
 area-square-kilometer
 area-hectare
 area-square-meter
 area-square-centimeter
 area-square-mile
 area-acre
 area-square-yard
 area-square-foot
 area-square-inch
 area-dunam
 concentr-karat
 concentr-milligram-ofglucose-per-deciliter
 concentr-millimole-per-liter
 concentr-item
 concentr-permillion
 concentr-percent
 concentr-permille
 concentr-permyriad
 concentr-mole
 consumption-liter-per-kilometer
 consumption-liter-per-100-kilometer
 consumption-mile-per-gallon
 consumption-mile-per-gallon-imperial
 digital-petabyte
 digital-terabyte
 digital-terabit
 digital-gigabyte
 digital-gigabit
 digital-megabyte
 digital-megabit
 digital-kilobyte
 digital-kilobit
 digital-byte
 digital-bit
 duration-century
 duration-decade
 duration-year
 duration-year-person
 duration-quarter
 duration-month
 duration-month-person
 duration-week
 duration-week-person
 duration-day
 duration-day-person
 duration-hour
 duration-minute
 duration-second
 duration-millisecond
 duration-microsecond
 duration-nanosecond
 electric-ampere
 electric-milliampere
 electric-ohm
 electric-volt
 energy-kilocalorie
 energy-calorie
 energy-foodcalorie
 energy-kilojoule
 energy-joule
 energy-kilowatt-hour
 energy-electronvolt
 energy-british-thermal-unit
 energy-therm-us
 force-pound-force
 force-newton
 force-kilowatt-hour-per-100-kilometer
 frequency-gigahertz
 frequency-megahertz
 frequency-kilohertz
 frequency-hertz
 graphics-em
 graphics-pixel
 graphics-megapixel
 graphics-pixel-per-centimeter
 graphics-pixel-per-inch
 graphics-dot-per-centimeter
 graphics-dot-per-inch
 graphics-dot
 length-earth-radius
 length-kilometer
 length-meter
 length-decimeter
 length-centimeter
 length-millimeter
 length-micrometer
 length-nanometer
 length-picometer
 length-mile
 length-yard
 length-foot
 length-inch
 length-parsec
 length-light-year
 length-astronomical-unit
 length-furlong
 length-fathom
 length-nautical-mile
 length-mile-scandinavian
 length-point
 length-solar-radius
 light-lux
 light-candela
 light-lumen
 light-solar-luminosity
 mass-tonne
 mass-kilogram
 mass-gram
 mass-milligram
 mass-microgram
 mass-ton
 mass-stone
 mass-pound
 mass-ounce
 mass-ounce-troy
 mass-carat
 mass-dalton
 mass-earth-mass
 mass-solar-mass
 mass-grain
 power-gigawatt
 power-megawatt
 power-kilowatt
 power-watt
 power-milliwatt
 power-horsepower
 pressure-millimeter-ofhg
 pressure-pound-force-per-square-inch
 pressure-inch-ofhg
 pressure-bar
 pressure-millibar
 pressure-atmosphere
 pressure-pascal
 pressure-hectopascal
 pressure-kilopascal
 pressure-megapascal
 speed-kilometer-per-hour
 speed-meter-per-second
 speed-mile-per-hour
 speed-knot
 temperature-generic
 temperature-celsius
 temperature-fahrenheit
 temperature-kelvin
 torque-pound-force-foot
 torque-newton-meter
 volume-cubic-kilometer
 volume-cubic-meter
 volume-cubic-centimeter
 volume-cubic-mile
 volume-cubic-yard
 volume-cubic-foot
 volume-cubic-inch
 volume-megaliter
 volume-hectoliter
 volume-liter
 volume-deciliter
 volume-centiliter
 volume-milliliter
 volume-pint-metric
 volume-cup-metric
 volume-acre-foot
 volume-bushel
 volume-gallon
 volume-gallon-imperial
 volume-quart
 volume-pint
 volume-cup
 volume-fluid-ounce
 volume-fluid-ounce-imperial
 volume-tablespoon
 volume-teaspoon
 volume-barrel
 volume-dessert-spoon
 volume-dessert-spoon-imperial
 volume-drop
 volume-dram
 volume-jigger
 volume-pinch
 volume-quart-imperial
 angle-steradian
 concentr-portion
 concentr-ofglucose
 concentr-katal
 duration-fortnight
 electric-coulomb
 electric-farad
 electric-henry
 electric-siemens
 energy-calorie-it
 energy-british-thermal-unit-it
 energy-becquerel
 energy-sievert
 energy-gray
 force-kilogram-force
 length-100-kilometer
 length-rod
 length-chain
 magnetic-tesla
 magnetic-weber
 mass-slug
 pressure-ofhg
 speed-beaufort
 temperature-rankine
 volume-pint-imperial
 pressure-gasoline-energy-density
 length-rin
 length-sun
 length-shaku-length
 length-shaku-cloth
 length-ken
 length-jo-jp
 length-ri-jp
 area-bu-jp
 area-se-jp
 area-cho
 volume-kosaji
 volume-osaji
 volume-cup-jp
 volume-shaku
 volume-sai
 volume-to-jp
 volume-koku
 concentr-portion-per-1e9
 mass-fun
 duration-night
 speed-light-speed
 acceleration-meter-per-second-squared
 consumption-liter-per-100kilometers
 concentr-part-per-million
 pressure-inch-hg
 pressure-pound-per-square-inch
 pressure-millimeter-of-mercury
 proportion-karat
 torque-pound-foot
 concentr-milligram-per-deciliter
 mass-metric-ton
 	)]},
);

around valid_units => sub {
    my ($orig, $self) = @_;

    my $units = $self->$orig;
    return @{$units};
};

has 'key_aliases' => (
	is			=> 'ro',
	isa			=> HashRef,
	init_arg	=> undef,
	default	=> sub { return {
		'ca' => 'calendar',
		'co' => 'collation',
		'cu' => 'currency',
		'hc' => 'hours',
		'ka' => 'colalternate',
		'kb' => 'colbackwards',
		'kc' => 'colcaselevel',
		'kf' => 'colcasefirst',
		'kh' => 'colhiraganaquaternary',
		'kk' => 'colnormalization',
		'kn' => 'colnumeric',
		'kr' => 'colreorder',
		'ks' => 'colstrength',
		'ms' => 'measure',
		'nu' => 'numbers',
		'tz' => 'timezone',
		'vt' => 'variabletop',
	}},
);

around key_aliases => sub {
    my ($orig, $self) = @_;
    my $aliases = $self->$orig;

    return %{$aliases};
};

has 'key_names' => (
	is			=> 'ro',
	isa			=> HashRef,
	init_arg	=> undef,
	lazy		=> 1,
	default	=> sub { return { reverse shift()->key_aliases }; },
);

around key_names => sub {
    my ($orig, $self) = @_;
    my $names = $self->$orig;

    return %{$names};
};

has 'valid_keys' => (
	is			=> 'ro',
	isa			=> HashRef,
	init_arg	=> undef,
	default	=> sub { return {
		ca	=> [
			'buddhist',
			'chinese',
			'coptic',
			'dangi',
			'ethioaa',
			'ethiopic-amete-alem',
			'ethiopic',
			'gregory',
			'gregorian',
			'hebrew',
			'indian',
			'islamic',
			'islamic-umalqura',
			'islamic-tbla',
			'islamic-civil',
			'islamic-rgsa',
			'iso8601',
			'japanese',
			'persian',
			'roc',
			'islamicc',
			'islamic-civil',
		],
		cf	=> [
			'standard',
			'account',
		],
		co	=> [
			'big5han',
			'compat',
			'dict',
			'dictionary',
			'direct',
			'ducet',
			'emoji',
			'eor',
			'gb2312',
			'gb2312han',
			'phonebk',
			'phonebook',
			'phonetic',
			'pinyin',
			'reformed',
			'search',
			'searchjl',
			'standard',
			'stroke',
			'trad',
			'traditional',
			'unihan',
			'zhuyin',
		],
		cu	=> [
			'adp',
			'aed',
			'afa',
			'afn',
			'alk',
			'all',
			'amd',
			'ang',
			'aoa',
			'aok',
			'aon',
			'aor',
			'ara',
			'arl',
			'arm',
			'arp',
			'ars',
			'ats',
			'aud',
			'awg',
			'azm',
			'azn',
			'bad',
			'bam',
			'ban',
			'bbd',
			'bdt',
			'bec',
			'bef',
			'bel',
			'bgl',
			'bgm',
			'bgn',
			'bgo',
			'bhd',
			'bif',
			'bmd',
			'bnd',
			'bob',
			'bol',
			'bop',
			'bov',
			'brb',
			'brc',
			'bre',
			'brl',
			'brn',
			'brr',
			'brz',
			'bsd',
			'btn',
			'buk',
			'bwp',
			'byb',
			'byn',
			'byr',
			'bzd',
			'cad',
			'cdf',
			'che',
			'chf',
			'chw',
			'cle',
			'clf',
			'clp',
			'cnh',
			'cnx',
			'cny',
			'cop',
			'cou',
			'crc',
			'csd',
			'csk',
			'cuc',
			'cup',
			'cve',
			'cyp',
			'czk',
			'ddm',
			'dem',
			'djf',
			'dkk',
			'dop',
			'dzd',
			'ecs',
			'ecv',
			'eek',
			'egp',
			'ern',
			'esa',
			'esb',
			'esp',
			'etb',
			'eur',
			'fim',
			'fjd',
			'fkp',
			'frf',
			'gbp',
			'gek',
			'gel',
			'ghc',
			'ghs',
			'gip',
			'gmd',
			'gnf',
			'gns',
			'gqe',
			'grd',
			'gtq',
			'gwe',
			'gwp',
			'gyd',
			'hkd',
			'hnl',
			'hrd',
			'hrk',
			'htg',
			'huf',
			'idr',
			'iep',
			'ilp',
			'ilr',
			'ils',
			'inr',
			'iqd',
			'irr',
			'isj',
			'isk',
			'itl',
			'jmd',
			'jod',
			'jpy',
			'kes',
			'kgs',
			'khr',
			'kmf',
			'kpw',
			'krh',
			'kro',
			'krw',
			'kwd',
			'kyd',
			'kzt',
			'lak',
			'lbp',
			'lkr',
			'lrd',
			'lsl',
			'ltl',
			'ltt',
			'luc',
			'luf',
			'lul',
			'lvl',
			'lvr',
			'lyd',
			'mad',
			'maf',
			'mcf',
			'mdc',
			'mdl',
			'mga',
			'mgf',
			'mkd',
			'mkn',
			'mlf',
			'mmk',
			'mnt',
			'mop',
			'mro',
			'mru',
			'mtl',
			'mtp',
			'mur',
			'mvp',
			'mvr',
			'mwk',
			'mxn',
			'mxp',
			'mxv',
			'myr',
			'mze',
			'mzm',
			'mzn',
			'nad',
			'ngn',
			'nic',
			'nio',
			'nlg',
			'nok',
			'npr',
			'nzd',
			'omr',
			'pab',
			'pei',
			'pen',
			'pes',
			'pgk',
			'php',
			'pkr',
			'pln',
			'plz',
			'pte',
			'pyg',
			'qar',
			'rhd',
			'rol',
			'ron',
			'rsd',
			'rub',
			'rur',
			'rwf',
			'sar',
			'sbd',
			'scr',
			'sdd',
			'sdg',
			'sdp',
			'sek',
			'sgd',
			'shp',
			'sit',
			'skk',
			'sle',
			'sll',
			'sos',
			'srd',
			'srg',
			'ssp',
			'std',
			'stn',
			'sur',
			'svc',
			'syp',
			'szl',
			'thb',
			'tjr',
			'tjs',
			'tmm',
			'tmt',
			'tnd',
			'top',
			'tpe',
			'trl',
			'try',
			'ttd',
			'twd',
			'tzs',
			'uah',
			'uak',
			'ugs',
			'ugx',
			'usd',
			'usn',
			'uss',
			'uyi',
			'uyp',
			'uyu',
			'uyw',
			'uzs',
			'veb',
			'ved',
			'vef',
			'ves',
			'vnd',
			'vnn',
			'vuv',
			'wst',
			'xaf',
			'xag',
			'xau',
			'xba',
			'xbb',
			'xbc',
			'xbd',
			'xcd',
			'xcg',
			'xdr',
			'xeu',
			'xfo',
			'xfu',
			'xof',
			'xpd',
			'xpf',
			'xpt',
			'xre',
			'xsu',
			'xts',
			'xua',
			'xxx',
			'ydd',
			'yer',
			'yud',
			'yum',
			'yun',
			'yur',
			'zal',
			'zar',
			'zmk',
			'zmw',
			'zrn',
			'zrz',
			'zwd',
			'zwl',
			'zwr',
			'zwg',
		],
		d0	=> [
			'ascii',
			'accents',
			'publish',
			'publishing',
			'casefold',
			'lower',
			'upper',
			'title',
			'digit',
			'fwidth',
			'fullwidth',
			'hwidth',
			'halfwidth',
			'hex',
			'nfc',
			'nfd',
			'nfkc',
			'nfkd',
			'fcd',
			'fcc',
			'charname',
			'name',
			'npinyin',
			'numericPinyin',
			'null',
			'remove',
			'zawgyi',
			'morse',
		],
		dx	=> [
			'SCRIPT_CODE',
		],
		em	=> [
			'emoji',
			'text',
			'default',
		],
		fw	=> [
			'sun',
			'mon',
			'tue',
			'wed',
			'thu',
			'fri',
			'sat',
		],
		h0	=> [
			'hybrid',
		],
		hc	=> [
			'h12',
			'h23',
			'h11',
			'h24',
		],
		i0	=> [
			'handwrit',
			'pinyin',
			'wubi',
			'und',
		],
		k0	=> [
			'osx',
			'windows',
			'chromeos',
			'android',
			'googlevk',
			'101key',
			'102key',
			'dvorak',
			'dvorakl',
			'dvorakr',
			'el220',
			'el319',
			'extended',
			'isiri',
			'nutaaq',
			'legacy',
			'lt1205',
			'lt1582',
			'patta',
			'qwerty',
			'qwertz',
			'var',
			'viqr',
			'ta99',
			'colemak',
			'600dpi',
			'768dpi',
			'azerty',
			'und',
		],
		ka	=> [
			'noignore',
			'non-ignorable',
			'shifted',
		],
		kb	=> [
			'true',
			'yes',
			'false',
			'no',
		],
		kc	=> [
			'true',
			'yes',
			'false',
			'no',
		],
		kf	=> [
			'upper',
			'lower',
			'false',
			'no',
		],
		kh	=> [
			'true',
			'yes',
			'false',
			'no',
		],
		kk	=> [
			'true',
			'yes',
			'false',
			'no',
		],
		kn	=> [
			'true',
			'yes',
			'false',
			'no',
		],
		kr	=> [
			'space',
			'punct',
			'symbol',
			'currency',
			'digit',
			'REORDER_CODE',
		],
		ks	=> [
			'level1',
			'primary',
			'level2',
			'secondary',
			'level3',
			'tertiary',
			'level4',
			'quaternary quarternary',
			'identic',
			'identical',
		],
		kv	=> [
			'space',
			'punct',
			'symbol',
			'currency',
		],
		lb	=> [
			'strict',
			'normal',
			'loose',
		],
		lw	=> [
			'normal',
			'breakall',
			'keepall',
			'phrase',
		],
		m0	=> [
			'alaloc',
			'bgn',
			'buckwalt',
			'din',
			'gost',
			'iso',
			'mcst',
			'mns',
			'satts',
			'ungegn',
			'c11',
			'c',
			'css',
			'java',
			'percent',
			'perl',
			'plain',
			'unicode',
			'xml',
			'xml10',
			'prprname',
			'names',
			'iast',
			'ewts',
			'aethiopi',
			'betamets',
			'beta-metsehaf',
			'iesjes',
			'ies-jes',
			'es3842',
			'lambdin',
			'gurage',
			'gutgarts',
			'sera',
			'tekieali',
			'tekie-alibekit',
			'xaleget',
		],
		ms	=> [
			'metric',
			'ussystem',
			'uksystem',
			'imperial',
		],
		mu	=> [
			'celsius',
			'kelvin',
			'fahrenhe',
		],
		nu	=> [
			'adlm',
			'ahom',
			'arab',
			'arabext',
			'armn',
			'armnlow',
			'bali',
			'beng',
			'bhks',
			'brah',
			'cakm',
			'cham',
			'cyrl',
			'deva',
			'diak',
			'ethi',
			'finance',
			'fullwide',
			'gara',
			'geor',
			'gong',
			'gonm',
			'grek',
			'greklow',
			'gujr',
			'gukh',
			'guru',
			'hanidays',
			'hanidec',
			'hans',
			'hansfin',
			'hant',
			'hantfin',
			'hebr',
			'hmng',
			'hmnp',
			'java',
			'jpan',
			'jpanfin',
			'jpanyear',
			'kali',
			'kawi',
			'khmr',
			'knda',
			'krai',
			'lana',
			'lanatham',
			'laoo',
			'latn',
			'lepc',
			'limb',
			'mathbold',
			'mathdbl',
			'mathmono',
			'mathsanb',
			'mathsans',
			'mlym',
			'modi',
			'mong',
			'mroo',
			'mtei',
			'mymr',
			'mymrepka',
			'mymrpao',
			'mymrshan',
			'mymrtlng',
			'nagm',
			'native',
			'newa',
			'nkoo',
			'olck',
			'onao',
			'orya',
			'osma',
			'outlined',
			'rohg',
			'roman',
			'romanlow',
			'saur',
			'segment',
			'shrd',
			'sind',
			'sinh',
			'sora',
			'sund',
			'sunu',
			'takr',
			'talu',
			'taml',
			'tamldec',
			'tnsa',
			'telu',
			'thai',
			'tirh',
			'tibt',
			'traditio',
			'traditional',
			'vaii',
			'wara',
			'wcho',
		],
		rg	=> [
			'RG_KEY_VALUE',
		],
		s0	=> [
			'accents',
			'ascii',
			'publish',
			'publishing',
			'hex',
			'npinyin',
			'numericPinyin',
			'zawgyi',
			'morse',
		],
		sd	=> [
			'SUBDIVISION_CODE',
		],
		ss	=> [
			'none',
			'standard',
		],
		t0	=> [
			'und',
		],
		tz	=> [
			'adalv',
			'Europe/Andorra',
			'aedxb',
			'Asia/Dubai',
			'afkbl',
			'Asia/Kabul',
			'aganu',
			'America/Antigua',
			'aiaxa',
			'America/Anguilla',
			'altia',
			'Europe/Tirane',
			'amevn',
			'Asia/Yerevan',
			'ancur',
			'America/Curacao',
			'aolad',
			'Africa/Luanda',
			'aqams',
			'aqcas',
			'Antarctica/Casey',
			'aqdav',
			'Antarctica/Davis',
			'aqddu',
			'Antarctica/DumontDUrville',
			'aqmaw',
			'Antarctica/Mawson',
			'aqmcm',
			'Antarctica/McMurdo',
			'aqplm',
			'Antarctica/Palmer',
			'aqrot',
			'Antarctica/Rothera',
			'aqsyw',
			'Antarctica/Syowa',
			'aqtrl',
			'Antarctica/Troll',
			'aqvos',
			'Antarctica/Vostok',
			'arbue',
			'America/Buenos_Aires America/Argentina/Buenos_Aires',
			'arcor',
			'America/Cordoba America/Argentina/Cordoba America/Rosario',
			'arctc',
			'America/Catamarca America/Argentina/Catamarca America/Argentina/ComodRivadavia',
			'arirj',
			'America/Argentina/La_Rioja',
			'arjuj',
			'America/Jujuy America/Argentina/Jujuy',
			'arluq',
			'America/Argentina/San_Luis',
			'armdz',
			'America/Mendoza America/Argentina/Mendoza',
			'arrgl',
			'America/Argentina/Rio_Gallegos',
			'arsla',
			'America/Argentina/Salta',
			'artuc',
			'America/Argentina/Tucuman',
			'aruaq',
			'America/Argentina/San_Juan',
			'arush',
			'America/Argentina/Ushuaia',
			'asppg',
			'Pacific/Pago_Pago Pacific/Samoa US/Samoa',
			'atvie',
			'Europe/Vienna',
			'auadl',
			'Australia/Adelaide Australia/South',
			'aubhq',
			'Australia/Broken_Hill Australia/Yancowinna',
			'aubne',
			'Australia/Brisbane Australia/Queensland',
			'audrw',
			'Australia/Darwin Australia/North',
			'aueuc',
			'Australia/Eucla',
			'auhba',
			'Australia/Hobart Australia/Tasmania Australia/Currie',
			'aukns',
			'auldc',
			'Australia/Lindeman',
			'auldh',
			'Australia/Lord_Howe Australia/LHI',
			'aumel',
			'Australia/Melbourne Australia/Victoria',
			'aumqi',
			'Antarctica/Macquarie',
			'auper',
			'Australia/Perth Australia/West',
			'ausyd',
			'Australia/Sydney Australia/ACT Australia/Canberra Australia/NSW',
			'awaua',
			'America/Aruba',
			'azbak',
			'Asia/Baku',
			'basjj',
			'Europe/Sarajevo',
			'bbbgi',
			'America/Barbados',
			'bddac',
			'Asia/Dhaka Asia/Dacca',
			'bebru',
			'Europe/Brussels CET MET',
			'bfoua',
			'Africa/Ouagadougou',
			'bgsof',
			'Europe/Sofia',
			'bhbah',
			'Asia/Bahrain',
			'bibjm',
			'Africa/Bujumbura',
			'bjptn',
			'Africa/Porto-Novo',
			'bmbda',
			'Atlantic/Bermuda',
			'bnbwn',
			'Asia/Brunei',
			'bolpb',
			'America/La_Paz',
			'bqkra',
			'America/Kralendijk',
			'braux',
			'America/Araguaina',
			'brbel',
			'America/Belem',
			'brbvb',
			'America/Boa_Vista',
			'brcgb',
			'America/Cuiaba',
			'brcgr',
			'America/Campo_Grande',
			'brern',
			'America/Eirunepe',
			'brfen',
			'America/Noronha Brazil/DeNoronha',
			'brfor',
			'America/Fortaleza',
			'brmao',
			'America/Manaus Brazil/West',
			'brmcz',
			'America/Maceio',
			'brpvh',
			'America/Porto_Velho',
			'brrbr',
			'America/Rio_Branco America/Porto_Acre Brazil/Acre',
			'brrec',
			'America/Recife',
			'brsao',
			'America/Sao_Paulo Brazil/East',
			'brssa',
			'America/Bahia',
			'brstm',
			'America/Santarem',
			'bsnas',
			'America/Nassau',
			'btthi',
			'Asia/Thimphu Asia/Thimbu',
			'bwgbe',
			'Africa/Gaborone',
			'bymsq',
			'Europe/Minsk',
			'bzbze',
			'America/Belize',
			'cacfq',
			'America/Creston',
			'caedm',
			'America/Edmonton Canada/Mountain America/Yellowknife',
			'caffs',
			'cafne',
			'America/Fort_Nelson',
			'caglb',
			'America/Glace_Bay',
			'cagoo',
			'America/Goose_Bay',
			'cahal',
			'America/Halifax Canada/Atlantic',
			'caiql',
			'America/Iqaluit America/Pangnirtung',
			'camon',
			'America/Moncton',
			'camtr',
			'capnt',
			'careb',
			'America/Resolute',
			'careg',
			'America/Regina Canada/East-Saskatchewan Canada/Saskatchewan',
			'casjf',
			'America/St_Johns Canada/Newfoundland',
			'canpg',
			'cathu',
			'cator',
			'America/Toronto America/Montreal Canada/Eastern America/Nipigon America/Thunder_Bay',
			'cavan',
			'America/Vancouver Canada/Pacific',
			'cawnp',
			'America/Winnipeg Canada/Central America/Rainy_River',
			'caybx',
			'America/Blanc-Sablon',
			'caycb',
			'America/Cambridge_Bay',
			'cayda',
			'America/Dawson',
			'caydq',
			'America/Dawson_Creek',
			'cayek',
			'America/Rankin_Inlet',
			'cayev',
			'America/Inuvik',
			'cayxy',
			'America/Whitehorse Canada/Yukon',
			'cayyn',
			'America/Swift_Current',
			'cayzf',
			'cayzs',
			'America/Coral_Harbour America/Atikokan',
			'cccck',
			'Indian/Cocos',
			'cdfbm',
			'Africa/Lubumbashi',
			'cdfih',
			'Africa/Kinshasa',
			'cfbgf',
			'Africa/Bangui',
			'cgbzv',
			'Africa/Brazzaville',
			'chzrh',
			'Europe/Zurich',
			'ciabj',
			'Africa/Abidjan',
			'ckrar',
			'Pacific/Rarotonga',
			'clipc',
			'Pacific/Easter Chile/EasterIsland',
			'clpuq',
			'America/Punta_Arenas',
			'clscl',
			'America/Santiago Chile/Continental',
			'cmdla',
			'Africa/Douala',
			'cnckg',
			'cnhrb',
			'cnkhg',
			'cnsha',
			'Asia/Shanghai Asia/Chongqing Asia/Chungking Asia/Harbin PRC',
			'cnurc',
			'Asia/Urumqi Asia/Kashgar',
			'cobog',
			'America/Bogota',
			'crsjo',
			'America/Costa_Rica',
			'cst6cdt',
			'cuhav',
			'America/Havana Cuba',
			'cvrai',
			'Atlantic/Cape_Verde',
			'cxxch',
			'Indian/Christmas',
			'cyfmg',
			'Asia/Famagusta',
			'cynic',
			'Asia/Nicosia Europe/Nicosia',
			'czprg',
			'Europe/Prague',
			'deber',
			'Europe/Berlin',
			'debsngn',
			'Europe/Busingen',
			'djjib',
			'Africa/Djibouti',
			'dkcph',
			'Europe/Copenhagen',
			'dmdom',
			'America/Dominica',
			'dosdq',
			'America/Santo_Domingo',
			'dzalg',
			'Africa/Algiers',
			'ecgps',
			'Pacific/Galapagos',
			'ecgye',
			'America/Guayaquil',
			'eetll',
			'Europe/Tallinn',
			'egcai',
			'Africa/Cairo Egypt',
			'eheai',
			'Africa/El_Aaiun',
			'erasm',
			'Africa/Asmera Africa/Asmara',
			'esceu',
			'Africa/Ceuta',
			'eslpa',
			'Atlantic/Canary',
			'esmad',
			'Europe/Madrid',
			'est5edt',
			'etadd',
			'Africa/Addis_Ababa',
			'fihel',
			'Europe/Helsinki',
			'fimhq',
			'Europe/Mariehamn',
			'fjsuv',
			'Pacific/Fiji',
			'fkpsy',
			'Atlantic/Stanley',
			'fmksa',
			'Pacific/Kosrae',
			'fmpni',
			'Pacific/Ponape Pacific/Pohnpei',
			'fmtkk',
			'Pacific/Truk Pacific/Chuuk Pacific/Yap',
			'fotho',
			'Atlantic/Faeroe Atlantic/Faroe',
			'frpar',
			'Europe/Paris',
			'galbv',
			'Africa/Libreville',
			'gaza',
			'gazastrp',
			'Asia/Gaza',
			'gblon',
			'Europe/London Europe/Belfast GB GB-Eire',
			'gdgnd',
			'America/Grenada',
			'getbs',
			'Asia/Tbilisi',
			'gfcay',
			'America/Cayenne',
			'gggci',
			'Europe/Guernsey',
			'ghacc',
			'Africa/Accra',
			'gigib',
			'Europe/Gibraltar',
			'gldkshvn',
			'America/Danmarkshavn',
			'glgoh',
			'America/Godthab America/Nuuk',
			'globy',
			'America/Scoresbysund',
			'glthu',
			'America/Thule',
			'gmbjl',
			'Africa/Banjul',
			'gmt',
			'Etc/GMT Etc/GMT+0 Etc/GMT-0 Etc/GMT0 Etc/Greenwich GMT GMT+0 GMT-0 GMT0 Greenwich',
			'gncky',
			'Africa/Conakry',
			'gpbbr',
			'America/Guadeloupe',
			'gpmsb',
			'America/Marigot',
			'gpsbh',
			'America/St_Barthelemy',
			'gqssg',
			'Africa/Malabo',
			'grath',
			'Europe/Athens EET',
			'gsgrv',
			'Atlantic/South_Georgia',
			'gtgua',
			'America/Guatemala',
			'gugum',
			'Pacific/Guam',
			'gwoxb',
			'Africa/Bissau',
			'gygeo',
			'America/Guyana',
			'hebron',
			'Asia/Hebron',
			'hkhkg',
			'Asia/Hong_Kong Hongkong',
			'hntgu',
			'America/Tegucigalpa',
			'hrzag',
			'Europe/Zagreb',
			'htpap',
			'America/Port-au-Prince',
			'hubud',
			'Europe/Budapest',
			'iddjj',
			'Asia/Jayapura',
			'idjkt',
			'Asia/Jakarta',
			'idmak',
			'Asia/Makassar Asia/Ujung_Pandang',
			'idpnk',
			'Asia/Pontianak',
			'iedub',
			'Europe/Dublin Eire',
			'imdgs',
			'Europe/Isle_of_Man',
			'inccu',
			'Asia/Calcutta Asia/Kolkata',
			'iodga',
			'Indian/Chagos',
			'iqbgw',
			'Asia/Baghdad',
			'irthr',
			'Asia/Tehran Iran',
			'isrey',
			'Atlantic/Reykjavik Iceland',
			'itrom',
			'Europe/Rome',
			'jeruslm',
			'Asia/Jerusalem Asia/Tel_Aviv Israel',
			'jesth',
			'Europe/Jersey',
			'jmkin',
			'America/Jamaica Jamaica',
			'joamm',
			'Asia/Amman',
			'jptyo',
			'Asia/Tokyo Japan',
			'kenbo',
			'Africa/Nairobi',
			'kgfru',
			'Asia/Bishkek',
			'khpnh',
			'Asia/Phnom_Penh',
			'kicxi',
			'Pacific/Kiritimati',
			'kipho',
			'Pacific/Enderbury Pacific/Kanton',
			'kitrw',
			'Pacific/Tarawa',
			'kmyva',
			'Indian/Comoro',
			'knbas',
			'America/St_Kitts',
			'kpfnj',
			'Asia/Pyongyang',
			'krsel',
			'Asia/Seoul ROK',
			'kwkwi',
			'Asia/Kuwait',
			'kygec',
			'America/Cayman',
			'kzaau',
			'Asia/Aqtau',
			'kzakx',
			'Asia/Aqtobe',
			'kzala',
			'Asia/Almaty',
			'kzguw',
			'Asia/Atyrau',
			'kzksn',
			'Asia/Qostanay',
			'kzkzo',
			'Asia/Qyzylorda',
			'kzura',
			'Asia/Oral',
			'lavte',
			'Asia/Vientiane',
			'lbbey',
			'Asia/Beirut',
			'lccas',
			'America/St_Lucia',
			'livdz',
			'Europe/Vaduz',
			'lkcmb',
			'Asia/Colombo',
			'lrmlw',
			'Africa/Monrovia',
			'lsmsu',
			'Africa/Maseru',
			'ltvno',
			'Europe/Vilnius',
			'lulux',
			'Europe/Luxembourg',
			'lvrix',
			'Europe/Riga',
			'lytip',
			'Africa/Tripoli Libya',
			'macas',
			'Africa/Casablanca',
			'mcmon',
			'Europe/Monaco',
			'mdkiv',
			'Europe/Chisinau Europe/Tiraspol',
			'metgd',
			'Europe/Podgorica',
			'mgtnr',
			'Indian/Antananarivo',
			'mhkwa',
			'Pacific/Kwajalein Kwajalein',
			'mhmaj',
			'Pacific/Majuro',
			'mkskp',
			'Europe/Skopje',
			'mlbko',
			'Africa/Bamako Africa/Timbuktu',
			'mmrgn',
			'Asia/Rangoon Asia/Yangon',
			'mncoq',
			'mnhvd',
			'Asia/Hovd',
			'mnuln',
			'Asia/Ulaanbaatar Asia/Ulan_Bator Asia/Choibalsan',
			'momfm',
			'Asia/Macau Asia/Macao',
			'mpspn',
			'Pacific/Saipan',
			'mqfdf',
			'America/Martinique',
			'mrnkc',
			'Africa/Nouakchott',
			'msmni',
			'America/Montserrat',
			'mst7mdt',
			'mtmla',
			'Europe/Malta',
			'muplu',
			'Indian/Mauritius',
			'mvmle',
			'Indian/Maldives',
			'mwblz',
			'Africa/Blantyre',
			'mxchi',
			'America/Chihuahua',
			'mxcun',
			'America/Cancun',
			'mxcjs',
			'America/Ciudad_Juarez',
			'mxhmo',
			'America/Hermosillo',
			'mxmam',
			'America/Matamoros',
			'mxmex',
			'America/Mexico_City Mexico/General',
			'mxmid',
			'America/Merida',
			'mxmty',
			'America/Monterrey',
			'mxmzt',
			'America/Mazatlan Mexico/BajaSur',
			'mxoji',
			'America/Ojinaga',
			'mxpvr',
			'America/Bahia_Banderas',
			'mxstis',
			'mxtij',
			'America/Tijuana America/Ensenada Mexico/BajaNorte America/Santa_Isabel',
			'mykch',
			'Asia/Kuching',
			'mykul',
			'Asia/Kuala_Lumpur',
			'mzmpm',
			'Africa/Maputo',
			'nawdh',
			'Africa/Windhoek',
			'ncnou',
			'Pacific/Noumea',
			'nenim',
			'Africa/Niamey',
			'nfnlk',
			'Pacific/Norfolk',
			'nglos',
			'Africa/Lagos',
			'nimga',
			'America/Managua',
			'nlams',
			'Europe/Amsterdam',
			'noosl',
			'Europe/Oslo',
			'npktm',
			'Asia/Katmandu Asia/Kathmandu',
			'nrinu',
			'Pacific/Nauru',
			'nuiue',
			'Pacific/Niue',
			'nzakl',
			'Pacific/Auckland Antarctica/South_Pole NZ',
			'nzcht',
			'Pacific/Chatham NZ-CHAT',
			'ommct',
			'Asia/Muscat',
			'papty',
			'America/Panama EST',
			'pelim',
			'America/Lima',
			'pfgmr',
			'Pacific/Gambier',
			'pfnhv',
			'Pacific/Marquesas',
			'pfppt',
			'Pacific/Tahiti',
			'pgpom',
			'Pacific/Port_Moresby',
			'pgraw',
			'Pacific/Bougainville',
			'phmnl',
			'Asia/Manila',
			'pkkhi',
			'Asia/Karachi',
			'plwaw',
			'Europe/Warsaw Poland',
			'pmmqc',
			'America/Miquelon',
			'pnpcn',
			'Pacific/Pitcairn',
			'prsju',
			'America/Puerto_Rico',
			'pst8pdt',
			'ptfnc',
			'Atlantic/Madeira',
			'ptlis',
			'Europe/Lisbon Portugal WET',
			'ptpdl',
			'Atlantic/Azores',
			'pwror',
			'Pacific/Palau',
			'pyasu',
			'America/Asuncion',
			'qadoh',
			'Asia/Qatar',
			'rereu',
			'Indian/Reunion',
			'robuh',
			'Europe/Bucharest',
			'rsbeg',
			'Europe/Belgrade',
			'ruasf',
			'Europe/Astrakhan',
			'rubax',
			'Asia/Barnaul',
			'ruchita',
			'Asia/Chita',
			'rudyr',
			'Asia/Anadyr',
			'rugdx',
			'Asia/Magadan',
			'ruikt',
			'Asia/Irkutsk',
			'rukgd',
			'Europe/Kaliningrad',
			'rukhndg',
			'Asia/Khandyga',
			'rukra',
			'Asia/Krasnoyarsk',
			'rukuf',
			'Europe/Samara',
			'rukvx',
			'Europe/Kirov',
			'rumow',
			'Europe/Moscow W-SU',
			'runoz',
			'Asia/Novokuznetsk',
			'ruoms',
			'Asia/Omsk',
			'ruovb',
			'Asia/Novosibirsk',
			'rupkc',
			'Asia/Kamchatka',
			'rurtw',
			'Europe/Saratov',
			'rusred',
			'Asia/Srednekolymsk',
			'rutof',
			'Asia/Tomsk',
			'ruuly',
			'Europe/Ulyanovsk',
			'ruunera',
			'Asia/Ust-Nera',
			'ruuus',
			'Asia/Sakhalin',
			'ruvog',
			'Europe/Volgograd',
			'ruvvo',
			'Asia/Vladivostok',
			'ruyek',
			'Asia/Yekaterinburg',
			'ruyks',
			'Asia/Yakutsk',
			'rwkgl',
			'Africa/Kigali',
			'saruh',
			'Asia/Riyadh',
			'sbhir',
			'Pacific/Guadalcanal',
			'scmaw',
			'Indian/Mahe',
			'sdkrt',
			'Africa/Khartoum',
			'sesto',
			'Europe/Stockholm',
			'sgsin',
			'Asia/Singapore Singapore',
			'shshn',
			'Atlantic/St_Helena',
			'silju',
			'Europe/Ljubljana',
			'sjlyr',
			'Arctic/Longyearbyen Atlantic/Jan_Mayen',
			'skbts',
			'Europe/Bratislava',
			'slfna',
			'Africa/Freetown',
			'smsai',
			'Europe/San_Marino',
			'sndkr',
			'Africa/Dakar',
			'somgq',
			'Africa/Mogadishu',
			'srpbm',
			'America/Paramaribo',
			'ssjub',
			'Africa/Juba',
			'sttms',
			'Africa/Sao_Tome',
			'svsal',
			'America/El_Salvador',
			'sxphi',
			'America/Lower_Princes',
			'sydam',
			'Asia/Damascus',
			'szqmn',
			'Africa/Mbabane',
			'tcgdt',
			'America/Grand_Turk',
			'tdndj',
			'Africa/Ndjamena',
			'tfpfr',
			'Indian/Kerguelen',
			'tglfw',
			'Africa/Lome',
			'thbkk',
			'Asia/Bangkok',
			'tjdyu',
			'Asia/Dushanbe',
			'tkfko',
			'Pacific/Fakaofo',
			'tldil',
			'Asia/Dili',
			'tmasb',
			'Asia/Ashgabat Asia/Ashkhabad',
			'tntun',
			'Africa/Tunis',
			'totbu',
			'Pacific/Tongatapu',
			'trist',
			'Europe/Istanbul Asia/Istanbul Turkey',
			'ttpos',
			'America/Port_of_Spain',
			'tvfun',
			'Pacific/Funafuti',
			'twtpe',
			'Asia/Taipei ROC',
			'tzdar',
			'Africa/Dar_es_Salaam',
			'uaiev',
			'Europe/Kiev Europe/Kyiv Europe/Zaporozhye Europe/Uzhgorod',
			'uaozh',
			'uasip',
			'Europe/Simferopol',
			'uauzh',
			'ugkla',
			'Africa/Kampala',
			'umawk',
			'Pacific/Wake',
			'umjon',
			'ummdy',
			'Pacific/Midway',
			'unk',
			'Etc/Unknown Factory',
			'usadk',
			'America/Adak America/Atka US/Aleutian',
			'usaeg',
			'America/Indiana/Marengo',
			'usanc',
			'America/Anchorage US/Alaska',
			'usboi',
			'America/Boise',
			'uschi',
			'America/Chicago US/Central CST6CDT',
			'usden',
			'America/Denver America/Shiprock Navajo US/Mountain MST7MDT',
			'usdet',
			'America/Detroit US/Michigan',
			'ushnl',
			'Pacific/Honolulu US/Hawaii Pacific/Johnston HST',
			'usind',
			'America/Indianapolis America/Fort_Wayne America/Indiana/Indianapolis US/East-Indiana',
			'usinvev',
			'America/Indiana/Vevay',
			'usjnu',
			'America/Juneau',
			'usknx',
			'America/Indiana/Knox America/Knox_IN US/Indiana-Starke',
			'uslax',
			'America/Los_Angeles US/Pacific US/Pacific-New PST8PDT',
			'uslui',
			'America/Louisville America/Kentucky/Louisville',
			'usmnm',
			'America/Menominee',
			'usmtm',
			'America/Metlakatla',
			'usmoc',
			'America/Kentucky/Monticello',
			'usnavajo',
			'usndcnt',
			'America/North_Dakota/Center',
			'usndnsl',
			'America/North_Dakota/New_Salem',
			'usnyc',
			'America/New_York US/Eastern EST5EDT',
			'usoea',
			'America/Indiana/Vincennes',
			'usome',
			'America/Nome',
			'usphx',
			'America/Phoenix US/Arizona MST',
			'ussit',
			'America/Sitka',
			'ustel',
			'America/Indiana/Tell_City',
			'uswlz',
			'America/Indiana/Winamac',
			'uswsq',
			'America/Indiana/Petersburg',
			'usxul',
			'America/North_Dakota/Beulah',
			'usyak',
			'America/Yakutat',
			'utc',
			'Etc/UTC Etc/UCT Etc/Universal Etc/Zulu UCT UTC Universal Zulu',
			'utce01',
			'Etc/GMT-1',
			'utce02',
			'Etc/GMT-2',
			'utce03',
			'Etc/GMT-3',
			'utce04',
			'Etc/GMT-4',
			'utce05',
			'Etc/GMT-5',
			'utce06',
			'Etc/GMT-6',
			'utce07',
			'Etc/GMT-7',
			'utce08',
			'Etc/GMT-8',
			'utce09',
			'Etc/GMT-9',
			'utce10',
			'Etc/GMT-10',
			'utce11',
			'Etc/GMT-11',
			'utce12',
			'Etc/GMT-12',
			'utce13',
			'Etc/GMT-13',
			'utce14',
			'Etc/GMT-14',
			'utcw01',
			'Etc/GMT+1',
			'utcw02',
			'Etc/GMT+2',
			'utcw03',
			'Etc/GMT+3',
			'utcw04',
			'Etc/GMT+4',
			'utcw05',
			'Etc/GMT+5',
			'utcw06',
			'Etc/GMT+6',
			'utcw07',
			'Etc/GMT+7',
			'utcw08',
			'Etc/GMT+8',
			'utcw09',
			'Etc/GMT+9',
			'utcw10',
			'Etc/GMT+10',
			'utcw11',
			'Etc/GMT+11',
			'utcw12',
			'Etc/GMT+12',
			'uymvd',
			'America/Montevideo',
			'uzskd',
			'Asia/Samarkand',
			'uztas',
			'Asia/Tashkent',
			'vavat',
			'Europe/Vatican',
			'vcsvd',
			'America/St_Vincent',
			'veccs',
			'America/Caracas',
			'vgtov',
			'America/Tortola',
			'vistt',
			'America/St_Thomas America/Virgin',
			'vnsgn',
			'Asia/Saigon Asia/Ho_Chi_Minh',
			'vuvli',
			'Pacific/Efate',
			'wfmau',
			'Pacific/Wallis',
			'wsapw',
			'Pacific/Apia',
			'yeade',
			'Asia/Aden',
			'ytmam',
			'Indian/Mayotte',
			'zajnb',
			'Africa/Johannesburg',
			'zmlun',
			'Africa/Lusaka',
			'zwhre',
			'Africa/Harare',
		],
		va	=> [
			'posix',
		],
		vt	=> [
			'CODEPOINTS',
		],
		x0	=> [
			'PRIVATE_USE',
		],
	}},
);

around valid_keys => sub {
    my ($orig, $self) = @_;

    my $keys = $self->$orig;
    return %{$keys};
};

has 'language_aliases' => (
	is			=> 'ro',
	isa			=> HashRef,
	init_arg	=> undef,
	default	=> sub { return {
	'art_lojban' => 'jbo',
	'i_ami' => 'ami',
	'i_bnn' => 'bnn',
	'i_hak' => 'hak',
	'i_klingon' => 'tlh',
	'i_lux' => 'lb',
	'i_navajo' => 'nv',
	'i_pwn' => 'pwn',
	'i_tao' => 'tao',
	'i_tay' => 'tay',
	'i_tsu' => 'tsu',
	'no_bok' => 'nb',
	'no_nyn' => 'nn',
	'sgn_BE_FR' => 'sfb',
	'sgn_BE_NL' => 'vgt',
	'sgn_CH_DE' => 'sgg',
	'zh_guoyu' => 'zh',
	'zh_hakka' => 'hak',
	'zh_min_nan' => 'nan',
	'zh_xiang' => 'hsn',
	'en_GB_oed' => 'en_GB_oxendict',
	'in' => 'id',
	'iw' => 'he',
	'ji' => 'yi',
	'jw' => 'jv',
	'mo' => 'ro',
	'scc' => 'sr',
	'scr' => 'hr',
	'aam' => 'aas',
	'adp' => 'dz',
	'aue' => 'ktz',
	'ayx' => 'nun',
	'bgm' => 'bcg',
	'bjd' => 'drl',
	'ccq' => 'rki',
	'cjr' => 'mom',
	'cka' => 'cmr',
	'cmk' => 'xch',
	'coy' => 'pij',
	'cqu' => 'quh',
	'drh' => 'mn',
	'drw' => 'fa_AF',
	'gav' => 'dev',
	'gfx' => 'vaj',
	'ggn' => 'gvr',
	'gti' => 'nyc',
	'guv' => 'duz',
	'hrr' => 'jal',
	'ibi' => 'opa',
	'ilw' => 'gal',
	'jeg' => 'oyb',
	'kgc' => 'tdf',
	'kgh' => 'kml',
	'koj' => 'kwv',
	'krm' => 'bmf',
	'ktr' => 'dtp',
	'kvs' => 'gdj',
	'kwq' => 'yam',
	'kxe' => 'tvd',
	'kzj' => 'dtp',
	'kzt' => 'dtp',
	'lii' => 'raq',
	'lmm' => 'rmx',
	'meg' => 'cir',
	'mst' => 'mry',
	'mwj' => 'vaj',
	'myt' => 'mry',
	'nad' => 'xny',
	'ncp' => 'kdz',
	'nnx' => 'ngv',
	'nts' => 'pij',
	'oun' => 'vaj',
	'pcr' => 'adx',
	'pmc' => 'huw',
	'pmu' => 'phr',
	'ppa' => 'bfy',
	'ppr' => 'lcq',
	'pry' => 'prt',
	'puz' => 'pub',
	'sca' => 'hle',
	'skk' => 'oyb',
	'tdu' => 'dtp',
	'thc' => 'tpo',
	'thx' => 'oyb',
	'tie' => 'ras',
	'tkk' => 'twm',
	'tlw' => 'weo',
	'tmp' => 'tyj',
	'tne' => 'kak',
	'tnf' => 'fa_AF',
	'tsf' => 'taj',
	'uok' => 'ema',
	'xba' => 'cax',
	'xia' => 'acn',
	'xkh' => 'waw',
	'xsj' => 'suj',
	'ybd' => 'rki',
	'yma' => 'lrr',
	'ymt' => 'mtm',
	'yos' => 'zom',
	'yuu' => 'yug',
	'asd' => 'snz',
	'dit' => 'dif',
	'llo' => 'ngt',
	'myd' => 'aog',
	'nns' => 'nbr',
	'agp' => 'apf',
	'ais' => 'ami',
	'ajt' => 'aeb',
	'baz' => 'nvo',
	'bhk' => 'fbl',
	'bic' => 'bir',
	'bjq' => 'bzc',
	'bkb' => 'ebk',
	'blg' => 'iba',
	'btb' => 'beb',
	'daf' => 'dnj',
	'dap' => 'njz',
	'djl' => 'dze',
	'dkl' => 'aqd',
	'drr' => 'kzk',
	'dud' => 'uth',
	'duj' => 'dwu',
	'dwl' => 'dbt',
	'elp' => 'amq',
	'gbc' => 'wny',
	'ggo' => 'esg',
	'ggr' => 'gtu',
	'gio' => 'aou',
	'gli' => 'kzk',
	'ill' => 'ilm',
	'izi' => 'eza',
	'jar' => 'jgk',
	'kdv' => 'zkd',
	'kgd' => 'ncq',
	'kpp' => 'jkm',
	'kxl' => 'kru',
	'kzh' => 'dgl',
	'lak' => 'ksp',
	'leg' => 'enl',
	'mgx' => 'jbk',
	'mnt' => 'wnn',
	'mof' => 'xnt',
	'mwd' => 'dmw',
	'nbf' => 'nru',
	'nbx' => 'ekc',
	'nln' => 'azd',
	'nlr' => 'nrk',
	'noo' => 'dtd',
	'nxu' => 'bpp',
	'pat' => 'kxr',
	'rmr' => 'emx',
	'sap' => 'aqt',
	'sgl' => 'isk',
	'smd' => 'kmb',
	'snb' => 'iba',
	'sul' => 'sgd',
	'sum' => 'ulw',
	'tgg' => 'bjp',
	'thw' => 'ola',
	'tid' => 'itd',
	'unp' => 'wro',
	'wgw' => 'wgb',
	'wit' => 'nol',
	'wiw' => 'nwo',
	'xrq' => 'dmw',
	'yen' => 'ynq',
	'yiy' => 'yrm',
	'zir' => 'scv',
	'sgn_BR' => 'bzs',
	'sgn_CO' => 'csn',
	'sgn_DE' => 'gsg',
	'sgn_DK' => 'dsl',
	'sgn_FR' => 'fsl',
	'sgn_GB' => 'bfi',
	'sgn_GR' => 'gss',
	'sgn_IE' => 'isg',
	'sgn_IT' => 'ise',
	'sgn_JP' => 'jsl',
	'sgn_MX' => 'mfs',
	'sgn_NI' => 'ncs',
	'sgn_NL' => 'dse',
	'sgn_NO' => 'nsi',
	'sgn_PT' => 'psr',
	'sgn_SE' => 'swl',
	'sgn_US' => 'ase',
	'sgn_ZA' => 'sfs',
	'sgn_ES' => 'ssp',
	'zh_cmn' => 'zh',
	'zh_cmn_Hans' => 'zh_Hans',
	'zh_cmn_Hant' => 'zh_Hant',
	'zh_gan' => 'gan',
	'zh_wuu' => 'wuu',
	'zh_yue' => 'yue',
	'no_bokmal' => 'nb',
	'no_nynorsk' => 'nn',
	'aa_saaho' => 'ssy',
	'sh' => 'sr_Latn',
	'cnr' => 'sr_ME',
	'tl' => 'fil',
	'aju' => 'jrb',
	'als' => 'sq',
	'arb' => 'ar',
	'ayr' => 'ay',
	'azj' => 'az',
	'bcc' => 'bal',
	'bcl' => 'bik',
	'bxk' => 'luy',
	'bxr' => 'bua',
	'cld' => 'syr',
	'cmn' => 'zh',
	'cwd' => 'cr',
	'dgo' => 'doi',
	'dhd' => 'mwr',
	'dik' => 'din',
	'diq' => 'zza',
	'lbk' => 'bnc',
	'ekk' => 'et',
	'emk' => 'man',
	'esk' => 'ik',
	'fat' => 'ak',
	'fuc' => 'ff',
	'gaz' => 'om',
	'gbo' => 'grb',
	'gno' => 'gon',
	'gom' => 'kok',
	'gug' => 'gn',
	'gya' => 'gba',
	'hdn' => 'hai',
	'hea' => 'hmn',
	'ike' => 'iu',
	'kmr' => 'ku',
	'knc' => 'kr',
	'kng' => 'kg',
	'kpv' => 'kv',
	'lvs' => 'lv',
	'mhr' => 'chm',
	'mup' => 'raj',
	'khk' => 'mn',
	'npi' => 'ne',
	'ojg' => 'oj',
	'ory' => 'or',
	'pbu' => 'ps',
	'pes' => 'fa',
	'plt' => 'mg',
	'pnb' => 'lah',
	'quz' => 'qu',
	'rmy' => 'rom',
	'spy' => 'kln',
	'src' => 'sc',
	'swh' => 'sw',
	'ttq' => 'tmh',
	'tw' => 'ak',
	'umu' => 'del',
	'uzn' => 'uz',
	'xpe' => 'kpe',
	'xsl' => 'den',
	'ydd' => 'yi',
	'zai' => 'zap',
	'zsm' => 'ms',
	'zyb' => 'za',
	'him' => 'srx',
	'mnk' => 'man',
	'bh' => 'bho',
	'prs' => 'fa_AF',
	'swc' => 'sw_CD',
	'aar' => 'aa',
	'abk' => 'ab',
	'ave' => 'ae',
	'afr' => 'af',
	'aka' => 'ak',
	'amh' => 'am',
	'arg' => 'an',
	'ara' => 'ar',
	'asm' => 'as',
	'ava' => 'av',
	'aym' => 'ay',
	'aze' => 'az',
	'bak' => 'ba',
	'bel' => 'be',
	'bul' => 'bg',
	'bih' => 'bho',
	'bis' => 'bi',
	'bam' => 'bm',
	'ben' => 'bn',
	'bod' => 'bo',
	'bre' => 'br',
	'bos' => 'bs',
	'cat' => 'ca',
	'che' => 'ce',
	'cha' => 'ch',
	'cos' => 'co',
	'cre' => 'cr',
	'ces' => 'cs',
	'chu' => 'cu',
	'chv' => 'cv',
	'cym' => 'cy',
	'dan' => 'da',
	'deu' => 'de',
	'div' => 'dv',
	'dzo' => 'dz',
	'ewe' => 'ee',
	'ell' => 'el',
	'eng' => 'en',
	'epo' => 'eo',
	'spa' => 'es',
	'est' => 'et',
	'eus' => 'eu',
	'fas' => 'fa',
	'ful' => 'ff',
	'fin' => 'fi',
	'fij' => 'fj',
	'fao' => 'fo',
	'fra' => 'fr',
	'fry' => 'fy',
	'gle' => 'ga',
	'gla' => 'gd',
	'glg' => 'gl',
	'grn' => 'gn',
	'guj' => 'gu',
	'glv' => 'gv',
	'hau' => 'ha',
	'heb' => 'he',
	'hin' => 'hi',
	'hmo' => 'ho',
	'hrv' => 'hr',
	'hat' => 'ht',
	'hun' => 'hu',
	'hye' => 'hy',
	'her' => 'hz',
	'ina' => 'ia',
	'ind' => 'id',
	'ile' => 'ie',
	'ibo' => 'ig',
	'iii' => 'ii',
	'ipk' => 'ik',
	'ido' => 'io',
	'isl' => 'is',
	'ita' => 'it',
	'iku' => 'iu',
	'jpn' => 'ja',
	'jav' => 'jv',
	'kat' => 'ka',
	'kon' => 'kg',
	'kik' => 'ki',
	'kua' => 'kj',
	'kaz' => 'kk',
	'kal' => 'kl',
	'khm' => 'km',
	'kan' => 'kn',
	'kor' => 'ko',
	'kau' => 'kr',
	'kas' => 'ks',
	'kur' => 'ku',
	'kom' => 'kv',
	'cor' => 'kw',
	'kir' => 'ky',
	'lat' => 'la',
	'ltz' => 'lb',
	'lug' => 'lg',
	'lim' => 'li',
	'lin' => 'ln',
	'lao' => 'lo',
	'lit' => 'lt',
	'lub' => 'lu',
	'lav' => 'lv',
	'mlg' => 'mg',
	'mah' => 'mh',
	'mri' => 'mi',
	'mkd' => 'mk',
	'mal' => 'ml',
	'mon' => 'mn',
	'mol' => 'ro',
	'mar' => 'mr',
	'msa' => 'ms',
	'mlt' => 'mt',
	'mya' => 'my',
	'nau' => 'na',
	'nob' => 'nb',
	'nde' => 'nd',
	'nep' => 'ne',
	'ndo' => 'ng',
	'nld' => 'nl',
	'nno' => 'nn',
	'nor' => 'no',
	'nbl' => 'nr',
	'nav' => 'nv',
	'nya' => 'ny',
	'oci' => 'oc',
	'oji' => 'oj',
	'orm' => 'om',
	'ori' => 'or',
	'oss' => 'os',
	'pan' => 'pa',
	'pli' => 'pi',
	'pol' => 'pl',
	'pus' => 'ps',
	'por' => 'pt',
	'que' => 'qu',
	'roh' => 'rm',
	'run' => 'rn',
	'ron' => 'ro',
	'rus' => 'ru',
	'kin' => 'rw',
	'san' => 'sa',
	'srd' => 'sc',
	'snd' => 'sd',
	'sme' => 'se',
	'sag' => 'sg',
	'hbs' => 'sr_Latn',
	'sin' => 'si',
	'slk' => 'sk',
	'slv' => 'sl',
	'smo' => 'sm',
	'sna' => 'sn',
	'som' => 'so',
	'sqi' => 'sq',
	'srp' => 'sr',
	'ssw' => 'ss',
	'sot' => 'st',
	'sun' => 'su',
	'swe' => 'sv',
	'swa' => 'sw',
	'tam' => 'ta',
	'tel' => 'te',
	'tgk' => 'tg',
	'tha' => 'th',
	'tir' => 'ti',
	'tuk' => 'tk',
	'tgl' => 'fil',
	'tsn' => 'tn',
	'ton' => 'to',
	'tur' => 'tr',
	'tso' => 'ts',
	'tat' => 'tt',
	'twi' => 'ak',
	'tah' => 'ty',
	'uig' => 'ug',
	'ukr' => 'uk',
	'urd' => 'ur',
	'uzb' => 'uz',
	'ven' => 've',
	'vie' => 'vi',
	'vol' => 'vo',
	'wln' => 'wa',
	'wol' => 'wo',
	'xho' => 'xh',
	'yid' => 'yi',
	'yor' => 'yo',
	'zha' => 'za',
	'zho' => 'zh',
	'zul' => 'zu',
	'alb' => 'sq',
	'arm' => 'hy',
	'baq' => 'eu',
	'bur' => 'my',
	'chi' => 'zh',
	'cze' => 'cs',
	'dut' => 'nl',
	'fre' => 'fr',
	'geo' => 'ka',
	'ger' => 'de',
	'gre' => 'el',
	'ice' => 'is',
	'mac' => 'mk',
	'mao' => 'mi',
	'may' => 'ms',
	'per' => 'fa',
	'rum' => 'ro',
	'slo' => 'sk',
	'tib' => 'bo',
	'wel' => 'cy',
	'cel_gaulish' => 'xtg',
	'i_default' => 'en_x_i_default',
	'i_enochian' => 'und_x_i_enochian',
	'i_mingo' => 'see_x_i_mingo',
	'zh_min' => 'nan_x_zh_min',
	'und_aaland' => 'und_AX',
	'hy_arevmda' => 'hyw',
	'und_arevmda' => 'und',
	'und_arevela' => 'und',
	'und_lojban' => 'und',
	'und_saaho' => 'und',
	'und_bokmal' => 'und',
	'und_nynorsk' => 'und',
	'und_hakka' => 'und',
	'und_xiang' => 'und',
	'und_hepburn_heploc' => 'und_alalc97',
	'ajp' => 'apc',
	'kgm' => 'plu',
	'nom' => 'cbr',
	'pmk' => 'crr',
	'prp' => 'gu',
	'szd' => 'umi',
	'tmk' => 'tdg',
	'tpw' => 'tpn',
	'xss' => 'zko',
	'zkb' => 'kjh',
	}},
);
has 'region_aliases' => (
	is			=> 'ro',
	isa			=> HashRef,
	init_arg	=> undef,
	default	=> sub { return {
	'AN' => [qw(CW SX BQ)],
	'BU' => [qw(MM)],
	'CS' => [qw(RS ME)],
	'CT' => [qw(KI)],
	'DD' => [qw(DE)],
	'DY' => [qw(BJ)],
	'FQ' => [qw(AQ TF)],
	'FX' => [qw(FR)],
	'HV' => [qw(BF)],
	'JT' => [qw(UM)],
	'MI' => [qw(UM)],
	'NH' => [qw(VU)],
	'NQ' => [qw(AQ)],
	'NT' => [qw(SA IQ)],
	'PC' => [qw(FM MH MP PW)],
	'PU' => [qw(UM)],
	'PZ' => [qw(PA)],
	'QU' => [qw(EU)],
	'RH' => [qw(ZW)],
	'SU' => [qw(RU AM AZ BY EE GE KZ KG LV LT MD TJ TM UA UZ)],
	'TP' => [qw(TL)],
	'UK' => [qw(GB)],
	'VD' => [qw(VN)],
	'WK' => [qw(UM)],
	'YD' => [qw(YE)],
	'YU' => [qw(RS ME)],
	'ZR' => [qw(CD)],
	'062' => [qw(034 143)],
	'172' => [qw(RU AM AZ BY GE KG KZ MD TJ TM UA UZ)],
	'200' => [qw(CZ SK)],
	'230' => [qw(ET)],
	'280' => [qw(DE)],
	'532' => [qw(CW SX BQ)],
	'582' => [qw(FM MH MP PW)],
	'736' => [qw(SD)],
	'830' => [qw(JE GG)],
	'886' => [qw(YE)],
	'890' => [qw(RS ME SI HR MK BA)],
	'AAA' => [qw(AA)],
	'ASC' => [qw(AC)],
	'AND' => [qw(AD)],
	'ARE' => [qw(AE)],
	'AFG' => [qw(AF)],
	'ATG' => [qw(AG)],
	'AIA' => [qw(AI)],
	'ALB' => [qw(AL)],
	'ARM' => [qw(AM)],
	'ANT' => [qw(CW SX BQ)],
	'AGO' => [qw(AO)],
	'ATA' => [qw(AQ)],
	'ARG' => [qw(AR)],
	'ASM' => [qw(AS)],
	'AUT' => [qw(AT)],
	'AUS' => [qw(AU)],
	'ABW' => [qw(AW)],
	'ALA' => [qw(AX)],
	'AZE' => [qw(AZ)],
	'BIH' => [qw(BA)],
	'BRB' => [qw(BB)],
	'BGD' => [qw(BD)],
	'BEL' => [qw(BE)],
	'BFA' => [qw(BF)],
	'BGR' => [qw(BG)],
	'BHR' => [qw(BH)],
	'BDI' => [qw(BI)],
	'BEN' => [qw(BJ)],
	'BLM' => [qw(BL)],
	'BMU' => [qw(BM)],
	'BRN' => [qw(BN)],
	'BOL' => [qw(BO)],
	'BES' => [qw(BQ)],
	'BRA' => [qw(BR)],
	'BHS' => [qw(BS)],
	'BTN' => [qw(BT)],
	'BUR' => [qw(MM)],
	'BVT' => [qw(BV)],
	'BWA' => [qw(BW)],
	'BLR' => [qw(BY)],
	'BLZ' => [qw(BZ)],
	'CAN' => [qw(CA)],
	'CCK' => [qw(CC)],
	'COD' => [qw(CD)],
	'CAF' => [qw(CF)],
	'COG' => [qw(CG)],
	'CHE' => [qw(CH)],
	'CIV' => [qw(CI)],
	'COK' => [qw(CK)],
	'CHL' => [qw(CL)],
	'CMR' => [qw(CM)],
	'CHN' => [qw(CN)],
	'COL' => [qw(CO)],
	'CPT' => [qw(CP)],
	'CRI' => [qw(CR)],
	'SCG' => [qw(RS ME)],
	'CUB' => [qw(CU)],
	'CPV' => [qw(CV)],
	'CUW' => [qw(CW)],
	'CXR' => [qw(CX)],
	'CYP' => [qw(CY)],
	'CZE' => [qw(CZ)],
	'DDR' => [qw(DE)],
	'DEU' => [qw(DE)],
	'DGA' => [qw(DG)],
	'DJI' => [qw(DJ)],
	'DNK' => [qw(DK)],
	'DMA' => [qw(DM)],
	'DOM' => [qw(DO)],
	'DZA' => [qw(DZ)],
	'ECU' => [qw(EC)],
	'EST' => [qw(EE)],
	'EGY' => [qw(EG)],
	'ESH' => [qw(EH)],
	'ERI' => [qw(ER)],
	'ESP' => [qw(ES)],
	'ETH' => [qw(ET)],
	'FIN' => [qw(FI)],
	'FJI' => [qw(FJ)],
	'FLK' => [qw(FK)],
	'FSM' => [qw(FM)],
	'FRO' => [qw(FO)],
	'FRA' => [qw(FR)],
	'FXX' => [qw(FR)],
	'GAB' => [qw(GA)],
	'GBR' => [qw(GB)],
	'GRD' => [qw(GD)],
	'GEO' => [qw(GE)],
	'GUF' => [qw(GF)],
	'GGY' => [qw(GG)],
	'GHA' => [qw(GH)],
	'GIB' => [qw(GI)],
	'GRL' => [qw(GL)],
	'GMB' => [qw(GM)],
	'GIN' => [qw(GN)],
	'GLP' => [qw(GP)],
	'GNQ' => [qw(GQ)],
	'GRC' => [qw(GR)],
	'SGS' => [qw(GS)],
	'GTM' => [qw(GT)],
	'GUM' => [qw(GU)],
	'GNB' => [qw(GW)],
	'GUY' => [qw(GY)],
	'HKG' => [qw(HK)],
	'HMD' => [qw(HM)],
	'HND' => [qw(HN)],
	'HRV' => [qw(HR)],
	'HTI' => [qw(HT)],
	'HUN' => [qw(HU)],
	'IDN' => [qw(ID)],
	'IRL' => [qw(IE)],
	'ISR' => [qw(IL)],
	'IMN' => [qw(IM)],
	'IND' => [qw(IN)],
	'IOT' => [qw(IO)],
	'IRQ' => [qw(IQ)],
	'IRN' => [qw(IR)],
	'ISL' => [qw(IS)],
	'ITA' => [qw(IT)],
	'JEY' => [qw(JE)],
	'JAM' => [qw(JM)],
	'JOR' => [qw(JO)],
	'JPN' => [qw(JP)],
	'KEN' => [qw(KE)],
	'KGZ' => [qw(KG)],
	'KHM' => [qw(KH)],
	'KIR' => [qw(KI)],
	'COM' => [qw(KM)],
	'KNA' => [qw(KN)],
	'PRK' => [qw(KP)],
	'KOR' => [qw(KR)],
	'KWT' => [qw(KW)],
	'CYM' => [qw(KY)],
	'KAZ' => [qw(KZ)],
	'LAO' => [qw(LA)],
	'LBN' => [qw(LB)],
	'LCA' => [qw(LC)],
	'LIE' => [qw(LI)],
	'LKA' => [qw(LK)],
	'LBR' => [qw(LR)],
	'LSO' => [qw(LS)],
	'LTU' => [qw(LT)],
	'LUX' => [qw(LU)],
	'LVA' => [qw(LV)],
	'LBY' => [qw(LY)],
	'MAR' => [qw(MA)],
	'MCO' => [qw(MC)],
	'MDA' => [qw(MD)],
	'MNE' => [qw(ME)],
	'MAF' => [qw(MF)],
	'MDG' => [qw(MG)],
	'MHL' => [qw(MH)],
	'MKD' => [qw(MK)],
	'MLI' => [qw(ML)],
	'MMR' => [qw(MM)],
	'MNG' => [qw(MN)],
	'MAC' => [qw(MO)],
	'MNP' => [qw(MP)],
	'MTQ' => [qw(MQ)],
	'MRT' => [qw(MR)],
	'MSR' => [qw(MS)],
	'MLT' => [qw(MT)],
	'MUS' => [qw(MU)],
	'MDV' => [qw(MV)],
	'MWI' => [qw(MW)],
	'MEX' => [qw(MX)],
	'MYS' => [qw(MY)],
	'MOZ' => [qw(MZ)],
	'NAM' => [qw(NA)],
	'NCL' => [qw(NC)],
	'NER' => [qw(NE)],
	'NFK' => [qw(NF)],
	'NGA' => [qw(NG)],
	'NIC' => [qw(NI)],
	'NLD' => [qw(NL)],
	'NOR' => [qw(NO)],
	'NPL' => [qw(NP)],
	'NRU' => [qw(NR)],
	'NTZ' => [qw(SA IQ)],
	'NIU' => [qw(NU)],
	'NZL' => [qw(NZ)],
	'OMN' => [qw(OM)],
	'PAN' => [qw(PA)],
	'PER' => [qw(PE)],
	'PYF' => [qw(PF)],
	'PNG' => [qw(PG)],
	'PHL' => [qw(PH)],
	'PAK' => [qw(PK)],
	'POL' => [qw(PL)],
	'SPM' => [qw(PM)],
	'PCN' => [qw(PN)],
	'PRI' => [qw(PR)],
	'PSE' => [qw(PS)],
	'PRT' => [qw(PT)],
	'PLW' => [qw(PW)],
	'PRY' => [qw(PY)],
	'QAT' => [qw(QA)],
	'QMM' => [qw(QM)],
	'QNN' => [qw(QN)],
	'QPP' => [qw(QP)],
	'QQQ' => [qw(QQ)],
	'QRR' => [qw(QR)],
	'QSS' => [qw(QS)],
	'QTT' => [qw(QT)],
	'QUU' => [qw(EU)],
	'QVV' => [qw(QV)],
	'QWW' => [qw(QW)],
	'QXX' => [qw(QX)],
	'QYY' => [qw(QY)],
	'QZZ' => [qw(QZ)],
	'REU' => [qw(RE)],
	'ROU' => [qw(RO)],
	'SRB' => [qw(RS)],
	'RUS' => [qw(RU)],
	'RWA' => [qw(RW)],
	'SAU' => [qw(SA)],
	'SLB' => [qw(SB)],
	'SYC' => [qw(SC)],
	'SDN' => [qw(SD)],
	'SWE' => [qw(SE)],
	'SGP' => [qw(SG)],
	'SHN' => [qw(SH)],
	'SVN' => [qw(SI)],
	'SJM' => [qw(SJ)],
	'SVK' => [qw(SK)],
	'SLE' => [qw(SL)],
	'SMR' => [qw(SM)],
	'SEN' => [qw(SN)],
	'SOM' => [qw(SO)],
	'SUR' => [qw(SR)],
	'SSD' => [qw(SS)],
	'STP' => [qw(ST)],
	'SUN' => [qw(RU AM AZ BY EE GE KZ KG LV LT MD TJ TM UA UZ)],
	'SLV' => [qw(SV)],
	'SXM' => [qw(SX)],
	'SYR' => [qw(SY)],
	'SWZ' => [qw(SZ)],
	'TAA' => [qw(TA)],
	'TCA' => [qw(TC)],
	'TCD' => [qw(TD)],
	'ATF' => [qw(TF)],
	'TGO' => [qw(TG)],
	'THA' => [qw(TH)],
	'TJK' => [qw(TJ)],
	'TKL' => [qw(TK)],
	'TLS' => [qw(TL)],
	'TKM' => [qw(TM)],
	'TUN' => [qw(TN)],
	'TON' => [qw(TO)],
	'TMP' => [qw(TL)],
	'TUR' => [qw(TR)],
	'TTO' => [qw(TT)],
	'TUV' => [qw(TV)],
	'TWN' => [qw(TW)],
	'TZA' => [qw(TZ)],
	'UKR' => [qw(UA)],
	'UGA' => [qw(UG)],
	'UMI' => [qw(UM)],
	'USA' => [qw(US)],
	'URY' => [qw(UY)],
	'UZB' => [qw(UZ)],
	'VAT' => [qw(VA)],
	'VCT' => [qw(VC)],
	'VEN' => [qw(VE)],
	'VGB' => [qw(VG)],
	'VIR' => [qw(VI)],
	'VNM' => [qw(VN)],
	'VUT' => [qw(VU)],
	'WLF' => [qw(WF)],
	'WSM' => [qw(WS)],
	'XAA' => [qw(XA)],
	'XBB' => [qw(XB)],
	'XCC' => [qw(XC)],
	'XDD' => [qw(XD)],
	'XEE' => [qw(XE)],
	'XFF' => [qw(XF)],
	'XGG' => [qw(XG)],
	'XHH' => [qw(XH)],
	'XII' => [qw(XI)],
	'XJJ' => [qw(XJ)],
	'XKK' => [qw(XK)],
	'XLL' => [qw(XL)],
	'XMM' => [qw(XM)],
	'XNN' => [qw(XN)],
	'XOO' => [qw(XO)],
	'XPP' => [qw(XP)],
	'XQQ' => [qw(XQ)],
	'XRR' => [qw(XR)],
	'XSS' => [qw(XS)],
	'XTT' => [qw(XT)],
	'XUU' => [qw(XU)],
	'XVV' => [qw(XV)],
	'XWW' => [qw(XW)],
	'XXX' => [qw(XX)],
	'XYY' => [qw(XY)],
	'XZZ' => [qw(XZ)],
	'YMD' => [qw(YE)],
	'YEM' => [qw(YE)],
	'MYT' => [qw(YT)],
	'YUG' => [qw(RS ME)],
	'ZAF' => [qw(ZA)],
	'ZMB' => [qw(ZM)],
	'ZAR' => [qw(CD)],
	'ZWE' => [qw(ZW)],
	'ZZZ' => [qw(ZZ)],
	'958' => [qw(AA)],
	'020' => [qw(AD)],
	'784' => [qw(AE)],
	'004' => [qw(AF)],
	'028' => [qw(AG)],
	'660' => [qw(AI)],
	'008' => [qw(AL)],
	'051' => [qw(AM)],
	'530' => [qw(CW SX BQ)],
	'024' => [qw(AO)],
	'010' => [qw(AQ)],
	'032' => [qw(AR)],
	'016' => [qw(AS)],
	'040' => [qw(AT)],
	'036' => [qw(AU)],
	'533' => [qw(AW)],
	'248' => [qw(AX)],
	'031' => [qw(AZ)],
	'070' => [qw(BA)],
	'052' => [qw(BB)],
	'050' => [qw(BD)],
	'056' => [qw(BE)],
	'854' => [qw(BF)],
	'100' => [qw(BG)],
	'048' => [qw(BH)],
	'108' => [qw(BI)],
	'204' => [qw(BJ)],
	'652' => [qw(BL)],
	'060' => [qw(BM)],
	'096' => [qw(BN)],
	'068' => [qw(BO)],
	'535' => [qw(BQ)],
	'076' => [qw(BR)],
	'044' => [qw(BS)],
	'064' => [qw(BT)],
	'104' => [qw(MM)],
	'074' => [qw(BV)],
	'072' => [qw(BW)],
	'112' => [qw(BY)],
	'084' => [qw(BZ)],
	'124' => [qw(CA)],
	'166' => [qw(CC)],
	'180' => [qw(CD)],
	'140' => [qw(CF)],
	'178' => [qw(CG)],
	'756' => [qw(CH)],
	'384' => [qw(CI)],
	'184' => [qw(CK)],
	'152' => [qw(CL)],
	'120' => [qw(CM)],
	'156' => [qw(CN)],
	'170' => [qw(CO)],
	'188' => [qw(CR)],
	'891' => [qw(RS ME)],
	'192' => [qw(CU)],
	'132' => [qw(CV)],
	'531' => [qw(CW)],
	'162' => [qw(CX)],
	'196' => [qw(CY)],
	'203' => [qw(CZ)],
	'278' => [qw(DE)],
	'276' => [qw(DE)],
	'262' => [qw(DJ)],
	'208' => [qw(DK)],
	'212' => [qw(DM)],
	'214' => [qw(DO)],
	'012' => [qw(DZ)],
	'218' => [qw(EC)],
	'233' => [qw(EE)],
	'818' => [qw(EG)],
	'732' => [qw(EH)],
	'232' => [qw(ER)],
	'724' => [qw(ES)],
	'231' => [qw(ET)],
	'246' => [qw(FI)],
	'242' => [qw(FJ)],
	'238' => [qw(FK)],
	'583' => [qw(FM)],
	'234' => [qw(FO)],
	'250' => [qw(FR)],
	'249' => [qw(FR)],
	'266' => [qw(GA)],
	'826' => [qw(GB)],
	'308' => [qw(GD)],
	'268' => [qw(GE)],
	'254' => [qw(GF)],
	'831' => [qw(GG)],
	'288' => [qw(GH)],
	'292' => [qw(GI)],
	'304' => [qw(GL)],
	'270' => [qw(GM)],
	'324' => [qw(GN)],
	'312' => [qw(GP)],
	'226' => [qw(GQ)],
	'300' => [qw(GR)],
	'239' => [qw(GS)],
	'320' => [qw(GT)],
	'316' => [qw(GU)],
	'624' => [qw(GW)],
	'328' => [qw(GY)],
	'344' => [qw(HK)],
	'334' => [qw(HM)],
	'340' => [qw(HN)],
	'191' => [qw(HR)],
	'332' => [qw(HT)],
	'348' => [qw(HU)],
	'360' => [qw(ID)],
	'372' => [qw(IE)],
	'376' => [qw(IL)],
	'833' => [qw(IM)],
	'356' => [qw(IN)],
	'086' => [qw(IO)],
	'368' => [qw(IQ)],
	'364' => [qw(IR)],
	'352' => [qw(IS)],
	'380' => [qw(IT)],
	'832' => [qw(JE)],
	'388' => [qw(JM)],
	'400' => [qw(JO)],
	'392' => [qw(JP)],
	'404' => [qw(KE)],
	'417' => [qw(KG)],
	'116' => [qw(KH)],
	'296' => [qw(KI)],
	'174' => [qw(KM)],
	'659' => [qw(KN)],
	'408' => [qw(KP)],
	'410' => [qw(KR)],
	'414' => [qw(KW)],
	'136' => [qw(KY)],
	'398' => [qw(KZ)],
	'418' => [qw(LA)],
	'422' => [qw(LB)],
	'662' => [qw(LC)],
	'438' => [qw(LI)],
	'144' => [qw(LK)],
	'430' => [qw(LR)],
	'426' => [qw(LS)],
	'440' => [qw(LT)],
	'442' => [qw(LU)],
	'428' => [qw(LV)],
	'434' => [qw(LY)],
	'504' => [qw(MA)],
	'492' => [qw(MC)],
	'498' => [qw(MD)],
	'499' => [qw(ME)],
	'663' => [qw(MF)],
	'450' => [qw(MG)],
	'584' => [qw(MH)],
	'807' => [qw(MK)],
	'466' => [qw(ML)],
	'496' => [qw(MN)],
	'446' => [qw(MO)],
	'580' => [qw(MP)],
	'474' => [qw(MQ)],
	'478' => [qw(MR)],
	'500' => [qw(MS)],
	'470' => [qw(MT)],
	'480' => [qw(MU)],
	'462' => [qw(MV)],
	'454' => [qw(MW)],
	'484' => [qw(MX)],
	'458' => [qw(MY)],
	'508' => [qw(MZ)],
	'516' => [qw(NA)],
	'540' => [qw(NC)],
	'562' => [qw(NE)],
	'574' => [qw(NF)],
	'566' => [qw(NG)],
	'558' => [qw(NI)],
	'528' => [qw(NL)],
	'578' => [qw(NO)],
	'524' => [qw(NP)],
	'520' => [qw(NR)],
	'536' => [qw(SA IQ)],
	'570' => [qw(NU)],
	'554' => [qw(NZ)],
	'512' => [qw(OM)],
	'591' => [qw(PA)],
	'604' => [qw(PE)],
	'258' => [qw(PF)],
	'598' => [qw(PG)],
	'608' => [qw(PH)],
	'586' => [qw(PK)],
	'616' => [qw(PL)],
	'666' => [qw(PM)],
	'612' => [qw(PN)],
	'630' => [qw(PR)],
	'275' => [qw(PS)],
	'620' => [qw(PT)],
	'585' => [qw(PW)],
	'600' => [qw(PY)],
	'634' => [qw(QA)],
	'959' => [qw(QM)],
	'960' => [qw(QN)],
	'962' => [qw(QP)],
	'963' => [qw(QQ)],
	'964' => [qw(QR)],
	'965' => [qw(QS)],
	'966' => [qw(QT)],
	'967' => [qw(EU)],
	'968' => [qw(QV)],
	'969' => [qw(QW)],
	'970' => [qw(QX)],
	'971' => [qw(QY)],
	'972' => [qw(QZ)],
	'638' => [qw(RE)],
	'642' => [qw(RO)],
	'688' => [qw(RS)],
	'643' => [qw(RU)],
	'646' => [qw(RW)],
	'682' => [qw(SA)],
	'090' => [qw(SB)],
	'690' => [qw(SC)],
	'729' => [qw(SD)],
	'752' => [qw(SE)],
	'702' => [qw(SG)],
	'654' => [qw(SH)],
	'705' => [qw(SI)],
	'744' => [qw(SJ)],
	'703' => [qw(SK)],
	'694' => [qw(SL)],
	'674' => [qw(SM)],
	'686' => [qw(SN)],
	'706' => [qw(SO)],
	'740' => [qw(SR)],
	'728' => [qw(SS)],
	'678' => [qw(ST)],
	'810' => [qw(RU AM AZ BY EE GE KZ KG LV LT MD TJ TM UA UZ)],
	'222' => [qw(SV)],
	'534' => [qw(SX)],
	'760' => [qw(SY)],
	'748' => [qw(SZ)],
	'796' => [qw(TC)],
	'148' => [qw(TD)],
	'260' => [qw(TF)],
	'768' => [qw(TG)],
	'764' => [qw(TH)],
	'762' => [qw(TJ)],
	'772' => [qw(TK)],
	'626' => [qw(TL)],
	'795' => [qw(TM)],
	'788' => [qw(TN)],
	'776' => [qw(TO)],
	'792' => [qw(TR)],
	'780' => [qw(TT)],
	'798' => [qw(TV)],
	'158' => [qw(TW)],
	'834' => [qw(TZ)],
	'804' => [qw(UA)],
	'800' => [qw(UG)],
	'581' => [qw(UM)],
	'840' => [qw(US)],
	'858' => [qw(UY)],
	'860' => [qw(UZ)],
	'336' => [qw(VA)],
	'670' => [qw(VC)],
	'862' => [qw(VE)],
	'092' => [qw(VG)],
	'850' => [qw(VI)],
	'704' => [qw(VN)],
	'548' => [qw(VU)],
	'876' => [qw(WF)],
	'882' => [qw(WS)],
	'973' => [qw(XA)],
	'974' => [qw(XB)],
	'975' => [qw(XC)],
	'976' => [qw(XD)],
	'977' => [qw(XE)],
	'978' => [qw(XF)],
	'979' => [qw(XG)],
	'980' => [qw(XH)],
	'981' => [qw(XI)],
	'982' => [qw(XJ)],
	'983' => [qw(XK)],
	'984' => [qw(XL)],
	'985' => [qw(XM)],
	'986' => [qw(XN)],
	'987' => [qw(XO)],
	'988' => [qw(XP)],
	'989' => [qw(XQ)],
	'990' => [qw(XR)],
	'991' => [qw(XS)],
	'992' => [qw(XT)],
	'993' => [qw(XU)],
	'994' => [qw(XV)],
	'995' => [qw(XW)],
	'996' => [qw(XX)],
	'997' => [qw(XY)],
	'998' => [qw(XZ)],
	'720' => [qw(YE)],
	'887' => [qw(YE)],
	'175' => [qw(YT)],
	'710' => [qw(ZA)],
	'894' => [qw(ZM)],
	'716' => [qw(ZW)],
	'999' => [qw(ZZ)],
	}},
);
has 'variant_aliases' => (
	is			=> 'ro',
	isa			=> HashRef,
	init_arg	=> undef,
	default	=> sub { return {
		bokmal		=> { language	=> 'nb' },
		nynorsk		=> { language	=> 'nn' },
		aaland		=> { region	=> 'AX' },
		polytoni	=> { variant	=> 'POLYTON' },
		saaho		=> { language	=> 'ssy' },
	}},
);
no Moo::Role;

1;

# vim: tabstop=4
