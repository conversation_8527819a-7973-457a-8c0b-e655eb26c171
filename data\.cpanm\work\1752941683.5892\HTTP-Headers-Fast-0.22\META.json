{"abstract": "faster implementation of HTTP::Headers", "author": ["<PERSON><PERSON><PERSON> <<EMAIL>>"], "dynamic_config": 0, "generated_by": "Minilla/v3.1.4", "license": ["perl_5"], "meta-spec": {"url": "http://search.cpan.org/perldoc?CPAN::Meta::Spec", "version": "2"}, "name": "HTTP-Headers-Fast", "no_index": {"directory": ["t", "xt", "inc", "share", "eg", "examples", "author", "builder"]}, "prereqs": {"configure": {"requires": {"Module::Build::Tiny": "0.035"}}, "develop": {"requires": {"Test::CPAN::Meta": "0", "Test::MinimumVersion::Fast": "0.04", "Test::PAUSE::Permissions": "0.04", "Test::Pod": "1.41", "Test::Spellunker": "v0.2.7"}}, "runtime": {"requires": {"HTTP::Date": "0", "perl": "5.008001"}}, "test": {"requires": {"Test::More": "0.98", "Test::Requires": "0"}}}, "provides": {"HTTP::Headers::Fast": {"file": "lib/HTTP/Headers/Fast.pm", "version": "0.22"}}, "release_status": "stable", "resources": {"bugtracker": {"web": "https://github.com/tokuhirom/HTTP-Headers-Fast/issues"}, "homepage": "https://github.com/tokuhirom/HTTP-Headers-Fast", "repository": {"url": "git://github.com/tokuhirom/HTTP-Headers-Fast.git", "web": "https://github.com/tokuhirom/HTTP-Headers-Fast"}}, "version": "0.22", "x_contributors": ["<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Sawyer X <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "daisuke <daisuke@d0d07461-0603-4401-acd4-de1884942a52>", "tokuhirom <tokuhirom@d0d07461-0603-4401-acd4-de1884942a52>", "yappo <yappo@d0d07461-0603-4401-acd4-de1884942a52>"], "x_serialization_backend": "JSON::PP version 2.27400", "x_static_install": 1}