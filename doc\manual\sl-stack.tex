\setlength{\unitlength}{3947sp}%
%
\begingroup\makeatletter\ifx\SetFigFont\undefined%
\gdef\SetFigFont#1#2#3#4#5{%
  \reset@font\fontsize{#1}{#2pt}%
  \fontfamily{#3}\fontseries{#4}\fontshape{#5}%
  \selectfont}%
\fi\endgroup%
\begin{picture}(4824,4824)(3589,-5173)
\thinlines
{\color[rgb]{0,0,0}\put(3601,-1261){\framebox(4800,900){}}
}%
{\color[rgb]{0,0,0}\put(6001,-3961){\framebox(2400,1200){}}
}%
{\color[rgb]{0,0,0}\put(3601,-5161){\framebox(4800,1200){}}
}%
{\color[rgb]{0,0,0}\put(3601,-2761){\framebox(4800,1500){}}
}%
{\color[rgb]{0,0,0}\put(3601,-3961){\framebox(2400,1200){}}
}%
\put(7201,-3361){\makebox(0,0)[b]{\smash{\SetFigFont{17}{20.4}{\rmdefault}{\mddefault}{\updefault}{\color[rgb]{0,0,0}PostgreSQL}%
}}}
\put(6001,-4561){\makebox(0,0)[b]{\smash{\SetFigFont{17}{20.4}{\rmdefault}{\mddefault}{\updefault}{\color[rgb]{0,0,0}Operating System}%
}}}
\put(6001,-811){\makebox(0,0)[b]{\smash{\SetFigFont{17}{20.4}{\rmdefault}{\mddefault}{\updefault}{\color[rgb]{0,0,0}SQL-Ledger}%
}}}
\put(4201,-3361){\makebox(0,0)[lb]{\smash{\SetFigFont{17}{20.4}{\rmdefault}{\mddefault}{\updefault}{\color[rgb]{0,0,0}Apache}%
}}}
\put(6001,-1936){\makebox(0,0)[b]{\smash{\SetFigFont{17}{20.4}{\rmdefault}{\mddefault}{\updefault}{\color[rgb]{0,0,0}Perl}%
}}}
\end{picture}
