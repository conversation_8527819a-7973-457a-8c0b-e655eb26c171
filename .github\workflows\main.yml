# This is a basic workflow to help you get started with Actions

name: CI

# Controls when the action will run.
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  push:
    tags:
      - '*'
    paths-ignore:
      - '.circleci'
    branches-ignore:
      # Run only renovate PR
      - 'renovate/*'
  pull_request:
    branches:
      - '*'
    paths-ignore:
      - '.circleci'
    types: [opened, reopened, synchronize]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

env:
  PGDB: lsmb_test
  PGHOST: localhost
  PGPASSWORD: test
  PGPORT: 5432
  PGUSER: postgres
  JOB_COUNT: 5
  TEST2_UUID_NO_WARN: 1
  TEST_NO_VERIFY_SCHEMA: 1
  DEVEL_COVER_OPTIONS: -silent,1,+ignore,^/home/<USER>/tests,+ignore,^utils,+ignore,local/,+ignore,^/usr/,+ignore,^/opt/,+ignore,\\.lttc,+ignore,starman\\b,-summary,1

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  cancel-prior:
    if: github.repository != 'ledgersmb/LedgerSMB' || startsWith(github.ref, 'refs/pull')
    runs-on: ubuntu-latest
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@0.12.1
        with:
          access_token: ${{ github.token }}

  webpack:

    if: github.event.action != 'closed' || github.event.pull_request.merged == true

    runs-on: ubuntu-latest

    steps:
      # Dump environment variables & event
      - uses: hmarr/debug-action@v3

      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          # Keep aligned with what's in the dev image...
          node-version: '22'


      # This step absolutely MUST come before "uncaching" node_modules!
      # there are HTML files in the node_modules directory which otherwise
      # impact the value of the hash
      - name: Cache JS
        id: cache-js
        uses: actions/cache@v4
        with:
          path: UI/js
          key: js-${{ hashFiles('UI/js-src/**',
                                  'UI/src/**',
                                  'UI/css/**',
                                  'UI/*.html',
                                  'UI/**/*.html',
                                  'doc/sources/**') }}

      - name: Cache node_modules
        id: cache-node_modules
        uses: actions/cache@v4
        with:
          path: |
             UI/node_modules
          key: |
            modules-${{ hashFiles('UI/yarn.lock') }}

      - name: Build JS
        run: |
          make js_deps_install
          make lint
          make js
          make readme
        if: steps.cache-js.outputs.cache-hit != 'true' ||
            steps.cache-node_modules.outputs.cache-hit != 'true'

  # Prep-up the cache
  build-perl-env:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        perl: [ "5.40", "5.38", "5.36" ]

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Perl environment & start starman
        uses: ./.github/workflows/Install TEX & Perl
        with:
          perl-version: ${{ matrix.perl }}

  test-pure-perl:
    runs-on: ubuntu-latest
    needs: build-perl-env
    strategy:
      matrix:
        include:
          - perl: "5.38"
            JOB_COUNT: 5
            COVERAGE: 1

          - perl: "5.36"
            JOB_COUNT: 5

          - perl: "5.40"
            JOB_COUNT: 5

    env:
      RELEASE_TESTING: 1

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Perl environment & start starman
        uses: ./.github/workflows/Install TEX & Perl
        with:
          perl-version: ${{ matrix.perl }}
          DEVEL_COVER_OPTIONS: ${{ env.DEVEL_COVER_OPTIONS }}
          coverage: ${{ matrix.COVERAGE }}

      # Do perl tests
      - name: Run Pure Perl tests
        run: |
          make test TESTS="--no-progress --job-count $JOB_COUNT $YATH_DEVEL_COVER_OPTIONS t/ xt/[0-3]*"
        env:
          COVERAGE: ${{ matrix.COVERAGE }}
          LSMB_TEST_DB: 1
          DB_TESTING: 1
          JOB_COUNT: ${{ matrix.JOB_COUNT }}

        # Upload coverage data if needed
      - name: Upload coverage data
        run: |
          unset PERL5OPT
          cover -report coveralls
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          COVERALLS_FLAG_NAME: pure-perl
          COVERALLS_PARALLEL: true
        if: ${{ matrix.COVERAGE }}

      - name: Archive coverage artifacts
        uses: actions/upload-artifact@v4
        with:
          name: pure-perl-coverage ${{ matrix.perl }} ${{ matrix.BROWSER }}
          path: |
            cover_db/**
        if: ${{ matrix.COVERAGE }}

  test-database:
    runs-on: ubuntu-latest
    needs: build-perl-env
    strategy:
      matrix:
        include:
          - postgres: "15"
            perl: "5.38"
            JOB_COUNT: 5

    services:
      # Label used to access the service container
      postgres:
        # Docker database image
        image: ghcr.io/ledgersmb/dev-postgres:${{ matrix.postgres }}
        # Maps tcp port 5432 on service container to the host
        ports:
          - 5432:5432
        # Provide the password for postgres
        env:
          POSTGRES_PASSWORD: test
        # needed because the postgres container does not provide a healthcheck
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Perl environment
        uses: ./.github/workflows/Install TEX & Perl
        with:
          perl-version: ${{ matrix.perl }}

      # Do database tests
      - name: Run pgTAP database tests
        run: |
          make devtest TESTS="--no-progress --job-count $JOB_COUNT xt/42-*.pg"
        env:
          LSMB_TEST_DB: 1
          DB_TESTING: 1
          JOB_COUNT: ${{ matrix.JOB_COUNT }}

  test-webservices:
    runs-on: ubuntu-latest
    # needs webpack because of collectively managed node modules cache
    needs: [ build-perl-env, webpack ]
    strategy:
      matrix:
        include:
          - postgres: "15"
            perl: "5.38"
            JOB_COUNT: 5
            COVERAGE: 1

    env:
      LSMB_BASE_URL: http://lsmb:5000
      PSGI_BASE_URL: http://lsmb:5762
      REMOTE_SERVER_ADDRESS: 127.0.0.1

    services:
      # Label used to access the service container
      postgres:
        # Docker database image
        image: ghcr.io/ledgersmb/dev-postgres:${{ matrix.postgres }}
        # Maps tcp port 5432 on service container to the host
        ports:
          - 5432:5432
        # Provide the password for postgres
        env:
          POSTGRES_PASSWORD: test
        # needed because the postgres container does not provide a healthcheck
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: latest

      - name: Pre-run installation steps
        run: |
          # Set host alias
          hostname -i | awk '{printf("%s lsmb\n",$1)}' | sudo tee -a /etc/hosts

          # Set the correct LedgerSMB Server configuration
          cp doc/conf/ledgersmb.yaml ledgersmb.yaml
          sed -i -e 's/# schema: public/schema: xyz/ ; s/#stylesheet: ledgersmb.css/stylesheet: ledgersmb-test.css/' ledgersmb.yaml

          # Start 'nginx' reverse proxy
          mkdir logs
          nginx -c $GITHUB_WORKSPACE/doc/conf/webserver/nginx-github.conf \
                -p $GITHUB_WORKSPACE &

      - name: Cache node_modules
        id: cache-npm
        uses: actions/cache@v4
        with:
          path: |
             UI/node_modules
          key: |
            modules-${{ hashFiles('UI/yarn.lock') }}

      - name: Setup Perl environment & start starman
        uses: ./.github/workflows/Install TEX & Perl
        with:
          perl-version: ${{ matrix.perl }}
          DEVEL_COVER_OPTIONS: ${{ env.DEVEL_COVER_OPTIONS }}
          coverage: ${{ matrix.COVERAGE }}

      - name: Run API tests
        run: |
          PERL5LIB="lib:old/lib:$PERL5LIB" make jstest TESTS="$JSCOVERAGE"

      - name: Stop 'starman'
        run: |
          if [[ ! -e starman.pid ]]
          then
            echo "ERROR: Can't terminate Starman without PID file"
          else
            bash -x logs/kill-starman.sh
            echo "Waiting for Starman to terminate "
            while kill -0 $(cat starman.pid) >/dev/null 2>&1
            do
              echo -n "."
              sleep 1
            done
            echo " "
          fi

      # Make Coverage appear from root instead of UI to integrate in Coveralls
      - name: Fix UI Coverage report
        run: |
          if [[ -e UI/coverage/lcov.info ]]
          then
            sed -i -E "s~^SF:(js-src|src)/~SF:UI/\1/~g" UI/coverage/lcov.info
          fi
        if: ${{ matrix.COVERAGE }}

      # Send UI coverage before Perl
      - name: Upload UI coverage data
        uses: coverallsapp/github-action@v2.3.6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          flag-name: webservices-ui
          parallel: true
          file: ${{ github.workspace }}/UI/coverage/lcov.info
        if: ${{ matrix.COVERAGE }}

        # Upload coverage data if needed
      - name: Upload coverage data
        run: |
          unset PERL5OPT
          cover -report coveralls
          cover -report text > logs/coverage.txt
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          COVERALLS_FLAG_NAME: webservices-perl
          COVERALLS_PARALLEL: true
        if: ${{ matrix.COVERAGE }}

      - name: Archive coverage artifacts
        uses: actions/upload-artifact@v4
        with:
          name: webservice-coverage ${{ matrix.perl }} ${{ matrix.BROWSER }}
          path: |
            cover_db/**
        if: ${{ matrix.COVERAGE }}

  test-remainder:
    if: (github.event.action != 'closed' || github.event.pull_request.merged == true)
    runs-on: ubuntu-latest
    needs: [ webpack, build-perl-env ]
    timeout-minutes: 60

    # Service containers to run
    strategy:
      fail-fast: false
      matrix:
        include:
          - perl: "5.38"
            postgres: "14"
            BROWSER: "firefox"
            COVERAGE: 1
            JOB_COUNT: 3

          - perl: "5.36"
            postgres: "15"
            BROWSER: "firefox"
            COA_TESTING: 1
            JOB_COUNT: 5

          - perl: "5.40"
            postgres: "13"
            BROWSER: "chrome"
            DB_TESTING: 1
            JOB_COUNT: 5

    services:
      # Label used to access the service container
      postgres:
        # Docker database image
        image: ghcr.io/ledgersmb/dev-postgres:${{ matrix.postgres }}
        # Maps tcp port 5432 on service container to the host
        ports:
          - 5432:5432
        # Provide the password for postgres
        env:
          POSTGRES_PASSWORD: test
        # needed because the postgres container does not provide a healthcheck
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5

      mailhog:
        image: mailhog/mailhog:latest
        ports:
          - 1025:1025
          - 8025:8025

    env:
      BROWSER: ${{ matrix.BROWSER }}
      COA_TESTING: ${{ matrix.COA_TESTING }}
      DB_TESTING: ${{ matrix.DB_TESTING }}
      JOB_COUNT: ${{ matrix.JOB_COUNT }}
      LSMB_BASE_URL: http://lsmb:5000
      LSMB_NEW_DB: lsmb_test
      LSMB_NEW_DB_API: lsmb_test_api
      MONITOR_FILE: cpu-memory-usage
      PSGI_BASE_URL: http://lsmb:5762
      RELEASE_TESTING: 1
      REMOTE_SERVER_ADDR: 127.0.0.1
      SSMTP_FROMLINE_OVERRIDE: YES
      SSMTP_HOSTNAME: lsmb
      SSMTP_MAILHUB: lsmb:1025
      MH_SENDMAIL_SMTP_ADDR: lsmb:1025
      UIUSER: Jest
      UIPASSWORD: Tester

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Pre-run installation steps
        run: |
          # Set host alias
          hostname -i | awk '{printf("%s lsmb\n",$1)}' | sudo tee -a /etc/hosts

          # Set up 'sendmail'
          wget --quiet https://github.com/mailhog/mhsendmail/releases/download/v0.2.0/mhsendmail_linux_amd64
          sudo chmod +x mhsendmail_linux_amd64
          sudo mv mhsendmail_linux_amd64 /usr/local/bin/sendmail
          echo -e "To: you@lsmb\n" \
                  "Subject: sendmail test\n" \
                  "From: me@lsmb\n" "\n" \
                  "And here goes the e-mail body, test test test..\n" | \
            sendmail me@test
          curl lsmb:8025/api/v2/messages

          # Create logging and coverage directories
          mkdir -p logs/screens cover_db

          # Set the correct LedgerSMB Server configuration
          cp doc/conf/ledgersmb.yaml ledgersmb.yaml
          sed -i -e 's/# schema: public/schema: xyz/' ledgersmb.yaml

          # Start 'nginx' reverse proxy
          nginx -c $GITHUB_WORKSPACE/doc/conf/webserver/nginx-github.conf \
                -p $GITHUB_WORKSPACE &


      # This step MUST absolutely run before uncaching node_modules: that contains
      # HTML files which otherwise interact with the hash being generated
      - name: Cache JS
        id: cache-js
        uses: actions/cache@v4
        with:
          path: UI/js
          key: js-${{ hashFiles('UI/js-src/**',
                                  'UI/src/**',
                                  'UI/css/**',
                                  'UI/*.html',
                                  'UI/**/*.html',
                                  'doc/sources/**') }}

      - name: Cache node_modules
        id: cache-npm
        uses: actions/cache@v4
        with:
          path: |
             UI/node_modules
          key: |
            modules-${{ hashFiles('UI/yarn.lock') }}

      # This will start a hub and JOB_COUNT matrix.
      - name: Starting hub with ${{ matrix.BROWSER }}
        run: |
          docker compose \
              --file=utils/selenium/docker-compose.yml \
              --file=utils/selenium/docker-compose-${{ matrix.BROWSER }}.yml \
              up \
              --detach \
              --scale ${{ matrix.BROWSER }}=$JOB_COUNT \

      - name: Setup Perl environment & start starman
        uses: ./.github/workflows/Install TEX & Perl
        with:
          perl-version: ${{ matrix.perl }}
          DEVEL_COVER_OPTIONS: ${{ env.DEVEL_COVER_OPTIONS }}
          coverage: ${{ matrix.COVERAGE }}

      # Fix the condition to debug
      - name: Setup upterm session
        uses: mxschmitt/action-tmate@v3
        if: ${{ matrix.BROWSER == 'chrome' && 0 }}

      # Do perl tests
      - name: Run Perl + Database + Browser tests
        run: |
          make devtest BDD_OPTS="--Feature-tags='not (@wip or @extended or @exclude-${{ matrix.BROWSER }})'" TESTS="--no-progress --job-count $JOB_COUNT $YATH_DEVEL_COVER_OPTIONS xt/4{[01],[3-9]}* xt/[5-9]*"
        env:
          LSMB_TEST_DB: 1
          COA_TESTING: ${{ matrix.COA_TESTING }}
          COVERAGE: ${{ matrix.COVERAGE }}
          DB_TESTING: ${{ matrix.DB_TESTING }}

      - name: Stop 'starman'
        run: |
          if [[ ! -e starman.pid ]]
          then
            echo "ERROR: Can't terminate Starman without PID file"
          else
            bash -x logs/kill-starman.sh
            echo "Waiting for Starman to terminate "
            while kill -0 $(cat starman.pid) >/dev/null 2>&1
            do
              echo -n "."
              sleep 1
            done
            echo " "
          fi

      # Upload coverage data if needed
      - name: Upload coverage data
        run: |
          unset PERL5OPT
          cover -report coveralls
          cover -report text > logs/coverage.txt
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          COVERALLS_FLAG_NAME: remainder-perl
          COVERALLS_PARALLEL: true
        if: ${{ matrix.COVERAGE }}

      # Send logs & artifacts
      - name: Collect docker logs
        uses: jwalton/gh-docker-logs@v2
        with:
          dest: 'logs/docker-logs'
        if: always()

      - name: Archive production artifacts
        uses: actions/upload-artifact@v4
        with:
          name: logs-screens-coverage ${{ matrix.perl }} ${{ matrix.BROWSER }}
          path: |
            logs/**
            cover_db/**
        if: always()

  close-parallel-coverage:
    runs-on: ubuntu-latest
    needs: [ test-pure-perl, test-remainder, test-webservices ]
    steps:
      - name: Close parallel UI coverage data
        uses: coverallsapp/github-action@v2.3.6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          parallel-finished: true

      # Run the build hook again (Coveralls suggested patch)
      - name: Rerun Build Webhook
        run: |
          curl --location --request GET "https://coveralls.io/rerun_build?repo_token=${{ secrets.GITHUB_TOKEN }}&build_num=${GITHUB_RUN_ID}"

  testing-done:
    runs-on: ubuntu-latest
    needs: [ test-database, test-pure-perl, test-remainder, test-webservices ]
    if: failure()
    # This job gets skipped on successful completion of the dependent jobs
    # Skipped jobs are interpreted as 'success' condition in branch protection rules...
    steps:
      - name: Failed
        run: |
          exit 1

  build-dev:
    if: github.event_name == 'push' && github.ref == 'refs/heads/master' && github.repository == 'ledgersmb/LedgerSMB'

    runs-on: ubuntu-latest

    needs: test-remainder

    steps:

      - name: Checkout
        uses: actions/checkout@v4

      - name: Check if push must trigger
        id: pr_trigger
        shell: bash
        run: |
          URL="https://api.github.com/repos/${{ github.repository }}/commits/${{ github.sha }}"
          if [ -n "$(curl -s -X GET -G $URL | jq -r '.files[].filename' | grep 'cpanfile')" ]; then
            echo "Setting MUST_TRIGGER to 1"
            echo "MUST_TRIGGER=1" >> $GITHUB_OUTPUT
          else
            echo "MUST_TRIGGER failed; not set"
          fi

      - name: Repository Dispatch to the Development image
        uses: peter-evans/repository-dispatch@v3
        with:
          token: ${{ secrets.KICK_DEV_IMG_BUILD_TOKEN }}
          repository: ${{ github.repository_owner }}/ledgersmb-dev-docker
          event-type: master-updated
          client-payload: '{"ref": "${{ github.ref }}", "sha": "${{ github.sha }}"}'
        if: steps.pr_trigger.outputs.MUST_TRIGGER
