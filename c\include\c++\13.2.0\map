// <map> -*- C++ -*-

// Copyright (C) 2001-2023 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/*
 *
 * Copyright (c) 1994
 * Hewlett-Packard Company
 *
 * Permission to use, copy, modify, distribute and sell this software
 * and its documentation for any purpose is hereby granted without fee,
 * provided that the above copyright notice appear in all copies and
 * that both that copyright notice and this permission notice appear
 * in supporting documentation.  Hewlett-Packard Company makes no
 * representations about the suitability of this software for any
 * purpose.  It is provided "as is" without express or implied warranty.
 *
 *
 * Copyright (c) 1996,1997
 * Silicon Graphics Computer Systems, Inc.
 *
 * Permission to use, copy, modify, distribute and sell this software
 * and its documentation for any purpose is hereby granted without fee,
 * provided that the above copyright notice appear in all copies and
 * that both that copyright notice and this permission notice appear
 * in supporting documentation.  Silicon Graphics makes no
 * representations about the suitability of this software for any
 * purpose.  It is provided "as is" without express or implied warranty.
 */

/** @file include/map
 *  This is a Standard C++ Library header.
 */

#ifndef _GLIBCXX_MAP
#define _GLIBCXX_MAP 1

#pragma GCC system_header

#include <bits/requires_hosted.h> // containers

#include <bits/stl_tree.h>
#include <bits/stl_map.h>
#include <bits/stl_multimap.h>
#include <bits/range_access.h>
#include <bits/erase_if.h>

#ifdef _GLIBCXX_DEBUG
# include <debug/map>
#endif

#if __cplusplus >= 201703L
#include <bits/memory_resource.h>
namespace std _GLIBCXX_VISIBILITY(default)
{
_GLIBCXX_BEGIN_NAMESPACE_VERSION
  namespace pmr
  {
    template<typename _Key, typename _Tp, typename _Cmp = std::less<_Key>>
      using map
	= std::map<_Key, _Tp, _Cmp,
		   polymorphic_allocator<pair<const _Key, _Tp>>>;
    template<typename _Key, typename _Tp, typename _Cmp = std::less<_Key>>
      using multimap
	= std::multimap<_Key, _Tp, _Cmp,
			polymorphic_allocator<pair<const _Key, _Tp>>>;
  } // namespace pmr
_GLIBCXX_END_NAMESPACE_VERSION
} // namespace std
#endif // C++17

#if __cplusplus > 201703L
namespace std _GLIBCXX_VISIBILITY(default)
{
_GLIBCXX_BEGIN_NAMESPACE_VERSION
  template<typename _Key, typename _Tp, typename _Compare, typename _Alloc,
	   typename _Predicate>
    inline typename map<_Key, _Tp, _Compare, _Alloc>::size_type
    erase_if(map<_Key, _Tp, _Compare, _Alloc>& __cont, _Predicate __pred)
    {
      _GLIBCXX_STD_C::map<_Key, _Tp, _Compare, _Alloc>& __ucont = __cont;
      return __detail::__erase_nodes_if(__cont, __ucont, __pred);
    }

  template<typename _Key, typename _Tp, typename _Compare, typename _Alloc,
	   typename _Predicate>
    inline typename multimap<_Key, _Tp, _Compare, _Alloc>::size_type
    erase_if(multimap<_Key, _Tp, _Compare, _Alloc>& __cont, _Predicate __pred)
    {
      _GLIBCXX_STD_C::multimap<_Key, _Tp, _Compare, _Alloc>& __ucont = __cont;
      return __detail::__erase_nodes_if(__cont, __ucont, __pred);
    }
_GLIBCXX_END_NAMESPACE_VERSION
} // namespace std
#endif // C++20

#endif /* _GLIBCXX_MAP */
