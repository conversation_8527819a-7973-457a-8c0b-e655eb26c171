# This file was automatically generated by Dist::Zilla::Plugin::MakeMaker v6.025.
use strict;
use warnings;

use 5.012000;

use ExtUtils::MakeMaker;

use File::ShareDir::Install;
$File::ShareDir::Install::INCLUDE_DOTFILES = 1;
$File::ShareDir::Install::INCLUDE_DOTDIRS = 1;
install_share dist => "share";


my %WriteMakefileArgs = (
  "ABSTRACT" => "Perl Superglue for Web frameworks and Web Servers (PSGI toolkit)",
  "AUTHOR" => "<PERSON><PERSON><PERSON>",
  "CONFIGURE_REQUIRES" => {
    "ExtUtils::MakeMaker" => 0,
    "File::ShareDir::Install" => "0.06"
  },
  "DISTNAME" => "Plack",
  "EXE_FILES" => [
    "script/plackup"
  ],
  "LICENSE" => "perl",
  "MIN_PERL_VERSION" => "5.012000",
  "NAME" => "Plack",
  "PREREQ_PM" => {
    "Apache::LogFormat::Compiler" => "0.33",
    "Cookie::Baker" => "0.07",
    "Devel::StackTrace" => "1.23",
    "Devel::StackTrace::AsHTML" => "0.11",
    "File::ShareDir" => "1.00",
    "Filesys::Notify::Simple" => 0,
    "HTTP::Entity::Parser" => "0.25",
    "HTTP::Headers::Fast" => "0.18",
    "HTTP::Message" => "5.814",
    "HTTP::Tiny" => "0.034",
    "Hash::MultiValue" => "0.05",
    "Pod::Usage" => "1.36",
    "Stream::Buffered" => "0.02",
    "Test::TCP" => "2.15",
    "Try::Tiny" => 0,
    "URI" => "1.59",
    "WWW::Form::UrlEncoded" => "0.23",
    "parent" => 0
  },
  "TEST_REQUIRES" => {
    "Test::More" => "0.88",
    "Test::Requires" => 0
  },
  "VERSION" => "1.0051",
  "test" => {
    "TESTS" => "t/*.t t/HTTP-Message-PSGI/*.t t/HTTP-Server-PSGI/*.t t/Plack-Builder/*.t t/Plack-HTTPParser-PP/*.t t/Plack-Handler/*.t t/Plack-Loader/*.t t/Plack-MIME/*.t t/Plack-Middleware/*.t t/Plack-Middleware/cascade/*.t t/Plack-Middleware/recursive/*.t t/Plack-Middleware/stacktrace/*.t t/Plack-Request/*.t t/Plack-Response/*.t t/Plack-Runner/*.t t/Plack-TempBuffer/*.t t/Plack-Test/*.t t/Plack-Util/*.t"
  }
);


my %FallbackPrereqs = (
  "Apache::LogFormat::Compiler" => "0.33",
  "Cookie::Baker" => "0.07",
  "Devel::StackTrace" => "1.23",
  "Devel::StackTrace::AsHTML" => "0.11",
  "File::ShareDir" => "1.00",
  "Filesys::Notify::Simple" => 0,
  "HTTP::Entity::Parser" => "0.25",
  "HTTP::Headers::Fast" => "0.18",
  "HTTP::Message" => "5.814",
  "HTTP::Tiny" => "0.034",
  "Hash::MultiValue" => "0.05",
  "Pod::Usage" => "1.36",
  "Stream::Buffered" => "0.02",
  "Test::More" => "0.88",
  "Test::Requires" => 0,
  "Test::TCP" => "2.15",
  "Try::Tiny" => 0,
  "URI" => "1.59",
  "WWW::Form::UrlEncoded" => "0.23",
  "parent" => 0
);


unless ( eval { ExtUtils::MakeMaker->VERSION(6.63_03) } ) {
  delete $WriteMakefileArgs{TEST_REQUIRES};
  delete $WriteMakefileArgs{BUILD_REQUIRES};
  $WriteMakefileArgs{PREREQ_PM} = \%FallbackPrereqs;
}

delete $WriteMakefileArgs{CONFIGURE_REQUIRES}
  unless eval { ExtUtils::MakeMaker->VERSION(6.52) };

WriteMakefile(%WriteMakefileArgs);

{
package
MY;
use File::ShareDir::Install qw(postamble);
}
