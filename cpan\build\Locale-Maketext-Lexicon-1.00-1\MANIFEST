# This file was automatically generated by Dist::Zilla::Plugin::Manifest v5.013.
AUTHORS
Changes
LICENSE
MANIFEST
MANIFEST.SKIP
META.json
META.yml
Makefile.PL
README
dist.ini
docs/webl10n.html
docs/webl10n.zh-tw.html
lib/Locale/Maketext/Extract.pm
lib/Locale/Maketext/Extract/Plugin/Base.pm
lib/Locale/Maketext/Extract/Plugin/FormFu.pm
lib/Locale/Maketext/Extract/Plugin/Generic.pm
lib/Locale/Maketext/Extract/Plugin/Haml.pm
lib/Locale/Maketext/Extract/Plugin/Mason.pm
lib/Locale/Maketext/Extract/Plugin/PPI.pm
lib/Locale/Maketext/Extract/Plugin/Perl.pm
lib/Locale/Maketext/Extract/Plugin/TT2.pm
lib/Locale/Maketext/Extract/Plugin/TextTemplate.pm
lib/Locale/Maketext/Extract/Plugin/YAML.pm
lib/Locale/Maketext/Extract/Run.pm
lib/Locale/Maketext/Lexicon.pm
lib/Locale/Maketext/Lexicon/Auto.pm
lib/Locale/Maketext/Lexicon/Gettext.pm
lib/Locale/Maketext/Lexicon/Msgcat.pm
lib/Locale/Maketext/Lexicon/Tie.pm
script/xgettext.pl
t/1-basic.t
t/2-lmg.t
t/3-big-endian.t
t/4-encodings.t
t/5-extract.t
t/51-perlextract.t
t/52-extractpluginselection.t
t/55-runextract.t
t/6-gettext.t
t/61-badcharset.t
t/7-roundtrip.t
t/8-plugin-args.t
t/9-bug-import-for-subclasses.t
t/91-pod_test.t
t/T_L10N.pm
t/badcharset.po
t/comments.po
t/gencat.m
t/lib/Myi18n.pm
t/lib/Myi18n/en.po
t/locale/en/LC_MESSAGES/test.mo
t/locale/en/LC_MESSAGES/test_be.mo
t/locale/en/LC_MESSAGES/test_utf8.mo
t/locale/zh_CN/LC_MESSAGES/test.mo
t/locale/zh_CN/LC_MESSAGES/test_be.mo
t/locale/zh_CN/LC_MESSAGES/test_utf8.mo
t/locale/zh_TW/LC_MESSAGES/test.mo
t/locale/zh_TW/LC_MESSAGES/test_be.mo
t/locale/zh_TW/LC_MESSAGES/test_utf8.mo
t/messages.mo
t/messages.po
t/preload.t
t/release-eol.t
t/release-pod-syntax.t
