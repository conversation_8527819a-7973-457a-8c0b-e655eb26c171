name: Install TEX & Perl
description: "Install TEX & packages, Perl"

inputs:
  perl-version:
    description: "Required perl version"
    required: true
  DEVEL_COVER_OPTIONS:
    description: "Devel::Cover options base"
    required: true
  coverage:
    description: "Coverage indicator"
    default: ''

runs:
  using: "composite"
  steps:
    - name: Install TinyTex
      uses: r-lib/actions/setup-tinytex@v2

    - name: Setup Perl environment
      uses: shogo82148/actions-setup-perl@v1
      with:
        perl-version: ${{ inputs.perl-version }}
        install-modules-with: cpm
        # Test2 warns when not using Data::UUID::MT
        install-modules: |
          Devel::Cover
          Devel::Cover::Report::Coveralls
          Syntax::Keyword::Try::Deparse
          Data::UUID::MT
        # Features and Devel modules
        install-modules-args: >
          --notest
          --resolver=metacpan
          --with-develop
          --feature=starman
          --feature=latex-pdf-ps
          --feature=openoffice
          --feature=xls
          --feature=edi

    - name: Pre-run installation steps
      shell: bash
      run: |
        sudo apt -q -y install gettext

        # Install missing TeX packages
        tlmgr update --self
        tlmgr install koma-script

    - name: Setup coverage
      shell: bash
      run: |
        echo "PERL5OPT=-MDevel::Cover=${{ inputs.DEVEL_COVER_OPTIONS }} -MSyntax::Keyword::Try::Deparse" >> $GITHUB_ENV
        echo "YATH_DEVEL_COVER_OPTIONS=--cover=${{ inputs.DEVEL_COVER_OPTIONS }}" >> $GITHUB_ENV
        echo "JSCOVERAGE=--coverage" >> $GITHUB_ENV
      if: ${{ inputs.coverage }}

    - name: Starting 'starman'
      shell: bash
      run: |
        mkdir -p logs
        echo "PSGI_BASE_URL=http://lsmb:5762" >> $GITHUB_ENV
        starman --preload-app -E test --pid starman.pid --workers $JOB_COUNT \
                --max-requests 50000 --error-log logs/starman-error.log \
                -Ilib -Iold/lib --port 5762 bin/ledgersmb-server.psgi &
        echo "kill -term \$(cat starman.pid)" > logs/kill-starman.sh
      if: ${{ ! inputs.coverage }}

    - name: Starting 'plackup'
      shell: bash
      run: |
        mkdir -p logs
        echo "PSGI_BASE_URL=http://lsmb:5762" >> $GITHUB_ENV
        plackup -E test -Ilib -Iold/lib --port 5762 bin/ledgersmb-server.psgi &
        echo $! > starman.pid
        # Dont use 'curl': there will not be a valid response
        echo "exec 3<>/dev/tcp/lsmb/5762 ; echo -ne \"GET /kill HTTP/1.0\\r\\n\\r\\n\"  >&3 ; cat <&3" > logs/kill-starman.sh
      if: ${{ inputs.coverage }}
