{
  "body" => undef,
  "upload" => {
    "upload2" => {
      "headers" => {
        "Content-Type" => "text/plain",
        "Content-Disposition" => "form-data; name=\"upload2\"; filename=\"C:\\Documents and Settings\\Administrator\\Desktop\\hello.pl\""
      },
      "filename" => "C:\\Documents and Settings\\Administrator\\Desktop\\hello.pl",
      "name" => "upload2",
      "size" => 71
    },
    "upload" => [
      {
        "headers" => {
          "Content-Type" => "text/plain",
          "Content-Disposition" => "form-data; name=\"upload\"; filename=\"C:\\Documents and Settings\\Administrator\\Desktop\\hello.pl\""
        },
        "filename" => "C:\\Documents and Settings\\Administrator\\Desktop\\hello.pl",
        "name" => "upload",
        "size" => 71
      },
      {
        "headers" => {
          "Content-Type" => "text/plain",
          "Content-Disposition" => "form-data; name=\"upload\"; filename=\"C:\\Documents and Settings\\Administrator\\Desktop\\hello.pl\""
        },
        "filename" => "C:\\Documents and Settings\\Administrator\\Desktop\\hello.pl",
        "name" => "upload",
        "size" => 71
      }
    ]
  },
  "param" => {
    "text2" => "",
    "text1" => "Ratione accusamus aspernatur aliquam",
    "textarea" => "Voluptatem cumque voluptate sit recusandae at. Et quas facere rerum unde esse. Sit est et voluptatem. Vel temporibus velit neque odio non.\r\n\r\nMolestias rerum ut sapiente facere repellendus illo. Eum nulla quis aut. Quidem voluptas vitae ipsam officia voluptatibus eveniet. Aspernatur cupiditate ratione aliquam quidem corrupti. Eos sunt rerum non optio culpa.",
    "select" => [
      "A",
      "B"
    ]
  },
  "param_order" => [
    "text1", "text2", "select", "select", "textarea"
  ]
}
