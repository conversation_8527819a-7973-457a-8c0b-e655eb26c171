Revision history for Perl extension Filesys::Notify::Simple

0.14  2020-01-03 11:36:49 PST
        - Inlude path for linux error message
        - Demote the error above to a warning

0.13  2018-03-07 23:02:15 PST
        - Add OpenBSD support #22
        - Fix for a new Mac::FSEvents API #25

0.12  2013-06-13 14:45:27 PDT
       - Fix GH#12, Tests should no longer fail under HARNESS_OPTIONS=j10 (<PERSON>)

0.11  2013-06-12 17:39:36 PDT
        - use Milla

0.10  Fri Feb  1 00:40:56 PST 2013
        - Support moved files with inotify2 (nihen)

0.09  Fri Jan 25 08:08:33 PST 2013
        - Added Win32 support (yak1ex)

0.08  Mon Sep 26 18:40:06 PDT 2011
        - Added KQueue support for FreeBSD

0.07  Thu Jan 13 11:32:09 PST 2011
        - Don't die when there's a symlink poiting to something already processed (clkao)

0.06  Mon Mar 29 17:21:58 PDT 2010
        - Fixed it so ->wait won't die if one of the given directory doesn't exist, on platforms
          like Win32.

0.05  Sun Nov 29 14:42:13 JST 2009
        - Fixed fails with Win32 (charsbar++)

0.04  Fri Oct 23 23:16:13 PDT 2009
        - Upgrade Test::SharedFork in inc/

0.03  Fri Oct 23 01:21:13 PDT 2009
        - Fixed a bug where sub directory is not being monitored with Inotify2 (Thanks to kazeburo)

0.02  Thu Oct 22 13:25:18 PDT 2009
        - Added Test::SharedFork to inc/

0.01  Wed Oct 21 01:42:58 2009
        - original version
