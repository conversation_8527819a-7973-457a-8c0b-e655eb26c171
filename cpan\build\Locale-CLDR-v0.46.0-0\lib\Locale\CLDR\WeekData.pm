package Locale::CLDR::WeekData;
# This file auto generated from Data.xml
#	on Fri 17 Jan 12:03:31 pm GMT

use strict;
use warnings;
use version;

our $VERSION = version->declare('v0.46.0');

use v5.12.0;
use mro 'c3';
use utf8;
use feature 'unicode_strings';
use Types::Standard qw( Str Int HashRef ArrayRef CodeRef RegexpRef );
use Moo::Role;

has '_week_data_min_days' => (
	is			=> 'ro',
	isa			=> HashRef,
	init_arg	=> undef,
	default	=> sub { {
		'001' => 1,
		'GU' => 1,
		'UM' => 1,
		'US' => 1,
		'VI' => 1,
		'AD' => 4,
		'AN' => 4,
		'AT' => 4,
		'AX' => 4,
		'BE' => 4,
		'BG' => 4,
		'CH' => 4,
		'CZ' => 4,
		'DE' => 4,
		'DK' => 4,
		'EE' => 4,
		'ES' => 4,
		'FI' => 4,
		'FJ' => 4,
		'FO' => 4,
		'FR' => 4,
		'GB' => 4,
		'GF' => 4,
		'GG' => 4,
		'GI' => 4,
		'GP' => 4,
		'GR' => 4,
		'HU' => 4,
		'IE' => 4,
		'IM' => 4,
		'IS' => 4,
		'IT' => 4,
		'JE' => 4,
		'LI' => 4,
		'LT' => 4,
		'LU' => 4,
		'MC' => 4,
		'MQ' => 4,
		'NL' => 4,
		'NO' => 4,
		'PL' => 4,
		'PT' => 4,
		'RE' => 4,
		'RU' => 4,
		'SE' => 4,
		'SJ' => 4,
		'SK' => 4,
		'SM' => 4,
		'VA' => 4,
	}},
);

has '_week_data_first_day' => (
	is			=> 'ro',
	isa			=> HashRef,
	init_arg	=> undef,
	default	=> sub { {
		'001' => 'mon',
		'AD' => 'mon',
		'AE' => 'mon',
		'AI' => 'mon',
		'AL' => 'mon',
		'AM' => 'mon',
		'AN' => 'mon',
		'AR' => 'mon',
		'AT' => 'mon',
		'AU' => 'mon',
		'AX' => 'mon',
		'AZ' => 'mon',
		'BA' => 'mon',
		'BE' => 'mon',
		'BG' => 'mon',
		'BM' => 'mon',
		'BN' => 'mon',
		'BY' => 'mon',
		'CH' => 'mon',
		'CL' => 'mon',
		'CM' => 'mon',
		'CN' => 'mon',
		'CR' => 'mon',
		'CY' => 'mon',
		'CZ' => 'mon',
		'DE' => 'mon',
		'DK' => 'mon',
		'EC' => 'mon',
		'EE' => 'mon',
		'ES' => 'mon',
		'FI' => 'mon',
		'FJ' => 'mon',
		'FO' => 'mon',
		'FR' => 'mon',
		'GB' => 'mon',
		'GE' => 'mon',
		'GF' => 'mon',
		'GP' => 'mon',
		'GR' => 'mon',
		'HR' => 'mon',
		'HU' => 'mon',
		'IE' => 'mon',
		'IS' => 'mon',
		'IT' => 'mon',
		'KG' => 'mon',
		'KZ' => 'mon',
		'LB' => 'mon',
		'LI' => 'mon',
		'LK' => 'mon',
		'LT' => 'mon',
		'LU' => 'mon',
		'LV' => 'mon',
		'MC' => 'mon',
		'MD' => 'mon',
		'ME' => 'mon',
		'MK' => 'mon',
		'MN' => 'mon',
		'MQ' => 'mon',
		'MY' => 'mon',
		'NL' => 'mon',
		'NO' => 'mon',
		'NZ' => 'mon',
		'PL' => 'mon',
		'RE' => 'mon',
		'RO' => 'mon',
		'RS' => 'mon',
		'RU' => 'mon',
		'SE' => 'mon',
		'SI' => 'mon',
		'SK' => 'mon',
		'SM' => 'mon',
		'TJ' => 'mon',
		'TM' => 'mon',
		'TR' => 'mon',
		'UA' => 'mon',
		'UY' => 'mon',
		'UZ' => 'mon',
		'VA' => 'mon',
		'VN' => 'mon',
		'XK' => 'mon',
		'MV' => 'fri',
		'AF' => 'sat',
		'BH' => 'sat',
		'DJ' => 'sat',
		'DZ' => 'sat',
		'EG' => 'sat',
		'IQ' => 'sat',
		'IR' => 'sat',
		'JO' => 'sat',
		'KW' => 'sat',
		'LY' => 'sat',
		'OM' => 'sat',
		'QA' => 'sat',
		'SD' => 'sat',
		'SY' => 'sat',
		'AG' => 'sun',
		'AS' => 'sun',
		'BD' => 'sun',
		'BR' => 'sun',
		'BS' => 'sun',
		'BT' => 'sun',
		'BW' => 'sun',
		'BZ' => 'sun',
		'CA' => 'sun',
		'CO' => 'sun',
		'DM' => 'sun',
		'DO' => 'sun',
		'ET' => 'sun',
		'GT' => 'sun',
		'GU' => 'sun',
		'HK' => 'sun',
		'HN' => 'sun',
		'ID' => 'sun',
		'IL' => 'sun',
		'IN' => 'sun',
		'JM' => 'sun',
		'JP' => 'sun',
		'KE' => 'sun',
		'KH' => 'sun',
		'KR' => 'sun',
		'LA' => 'sun',
		'MH' => 'sun',
		'MM' => 'sun',
		'MO' => 'sun',
		'MT' => 'sun',
		'MX' => 'sun',
		'MZ' => 'sun',
		'NI' => 'sun',
		'NP' => 'sun',
		'PA' => 'sun',
		'PE' => 'sun',
		'PH' => 'sun',
		'PK' => 'sun',
		'PR' => 'sun',
		'PT' => 'sun',
		'PY' => 'sun',
		'SA' => 'sun',
		'SG' => 'sun',
		'SV' => 'sun',
		'TH' => 'sun',
		'TT' => 'sun',
		'TW' => 'sun',
		'UM' => 'sun',
		'US' => 'sun',
		'VE' => 'sun',
		'VI' => 'sun',
		'WS' => 'sun',
		'YE' => 'sun',
		'ZA' => 'sun',
		'ZW' => 'sun',
		'GB' => 'sun',
	}},
);

has '_week_data_weekend_start' => (
	is			=> 'ro',
	isa			=> HashRef,
	init_arg	=> undef,
	default	=> sub { {
		'AF' => 'thu',
		'BH' => 'fri',
		'DZ' => 'fri',
		'EG' => 'fri',
		'IL' => 'fri',
		'IQ' => 'fri',
		'IR' => 'fri',
		'JO' => 'fri',
		'KW' => 'fri',
		'LY' => 'fri',
		'OM' => 'fri',
		'QA' => 'fri',
		'SA' => 'fri',
		'SD' => 'fri',
		'SY' => 'fri',
		'YE' => 'fri',
		'001' => 'sat',
		'IN' => 'sun',
		'UG' => 'sun',
	}},
);

has '_week_data_weekend_end' => (
	is			=> 'ro',
	isa			=> HashRef,
	init_arg	=> undef,
	default	=> sub { {
		'AF' => 'fri',
		'IR' => 'fri',
		'BH' => 'sat',
		'DZ' => 'sat',
		'EG' => 'sat',
		'IL' => 'sat',
		'IQ' => 'sat',
		'JO' => 'sat',
		'KW' => 'sat',
		'LY' => 'sat',
		'OM' => 'sat',
		'QA' => 'sat',
		'SA' => 'sat',
		'SD' => 'sat',
		'SY' => 'sat',
		'YE' => 'sat',
		'001' => 'sun',
	}},
);

no Moo::Role;

1;

# vim: tabstop=4
