{"abstract": "Compile a log format string to perl-code ", "author": ["<PERSON><PERSON><PERSON> <<EMAIL>>"], "dynamic_config": 0, "generated_by": "Minilla/v3.1.7, CPAN::Meta::Converter version 2.150010", "license": ["perl_5"], "meta-spec": {"url": "http://search.cpan.org/perldoc?CPAN::Meta::Spec", "version": 2}, "name": "Apache-LogFormat-Compiler", "no_index": {"directory": ["t", "xt", "inc", "share", "eg", "examples", "author", "builder"]}, "prereqs": {"configure": {"requires": {"Module::Build::Tiny": "0.035"}}, "develop": {"requires": {"Test::CPAN::Meta": "0", "Test::MinimumVersion::Fast": "0.04", "Test::PAUSE::Permissions": "0.07", "Test::Pod": "1.41", "Test::Spellunker": "v0.2.7"}}, "runtime": {"requires": {"POSIX": "0", "POSIX::strftime::Compiler": "0.30", "Time::Local": "0", "perl": "5.008001"}}, "test": {"requires": {"HTTP::Request::Common": "0", "Test::MockTime": "0", "Test::More": "0.98", "Test::Requires": "0", "Try::Tiny": "0.12", "URI::Escape": "1.60"}}}, "provides": {"Apache::LogFormat::Compiler": {"file": "lib/Apache/LogFormat/Compiler.pm", "version": "0.36"}}, "release_status": "stable", "resources": {"bugtracker": {"web": "https://github.com/kazeburo/Apache-LogFormat-Compiler/issues"}, "homepage": "https://github.com/kazeburo/Apache-LogFormat-Compiler", "repository": {"type": "git", "url": "git://github.com/kazeburo/Apache-LogFormat-Compiler.git", "web": "https://github.com/kazeburo/Apache-LogFormat-Compiler"}}, "version": "0.36", "x_contributors": ["<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <piotr.r<PERSON>@gmail.com>"], "x_serialization_backend": "JSON::PP version 4.16", "x_static_install": 1}