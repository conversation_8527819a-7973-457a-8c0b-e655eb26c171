=pod

=for comment
DO NOT EDIT. This Pod was generated by Swim v0.1.48.
See http://github.com/ingydotnet/swim-pm#readme

=encoding utf8

=head1 NAME

YAML::Types - Marshall Perl internal data types to/from YAML

=head1 SYNOPSIS

    $::foo = 42;
    print YAML::Dump(*::foo);

    print YAML::Dump(qr{match me});

=head1 DESCRIPTION

This module has the helper classes for transferring objects, subroutines,
references, globs, regexps and file handles to and from YAML.

=head1 AUTHOR

ingy döt Net <<EMAIL>>

=head1 COPYRIGHT

Copyright 2001-2014. Ingy döt Net

This program is free software; you can redistribute it and/or modify it under
the same terms as Perl itself.

See L<http://www.perl.com/perl/misc/Artistic.html>

=cut
