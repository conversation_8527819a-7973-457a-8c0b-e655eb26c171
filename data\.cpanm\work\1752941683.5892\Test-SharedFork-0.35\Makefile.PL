# =========================================================================
# THIS FILE IS AUTOMATICALLY GENERATED BY MINILLA.
# DO NOT EDIT DIRECTLY.
# =========================================================================

use 5.006;
use strict;

use ExtUtils::MakeMaker 6.64;


my %WriteMakefileArgs = (
    NAME     => 'Test::SharedFork',
    DISTNAME => 'Test-SharedFork',
    VERSION  => '0.35',
    EXE_FILES => [glob('script/*'), glob('bin/*')],
    CONFIGURE_REQUIRES => {
  "ExtUtils::MakeMaker" => "6.64"
}
,
    BUILD_REQUIRES     => {}
,
    TEST_REQUIRES      => {
  "App::Prove" => 0,
  "Test::Builder::Tester" => 0,
  "Test::Requires" => 0,
  "Time::HiRes" => 0
}
,
    PREREQ_PM          => {
  "File::Temp" => 0,
  "Test::Builder" => "0.32",
  "Test::Builder::Module" => 0,
  "Test::More" => "0.88",
  "perl" => "5.008_001"
}
,
);

WriteMakefile(%WriteMakefileArgs);
