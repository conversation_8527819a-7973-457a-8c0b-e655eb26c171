=pod

=for comment
DO NOT EDIT. This Pod was generated by Swim v0.1.48.
See http://github.com/ingydotnet/swim-pm#readme

=encoding utf8

=head1 NAME

YAML::Error - Error formatting class for YAML modules

=head1 SYNOPSIS

    $self->die('YAML_PARSE_ERR_NO_ANCHOR', $alias);
    $self->warn('YAML_LOAD_WARN_DUPLICATE_KEY');

=head1 DESCRIPTION

This module provides a C<die> and a C<warn> facility.

=head1 AUTHOR

Ingy döt Net <<EMAIL>>

=head1 COPYRIGHT

Copyright 2001-2014. Ingy döt Net

This program is free software; you can redistribute it and/or modify it under
the same terms as Perl itself.

See L<http://www.perl.com/perl/misc/Artistic.html>

=cut
