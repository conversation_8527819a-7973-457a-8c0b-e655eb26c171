---
abstract: '<PERSON>ie string generator / parser'
author:
  - '<PERSON><PERSON><PERSON> <<EMAIL>>'
build_requires:
  Test::More: '0.98'
  Test::Time: '0'
configure_requires:
  Module::Build::Tiny: '0.035'
dynamic_config: 0
generated_by: 'Minilla/v3.1.23, CPAN::Meta::Converter version 2.150010'
license: perl
meta-spec:
  url: http://module-build.sourceforge.net/META-spec-v1.4.html
  version: '1.4'
name: <PERSON><PERSON><PERSON>
no_index:
  directory:
    - t
    - xt
    - inc
    - share
    - eg
    - examples
    - author
    - builder
provides:
  <PERSON><PERSON>::<PERSON>:
    file: lib/<PERSON>ie/<PERSON>.pm
    version: '0.12'
requires:
  Exporter: '0'
  URI::Escape: '0'
  perl: '5.008001'
resources:
  bugtracker: https://github.com/kazeburo/<PERSON><PERSON>-<PERSON>/issues
  homepage: https://github.com/kazeburo/<PERSON><PERSON>-<PERSON>
  repository: https://github.com/kazeburo/<PERSON>.git
version: '0.12'
x_contributors:
  - '<PERSON> <<EMAIL>>'
  - 'I<PERSON><PERSON> <<EMAIL>>'
  - '<PERSON> <<EMAIL>>'
  - 'Shoichi Kaji <<EMAIL>>'
  - 'kwry <<EMAIL>>'
  - 'yoshikazusawa <<EMAIL>>'
x_serialization_backend: 'CPAN::Meta::YAML version 0.020'
x_static_install: 1
