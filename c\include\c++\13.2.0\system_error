// <system_error> -*- C++ -*-

// Copyright (C) 2007-2023 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file include/system_error
 *  This is a Standard C++ Library header.
 */

#ifndef _GLIBCXX_SYSTEM_ERROR
#define _GLIBCXX_SYSTEM_ERROR 1

#pragma GCC system_header

#include <bits/requires_hosted.h> // OS-dependent

#if __cplusplus < 201103L
# include <bits/c++0x_warning.h>
#else

#include <bits/c++config.h>
#include <bits/error_constants.h>
#include <iosfwd>
#include <stdexcept>
#if __cplusplus > 201703L
# include <compare>
#endif

namespace std _GLIBCXX_VISIBILITY(default)
{
_GLIBCXX_BEGIN_NAMESPACE_VERSION

  /** @addtogroup diagnostics
   * @{
   */

  class error_code;
  class error_condition;
  class system_error;

  /// is_error_code_enum
  template<typename _Tp>
    struct is_error_code_enum : public false_type { };

  /// is_error_condition_enum
  template<typename _Tp>
    struct is_error_condition_enum : public false_type { };

  template<>
    struct is_error_condition_enum<errc>
    : public true_type { };

#if __cplusplus > 201402L
  template <typename _Tp>
    inline constexpr bool is_error_code_enum_v =
      is_error_code_enum<_Tp>::value;
  template <typename _Tp>
    inline constexpr bool is_error_condition_enum_v =
      is_error_condition_enum<_Tp>::value;
#endif // C++17
  /// @}

_GLIBCXX_BEGIN_INLINE_ABI_NAMESPACE(_V2)

  /** @addtogroup diagnostics
   * @{
   */

  /** Abstract base class for types defining a category of error codes.
   *
   * An error category defines a context that gives meaning to the integer
   * stored in an `error_code` or `error_condition` object. For example,
   * the standard `errno` constants such a `EINVAL` and `ENOMEM` are
   * associated with the "generic" category and other OS-specific error
   * numbers are associated with the "system" category, but a user-defined
   * category might give different meanings to the same numerical values.
   *
   * A user-defined category can override the `equivalent` member functions
   * to define correspondence between errors in different categories.
   * For example, a category for errors from disk I/O could consider some
   * of its error numbers equivalent to ENOSPC and ENOENT in the generic
   * category.
   *
   * @headerfile system_error
   * @since C++11
   */
  class error_category
  {
  public:
    constexpr error_category() noexcept = default;

    virtual ~error_category();

    error_category(const error_category&) = delete;
    error_category& operator=(const error_category&) = delete;

    /// A string that identifies the error category.
    virtual const char*
    name() const noexcept = 0;

    // We need two different virtual functions here, one returning a
    // COW string and one returning an SSO string. Their positions in the
    // vtable must be consistent for dynamic dispatch to work, but which one
    // the name "message()" finds depends on which ABI the caller is using.
#if _GLIBCXX_USE_CXX11_ABI
  private:
    _GLIBCXX_DEFAULT_ABI_TAG
    virtual __cow_string
    _M_message(int) const;

  public:
    /// A description of the error condition corresponding to the number.
    _GLIBCXX_DEFAULT_ABI_TAG
    virtual string
    message(int) const = 0;
#else
    virtual string
    message(int) const = 0;

  private:
    virtual __sso_string
    _M_message(int) const;
#endif

  public:
    /// Return an error_condition corresponding to `i` in this category.
    virtual error_condition
    default_error_condition(int __i) const noexcept;

    /// Test whether `cond` corresponds to `i` for this category.
    virtual bool
    equivalent(int __i, const error_condition& __cond) const noexcept;

    /// Test whether `code` corresponds to `i` for this category.
    virtual bool
    equivalent(const error_code& __code, int __i) const noexcept;

    /// An error_category only compares equal to itself.
    [[__nodiscard__]]
    bool
    operator==(const error_category& __other) const noexcept
    { return this == &__other; }

    /// Ordered comparison that defines a total order for error categories.
#if __cpp_lib_three_way_comparison
    [[nodiscard]]
    strong_ordering
    operator<=>(const error_category& __rhs) const noexcept
    { return std::compare_three_way()(this, &__rhs); }
#else
    bool
    operator<(const error_category& __other) const noexcept
    { return less<const error_category*>()(this, &__other); }

    bool
    operator!=(const error_category& __other) const noexcept
    { return this != &__other; }
#endif
  };

  // DR 890.

  /// Error category for `errno` error codes.
  [[__nodiscard__, __gnu__::__const__]]
  const error_category&
  generic_category() noexcept;

  /// Error category for other error codes defined by the OS.
  [[__nodiscard__, __gnu__::__const__]]
  const error_category&
  system_category() noexcept;

  /// @}

_GLIBCXX_END_INLINE_ABI_NAMESPACE(_V2)

  /** @addtogroup diagnostics
   * @{
   */

namespace __adl_only
{
  void make_error_code() = delete;
  void make_error_condition() = delete;
}

  /** Class error_code
   *
   * This class is a value type storing an integer error number and a
   * category that gives meaning to the error number. Typically this is done
   * close the the point where the error happens, to capture the original
   * error value.
   *
   * An `error_code` object can be used to store the original error value
   * emitted by some subsystem, with a category relevant to the subsystem.
   * For example, errors from POSIX library functions can be represented by
   * an `errno` value and the "generic" category, but errors from an HTTP
   * library might be represented by an HTTP response status code (e.g. 404)
   * and a custom category defined by the library.
   *
   * @headerfile system_error
   * @since C++11
   */
  class error_code
  {
    template<typename _ErrorCodeEnum>
      using _Check
	= __enable_if_t<is_error_code_enum<_ErrorCodeEnum>::value>;

  public:
    error_code() noexcept
    : _M_value(0), _M_cat(&system_category()) { }

    error_code(int __v, const error_category& __cat) noexcept
    : _M_value(__v), _M_cat(&__cat) { }

    /// Initialize with a user-defined type, by calling make_error_code.
    template<typename _ErrorCodeEnum,
	     typename = _Check<_ErrorCodeEnum>>
      error_code(_ErrorCodeEnum __e) noexcept
      {
	using __adl_only::make_error_code;
	*this = make_error_code(__e);
      }

    error_code(const error_code&) = default;
    error_code& operator=(const error_code&) = default;

    void
    assign(int __v, const error_category& __cat) noexcept
    {
      _M_value = __v;
      _M_cat = &__cat;
    }

    void
    clear() noexcept
    { assign(0, system_category()); }

    /// The error value.
    [[__nodiscard__]]
    int
    value() const noexcept { return _M_value; }

    /// The error category that this error belongs to.
    [[__nodiscard__]]
    const error_category&
    category() const noexcept { return *_M_cat; }

    /// An `error_condition` for this error's category and value.
    error_condition
    default_error_condition() const noexcept;

    /// The category's description of the value.
    _GLIBCXX_DEFAULT_ABI_TAG
    string
    message() const
    { return category().message(value()); }

    /// Test whether `value()` is non-zero.
    [[__nodiscard__]]
    explicit operator bool() const noexcept
    { return _M_value != 0; }

    // DR 804.
  private:
    int            		_M_value;
    const error_category* 	_M_cat;
  };

  // C++11 ******** non-member functions

  /** Create an `error_code` representing a standard `errc` condition.
   *
   * The `std::errc` constants correspond to `errno` macros and so use the
   * generic category.
   *
   * @relates error_code
   * @since C++11
   */
  [[__nodiscard__]]
  inline error_code
  make_error_code(errc __e) noexcept
  { return error_code(static_cast<int>(__e), generic_category()); }

  /** Ordered comparison for std::error_code.
   *
   * This defines a total order by comparing the categories, and then
   * if they are equal comparing the values.
   *
   * @relates error_code
   * @since C++11
   */
#if __cpp_lib_three_way_comparison
  [[nodiscard]]
  inline strong_ordering
  operator<=>(const error_code& __lhs, const error_code& __rhs) noexcept
  {
    if (auto __c = __lhs.category() <=> __rhs.category(); __c != 0)
      return __c;
    return __lhs.value() <=> __rhs.value();
  }
#else
  inline bool
  operator<(const error_code& __lhs, const error_code& __rhs) noexcept
  {
    return (__lhs.category() < __rhs.category()
	    || (__lhs.category() == __rhs.category()
		&& __lhs.value() < __rhs.value()));
  }
#endif

  /** Write a std::error_code to an ostream.
   *
   * @relates error_code
   * @since C++11
   */
  template<typename _CharT, typename _Traits>
    basic_ostream<_CharT, _Traits>&
    operator<<(basic_ostream<_CharT, _Traits>& __os, const error_code& __e)
    { return (__os << __e.category().name() << ':' << __e.value()); }

  /** Class error_condition
   *
   * This class represents error conditions that may be visible at an API
   * boundary. Different `error_code` values that can occur within a library
   * or module might map to the same `error_condition`.
   *
   * An `error_condition` represents something that the program can test for,
   * and subsequently take appropriate action.
   *
   * @headerfile system_error
   * @since C++11
   */
  class error_condition
  {
    template<typename _ErrorConditionEnum>
      using _Check
	= __enable_if_t<is_error_condition_enum<_ErrorConditionEnum>::value>;

  public:
    /// Initialize with a zero (no error) value and the generic category.
    error_condition() noexcept
    : _M_value(0), _M_cat(&generic_category()) { }

    /// Initialize with the specified value and category.
    error_condition(int __v, const error_category& __cat) noexcept
    : _M_value(__v), _M_cat(&__cat) { }

    /// Initialize with a user-defined type, by calling make_error_condition.
    template<typename _ErrorConditionEnum,
	     typename = _Check<_ErrorConditionEnum>>
      error_condition(_ErrorConditionEnum __e) noexcept
      {
	using __adl_only::make_error_condition;
	*this = make_error_condition(__e);
      }

    error_condition(const error_condition&) = default;
    error_condition& operator=(const error_condition&) = default;

    /// Set the value and category.
    void
    assign(int __v, const error_category& __cat) noexcept
    {
      _M_value = __v;
      _M_cat = &__cat;
    }

    /// Reset the value and category to the default-constructed state.
    void
    clear() noexcept
    { assign(0, generic_category()); }

    // C++11 ******** observers

    /// The error value.
    [[__nodiscard__]]
    int
    value() const noexcept { return _M_value; }

    /// The error category that this error belongs to.
    [[__nodiscard__]]
    const error_category&
    category() const noexcept { return *_M_cat; }

    /// The category's description of the value.
    _GLIBCXX_DEFAULT_ABI_TAG
    string
    message() const
    { return category().message(value()); }

    /// Test whether `value()` is non-zero.
    [[__nodiscard__]]
    explicit operator bool() const noexcept
    { return _M_value != 0; }

    // DR 804.
  private:
    int 			_M_value;
    const error_category* 	_M_cat;
  };

  // C++11 ******** non-member functions

  /** Create an `error_condition` representing a standard `errc` condition.
   *
   * The `std::errc` constants correspond to `errno` macros and so use the
   * generic category.
   *
   * @relates error_condition
   * @since C++11
   */
  [[__nodiscard__]]
  inline error_condition
  make_error_condition(errc __e) noexcept
  { return error_condition(static_cast<int>(__e), generic_category()); }

  // C++11 19.5.4 Comparison operators

  /** Equality comparison for std::error_code.
   *
   * Returns true only if they have the same category and the same value.
   *
   * @relates error_condition
   * @since C++11
   */
  [[__nodiscard__]]
  inline bool
  operator==(const error_code& __lhs, const error_code& __rhs) noexcept
  {
    return __lhs.category() == __rhs.category()
	     && __lhs.value() == __rhs.value();
  }

  /** Equality comparison for std::error_code and std::error_condition.
   *
   * Uses each category's `equivalent` member function to check whether
   * the values correspond to an equivalent error in that category.
   *
   * @relates error_condition
   * @since C++11
   */
  [[__nodiscard__]]
  inline bool
  operator==(const error_code& __lhs, const error_condition& __rhs) noexcept
  {
    return __lhs.category().equivalent(__lhs.value(), __rhs)
	     || __rhs.category().equivalent(__lhs, __rhs.value());
  }

  /** Equality comparison for std::error_condition.
   *
   * Returns true only if they have the same category and the same value.
   *
   * @relates error_condition
   * @since C++11
   */
  [[__nodiscard__]]
  inline bool
  operator==(const error_condition& __lhs,
	     const error_condition& __rhs) noexcept
  {
    return __lhs.category() == __rhs.category()
	     && __lhs.value() == __rhs.value();
  }

  /** Ordered comparison for std::error_condition.
   *
   * This defines a total order by comparing the categories, and then
   * if they are equal comparing the values.
   *
   * @relates error_condition
   * @since C++11
   */
#if __cpp_lib_three_way_comparison
  [[nodiscard]]
  inline strong_ordering
  operator<=>(const error_condition& __lhs,
	      const error_condition& __rhs) noexcept
  {
    if (auto __c = __lhs.category() <=> __rhs.category(); __c != 0)
      return __c;
    return __lhs.value() <=> __rhs.value();
  }
#else
  inline bool
  operator<(const error_condition& __lhs,
	    const error_condition& __rhs) noexcept
  {
    return (__lhs.category() < __rhs.category()
	    || (__lhs.category() == __rhs.category()
		&& __lhs.value() < __rhs.value()));
  }

  /// @relates error_condition
  inline bool
  operator==(const error_condition& __lhs, const error_code& __rhs) noexcept
  {
    return (__rhs.category().equivalent(__rhs.value(), __lhs)
	    || __lhs.category().equivalent(__rhs, __lhs.value()));
  }

  /// @relates error_code
  inline bool
  operator!=(const error_code& __lhs, const error_code& __rhs) noexcept
  { return !(__lhs == __rhs); }

  /// @relates error_code
  inline bool
  operator!=(const error_code& __lhs, const error_condition& __rhs) noexcept
  { return !(__lhs == __rhs); }

  /// @relates error_condition
  inline bool
  operator!=(const error_condition& __lhs, const error_code& __rhs) noexcept
  { return !(__lhs == __rhs); }

  /// @relates error_condition
  inline bool
  operator!=(const error_condition& __lhs,
	     const error_condition& __rhs) noexcept
  { return !(__lhs == __rhs); }
#endif // three_way_comparison
  /// @}

  /**
   * @brief An exception type that includes an `error_code` value.
   *
   * Typically used to report errors from the operating system and other
   * low-level APIs.
   *
   * @headerfile system_error
   * @since C++11
   * @ingroup exceptions
   */
  class system_error : public std::runtime_error
  {
  private:
    error_code 	_M_code;

  public:
    system_error(error_code __ec = error_code())
    : runtime_error(__ec.message()), _M_code(__ec) { }

    system_error(error_code __ec, const string& __what)
    : runtime_error(__what + ": " + __ec.message()), _M_code(__ec) { }

    system_error(error_code __ec, const char* __what)
    : runtime_error(__what + (": " + __ec.message())), _M_code(__ec) { }

    system_error(int __v, const error_category& __ecat, const char* __what)
    : system_error(error_code(__v, __ecat), __what) { }

    system_error(int __v, const error_category& __ecat)
    : runtime_error(error_code(__v, __ecat).message()),
      _M_code(__v, __ecat) { }

    system_error(int __v, const error_category& __ecat, const string& __what)
    : runtime_error(__what + ": " + error_code(__v, __ecat).message()),
      _M_code(__v, __ecat) { }

#if __cplusplus >= 201103L
    system_error (const system_error &) = default;
    system_error &operator= (const system_error &) = default;
#endif

    virtual ~system_error() noexcept;

    const error_code&
    code() const noexcept { return _M_code; }
  };

_GLIBCXX_END_NAMESPACE_VERSION
} // namespace

#include <bits/functional_hash.h>

namespace std _GLIBCXX_VISIBILITY(default)
{
_GLIBCXX_BEGIN_NAMESPACE_VERSION

#ifndef _GLIBCXX_COMPATIBILITY_CXX0X
  // DR 1182.
  /// std::hash specialization for error_code.
  /// @relates error_code
  template<>
    struct hash<error_code>
    : public __hash_base<size_t, error_code>
    {
      size_t
      operator()(const error_code& __e) const noexcept
      {
	const size_t __tmp = std::_Hash_impl::hash(__e.value());
	return std::_Hash_impl::__hash_combine(&__e.category(), __tmp);
      }
    };
#endif // _GLIBCXX_COMPATIBILITY_CXX0X

#if __cplusplus >= 201703L
  // DR 2686.
  /// std::hash specialization for error_condition.
  /// @relates error_condition
  template<>
    struct hash<error_condition>
    : public __hash_base<size_t, error_condition>
    {
      size_t
      operator()(const error_condition& __e) const noexcept
      {
	const size_t __tmp = std::_Hash_impl::hash(__e.value());
	return std::_Hash_impl::__hash_combine(&__e.category(), __tmp);
      }
    };
#endif

_GLIBCXX_END_NAMESPACE_VERSION
} // namespace

#endif // C++11

#endif // _GLIBCXX_SYSTEM_ERROR
