# =========================================================================
# THIS FILE IS AUTOMATICALLY GENERATED BY MINILLA.
# DO NOT EDIT DIRECTLY.
# =========================================================================

use 5.008_001;

use strict;
use warnings;
use utf8;

BEGIN { push @INC, '.' }
use builder::MyBuilder;
use File::Basename;
use File::Spec;

my %args = (
    license              => 'perl_5',
    dynamic_config       => 0,

    configure_requires => {
        'Module::Build' => '0.4005',
    },

    requires => {
        'Exporter' => '0',
        'perl' => '5.008001',
    },

    recommends => {
    },

    suggests => {
        'WWW::Form::UrlEncoded::XS' => '0.19',
    },

    build_requires => {
    },

    test_requires => {
        'JSON::PP' => '2',
        'Test::More' => '0.98',
    },

    name            => 'WWW-Form-UrlEncoded',
    module_name     => 'WWW::Form::UrlEncoded',
    allow_pureperl => 0,

    script_files => [glob('script/*'), glob('bin/*')],
    PL_files => {},

    test_files           => ((-d '.git' || $ENV{RELEASE_TESTING}) && -d 'xt') ? 't/ xt/' : 't/',
    recursive_test_files => 1,


);
if (-d 'share') {
    $args{share_dir} = 'share';
}

my $builder = builder::MyBuilder->subclass(
    class => 'MyBuilder',
    code => q{
        sub ACTION_distmeta {
            die "Do not run distmeta. Install Minilla and `minil install` instead.\n";
        }
        sub ACTION_installdeps {
            die "Do not run installdeps. Run `cpanm --installdeps .` instead.\n";
        }
    }
)->new(%args);
$builder->create_build_script();

