---
abstract: 'HTTP MultiPart Parser'
author:
  - '<PERSON><<EMAIL>>'
build_requires:
  ExtUtils::MakeMaker: '6.59'
  Test::Deep: '0'
  Test::More: '0.88'
configure_requires:
  ExtUtils::MakeMaker: '0'
dynamic_config: 0
generated_by: 'Module::Install version 1.18, CPAN::Meta::Converter version 2.150010'
license: perl
meta-spec:
  url: http://module-build.sourceforge.net/META-spec-v1.4.html
  version: '1.4'
name: HTTP-MultiPartParser
no_index:
  directory:
    - inc
    - t
requires:
  Carp: '0'
  Scalar::Util: '0'
  perl: '5.008001'
resources:
  license: http://dev.perl.org/licenses/
  repository: http://github.com/chansen/p5-http-multipartparser
version: '0.02'
x_serialization_backend: 'CPAN::Meta::YAML version 0.020'
