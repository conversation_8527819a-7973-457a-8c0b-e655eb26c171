# Place this in /etc/systemd/system/ledgersmb_starman.service
# systemctl enable ledgersmb_starman
# service start ledgersmb_starman

[Unit]
Description=LedgerSMB Starman
After=network.target

[Service]
WorkingDirectory=WORKING_DIR

# In case you installed dependencies into a 'local::lib'
# make sure you set the PERL5LIB environment variable
# to the correct location by uncommenting the line below
#Environment=PERL5LIB=/path/to/local-lib/lib/perl5

# Be sure to set a user and group below
# which don't have write access to the directories
# holding the LedgerSMB sources
User=ledgersmb
Group=ledgersmb
ExecStart=/usr/bin/starman                     \
    --preload-app                              \
    --listen localhost:5762                    \
    -I lib                                     \
    -I old/lib                                 \
    bin/ledgersmb-server.psgi
Restart=always

[Install]
WantedBy=multi-user.target
