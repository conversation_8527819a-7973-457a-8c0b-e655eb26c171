=pod

=encoding utf8

=head1 NAME

YAML::XS - Perl YAML Serialization using XS and libyaml

=for html
<a href="https://github.com/ingydotnet/yaml-libyaml-pm/actions/workflows/linux.yml"><img src="https://github.com/ingydotnet/yaml-libyaml-pm/actions/workflows/linux.yml/badge.svg" alt="linux"></a>



=head1 SYNOPSIS

    use YAML::XS;

    # Classic functional interface
    my $yaml = Dump [ 1..4 ];
    my $array = Load $yaml;

    # EXPERIMENTAL: Object Oriented interface for YAML 1.2
    # Incompatible to functional interface!
    my $xs = YAML::XS->new;
    my $yaml = $xs->dump([ 1..4 ]);
    my $array = $xs->load($yaml);

=head1 DESCRIPTION

Kirill <PERSON>'s C<libyaml> is arguably the best YAML implementation. The C library is written precisely to the YAML 1.1 specification. It was originally bound to Python and was later bound to Ruby.

This module is a Perl XS binding to libyaml which offers Perl the best YAML support to date.

This module exports the functions C<Dump>, C<Load>, C<DumpFile> and C<LoadFile>. These functions are intended to work exactly like C<YAML.pm>'s corresponding functions. Only C<Load> and C<Dump> are exported by default.

=head1 CONFIGURATION

The object oriented interface is described below: L<"OBJECT ORIENTED INTERFACE">

=over

=item * C<$YAML::XS::LoadBlessed> (since v0.69)

Default: false.

The default was changed in version 0.81.

When set to false, it will not bless data into objects, which can be a security problem, when loading YAML from an untrusted source. It will silently ignore the tag and just load the data unblessed.

In PyYAML, this is called SafeLoad.

If set to true, it will load the following YAML as objects:

    ---
    local: !Foo::Bar [a]
    perl: !!perl/hash:Foo::Bar { a: 1 }
    regex: !!perl/regexp:Foo::Bar pattern

You can create any kind of object with YAML. The creation itself is not the critical part. If the class has a C<DESTROY> method, it will be called once the object is deleted. An example with File::Temp removing files can be found at L<https://bugs.debian.org/cgi-bin/bugreport.cgi?bug=862373|https://bugs.debian.org/cgi-bin/bugreport.cgi?bug=862373>.

=item * C<$YAML::XS::ForbidDuplicateKeys> (since 0.84)

Default: false

When set to true, C<Load> will die when encountering a duplicate key in a hash, e.g.

    key: value
    key: another value

This can be useful for bigger YAML documents where it is not that obvious, and it is recommended to set it to true. That's also what a YAML loader should do by default according to the YAML specification.

=item * C<$YAML::XS::UseCode>

=item * C<$YAML::XS::DumpCode>

=item * C<$YAML::XS::LoadCode>

If enabled supports deparsing and evaling of code blocks.

Note that support for loading code was added in version 0.75, although C<$LoadCode> was documented already in earlier versions.

=item * C<$YAML::XS::QuoteNumericStrings>

When true (the default) strings that look like numbers but have not been numified will be quoted when dumping.

This ensures leading that things like leading zeros and other formatting are preserved.

=item * C<$YAML::XS::Boolean> (since v0.67)

Default: undef

Since YAML::XS 0.89: When used with perl 5.36 or later, builtin booleans will work out of the box. They will be created by C<Load> and recognized by C<Dump> automatically (since YAML::XS 0.89).

    say Dump({ truth => builtin::true });
    # truth: true

Since YAML::XS v0.902: loaded booleans are not set to readonly anymore.

For older perl versions you can use the following configuration to serialize data as YAML booleans:

When set to C<"JSON::PP"> or C<"boolean">, the plain (unquoted) strings C<true> and C<false> will be loaded as C<JSON::PP::Boolean> or C<boolean.pm> objects. Those objects will be dumped again as plain "true" or "false".

It will try to load [JSON::PP] or [boolean] and die if it can't be loaded.

With that it's possible to add new "real" booleans to a data structure:

    local $YAML::XS::Boolean = "JSON::PP"; # or "boolean"
    my $data = Load("booltrue: true");
    $data->{boolfalse} = JSON::PP::false;
    my $yaml = Dump($data);
    # boolfalse: false
    # booltrue: true

It also lets booleans survive when loading YAML via YAML::XS and encode it in JSON via one of the various JSON encoders, which mostly support JSON::PP booleans.

Please note that JSON::PP::Boolean and boolean.pm behave a bit differently. Ideally you should only use them in boolean context.

If not set, booleans are loaded as special perl variables C<PL_sv_yes> and C<PL_sv_no>, which have the disadvantage that they are readonly, and you can't add those to an existing data structure with pure perl.

If you simply need to load "perl booleans" that are true or false in boolean context, you will be fine with the default setting.

=item * C<$YAML::XS::Indent> (since v0.76)

Default is 2.

Sets the number of spaces for indentation for C<Dump>.

=back

=head1 USING YAML::XS WITH UNICODE

Handling unicode properly in Perl can be a pain. YAML::XS only deals with streams of utf8 octets. Just remember this:

    $perl = Load($utf8_octets);
    $utf8_octets = Dump($perl);

There are many, many places where things can go wrong with unicode. If you are having problems, use Devel::Peek on all the possible data points.


=head1 OBJECT ORIENTED INTERFACE

Since version v0.904.0, EXPERIMENTAL

+++NOTE: This is incompatible with the functional interface and will treat
YAML values in a different way.+++

This has two MAJOR differences to the old functional interface:

=over

=item Object with options

This provides an interface where you create a YAML::XS object with
options (instead of the old interface with global variables).

=item YAML 1.2 Core Schema

It implements the YAML 1.2 Core Schema.

(Note that the functional interface does not implement YAML 1.1, when it comes
to loading numbers, booleans, null etc. It implements its own set of rules.)

Here is an (incomplete!) example of values that are treated differently than
with the functional interface. YAML values that match a certain pattern,
are not loaded as strings, but as other types:

    # Functional interface: special values (not compatible to other YAML modules)
    - [true, false]                             # booleans
    - [null, ~]                                 # undef
    - [inf, INF, iNf, iNF, InF, INf, -inf, ...] # Inf
    - [100_000]                                 # 100000
    - # anything that looks_like_number()       # number

    # OOP YAML 1.2 special values
    - [true, True, TRUE, false, False, FALSE]   # booleans
    - [null, Null, NULL, ~]                     # undef
    - [.inf, .Inf, .INF, -.inf, -.Inf, -.INF]   # Inf
    - [.nan, .NAN, .NaN]                        # nan
    - [42, 0x10, 0o10]                          # dec 42, hex 16, oct 8

For more subtle differences regarding numbers checkout the comprehensive data
here:

=over

=item YAML 1.1 / 1.2 definitions: L<https://perlpunk.github.io/yaml-test-schema/schemas.html>

=item Test data: L<https://perlpunk.github.io/yaml-test-schema/data.html>

=back

This way the OOP interface is compatible to YAML::PP and YAML processors in
other languages supporting YAML 1.2.

=item C<load> will return the first document in scalar context

The functional interface returns the last.

=item Numbers don't have string flags

=item Booleans currently are only preserved for the new builtin booleans

Adding a JSON::PP boolean option for older perls is planned

=item References, objects, globs, regexes and coderefs cannot be loaded or dumped

This is planned.

=back

=head2 Usage

    use YAML::XS ();
    my $xs = YAML::XS->new;
    my $yaml = "foo: bar";

    # Load single (first) document:
    my $data = $xs->load($yaml);
    $yaml = $xs->dump($data);

    # Or to load all documents:
    my @data = $xs->load($yaml);
    $yaml = $xs->dump(@data);

=head2 METHODS

=head3 new

    use YAML::XS;
    my $xs = YAML::XS->new(
        # load options
        # require_footer => 0,

        # dump options
        # indent => 2,
        # header => 1,
        # footer => 0,
        # width => 80,
        # anchor_prefix => '',

        # load and dump options
        # utf8 => 0,
    );

Options:

=over

=item indent

Default: 2

Sets the number of spaces for indentation for dumping.

=item utf8

Default: false

When false, the loader will accept unencoded character strings, and the dumper
returns unencoded character strings.

When true, the loader accepts a UTF-8 encoded string of bytes, and the dumper
returns a UTF-8 encoded string of bytes.
This typically makes sense when you read from / write to a file directly.

=item header

Default: 1

Writes a C<---> before every document

=item footer

Default: 0

Writes a C<...> at the end of every document.

=item width

Set the maximum number of colums for dumping. If a value has too many
characters, it will be split into multiple lines.

=item require_footer

Default: 0

Can be useful in a use case where you want to make sure you received the
complete document from the sender.

=item anchor_prefix

Default: C<''>

If set to a string like C<ANCHOR>, anchors and aliases will be prefixed
with this instead of just being numbers:

    # my $xs = YAML::XS->new( anchor_prefix => 'ANCHOR' );
    # my $hash = { some => "mapping" };
    # say $xs->dump([$hash, $hash]);
    - &ANCHOR1
      some: mapping
    - *ANCHOR1

=back

=head3 load

    my $yaml = <<'EOM';
    ---
    - 23
    ---
    some: mapping
    EOM
    my $array = $xs->load($yaml);
    # [23]
    my @documents = $xs->load($yaml);
    # (
    #   [23],
    #   { some => "mapping" }
    # )

In scalar context, if the YAML string contains more than one document, it will
return the first document.

=head3 dump

    $yaml = $xs->dump($data);
    $yaml = $xs->dump($data1, $data2, $data3);
    $yaml = $xs->dump(@data);

=head1 LIBYAML

You can find out (since v.079) which libyaml version this module was built with:

    my $libyaml_version = YAML::XS::LibYAML::libyaml_version();

=head1 SEE ALSO

=over

=item * YAML.pm

=item * YAML::Syck

=item * YAML::Tiny

=item * YAML::PP

=item * YAML::PP::LibYAML

=back

=head1 AUTHORS

=over

=item Ingy döt Net L<<EMAIL>|mailto:<EMAIL>>

=item Tina Müller <<EMAIL>>

=back

=head1 COPYRIGHT AND LICENSE

Copyright 2007-2025 - Ingy döt Net

This program is free software; you can redistribute it and/or modify it under the same terms as Perl itself.

See L<http://www.perl.com/perl/misc/Artistic.html|http://www.perl.com/perl/misc/Artistic.html>

=cut
