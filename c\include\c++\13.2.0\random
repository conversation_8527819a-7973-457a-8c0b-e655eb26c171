// <random> -*- C++ -*-

// Copyright (C) 2007-2023 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file include/random
 *  This is a Standard C++ Library header.
 */

#ifndef _GLIBCXX_RANDOM
#define _GLIBCXX_RANDOM 1

#pragma GCC system_header

#include <bits/requires_hosted.h> // OS-dependent random

#if __cplusplus < 201103L
# include <bits/c++0x_warning.h>
#else

#include <cmath>
#include <cstdlib>
#include <string>
#include <iosfwd>
#include <limits>
#include <debug/debug.h>
#include <type_traits>

#ifdef _GLIBCXX_USE_C99_STDINT_TR1

#include <cstdint> // For uint_fast32_t, uint_fast64_t, uint_least32_t
#include <bits/random.h>
#include <bits/opt_random.h>
#include <bits/random.tcc>

#endif // _GLIBCXX_USE_C99_STDINT_TR1

#endif // C++11

#endif // _GLIBCXX_RANDOM
