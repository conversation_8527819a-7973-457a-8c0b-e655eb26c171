---
abstract: 'Perl Superglue for Web frameworks and Web Servers (PSGI toolkit)'
author:
  - '<PERSON><PERSON><PERSON>'
build_requires:
  ExtUtils::MakeMaker: '0'
  Test::More: '0.88'
  Test::Requires: '0'
configure_requires:
  ExtUtils::MakeMaker: '0'
  File::ShareDir::Install: '0.06'
dynamic_config: 0
generated_by: 'Dist::Milla version v1.0.22, Dist::Zilla version 6.025, CPAN::Meta::Converter version 2.150010'
license: perl
meta-spec:
  url: http://module-build.sourceforge.net/META-spec-v1.4.html
  version: '1.4'
name: Plack
no_index:
  directory:
    - eg
    - examples
    - inc
    - share
    - t
    - xt
requires:
  Apache::LogFormat::Compiler: '0.33'
  Cookie::Baker: '0.07'
  Devel::StackTrace: '1.23'
  Devel::StackTrace::AsHTML: '0.11'
  File::ShareDir: '1.00'
  Filesys::Notify::Simple: '0'
  HTTP::Entity::Parser: '0.25'
  HTTP::Headers::Fast: '0.18'
  HTTP::Message: '5.814'
  HTTP::Tiny: '0.034'
  Hash::MultiValue: '0.05'
  Pod::Usage: '1.36'
  Stream::Buffered: '0.02'
  Test::TCP: '2.15'
  Try::Tiny: '0'
  URI: '1.59'
  WWW::Form::UrlEncoded: '0.23'
  parent: '0'
  perl: '5.012000'
resources:
  bugtracker: https://github.com/plack/Plack/issues
  homepage: https://github.com/plack/Plack
  repository: https://github.com/plack/Plack.git
version: '1.0051'
x_authority: cpan:MIYAGAWA
x_contributors:
  - 'Aaron Trevena <<EMAIL>>'
  - 'Ævar Arnfjörð Bjarmason <<EMAIL>>'
  - 'Akzhan Abdulin <<EMAIL>>'
  - 'Alexandr Ciornii <<EMAIL>>'
  - 'Alex J. G. Burzyński <<EMAIL>>'
  - 'Allan Whiteford <<EMAIL>>'
  - 'Andrew Fresh <<EMAIL>>'
  - 'Andrew Rodland <<EMAIL>>'
  - 'Andy Wardley <<EMAIL>>'
  - 'Aristotle Pagaltzis <<EMAIL>>'
  - "Arthur Axel 'fREW' Schmidt <<EMAIL>>"
  - 'Asato Wakisaka <<EMAIL>>'
  - 'Ashley Pond V <<EMAIL>>'
  - 'Ask Bjørn Hansen <<EMAIL>>'
  - 'ben hengst <<EMAIL>>'
  - 'Ben Morrow <<EMAIL>>'
  - 'Bernhard Graf <<EMAIL>>'
  - 'Chad Granum <<EMAIL>>'
  - 'chansen <<EMAIL>>'
  - 'Chia-liang Kao <<EMAIL>>'
  - 'cho45 <<EMAIL>>'
  - 'Christian Walde <<EMAIL>>'
  - 'chromatic <<EMAIL>>'
  - 'Cosimo Streppone <<EMAIL>>'
  - 'Dagfinn Ilmari Mannsåker <<EMAIL>>'
  - 'Daisuke Maki <<EMAIL>>'
  - 'Daisuke Murase <<EMAIL>>'
  - 'Daniel Mita <<EMAIL>>'
  - 'Dave Marr <<EMAIL>>'
  - 'Dave Rolsky <<EMAIL>>'
  - 'David E. Wheeler <<EMAIL>>'
  - 'David Schmidt <<EMAIL>>'
  - 'David Steinbrunner <<EMAIL>>'
  - 'dmaestro <<EMAIL>>'
  - 'Eduardo Arino de la Rubia <<EMAIL>>'
  - 'Emmanuel Seyman <<EMAIL>>'
  - 'Eric Johnson <<EMAIL>>'
  - 'Eugen Konkov <<EMAIL>>'
  - 'Fabrice Gabolde <<EMAIL>>'
  - 'Fabrice Gabolde <<EMAIL>>'
  - 'fayland <<EMAIL>>'
  - 'Flavio Poletti <<EMAIL>>'
  - 'Florian Ragwitz <<EMAIL>>'
  - 'franck cuny <<EMAIL>>'
  - 'Gianni Ceccarelli <<EMAIL>>'
  - 'Graham Knop <<EMAIL>>'
  - 'Grant McLean <<EMAIL>>'
  - 'Hans Dieter Pearcey <<EMAIL>>'
  - 'Haruka Iwao <<EMAIL>>'
  - 'Henry Baragar <<EMAIL>>'
  - 'hiratara <<EMAIL>>'
  - 'HIROSE Masaaki <<EMAIL>>'
  - 'Hiroshi Sakai <<EMAIL>>'
  - 'Ian Bradley <<EMAIL>>'
  - 'Ian Burrell <<EMAIL>>'
  - 'Jakob Voss <<EMAIL>>'
  - 'Jakob Voss <<EMAIL>>'
  - 'Jay Hannah <<EMAIL>>'
  - 'Jesse Luehrs <<EMAIL>>'
  - 'Jiro Nishiguchi <<EMAIL>>'
  - 'Johannes Plunien <<EMAIL>>'
  - 'John Beppu <<EMAIL>>'
  - 'John Napiorkowski <<EMAIL>>'
  - 'Jonathan Swartz <<EMAIL>>'
  - 'José Pinheiro Neta <<EMAIL>>'
  - 'Justin Davis <<EMAIL>>'
  - 'kakuno <<EMAIL>>'
  - 'Kang-min Liu <<EMAIL>>'
  - 'Karen Etheridge <<EMAIL>>'
  - 'Kazuho Oku <<EMAIL>>'
  - 'Keedi Kim <<EMAIL>>'
  - 'Lee Aylward <<EMAIL>>'
  - 'Leo Lapworth <<EMAIL>>'
  - 'mala <<EMAIL>>'
  - 'Marco Pessotto <<EMAIL>>'
  - 'Marian Schubert <<EMAIL>>'
  - 'Mark Fowler <<EMAIL>>'
  - 'Mark Stosberg <<EMAIL>>'
  - 'Masahiro Chiba <<EMAIL>>'
  - 'Masahiro Nagano <<EMAIL>>'
  - 'Michael G. Schwern <<EMAIL>>'
  - 'Michael R. Davis <<EMAIL>>'
  - 'Michal Josef Špaček <<EMAIL>>'
  - 'mickey <<EMAIL>>'
  - 'Narsimham Chelluri <<EMAIL>>'
  - 'Narsimham Chelluri <<EMAIL>>'
  - 'Nick Wellnhofer <<EMAIL>>'
  - 'Nobuo Danjou <<EMAIL>>'
  - 'Olaf Alders <<EMAIL>>'
  - 'Oliver Gorwits <<EMAIL>>'
  - 'Oliver Paukstadt <<EMAIL>>'
  - 'Oliver Trosien <<EMAIL>>'
  - 'Olivier Mengué <<EMAIL>>'
  - 'osfameron <<EMAIL>>'
  - 'Panu Ervamaa <<EMAIL>>'
  - 'Paul Driver <<EMAIL>>'
  - 'Pedro Melo <<EMAIL>>'
  - 'Perlover <<EMAIL>>'
  - 'Peter Flanigan <<EMAIL>>'
  - 'Peter Makholm <<EMAIL>>'
  - 'Piotr Roszatycki <<EMAIL>>'
  - 'punytan <<EMAIL>>'
  - 'Rafael Kitover <<EMAIL>>'
  - 'Randy Stauner <<EMAIL>>'
  - 'Ray Miller <<EMAIL>>'
  - 'Richard Simões <<EMAIL>>'
  - 'Ricky Morse <<EMAIL>>'
  - 'Robert Rothenberg <<EMAIL>>'
  - 'Rob Hoelz <<EMAIL>>'
  - 'runarb <<EMAIL>>'
  - 'Ryo Miyake <<EMAIL>>'
  - 'Sawyer X <<EMAIL>>'
  - 'Scott S. McCoy <<EMAIL>>'
  - 'Shawn M Moore <<EMAIL>>'
  - 'Shoichi Kaji <<EMAIL>>'
  - 'Slaven Rezic <<EMAIL>>'
  - 'smcmurray <<EMAIL>>'
  - 'Stephen Clouse <<EMAIL>>'
  - 'Stevan Little <<EMAIL>>'
  - 'Stig Palmquist <***********>'
  - 'Stuart A Johnston <<EMAIL>>'
  - 'Takeshi OKURA <<EMAIL>>'
  - 'Tatsuhiko Miyagawa <<EMAIL>>'
  - 'Tatsuhiko Miyagawa <<EMAIL>>'
  - 'The Dumb Terminal <<EMAIL>>'
  - 'Thomas Klausner <<EMAIL>>'
  - 'Thomas Sibley <<EMAIL>>'
  - 'Tim Bunce <<EMAIL>>'
  - 'Tokuhiro Matsuno <<EMAIL>>'
  - 'Tomas Doran <<EMAIL>>'
  - 'Tom Heady <<EMAIL>>'
  - 'vti <<EMAIL>>'
  - 'Wallace Reis <<EMAIL>>'
  - 'xaicron <<EMAIL>>'
  - 'Yann Kerherve <<EMAIL>>'
  - 'yappo <<EMAIL>>'
  - 'Yury Zavarin <<EMAIL>>'
  - 'Yuval Kogman <<EMAIL>>'
  - '唐鳳 <<EMAIL>>'
x_generated_by_perl: v5.34.1
x_serialization_backend: 'CPAN::Meta::YAML version 0.020'
x_spdx_expression: 'Artistic-1.0-Perl OR GPL-1.0-or-later'
x_static_install: 1
