do{ my $x = [
       {
         'ARGV' => []
       },
       {},
       {
         'PL_files' => {},
         '_added_to_INC' => [
                              'E:\\mohammi\\LedgerSMB\\data\\.cpanm\\work\\**********.5892\\WWW-Form-UrlEncoded-0.26\\_build\\lib'
                            ],
         'allow_mb_mismatch' => 0,
         'allow_pureperl' => 0,
         'auto_configure_requires' => 1,
         'autosplit' => undef,
         'base_dir' => 'E:/mohammi/LedgerSMB/data/.cpanm/work/**********.5892/WWW-Form-UrlEncoded-0.26',
         'bindoc_dirs' => [
                            'blib\\script'
                          ],
         'blib' => 'blib',
         'build_bat' => 0,
         'build_class' => 'MyBuilder',
         'build_elements' => [
                               'PL',
                               'support',
                               'pm',
                               'xs',
                               'share_dir',
                               'pod',
                               'script'
                             ],
         'build_requires' => {},
         'build_script' => 'Build',
         'bundle_inc' => [],
         'bundle_inc_preload' => [],
         'c_source' => undef,
         'config' => undef,
         'config_dir' => '_build',
         'configure_requires' => {
                                   'Module::Build' => '0.4005'
                                 },
         'conflicts' => {},
         'cover' => undef,
         'cpan_client' => 'cpan',
         'create_license' => undef,
         'create_makefile_pl' => undef,
         'create_packlist' => 1,
         'create_readme' => undef,
         'debug' => undef,
         'debugger' => undef,
         'destdir' => undef,
         'dist_abstract' => undef,
         'dist_author' => undef,
         'dist_name' => 'WWW-Form-UrlEncoded',
         'dist_suffix' => undef,
         'dist_version' => '0.26',
         'dist_version_from' => 'lib/WWW/Form/UrlEncoded.pm',
         'dynamic_config' => 0,
         'extra_compiler_flags' => [],
         'extra_linker_flags' => [],
         'extra_manify_args' => undef,
         'get_options' => {},
         'has_config_data' => undef,
         'html_css' => '',
         'include_dirs' => [],
         'install_base' => undef,
         'install_base_relpaths' => {},
         'install_path' => {},
         'install_sets' => {},
         'installdirs' => 'site',
         'libdoc_dirs' => [
                            'blib\\lib',
                            'blib\\arch'
                          ],
         'license' => 'perl_5',
         'magic_number' => undef,
         'mb_version' => '0.4234',
         'meta_add' => {},
         'meta_merge' => {},
         'metafile' => 'META.yml',
         'metafile2' => 'META.json',
         'module_name' => 'WWW::Form::UrlEncoded',
         'mymetafile' => 'MYMETA.yml',
         'mymetafile2' => 'MYMETA.json',
         'name' => 'WWW-Form-UrlEncoded',
         'needs_compiler' => !!0,
         'orig_dir' => 'E:/mohammi/LedgerSMB/data/.cpanm/work/**********.5892/WWW-Form-UrlEncoded-0.26',
         'original_prefix' => {},
         'perl' => 'E:\\mohammi\\LedgerSMB\\perl\\bin\\perl.exe',
         'pm_files' => undef,
         'pod_files' => undef,
         'pollute' => undef,
         'prefix' => undef,
         'prefix_relpaths' => {},
         'prereq_action_types' => [
                                    'requires',
                                    'build_requires',
                                    'test_requires',
                                    'conflicts',
                                    'recommends'
                                  ],
         'program_name' => undef,
         'pureperl_only' => 0,
         'quiet' => undef,
         'recommends' => {},
         'recurse_into' => [],
         'recursive_test_files' => 1,
         'release_status' => 'stable',
         'requires' => {
                         'Exporter' => '0',
                         'perl' => '5.008001'
                       },
         'script_files' => [],
         'scripts' => undef,
         'share_dir' => undef,
         'sign' => undef,
         'suggests' => {
                         'WWW::Form::UrlEncoded::XS' => '0.19'
                       },
         'tap_harness_args' => {},
         'test_file_exts' => [
                               '.t'
                             ],
         'test_files' => 't/',
         'test_requires' => {
                              'JSON::PP' => '2',
                              'Test::More' => '0.98'
                            },
         'use_rcfile' => 1,
         'use_tap_harness' => 0,
         'verbose' => undef,
         'xs_files' => undef
       }
     ];
$x; }