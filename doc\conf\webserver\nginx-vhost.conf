# This is a 'vhost' definition file example for use with Starman/LedgerSMB
# reverse proxying.
#
# Please replace the following parameters:
#
#   * WORKING_DIR
#   * YOUR_SERVER_NAME
#   * SSL_KEY_FILE
#   * SSL_CERT_FILE
#
#
server {
  listen 443 ssl;
  listen [::]:443 ssl ipv6only=on;

  server_name YOUR_SERVER_NAME;

  # If you own a publicly exposed server, consider submitting it
  # to the SSL security tests available at
  #    https://www.ssllabs.com/ssltest/

  # Replace snippets/snakeoil.conf with either your own version of snakeoil.conf
  # or modified versions of the following ssl_certificate, ssl_certificate_key lines
  include snippets/snakeoil.conf;
  #ssl_certificate SSL_CERT_FILE;
  # after expansion, the above may look like:
  # ssl_certificate /etc/certs/example.com.pem;
  #ssl_certificate_key SSL_KEY_FILE;

  root WORKING_DIR/UI;

  gzip off;
  gzip_static on;

  # Configuration files don't exist
  location ^~ \.conf$ {
     return 404;
  }
  # 'Hidden' files don't exist
  location ~ /\. {
     return 404;
  }
  location / {
     try_files $uri @strippedprefix @starman;
  }

  location @strippedprefix {
     rewrite ^/([a-z0-9A-Z]+)/(.*) /$2 break;
  }

  location @starman {
     # If you changed the port in the Starman service file, change it here too
     proxy_pass          http://localhost:5762;
     proxy_read_timeout  300;
     proxy_set_header    Host $host;
     proxy_set_header    X-Real-IP $remote_addr;
     proxy_set_header    X-Forwarded-For $proxy_add_x_forwarded_for;
     proxy_set_header    X-Forwarded-Host $host;
     proxy_set_header    X-Forwarded-Server $host;
     proxy_set_header    X-Forwarded-Proto $scheme;
  }

}
