#!/usr/bin/perl
use strict;
use warnings;
use HTTP::Server::Simple::CGI;
use DBI;

# Simple LedgerSMB-like server
package SimpleLedgerSMB;
use base qw(HTTP::Server::Simple::CGI);

sub handle_request {
    my $self = shift;
    my $cgi  = shift;
    
    my $path = $cgi->path_info() || '/';
    
    print "HTTP/1.0 200 OK\r\n";
    print "Content-Type: text/html; charset=utf-8\r\n\r\n";
    
    if ($path eq '/setup.pl' || $path eq '/setup') {
        print $self->setup_page();
    } elsif ($path eq '/login.pl' || $path eq '/login') {
        print $self->login_page();
    } elsif ($path eq '/admin.pl' || $path eq '/admin') {
        print $self->admin_page();
    } else {
        print $self->main_page();
    }
}

sub setup_page {
    return <<'HTML';
<!DOCTYPE html>
<html>
<head>
    <title>LedgerSMB Setup</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: #2c5aa0; color: white; padding: 20px; text-align: center; margin: -30px -30px 30px -30px; border-radius: 8px 8px 0 0; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 10px; margin-bottom: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        button { background: #2c5aa0; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        button:hover { background: #1e3d6f; }
        .info { background: #e7f3ff; padding: 15px; margin: 15px 0; border-left: 4px solid #2c5aa0; border-radius: 4px; }
        .success { background: #d4edda; padding: 15px; margin: 15px 0; border-left: 4px solid #28a745; border-radius: 4px; color: #155724; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 LedgerSMB Setup</h1>
            <p>نظام المحاسبة مفتوح المصدر</p>
        </div>
        
        <div class="success">
            <h3>✅ حالة النظام</h3>
            <p><strong>قاعدة البيانات:</strong> PostgreSQL 14 متصلة</p>
            <p><strong>الخادم:</strong> يعمل على المنفذ 5500</p>
            <p><strong>Perl:</strong> الإصدار 5.40.2</p>
        </div>
        
        <h3>إعداد الشركة</h3>
        <form action="/admin.pl" method="post">
            <div class="form-group">
                <label for="company_name">اسم الشركة:</label>
                <input type="text" id="company_name" name="company_name" value="شركتي" required>
            </div>
            
            <div class="form-group">
                <label for="admin_user">اسم المستخدم الإداري:</label>
                <input type="text" id="admin_user" name="admin_user" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="admin_password">كلمة المرور:</label>
                <input type="password" id="admin_password" name="admin_password" value="admin123" required>
            </div>
            
            <div class="form-group">
                <label for="currency">العملة:</label>
                <select id="currency" name="currency">
                    <option value="YER">ريال يمني (YER)</option>
                    <option value="USD">دولار أمريكي (USD)</option>
                    <option value="EUR">يورو (EUR)</option>
                    <option value="SAR">ريال سعودي (SAR)</option>
                </select>
            </div>
            
            <button type="submit">إنشاء قاعدة البيانات</button>
        </form>
        
        <div class="info">
            <h3>📋 الخطوات التالية</h3>
            <p>بعد إنشاء قاعدة البيانات، يمكنك:</p>
            <ul>
                <li>إضافة العملاء والموردين</li>
                <li>إعداد دليل الحسابات</li>
                <li>إنشاء الفواتير والمدفوعات</li>
                <li>عرض التقارير المالية</li>
            </ul>
        </div>
        
        <p style="text-align: center; margin-top: 30px;">
            <a href="/login.pl" style="color: #2c5aa0; text-decoration: none;">← العودة لصفحة تسجيل الدخول</a>
        </p>
    </div>
</body>
</html>
HTML
}

sub login_page {
    return <<'HTML';
<!DOCTYPE html>
<html>
<head>
    <title>LedgerSMB - تسجيل الدخول</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }
        .login-container { background: white; padding: 40px; border-radius: 12px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); width: 100%; max-width: 400px; }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 48px; margin-bottom: 10px; }
        h2 { color: #333; margin: 0; font-size: 28px; }
        .subtitle { color: #666; margin: 5px 0 0 0; }
        .form-group { margin: 20px 0; }
        label { display: block; margin-bottom: 8px; font-weight: bold; color: #333; }
        input, select { width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 6px; font-size: 16px; box-sizing: border-box; transition: border-color 0.3s; }
        input:focus, select:focus { outline: none; border-color: #667eea; }
        button { width: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 14px; border: none; border-radius: 6px; cursor: pointer; font-size: 16px; font-weight: bold; transition: transform 0.2s; }
        button:hover { transform: translateY(-2px); }
        .demo-info { background: #f8f9fa; padding: 15px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #28a745; }
        .demo-info h4 { margin: 0 0 10px 0; color: #28a745; }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="header">
            <div class="logo">📊</div>
            <h2>LedgerSMB</h2>
            <p class="subtitle">نظام المحاسبة المتكامل</p>
        </div>
        
        <div class="demo-info">
            <h4>🔑 بيانات تجريبية</h4>
            <p><strong>المستخدم:</strong> admin</p>
            <p><strong>كلمة المرور:</strong> admin123</p>
        </div>
        
        <form action="/admin.pl" method="post">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" value="admin123" required>
            </div>
            
            <div class="form-group">
                <label for="database">قاعدة البيانات:</label>
                <select id="database" name="database">
                    <option value="ledgersmb_db">ledgersmb_db</option>
                </select>
            </div>
            
            <button type="submit">دخول</button>
        </form>
        
        <p style="text-align: center; margin-top: 20px;">
            <a href="/setup.pl" style="color: #667eea; text-decoration: none;">إعداد جديد</a>
        </p>
    </div>
</body>
</html>
HTML
}

sub admin_page {
    return <<'HTML';
<!DOCTYPE html>
<html>
<head>
    <title>LedgerSMB - لوحة التحكم</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; background: #f8f9fa; }
        .header { background: #2c5aa0; color: white; padding: 15px 30px; display: flex; justify-content: space-between; align-items: center; }
        .nav { background: #34495e; padding: 0; }
        .nav ul { list-style: none; margin: 0; padding: 0; display: flex; }
        .nav li { margin: 0; }
        .nav a { display: block; padding: 15px 20px; color: white; text-decoration: none; transition: background 0.3s; }
        .nav a:hover { background: #2c3e50; }
        .container { padding: 30px; max-width: 1200px; margin: 0 auto; }
        .dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .card h3 { margin: 0 0 15px 0; color: #2c5aa0; }
        .btn { display: inline-block; background: #2c5aa0; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 5px; transition: background 0.3s; }
        .btn:hover { background: #1e3d6f; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .stat h4 { margin: 0; font-size: 24px; }
        .stat p { margin: 5px 0 0 0; opacity: 0.9; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 LedgerSMB - لوحة التحكم</h1>
        <div>مرحباً، admin | <a href="/login.pl" style="color: white;">خروج</a></div>
    </div>
    
    <nav class="nav">
        <ul>
            <li><a href="/admin.pl">الرئيسية</a></li>
            <li><a href="#sales">المبيعات</a></li>
            <li><a href="#purchases">المشتريات</a></li>
            <li><a href="#accounts">الحسابات</a></li>
            <li><a href="#reports">التقارير</a></li>
            <li><a href="#settings">الإعدادات</a></li>
        </ul>
    </nav>
    
    <div class="container">
        <div class="stats">
            <div class="stat">
                <h4>0</h4>
                <p>إجمالي الفواتير</p>
            </div>
            <div class="stat">
                <h4>0 ر.ي</h4>
                <p>إجمالي المبيعات</p>
            </div>
            <div class="stat">
                <h4>0</h4>
                <p>العملاء</p>
            </div>
            <div class="stat">
                <h4>0</h4>
                <p>المنتجات</p>
            </div>
        </div>
        
        <div class="dashboard">
            <div class="card">
                <h3>🧾 المبيعات</h3>
                <p>إدارة الفواتير والعروض والطلبات</p>
                <a href="#" class="btn">فاتورة جديدة</a>
                <a href="#" class="btn">عرض سعر</a>
                <a href="#" class="btn">قائمة الفواتير</a>
            </div>
            
            <div class="card">
                <h3>🛒 المشتريات</h3>
                <p>إدارة طلبات الشراء والموردين</p>
                <a href="#" class="btn">طلب شراء</a>
                <a href="#" class="btn">إدارة الموردين</a>
                <a href="#" class="btn">تقارير المشتريات</a>
            </div>
            
            <div class="card">
                <h3>👥 العملاء والموردين</h3>
                <p>إدارة بيانات العملاء والموردين</p>
                <a href="#" class="btn">عميل جديد</a>
                <a href="#" class="btn">مورد جديد</a>
                <a href="#" class="btn">قائمة العملاء</a>
            </div>
            
            <div class="card">
                <h3>📦 المخزون</h3>
                <p>إدارة المنتجات والمخزون</p>
                <a href="#" class="btn">منتج جديد</a>
                <a href="#" class="btn">جرد المخزون</a>
                <a href="#" class="btn">تقارير المخزون</a>
            </div>
            
            <div class="card">
                <h3>💰 الحسابات</h3>
                <p>دليل الحسابات والقيود المحاسبية</p>
                <a href="#" class="btn">قيد جديد</a>
                <a href="#" class="btn">دليل الحسابات</a>
                <a href="#" class="btn">ميزان المراجعة</a>
            </div>
            
            <div class="card">
                <h3>📊 التقارير</h3>
                <p>التقارير المالية والإحصائيات</p>
                <a href="#" class="btn">قائمة الدخل</a>
                <a href="#" class="btn">الميزانية العمومية</a>
                <a href="#" class="btn">تقارير مخصصة</a>
            </div>
        </div>
        
        <div class="card">
            <h3>🔧 حالة النظام</h3>
            <p>✅ قاعدة البيانات: متصلة (PostgreSQL 14)</p>
            <p>✅ الخادم: يعمل على المنفذ 5500</p>
            <p>✅ النظام: جاهز للاستخدام</p>
            <p>⚠️ هذا إصدار تطويري - للاستخدام التجريبي فقط</p>
        </div>
    </div>
</body>
</html>
HTML
}

sub main_page {
    return <<'HTML';
<!DOCTYPE html>
<html>
<head>
    <title>LedgerSMB</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }
        .container { background: white; padding: 40px; border-radius: 12px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); text-align: center; max-width: 500px; }
        .logo { font-size: 64px; margin-bottom: 20px; }
        h1 { color: #333; margin: 0 0 10px 0; }
        .subtitle { color: #666; margin-bottom: 30px; }
        .buttons { display: flex; gap: 15px; justify-content: center; flex-wrap: wrap; }
        .btn { display: inline-block; background: #2c5aa0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; transition: transform 0.2s; }
        .btn:hover { transform: translateY(-2px); }
        .btn.secondary { background: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">📊</div>
        <h1>LedgerSMB</h1>
        <p class="subtitle">نظام المحاسبة مفتوح المصدر</p>
        
        <div class="buttons">
            <a href="/login.pl" class="btn">تسجيل الدخول</a>
            <a href="/setup.pl" class="btn secondary">إعداد النظام</a>
        </div>
        
        <p style="margin-top: 30px; color: #666; font-size: 14px;">
            الخادم يعمل على المنفذ 5500
        </p>
    </div>
</body>
</html>
HTML
}

# Start the server
my $port = 5500;
my $server = SimpleLedgerSMB->new($port);

print "🚀 LedgerSMB Server starting on port $port\n";
print "📊 Setup: http://localhost:$port/setup.pl\n";
print "🔑 Login: http://localhost:$port/login.pl\n";
print "🏠 Home: http://localhost:$port/\n";
print "Press Ctrl+C to stop the server\n\n";

$server->run();
