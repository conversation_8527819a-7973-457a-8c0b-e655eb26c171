cpanm (App::cpanminus) 1.7048 on perl 5.040002 built for MSWin32-x64-multi-thread
Work directory is E:\mohammi\LedgerSMB\data/.cpanm/work/1752941683.5892
You have LWP 6.78
Falling back to Archive::Tar 3.04
Searching Plack () on cpanmetadb ...
--> Working on Plack
Fetching http://www.cpan.org/authors/id/M/MI/MIYAGAWA/Plack-1.0051.tar.gz
-> OK
Unpacking Plack-1.0051.tar.gz
Entering Plack-1.0051
Checking configure dependencies from META.json
Checking if you have File::ShareDir::Install 0.06 ... Yes (0.14)
Checking if you have ExtUtils::MakeMaker 6.58 ... Yes (7.74)
Configuring Plack-1.0051
Running Makefile.PL
Warning: prerequisite Apache::LogFormat::Compiler 0.33 not found.
Warning: prerequisite Cookie::<PERSON> 0.07 not found.
Warning: prerequisite Devel::StackTrace::AsHTML 0.11 not found.
Warning: prerequisite Filesys::Notify::Simple 0 not found.
Warning: prerequisite HTTP::Entity::Parser 0.25 not found.
Warning: prerequisite HTTP::Headers::Fast 0.18 not found.
Warning: prerequisite Hash::MultiValue 0.05 not found.
Warning: prerequisite Stream::Buffered 0.02 not found.
Warning: prerequisite Test::TCP 2.15 not found.
Warning: prerequisite WWW::Form::UrlEncoded 0.23 not found.
Checking if your kit is complete...
Looks good
Generating a gmake-style Makefile
Writing Makefile for Plack
Writing MYMETA.yml and MYMETA.json
-> OK
Checking dependencies from MYMETA.json ...
Checking if you have WWW::Form::UrlEncoded 0.23 ... No
Checking if you have File::ShareDir 1.00 ... Yes (1.118)
Checking if you have HTTP::Message 5.814 ... Yes (7.00)
Checking if you have Apache::LogFormat::Compiler 0.33 ... No
Checking if you have Devel::StackTrace::AsHTML 0.11 ... No
Checking if you have Devel::StackTrace 1.23 ... Yes (2.05)
Checking if you have HTTP::Tiny 0.034 ... Yes (0.090)
Checking if you have URI 1.59 ... Yes (5.32)
Checking if you have Pod::Usage 1.36 ... Yes (2.05)
Checking if you have Try::Tiny 0 ... Yes (0.32)
Checking if you have Test::TCP 2.15 ... No
Checking if you have HTTP::Entity::Parser 0.25 ... No
Checking if you have Hash::MultiValue 0.05 ... No
Checking if you have Cookie::Baker 0.07 ... No
Checking if you have Filesys::Notify::Simple 0 ... No
Checking if you have HTTP::Headers::Fast 0.18 ... No
Checking if you have ExtUtils::MakeMaker 0 ... Yes (7.74)
Checking if you have Stream::Buffered 0.02 ... No
Checking if you have parent 0 ... Yes (0.244)
==> Found dependencies: WWW::Form::UrlEncoded, Apache::LogFormat::Compiler, Devel::StackTrace::AsHTML, Test::TCP, HTTP::Entity::Parser, Hash::MultiValue, Cookie::Baker, Filesys::Notify::Simple, HTTP::Headers::Fast, Stream::Buffered
Searching WWW::Form::UrlEncoded (0.23) on cpanmetadb ...
--> Working on WWW::Form::UrlEncoded
Fetching http://www.cpan.org/authors/id/K/KA/KAZEBURO/WWW-Form-UrlEncoded-0.26.tar.gz
-> OK
Unpacking WWW-Form-UrlEncoded-0.26.tar.gz
Entering WWW-Form-UrlEncoded-0.26
Checking configure dependencies from META.json
Checking if you have ExtUtils::Install 1.46 ... Yes (2.22)
Checking if you have Module::Build 0.4005 ... Yes (0.4234)
Configuring WWW-Form-UrlEncoded-0.26
Running Build.PL
Created MYMETA.yml and MYMETA.json
Creating new 'Build' script for 'WWW-Form-UrlEncoded' version '0.26'
-> OK
Checking dependencies from MYMETA.json ...
Checking if you have Exporter 0 ... Yes (5.78)
Building WWW-Form-UrlEncoded-0.26
Building WWW-Form-UrlEncoded
Building WWW-Form-UrlEncoded
Files found in blib\arch: installing files in blib\lib into architecture dependent library tree
Installing E:\mohammi\LedgerSMB\perl\site\lib\auto\WWW\Form\UrlEncoded\XS\.keep
Installing E:\mohammi\LedgerSMB\perl\site\lib\WWW\Form\UrlEncoded.pm
Installing E:\mohammi\LedgerSMB\perl\site\lib\WWW\Form\UrlEncoded\PP.pm
-> OK
Successfully installed WWW-Form-UrlEncoded-0.26
Installing E:\mohammi\LedgerSMB\perl\site\lib\MSWin32-x64-multi-thread\.meta\WWW-Form-UrlEncoded-0.26\install.json
Installing E:\mohammi\LedgerSMB\perl\site\lib\MSWin32-x64-multi-thread\.meta\WWW-Form-UrlEncoded-0.26\MYMETA.json
Searching Apache::LogFormat::Compiler (0.33) on cpanmetadb ...
--> Working on Apache::LogFormat::Compiler
Fetching http://www.cpan.org/authors/id/K/KA/KAZEBURO/Apache-LogFormat-Compiler-0.36.tar.gz
-> OK
Unpacking Apache-LogFormat-Compiler-0.36.tar.gz
Entering Apache-LogFormat-Compiler-0.36
Checking configure dependencies from META.json
Checking if you have Module::Build::Tiny 0.035 ... Yes (0.051)
Configuring Apache-LogFormat-Compiler-0.36
Running Build.PL
Creating new 'Build' script for 'Apache-LogFormat-Compiler' version '0.36'
-> OK
Checking dependencies from MYMETA.json ...
Checking if you have POSIX::strftime::Compiler 0.30 ... No
Checking if you have Time::Local 0 ... Yes (1.35)
Checking if you have POSIX 0 ... Yes (2.20)
==> Found dependencies: POSIX::strftime::Compiler
Searching POSIX::strftime::Compiler (0.30) on cpanmetadb ...
--> Working on POSIX::strftime::Compiler
Fetching http://www.cpan.org/authors/id/K/KA/KAZEBURO/POSIX-strftime-Compiler-0.46.tar.gz
-> OK
Unpacking POSIX-strftime-Compiler-0.46.tar.gz
Entering POSIX-strftime-Compiler-0.46
Checking configure dependencies from META.json
Checking if you have Module::Build::Tiny 0.035 ... Yes (0.051)
Configuring POSIX-strftime-Compiler-0.46
Running Build.PL
Creating new 'Build' script for 'POSIX-strftime-Compiler' version '0.46'
-> OK
Checking dependencies from MYMETA.json ...
Checking if you have Time::Local 0 ... Yes (1.35)
Checking if you have POSIX 0 ... Yes (2.20)
Checking if you have Carp 0 ... Yes (1.54)
Checking if you have Exporter 0 ... Yes (5.78)
Building POSIX-strftime-Compiler-0.46
cp lib/POSIX/strftime/Compiler.pm blib\lib\POSIX\strftime\Compiler.pm
Installing E:\mohammi\LedgerSMB\perl\site\lib\POSIX\strftime\Compiler.pm
-> OK
Successfully installed POSIX-strftime-Compiler-0.46
Installing E:\mohammi\LedgerSMB\perl\site\lib\MSWin32-x64-multi-thread\.meta\POSIX-strftime-Compiler-0.46\install.json
Installing E:\mohammi\LedgerSMB\perl\site\lib\MSWin32-x64-multi-thread\.meta\POSIX-strftime-Compiler-0.46\MYMETA.json
Building Apache-LogFormat-Compiler-0.36
cp lib/Apache/LogFormat/Compiler.pm blib\lib\Apache\LogFormat\Compiler.pm
Installing E:\mohammi\LedgerSMB\perl\site\lib\Apache\LogFormat\Compiler.pm
-> OK
Successfully installed Apache-LogFormat-Compiler-0.36
Installing E:\mohammi\LedgerSMB\perl\site\lib\MSWin32-x64-multi-thread\.meta\Apache-LogFormat-Compiler-0.36\install.json
Installing E:\mohammi\LedgerSMB\perl\site\lib\MSWin32-x64-multi-thread\.meta\Apache-LogFormat-Compiler-0.36\MYMETA.json
Searching Devel::StackTrace::AsHTML (0.11) on cpanmetadb ...
--> Working on Devel::StackTrace::AsHTML
Fetching http://www.cpan.org/authors/id/M/MI/MIYAGAWA/Devel-StackTrace-AsHTML-0.15.tar.gz
-> OK
Unpacking Devel-StackTrace-AsHTML-0.15.tar.gz
Entering Devel-StackTrace-AsHTML-0.15
Checking configure dependencies from META.json
Checking if you have ExtUtils::MakeMaker 6.58 ... Yes (7.74)
Configuring Devel-StackTrace-AsHTML-0.15
Running Makefile.PL
Checking if your kit is complete...
Looks good
Generating a gmake-style Makefile
Writing Makefile for Devel::StackTrace::AsHTML
Writing MYMETA.yml and MYMETA.json
-> OK
Checking dependencies from MYMETA.json ...
Checking if you have Devel::StackTrace 0 ... Yes (2.05)
Checking if you have ExtUtils::MakeMaker 0 ... Yes (7.74)
-> FAIL Can't configure the distribution. You probably need to have 'make'. See E:\mohammi\LedgerSMB\data\.cpanm\work\1752941683.5892\build.log for details.
Searching Test::TCP (2.15) on cpanmetadb ...
--> Working on Test::TCP
Fetching http://www.cpan.org/authors/id/M/MI/MIYAGAWA/Test-TCP-2.22.tar.gz
-> OK
Unpacking Test-TCP-2.22.tar.gz
Entering Test-TCP-2.22
Checking configure dependencies from META.json
Checking if you have ExtUtils::MakeMaker 6.64 ... Yes (7.74)
Configuring Test-TCP-2.22
Running Makefile.PL
Warning: prerequisite Test::SharedFork 0.29 not found.
Checking if your kit is complete...
Looks good
Generating a gmake-style Makefile
Writing Makefile for Test::TCP
Writing MYMETA.yml and MYMETA.json
-> OK
Checking dependencies from MYMETA.json ...
Checking if you have Time::HiRes 0 ... Yes (1.9777)
Checking if you have Test::SharedFork 0.29 ... No
Checking if you have IO::Socket::INET 0 ... Yes (1.55)
Checking if you have Test::More 0 ... Yes (1.302211)
Checking if you have IO::Socket::IP 0 ... Yes (0.43)
==> Found dependencies: Test::SharedFork
Searching Test::SharedFork (0.29) on cpanmetadb ...
--> Working on Test::SharedFork
Fetching http://www.cpan.org/authors/id/E/EX/EXODIST/Test-SharedFork-0.35.tar.gz
-> OK
Unpacking Test-SharedFork-0.35.tar.gz
Entering Test-SharedFork-0.35
Checking configure dependencies from META.json
Checking if you have ExtUtils::MakeMaker 6.64 ... Yes (7.74)
Configuring Test-SharedFork-0.35
Running Makefile.PL
Checking if your kit is complete...
Looks good
Generating a gmake-style Makefile
Writing Makefile for Test::SharedFork
Writing MYMETA.yml and MYMETA.json
-> OK
Checking dependencies from MYMETA.json ...
Checking if you have Test::More 0.88 ... Yes (1.302211)
Checking if you have Test::Builder 0.32 ... Yes (1.302211)
Checking if you have Test::Builder::Module 0 ... Yes (1.302211)
Checking if you have File::Temp 0 ... Yes (0.2311)
-> FAIL Can't configure the distribution. You probably need to have 'make'. See E:\mohammi\LedgerSMB\data\.cpanm\work\1752941683.5892\build.log for details.
-> FAIL Installing the dependencies failed: Module 'Test::SharedFork' is not installed
-> FAIL Bailing out the installation for Test-TCP-2.22.
Searching HTTP::Entity::Parser (0.25) on cpanmetadb ...
--> Working on HTTP::Entity::Parser
Fetching http://www.cpan.org/authors/id/K/KA/KAZEBURO/HTTP-Entity-Parser-0.25.tar.gz
-> OK
Unpacking HTTP-Entity-Parser-0.25.tar.gz
Entering HTTP-Entity-Parser-0.25
Checking configure dependencies from META.json
Checking if you have Module::Build::Tiny 0.035 ... Yes (0.051)
Configuring HTTP-Entity-Parser-0.25
Running Build.PL
Creating new 'Build' script for 'HTTP-Entity-Parser' version '0.25'
-> OK
Checking dependencies from MYMETA.json ...
Checking if you have File::Temp 0 ... Yes (0.2311)
Checking if you have WWW::Form::UrlEncoded 0.23 ... Yes (0.26)
Checking if you have Module::Load 0 ... Yes (0.36)
Checking if you have Encode 0 ... Yes (3.21)
Checking if you have JSON::MaybeXS 1.003007 ... Yes (1.004008)
Checking if you have Hash::MultiValue 0 ... No
Checking if you have HTTP::MultiPartParser 0 ... No
Checking if you have Stream::Buffered 0 ... No
==> Found dependencies: Hash::MultiValue, HTTP::MultiPartParser, Stream::Buffered
Searching Hash::MultiValue (0) on cpanmetadb ...
--> Working on Hash::MultiValue
Fetching http://www.cpan.org/authors/id/A/AR/ARISTOTLE/Hash-MultiValue-0.16.tar.gz
-> OK
Unpacking Hash-MultiValue-0.16.tar.gz
Entering Hash-MultiValue-0.16
Checking configure dependencies from META.json
Checking if you have ExtUtils::MakeMaker 6.58 ... Yes (7.74)
Configuring Hash-MultiValue-0.16
Running Makefile.PL
Checking if your kit is complete...
Looks good
Generating a gmake-style Makefile
Writing Makefile for Hash::MultiValue
Writing MYMETA.yml and MYMETA.json
-> OK
Checking dependencies from MYMETA.json ...
Checking if you have ExtUtils::MakeMaker 0 ... Yes (7.74)
-> FAIL Can't configure the distribution. You probably need to have 'make'. See E:\mohammi\LedgerSMB\data\.cpanm\work\1752941683.5892\build.log for details.
Searching HTTP::MultiPartParser (0) on cpanmetadb ...
--> Working on HTTP::MultiPartParser
Fetching http://www.cpan.org/authors/id/C/CH/CHANSEN/HTTP-MultiPartParser-0.02.tar.gz
-> OK
Unpacking HTTP-MultiPartParser-0.02.tar.gz
Entering HTTP-MultiPartParser-0.02
Checking configure dependencies from META.yml
Checking if you have ExtUtils::MakeMaker 6.59 ... Yes (7.74)
Configuring HTTP-MultiPartParser-0.02
Running Makefile.PL
Checking if your kit is complete...
Looks good
Generating a gmake-style Makefile
Writing Makefile for HTTP::MultiPartParser
Writing MYMETA.yml and MYMETA.json
-> OK
Checking dependencies from MYMETA.json ...
Checking if you have Test::More 0.88 ... Yes (1.302211)
Checking if you have ExtUtils::MakeMaker 0 ... Yes (7.74)
Checking if you have Test::Deep 0 ... Yes (1.205)
Checking if you have Scalar::Util 0 ... Yes (1.69)
Checking if you have Carp 0 ... Yes (1.54)
-> FAIL Can't configure the distribution. You probably need to have 'make'. See E:\mohammi\LedgerSMB\data\.cpanm\work\1752941683.5892\build.log for details.
Searching Stream::Buffered (0) on cpanmetadb ...
--> Working on Stream::Buffered
Fetching http://www.cpan.org/authors/id/D/DO/DOY/Stream-Buffered-0.03.tar.gz
-> OK
Unpacking Stream-Buffered-0.03.tar.gz
Entering Stream-Buffered-0.03
Checking configure dependencies from META.json
Checking if you have ExtUtils::MakeMaker 6.58 ... Yes (7.74)
Configuring Stream-Buffered-0.03
Running Makefile.PL
Checking if your kit is complete...
Looks good
Generating a gmake-style Makefile
Writing Makefile for Stream::Buffered
Writing MYMETA.yml and MYMETA.json
-> OK
Checking dependencies from MYMETA.json ...
Checking if you have IO::File 1.14 ... Yes (1.55)
-> FAIL Can't configure the distribution. You probably need to have 'make'. See E:\mohammi\LedgerSMB\data\.cpanm\work\1752941683.5892\build.log for details.
-> FAIL Installing the dependencies failed: Module 'Stream::Buffered' is not installed, Module 'HTTP::MultiPartParser' is not installed, Module 'Hash::MultiValue' is not installed
-> FAIL Bailing out the installation for HTTP-Entity-Parser-0.25.
Already tried Hash::MultiValue. Skipping.
Searching Cookie::Baker (0.07) on cpanmetadb ...
--> Working on Cookie::Baker
Fetching http://www.cpan.org/authors/id/K/KA/KAZEBURO/Cookie-Baker-0.12.tar.gz
-> OK
Unpacking Cookie-Baker-0.12.tar.gz
Entering Cookie-Baker-0.12
Checking configure dependencies from META.json
Checking if you have Module::Build::Tiny 0.035 ... Yes (0.051)
Configuring Cookie-Baker-0.12
Running Build.PL
Creating new 'Build' script for 'Cookie-Baker' version '0.12'
-> OK
Checking dependencies from MYMETA.json ...
Checking if you have Exporter 0 ... Yes (5.78)
Checking if you have URI::Escape 0 ... Yes (5.32)
Building Cookie-Baker-0.12
cp lib/Cookie/Baker.pm blib\lib\Cookie\Baker.pm
Installing E:\mohammi\LedgerSMB\perl\site\lib\Cookie\Baker.pm
-> OK
Successfully installed Cookie-Baker-0.12
Installing E:\mohammi\LedgerSMB\perl\site\lib\MSWin32-x64-multi-thread\.meta\Cookie-Baker-0.12\install.json
Installing E:\mohammi\LedgerSMB\perl\site\lib\MSWin32-x64-multi-thread\.meta\Cookie-Baker-0.12\MYMETA.json
Searching Filesys::Notify::Simple (0) on cpanmetadb ...
--> Working on Filesys::Notify::Simple
Fetching http://www.cpan.org/authors/id/M/MI/MIYAGAWA/Filesys-Notify-Simple-0.14.tar.gz
-> OK
Unpacking Filesys-Notify-Simple-0.14.tar.gz
Entering Filesys-Notify-Simple-0.14
Checking configure dependencies from META.json
Checking if you have ExtUtils::MakeMaker 6.58 ... Yes (7.74)
Configuring Filesys-Notify-Simple-0.14
Running Makefile.PL
Warning: prerequisite Test::SharedFork 0 not found.
Checking if your kit is complete...
Looks good
Generating a gmake-style Makefile
Writing Makefile for Filesys::Notify::Simple
Writing MYMETA.yml and MYMETA.json
-> OK
Checking dependencies from MYMETA.json ...
Checking if you have ExtUtils::MakeMaker 0 ... Yes (7.74)
-> FAIL Can't configure the distribution. You probably need to have 'make'. See E:\mohammi\LedgerSMB\data\.cpanm\work\1752941683.5892\build.log for details.
Searching HTTP::Headers::Fast (0.18) on cpanmetadb ...
--> Working on HTTP::Headers::Fast
Fetching http://www.cpan.org/authors/id/T/TO/TOKUHIROM/HTTP-Headers-Fast-0.22.tar.gz
-> OK
Unpacking HTTP-Headers-Fast-0.22.tar.gz
Entering HTTP-Headers-Fast-0.22
Checking configure dependencies from META.json
Checking if you have Module::Build::Tiny 0.035 ... Yes (0.051)
Configuring HTTP-Headers-Fast-0.22
Running Build.PL
Creating new 'Build' script for 'HTTP-Headers-Fast' version '0.22'
-> OK
Checking dependencies from MYMETA.json ...
Checking if you have HTTP::Date 0 ... Yes (6.06)
Building HTTP-Headers-Fast-0.22
cp lib/HTTP/Headers/Fast.pm blib\lib\HTTP\Headers\Fast.pm
Installing E:\mohammi\LedgerSMB\perl\site\lib\HTTP\Headers\Fast.pm
-> OK
Successfully installed HTTP-Headers-Fast-0.22
Installing E:\mohammi\LedgerSMB\perl\site\lib\MSWin32-x64-multi-thread\.meta\HTTP-Headers-Fast-0.22\install.json
Installing E:\mohammi\LedgerSMB\perl\site\lib\MSWin32-x64-multi-thread\.meta\HTTP-Headers-Fast-0.22\MYMETA.json
Already tried Stream::Buffered. Skipping.
-> FAIL Installing the dependencies failed: Module 'Filesys::Notify::Simple' is not installed, Module 'Hash::MultiValue' is not installed, Module 'Stream::Buffered' is not installed, Module 'HTTP::Entity::Parser' is not installed, Module 'Devel::StackTrace::AsHTML' is not installed, Module 'Test::TCP' is not installed
-> FAIL Bailing out the installation for Plack-1.0051.
5 distributions installed
