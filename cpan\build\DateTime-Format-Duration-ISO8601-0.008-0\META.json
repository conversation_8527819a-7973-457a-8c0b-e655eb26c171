{"abstract": "Parse and format ISO8601 duration", "author": ["perlancar <<EMAIL>>"], "dynamic_config": 0, "generated_by": "Dist::Zilla version 6.010, CPAN::Meta::Converter version 2.150010", "license": ["perl_5"], "meta-spec": {"url": "http://search.cpan.org/perldoc?CPAN::Meta::Spec", "version": 2}, "name": "DateTime-Format-Duration-ISO8601", "prereqs": {"configure": {"requires": {"ExtUtils::MakeMaker": "0"}}, "develop": {"requires": {"Pod::Coverage::TrustPod": "0", "Test::Perl::Critic": "0", "Test::Pod": "1.41", "Test::Pod::Coverage": "1.08"}}, "runtime": {"requires": {"DateTime::Duration": "0", "perl": "5.010001", "strict": "0", "warnings": "0"}}, "test": {"requires": {"File::Spec": "0", "IO::Handle": "0", "IPC::Open3": "0", "Test::More": "0.98"}}}, "release_status": "stable", "resources": {"bugtracker": {"web": "https://rt.cpan.org/Public/Dist/Display.html?Name=DateTime-Format-Duration-ISO8601"}, "homepage": "https://metacpan.org/release/DateTime-Format-Duration-ISO8601", "repository": {"type": "git", "url": "git://github.com/perlancar/perl-DateTime-Format-Duration-ISO8601.git", "web": "https://github.com/perlancar/perl-DateTime-Format-Duration-ISO8601"}}, "version": "0.008", "x_Dist_Zilla": {"perl": {"version": "5.026001"}, "plugins": [{"class": "Dist::Zilla::Plugin::GatherDir", "config": {"Dist::Zilla::Plugin::GatherDir": {"exclude_filename": [], "exclude_match": [], "follow_symlinks": 0, "include_dotfiles": 0, "prefix": "", "prune_directory": [], "root": "."}}, "name": "@Author::<PERSON><PERSON><PERSON><PERSON><PERSON>/@Filter/GatherDir", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::PruneCruft", "name": "@Author::P<PERSON><PERSON><PERSON><PERSON>/@Filter/PruneCruft", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::ManifestSkip", "name": "@Author::<PERSON><PERSON><PERSON><PERSON><PERSON>/@Filter/ManifestSkip", "version": "6.010"}, {"class": "Dist::<PERSON><PERSON>::Plugin::MetaYAML", "name": "@Author::<PERSON><PERSON><PERSON><PERSON><PERSON>/@Filter/MetaYAML", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::License", "name": "@Author::P<PERSON><PERSON><PERSON><PERSON>/@Filter/License", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::PodCoverageTests", "name": "@Author::P<PERSON><PERSON><PERSON><PERSON>/@Filter/PodCoverageTests", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::PodSyntaxTests", "name": "@Author::P<PERSON><PERSON><PERSON><PERSON>/@Filter/PodSyntaxTests", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::ExtraTests", "name": "@Author::<PERSON><PERSON><PERSON><PERSON><PERSON>/@Filter/ExtraTests", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::ExecDir", "name": "@Author::P<PERSON><PERSON><PERSON><PERSON>/@Filter/ExecDir", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::ShareDir", "name": "@Author::P<PERSON><PERSON><PERSON><PERSON>/@Filter/ShareDir", "version": "6.010"}, {"class": "Dist::<PERSON><PERSON>::Plugin::MakeMaker", "config": {"Dist::Zilla::Role::TestRunner": {"default_jobs": 1}}, "name": "@Author::<PERSON><PERSON><PERSON><PERSON><PERSON>/@Filter/MakeMaker", "version": "6.010"}, {"class": "Dist::<PERSON><PERSON>::Plugin::<PERSON><PERSON><PERSON>", "name": "@Author::<PERSON><PERSON><PERSON><PERSON><PERSON>/@Filter/Manifest", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::ConfirmRelease", "name": "@Author::P<PERSON><PERSON><PERSON><PERSON>/@Filter/ConfirmRelease", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::ExecDir", "name": "@Author::PERLANCAR/ExecDir script", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::PERLANCAR::BeforeBuild", "name": "@Author::PERLANCAR/PERLANCAR::BeforeBuild", "version": "0.592"}, {"class": "Dist::Zilla::Plugin::Rinci::AbstractFromMeta", "name": "@Author::PERLANCAR/Rinci::AbstractFromMeta", "version": "0.10"}, {"class": "Dist::Zilla::Plugin::PodnameFromFilename", "name": "@Author::PERLANCAR/PodnameFromFilename", "version": "0.02"}, {"class": "Dist::Zilla::Plugin::PERLANCAR::EnsurePrereqToSpec", "name": "@Author::PERLANCAR/PERLANCAR::EnsurePrereqToSpec", "version": "0.05"}, {"class": "Dist::Zilla::Plugin::PERLANCAR::MetaResources", "name": "@Author::PERLANCAR/PERLANCAR::MetaResources", "version": "0.03"}, {"class": "Dist::Z<PERSON>::Plugin::CheckChangeLog", "name": "@Author::PERLANCAR/CheckChangeLog", "version": "0.05"}, {"class": "Dist::Zilla::Plugin::CheckMetaResources", "name": "@Author::PERLANCAR/CheckMetaResources", "version": "0.001"}, {"class": "Dist::Z<PERSON>::Plugin::CopyrightYearFromGit", "name": "@Author::PERLANCAR/CopyrightYearFromGit", "version": "0.003"}, {"class": "Dist::<PERSON><PERSON>::Plugin::IfBuilt", "name": "@Author::PERLANCAR/IfBuilt", "version": "0.03"}, {"class": "Dist::<PERSON><PERSON>::Plugin::MetaJSON", "name": "@Author::PERLANCAR/MetaJSON", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::MetaConfig", "name": "@Author::PERLANCAR/MetaConfig", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::Authority", "name": "@Author::PERLANCAR/Authority", "version": "1.009"}, {"class": "Dist::Zilla::Plugin::OurDate", "name": "@Author::PERLANCAR/OurDate", "version": "0.03"}, {"class": "Dist::<PERSON><PERSON>::Plugin::OurDist", "name": "@Author::PERLANCAR/OurDist", "version": "0.02"}, {"class": "Dist::Zilla::Plugin::PERLANCAR::OurPkgVersion", "name": "@Author::PERLANCAR/PERLANCAR::OurPkgVersion", "version": "0.04"}, {"class": "Dist::Zilla::Plugin::PodWeaver", "config": {"Dist::Zilla::Plugin::PodWeaver": {"finder": [":InstallModules", ":ExecFiles"], "plugins": [{"class": "Pod::Weaver::Plugin::EnsurePod5", "name": "@CorePrep/EnsurePod5", "version": "4.015"}, {"class": "Pod::Weaver::Plugin::H1Nester", "name": "@CorePrep/H1Nester", "version": "4.015"}, {"class": "Pod::Weaver::Section::Name", "name": "@Author::PERLANCAR/Name", "version": "4.015"}, {"class": "Pod::Weaver::Section::Version", "name": "@Author::PERLANCAR/Version", "version": "4.015"}, {"class": "Pod::Weaver::Section::Region", "name": "@Author::PERLANCAR/prelude", "version": "4.015"}, {"class": "Pod::Weaver::Section::Generic", "name": "SYNOPSIS", "version": "4.015"}, {"class": "Pod::Weaver::Section::Generic", "name": "DESCRIPTION", "version": "4.015"}, {"class": "Pod::Weaver::Section::Generic", "name": "OVERVIEW", "version": "4.015"}, {"class": "Pod::Weaver::Section::Collect", "name": "ATTRIBUTES", "version": "4.015"}, {"class": "Pod::Weaver::Section::Collect", "name": "METHODS", "version": "4.015"}, {"class": "Pod::Weaver::Section::Collect", "name": "FUNCTIONS", "version": "4.015"}, {"class": "Pod::Weaver::Section::Leftovers", "name": "@Author::PERLANCAR/Leftovers", "version": "4.015"}, {"class": "Pod::Weaver::Section::Region", "name": "@Author::P<PERSON><PERSON><PERSON><PERSON>/postlude", "version": "4.015"}, {"class": "Pod::Weaver::Section::Completion::GetoptLongComplete", "name": "@Author::PERLANCAR/Completion::GetoptLongComplete", "version": "0.08"}, {"class": "Pod::Weaver::Section::Completion::GetoptLongSubcommand", "name": "@Author::PERLANCAR/Completion::GetoptLongSubcommand", "version": "0.04"}, {"class": "Pod::Weaver::Section::Completion::GetoptLongMore", "name": "@Author::PERLANCAR/Completion::GetoptLongMore", "version": "0.001"}, {"class": "Pod::Weaver::Section::Homepage::DefaultCPAN", "name": "@Author::PERLANCAR/Homepage::DefaultCPAN", "version": "0.05"}, {"class": "Pod::Weaver::Section::Source::DefaultGitHub", "name": "@Author::PERLANCAR/Source::DefaultGitHub", "version": "0.07"}, {"class": "Pod::Weaver::Section::Bugs::De<PERSON>ultRT", "name": "@Author::PERLANCAR/Bugs::DefaultRT", "version": "0.06"}, {"class": "Pod::Weaver::Section::Authors", "name": "@Author::PERLANCAR/Authors", "version": "4.015"}, {"class": "Pod::Weaver::Section::Legal", "name": "@Author::PERLANCAR/Legal", "version": "4.015"}, {"class": "Pod::Weaver::Plugin::<PERSON><PERSON><PERSON>", "name": "@Author::PERLANCAR/Rinci", "version": "0.77"}, {"class": "Pod::Weaver::Plugin::AppendPrepend", "name": "@Author::PERLANCAR/AppendPrepend", "version": "0.01"}, {"class": "Pod::Weaver::Plugin::EnsureUniqueSections", "name": "@Author::PERLANCAR/EnsureUniqueSections", "version": "0.163250"}, {"class": "Pod::Weaver::Plugin::SingleEncoding", "name": "@Author::PERLANCAR/SingleEncoding", "version": "4.015"}, {"class": "Pod::Weaver::Plugin::PERLANCAR::SortSections", "name": "@Author::PERLANCAR/PERLANCAR::SortSections", "version": "0.06"}]}}, "name": "@Author::PERLANCAR/PodWeaver", "version": "4.008"}, {"class": "Dist::Zilla::Plugin::<PERSON><PERSON>eFiles", "name": "@Author::PERLANCAR/PruneFiles", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::Pod2Readme", "name": "@Author::PERLANCAR/Pod2Readme", "version": "0.004"}, {"class": "Dist::Zilla::Plugin::Rinci::AddPrereqs", "name": "@Author::PERLANCAR/Rinci::AddPrereqs", "version": "0.142"}, {"class": "Dist::Zilla::Plugin::Rinci::AddToDb", "name": "@Author::PERLANCAR/Rinci::AddToDb", "version": "0.01"}, {"class": "Dist::Zilla::Plugin::Rinci::Val<PERSON><PERSON>", "name": "@Author::PERLANCAR/Rinci::Validate", "version": "0.24"}, {"class": "Dist::Zilla::Plugin::SetScriptShebang", "name": "@Author::PERLANCAR/SetScriptShebang", "version": "0.01"}, {"class": "Dist::Zilla::Plugin::Test::Compile", "config": {"Dist::Zilla::Plugin::Test::Compile": {"bail_out_on_fail": 0, "fail_on_warning": "author", "fake_home": 0, "filename": "t/00-compile.t", "module_finder": [":InstallModules"], "needs_display": 0, "phase": "test", "script_finder": [":PerlExecFiles"], "skips": [], "switch": []}}, "name": "@Author::PERLANCAR/Test::Compile", "version": "2.058"}, {"class": "Dist::<PERSON><PERSON>::Plugin::Test::Perl::Critic", "name": "@Author::PERLANCAR/Test::Perl::Critic", "version": "3.001"}, {"class": "Dist::<PERSON><PERSON>::Plugin::Test::<PERSON><PERSON><PERSON>", "name": "@Author::PERLANCAR/Test::<PERSON><PERSON><PERSON>", "version": "0.03"}, {"class": "Dist::Zilla::Plugin::StaticInstall", "config": {"Dist::Zilla::Plugin::StaticInstall": {"dry_run": 0, "mode": "on"}}, "name": "@Author::PERLANCAR/StaticInstall", "version": "0.011"}, {"class": "Dist::Zilla::Plugin::EnsureSQLSchemaVersionedTest", "name": "@Author::PERLANCAR/EnsureSQLSchemaVersionedTest", "version": "0.03"}, {"class": "Dist::Zilla::Plugin::Acme::CPANModules::Blacklist", "name": "@Author::PERLANCAR/Acme::CPANModules::Blacklist", "version": "0.001"}, {"class": "Dist::Zilla::Plugin::Prereqs::EnsureVersion", "name": "@Author::PERLANCAR/Prereqs::EnsureVersion", "version": "0.04"}, {"class": "Dist::Zilla::Plugin::Prereqs::CheckCircular", "name": "@Author::PERLANCAR/Prereqs::CheckCircular", "version": "0.006"}, {"class": "Dist::Zilla::Plugin::UploadToCPAN::WWWPAUSESimple", "name": "@Author::PERLANCAR/UploadToCPAN::WWWPAUSESimple", "version": "0.04"}, {"class": "Dist::Zilla::Plugin::Prereqs", "config": {"Dist::Zilla::Plugin::Prereqs": {"phase": "runtime", "type": "requires"}}, "name": "Prereqs", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::Prereqs", "config": {"Dist::Zilla::Plugin::Prereqs": {"phase": "test", "type": "requires"}}, "name": "TestRequires", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::FinderCode", "name": ":InstallModules", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::FinderCode", "name": ":IncModules", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::FinderCode", "name": ":TestFiles", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::FinderCode", "name": ":ExtraTestFiles", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::FinderCode", "name": ":ExecFiles", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::FinderCode", "name": ":PerlExecFiles", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::FinderCode", "name": ":ShareFiles", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::FinderCode", "name": ":MainModule", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::FinderCode", "name": ":AllFiles", "version": "6.010"}, {"class": "Dist::Zilla::Plugin::FinderCode", "name": ":NoFiles", "version": "6.010"}], "zilla": {"class": "Dist::Zilla::Dist::Builder", "config": {"is_trial": 0}, "version": "6.010"}}, "x_authority": "cpan:PERLANCAR", "x_serialization_backend": "Cpanel::JSON::XS version 3.0239", "x_static_install": 1}