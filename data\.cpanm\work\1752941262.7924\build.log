cpanm (App::cpanminus) 1.7048 on perl 5.040002 built for MSWin32-x64-multi-thread
Work directory is E:\mohammi\LedgerSMB\data/.cpanm/work/1752941262.7924
You have LWP 6.78
Falling back to Archive::Tar 3.04
Searching Locale::Maketext::Lexicon () on cpanmetadb ...
--> Working on Locale::Maketext::Lexicon
Fetching http://www.cpan.org/authors/id/D/DR/DRTECH/Locale-Maketext-Lexicon-1.00.tar.gz
-> OK
Unpacking Locale-Maketext-Lexicon-1.00.tar.gz
Entering Locale-Maketext-Lexicon-1.00
Checking configure dependencies from META.json
Checking if you have ExtUtils::MakeMaker 6.58 ... Yes (7.74)
Configuring Locale-Maketext-Lexicon-1.00
Running Makefile.PL
Checking if your kit is complete...
Looks good
Generating a gmake-style Makefile
Writing Makefile for Locale::Maketext::Lexicon
Writing MYMETA.yml and MYMETA.json
-> OK
Checking dependencies from MYMETA.json ...
Checking if you have Locale::Maketext 1.17 ... Yes (1.33)
-> FAIL Can't configure the distribution. You probably need to have 'make'. See E:\mohammi\LedgerSMB\data\.cpanm\work\1752941262.7924\build.log for details.
