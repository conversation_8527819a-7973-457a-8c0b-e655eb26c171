---
abstract: 'Displays stack trace in HTML'
author:
  - '<PERSON><PERSON><PERSON> <<EMAIL>>'
build_requires:
  ExtUtils::MakeMaker: '0'
  Test::More: '0.88'
configure_requires:
  ExtUtils::MakeMaker: '0'
dynamic_config: 0
generated_by: 'Dist::Zilla version 5.043, Dist::Milla version v1.0.15, CPAN::Meta::Converter version 2.150005, CPAN::Meta::Converter version 2.150010'
license: perl
meta-spec:
  url: http://module-build.sourceforge.net/META-spec-v1.4.html
  version: '1.4'
name: Devel-StackTrace-AsHTML
no_index:
  directory:
    - eg
    - examples
    - inc
    - share
    - t
    - xt
requires:
  Devel::StackTrace: '0'
resources:
  bugtracker: https://github.com/miyagawa/Devel-StackTrace-AsHTML/issues
  homepage: https://github.com/miyagawa/Devel-StackTrace-AsHTML
  repository: https://gith<PERSON>.com/miyagawa/Devel-StackTrace-AsHTML.git
version: '0.15'
x_authority: cpan:MIYAGAWA
x_contributors:
  - '<PERSON> <<EMAIL>>'
  - '<PERSON>Ford <<EMAIL>>'
  - 'Shawn M Moore <<EMAIL>>'
  - 'tokuhirom <<EMAIL>>'
x_serialization_backend: 'CPAN::Meta::YAML version 0.020'
