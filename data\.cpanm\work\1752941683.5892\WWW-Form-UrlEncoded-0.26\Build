#!perl

use strict;
use Cwd;
use File::Basename;
use File::Spec;

sub magic_number_matches {
  return 0 unless -e '_build\\magicnum';
  my $FH;
  open $FH, '<','_build\\magicnum' or return 0;
  my $filenum = <$FH>;
  close $FH;
  return $filenum == 210177;
}

my $progname;
my $orig_dir;
BEGIN {
  $^W = 1;  # Use warnings
  $progname = basename($0);
  $orig_dir = Cwd::cwd();
  my $base_dir = 'E:\\mohammi\\LedgerSMB\\data\\.cpanm\\work\\1752941683.5892\\WWW-Form-UrlEncoded-0.26';
  if (!magic_number_matches()) {
    unless (chdir($base_dir)) {
      die ("Couldn't chdir($base_dir), aborting\n");
    }
    unless (magic_number_matches()) {
      die ("Configuration seems to be out of date, please re-run 'perl Build.PL' again.\n");
    }
  }
  unshift @INC,
    (
     'E:\\mohammi\\LedgerSMB\\data\\.cpanm\\work\\1752941683.5892\\WWW-Form-UrlEncoded-0.26\\_build\\lib'
    );
    if ($INC[-1] ne '.') {
        push @INC, '.';
    }

}

close(*DATA) unless eof(*DATA); # ensure no open handles to this script

use MyBuilder;
Module::Build->VERSION(q{0.4005});

# Some platforms have problems setting $^X in shebang contexts, fix it up here
$^X = Module::Build->find_perl_interpreter;

if (-e 'Build.PL' and not MyBuilder->up_to_date('Build.PL', $progname)) {
   warn "Warning: Build.PL has been altered.  You may need to run 'perl Build.PL' again.\n";
}

# This should have just enough arguments to be able to bootstrap the rest.
my $build = MyBuilder->resume (
  properties => {
    config_dir => '_build',
    orig_dir => $orig_dir,
  },
);

$build->dispatch;
