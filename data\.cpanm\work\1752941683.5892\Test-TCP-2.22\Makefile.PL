# =========================================================================
# THIS FILE IS AUTOMATICALLY GENERATED BY MINILLA.
# DO NOT EDIT DIRECTLY.
# =========================================================================

use 5.006;
use strict;

use ExtUtils::MakeMaker 6.64;

my %WriteMakefileArgs = (
    NAME     => 'Test::TCP',
    DISTNAME => 'Test-TCP',
    VERSION  => '2.22',
    EXE_FILES => [glob('script/*'), glob('bin/*')],
    CONFIGURE_REQUIRES => {
  "ExtUtils::MakeMaker" => "6.64"
}
,
    BUILD_REQUIRES     => {}
,
    TEST_REQUIRES      => {
  "File::Temp" => 0,
  "Socket" => 0,
  "Test::More" => "0.98"
}
,
    PREREQ_PM          => {
  "IO::Socket::INET" => 0,
  "IO::Socket::IP" => 0,
  "Test::More" => 0,
  "Test::SharedFork" => "0.29",
  "Time::HiRes" => 0,
  "perl" => "5.008001"
}
,
);

WriteMakefile(%WriteMakefileArgs);
