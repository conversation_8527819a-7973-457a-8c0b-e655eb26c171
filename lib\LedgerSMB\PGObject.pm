
package LedgerSMB::PGObject;

=head1 NAME

LedgerSMB::PGObject - PGObject::Simple wrapper for LedgerSMB

=head1 SYNPOSIS

use LedgerSMB::PGObject

sub foo {
    return call_dbmethod(funcname => 'foo', args => {id => 1});
}

=head1 DESCRIPTION

This replaces the older LedgerSMB::DBObject, as it has more features and
better consistency

=head1 METHODS

This module doesn't specify any (public) methods.

=cut

use Moose::Role;
use namespace::autoclean;
with 'LedgerSMB::PGObject::Role' => { -excludes => [ '_get_dbh' ], };

use LedgerSMB::App_State;

# nulls come back from the db as undefs.
# we have not put this in the main PGObject module because
# it allows other users of the software to do things however they like.
around BUILDARGS => sub {
      my $orig  = shift;
      my $class = shift;
      my %args;

      if (scalar @_ == 1){
           %args = %{$_[0]};
      } else {
           %args = @_;
      }
      return $class->$orig(
          map { $_ => $args{$_} } grep {defined $args{$_}} keys %args
      );
};

sub _get_dbh { return LedgerSMB::App_State::DBH() }


=head1 LICENSE AND COPYRIGHT

Copyright (C) 2014 The LedgerSMB Core Team

This file is licensed under the GNU General Public License version 2, or at your
option any later version.  A copy of the license should have been included with
your software.

=cut

1;
