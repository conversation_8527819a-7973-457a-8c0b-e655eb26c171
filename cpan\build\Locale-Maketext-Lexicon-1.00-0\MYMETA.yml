---
abstract: 'Use other catalog formats in Maketext'
author:
  - '<PERSON> <<EMAIL>>'
  - '<PERSON> <<EMAIL>>'
build_requires: {}
configure_requires:
  ExtUtils::MakeMaker: '6.30'
dynamic_config: 0
generated_by: 'Dist::Zilla version 5.013, CPAN::Meta::Converter version 2.133380, CPAN::Meta::Converter version 2.150010'
license: mit
meta-spec:
  url: http://module-build.sourceforge.net/META-spec-v1.4.html
  version: '1.4'
name: Locale-Maketext-Lexicon
recommends:
  HTML::Parser: '3.56'
  Lingua::EN::Sentence: '0.25'
  PPI: '1.203'
  Template: '2.20'
  Template::Constants: '2.75'
  Text::Haml: '0'
  YAML: '0.66'
  YAML::Loader: '0.66'
requires:
  Locale::Maketext: '1.17'
resources:
  bugtracker: https://github.com/c<PERSON><PERSON>/locale-maketext-lexicon/issues
  homepage: http://search.cpan.org/dist/Locale-Maketext-Lexicon
  repository: git://github.com/clintongormley/locale-maketext-lexicon.git
version: '1.00'
x_serialization_backend: 'CPAN::Meta::YAML version 0.020'
