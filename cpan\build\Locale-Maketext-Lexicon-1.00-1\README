NAME
    Locale::Maketext::Lexicon - Use other catalog formats in Maketext

VERSION
    version 1.00

SYNOPSIS
    As part of a localization class, automatically glob for available
    lexicons:

        package Hello::I18N;
        use base 'Locale::Maketext';
        use Locale::Maketext::Lexicon {
            '*' => [Gettext => '/usr/local/share/locale/*/LC_MESSAGES/hello.mo'],
            ### Uncomment to fallback when a key is missing from lexicons
            # _auto   => 1,
            ### Uncomment to decode lexicon entries into Unicode strings
            # _decode => 1,
            ### Uncomment to load and parse everything right away
            # _preload => 1,
            ### Uncomment to use %1 / %quant(%1) instead of [_1] / [quant, _1]
            # _style  => 'gettext',
        };

    Explicitly specify languages, during compile- or run-time:

        package Hello::I18N;
        use base 'Locale::Maketext';
        use Locale::Maketext::Lexicon {
            de => [Gettext => 'hello_de.po'],
            fr => [
                Gettext => 'hello_fr.po',
                Gettext => 'local/hello/fr.po',
            ],
        };
        # ... incrementally add new lexicons
        Locale::Maketext::Lexicon->import({
            de => [Gettext => 'local/hello/de.po'],
        })

    Alternatively, as part of a localization subclass:

        package Hello::I18N::de;
        use base 'Hello::I18N';
        use Locale::Maketext::Lexicon (Gettext => \*DATA);
        __DATA__
        # Some sample data
        msgid ""
        msgstr ""
        "Project-Id-Version: Hello ********\n"
        "MIME-Version: 1.0\n"
        "Content-Type: text/plain; charset=iso8859-1\n"
        "Content-Transfer-Encoding: 8bit\n"

        #: Hello.pm:10
        msgid "Hello, World!"
        msgstr "Hallo, Welt!"

        #: Hello.pm:11
        msgid "You have %quant(%1,piece) of mail."
        msgstr "Sie haben %quant(%1,Poststueck,Poststuecken)."

DESCRIPTION
    This module provides lexicon-handling modules to read from other
    localization formats, such as *Gettext*, *Msgcat*, and so on.

    If you are unfamiliar with the concept of lexicon modules, please
    consult Locale::Maketext and the "webl10n" HTML files in the "docs/"
    directory of this module.

    A command-line utility xgettext.pl is also installed with this module,
    for extracting translatable strings from source files.

  The "import" function
    The "import()" function accepts two forms of arguments:

    (*format* => *source* ... )
        This form takes any number of argument pairs (usually one); *source*
        may be a file name, a filehandle, or an array reference.

        For each such pair, it pass the contents specified by the second
        argument to Locale::Maketext::Lexicon::*format*->parse as a plain
        list, and export its return value as the %Lexicon hash in the
        calling package.

        In the case that there are multiple such pairs, the lexicon defined
        by latter ones overrides earlier ones.

    { *language* => [ *format*, *source* ... ] ... }
        This form accepts a hash reference. It will export a %Lexicon into
        the subclasses specified by each *language*, using the process
        described above. It is designed to alleviate the need to set up a
        separate subclass for each localized language, and just use the
        catalog files.

        This module will convert the *language* arguments into lowercase,
        and replace all "-" with "_", so "zh_TW" and "zh-tw" will both map
        to the "zh_tw" subclass.

        If *language* begins with "_", it is taken as an option that
        controls how lexicons are parsed. See "Options" for a list of
        available options.

        The "*" is a special *language*; it must be used in conjunction with
        a filename that also contains "*"; all matched files with a valid
        language code in the place of "*" will be automatically prepared as
        a lexicon subclass. If there is multiple "*" in the filename, the
        last one is used as the language name.

  Options
    "_auto"
        If set to a true value, missing lookups on lexicons are handled
        silently, as if an "Auto" lexicon has been appended on all language
        lexicons.

    "_decode"
        If set to a true value, source entries will be converted into
        utf8-strings (available in Perl 5.6.1 or later). This feature needs
        the Encode or Encode::compat module.

        Currently, only the "Gettext" backend supports this option.

    "_encoding"
        This option only has effect when "_decode" is set to true. It
        specifies an encoding to store lexicon entries, instead of
        utf8-strings.

        If "_encoding" is set to "locale", the encoding from the current
        locale setting is used.

    "_preload"
        By default parsing is delayed until first use of the lexicon, set
        this option to true value to parse it asap. Increment adding
        lexicons forces parsing.

  Subclassing format handlers
    If you wish to override how sources specified in different data types
    are handled, please use a subclass that overrides "lexicon_get_*TYPE*".

    XXX: not documented well enough yet. Patches welcome.

VERSION
    This document describes version 0.91 of Locale::Maketext::Lexicon.

NOTES
    When you attempt to localize an entry missing in the lexicon, Maketext
    will throw an exception by default. To inhibit this behaviour, override
    the "_AUTO" key in your language subclasses, for example:

        $Hello::I18N::en::Lexicon{_AUTO} = 1; # autocreate missing keys

    If you want to implement a new "Lexicon::*" backend module, please note
    that "parse()" takes an array containing the source strings from the
    specified filehandle or filename, which are *not* "chomp"ed. Although if
    the source is an array reference, its elements will probably not contain
    any newline characters anyway.

    The "parse()" function should return a hash reference, which will be
    assigned to the *typeglob* (*Lexicon) of the language module. All it
    amounts to is that if the returned reference points to a tied hash, the
    %Lexicon will be aliased to the same tied hash if it was not initialized
    previously.

ACKNOWLEDGMENTS
    Thanks to Jesse Vincent for suggesting this module to be written.

    Thanks also to Sean M. Burke for coming up with Locale::Maketext in the
    first place, and encouraging me to experiment with alternative Lexicon
    syntaxes.

    Thanks also to Yi Ma Mao for providing the MO file parsing subroutine,
    as well as inspiring me to implement file globbing and transcoding
    support.

    See the AUTHORS file in the distribution for a list of people who have
    sent helpful patches, ideas or comments.

SEE ALSO
    xgettext.pl for extracting translatable strings from common template
    systems and perl source files.

    Locale::Maketext, Locale::Maketext::Lexicon::Auto,
    Locale::Maketext::Lexicon::Gettext, Locale::Maketext::Lexicon::Msgcat,
    Locale::Maketext::Lexicon::Tie

AUTHORS
    Audrey Tang <<EMAIL>>

COPYRIGHT
    Copyright 2002-2013 by Audrey Tang <<EMAIL>>.

    This software is released under the MIT license cited below.

  The "MIT" License
    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the
    "Software"), to deal in the Software without restriction, including
    without limitation the rights to use, copy, modify, merge, publish,
    distribute, sublicense, and/or sell copies of the Software, and to
    permit persons to whom the Software is furnished to do so, subject to
    the following conditions:

    The above copyright notice and this permission notice shall be included
    in all copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
    OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
    IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
    CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
    TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
    SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

AUTHORS
    *   Clinton Gormley <<EMAIL>>

    *   Audrey Tang <<EMAIL>>

COPYRIGHT AND LICENSE
    This software is Copyright (c) 2014 by Audrey Tang.

    This is free software, licensed under:

      The MIT (X11) License

