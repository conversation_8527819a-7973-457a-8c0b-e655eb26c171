=head1 NAME

XML::LibXML::DocumentFragment - XML::LibXML's DOM L2 Document Fragment Implementation

=head1 SYNOPSIS



  use XML::LibXML;


=head1 DESCRIPTION

This class is a helper class as described in the DOM Level 2 Specification. It
is implemented as a node without name. All adding, inserting or replacing
functions are aware of document fragments now.

As well I<<<<<< all >>>>>> unbound nodes (all nodes that do not belong to any document sub-tree) are
implicit members of document fragments.

=head1 AUTHORS

<PERSON>,
<PERSON>,
<PERSON><PERSON>


=head1 VERSION

2.0210

=head1 COPYRIGHT

2001-2007, AxKit.com Ltd.

2002-2006, <PERSON>.

2006-2009, Petr <PERSON>.

=cut


=head1 LICENSE

This program is free software; you can redistribute it and/or modify it under
the same terms as Perl itself.

