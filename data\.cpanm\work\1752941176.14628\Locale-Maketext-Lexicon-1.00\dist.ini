name                = Locale-Maketext-Lexicon
author              = <PERSON> <<EMAIL>>
author              = <PERSON> <<EMAIL>>
license             = MIT
copyright_holder    = <PERSON>
version             = 1.00

[GatherDir]
exclude_filename = README.pod
exclude_match    = ^cover.*
exclude_match    = ^nytprof.*
exclude_match    = ^bench.*

[ExecDir]
dir = script

[PruneCruft]
[PkgVersion]
[MetaJSON]
[ExtraTests]
[PodSyntaxTests]
[EOLTests]

[Manifest]
[MakeMaker]

[Prereqs / BuildRequires]

[Prereqs]
  perl                  = 5.005
  Locale::Maketext      = 1.17

[Prereqs / Recommends]
  HTML::Parser          = 3.56
  Lingua::EN::Sentence  = 0.25
  PPI                   = 1.203
  Template              = 2.20
  Template::Constants   = 2.75
  YAML                  = 0.66
  YAML::Loader          = 0.66
  Text::Haml            = 0


[@Git]
tag_format = version_%v

[GitHub::Meta]
fork = 0
repo = locale-maketext-lexicon

[PodWeaver]

[MetaYAML]
[License]
[ReadmeFromPod]

[CheckChangeLog]
[ConfirmRelease]
[UploadToCPAN]

