---
abstract: 'Store multiple values per key'
author:
  - '<PERSON><PERSON><PERSON> <<EMAIL>>'
build_requires:
  ExtUtils::MakeMaker: '0'
  Test::More: '0'
configure_requires:
  ExtUtils::MakeMaker: '0'
dynamic_config: 0
generated_by: 'Dist::Zilla version 5.030, Dist::Milla version v1.0.14, CPAN::Meta::Converter version 2.143240, CPAN::Meta::Converter version 2.150010'
license: perl
meta-spec:
  url: http://module-build.sourceforge.net/META-spec-v1.4.html
  version: '1.4'
name: Hash-MultiValue
no_index:
  directory:
    - t
    - xt
    - inc
    - share
    - eg
    - examples
requires:
  perl: '5.008001'
resources:
  bugtracker: https://github.com/miyagawa/Hash-MultiValue/issues
  homepage: https://github.com/miyagawa/Hash-MultiValue
  repository: https://github.com/miyagawa/Hash-MultiValue.git
version: '0.16'
x_contributors:
  - '<PERSON> <pagaltz<PERSON>@gmx.de>'
  - 'chansen <<EMAIL>>'
  - '<PERSON> <<EMAIL>>'
  - '<PERSON> <<EMAIL>>'
  - 'Tatsuhiko Miyagawa <<EMAIL>>'
x_serialization_backend: 'CPAN::Meta::YAML version 0.020'
