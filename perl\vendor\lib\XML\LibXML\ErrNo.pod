=head1 NAME

XML::LibXML::ErrNo - Structured Errors

=head1 DESCRIPTION

This module is based on xmlerror.h libxml2 C header file. It defines symbolic
constants for all libxml2 error codes. Currently libxml2 uses over 480
different error codes. See also XML::LibXML::Error.

=head1 AUTHOR<PERSON>,
<PERSON>,
<PERSON><PERSON>


=head1 VERSION

2.0210

=head1 COPYRIGHT

2001-2007, AxKit.com Ltd.

2002-2006, <PERSON>.

2006-2009, Petr <PERSON>.

=cut


=head1 LICENSE

This program is free software; you can redistribute it and/or modify it under
the same terms as Perl itself.

