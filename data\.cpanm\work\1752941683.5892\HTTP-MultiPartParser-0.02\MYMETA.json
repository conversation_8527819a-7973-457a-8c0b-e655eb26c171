{"abstract": "HTTP MultiPart Parser", "author": ["<PERSON><<EMAIL>>"], "dynamic_config": 0, "generated_by": "Module::Install version 1.18, CPAN::Meta::Converter version 2.150010", "license": ["perl_5"], "meta-spec": {"url": "http://search.cpan.org/perldoc?CPAN::Meta::Spec", "version": 2}, "name": "HTTP-MultiPartParser", "no_index": {"directory": ["inc", "t"]}, "prereqs": {"build": {"requires": {"ExtUtils::MakeMaker": "6.59", "Test::Deep": "0", "Test::More": "0.88"}}, "configure": {"requires": {"ExtUtils::MakeMaker": "0"}}, "runtime": {"requires": {"Carp": "0", "Scalar::Util": "0", "perl": "5.008001"}}}, "release_status": "stable", "resources": {"license": ["http://dev.perl.org/licenses/"], "repository": {"url": "http://github.com/chansen/p5-http-multipartparser"}}, "version": "0.02", "x_serialization_backend": "JSON::PP version 4.16"}