#ifndef _GDFONTG_H_
#define _GDFONTG_H_ 1

#ifdef __cplusplus
extern "C"
{
#endif

/*
	This is a header file for gd font, generated using
	bdftogd version 0.51 by <PERSON>, <PERSON><PERSON><PERSON>@fi.muni.cz
	from bdf font
	-Misc-Fixed-Bold-R-Normal-Sans-15-140-75-75-C-90-ISO8859-2
	at Mon Jan 26 14:45:58 1998.
	The original bdf was holding following copyright:
	"Libor Skarvada, <EMAIL>"
 */

#include "gd.h"

extern BGD_EXPORT_DATA_PROT gdFontPtr gdFontGiant;
BGD_DECLARE(gdFontPtr) gdFontGetGiant(void);

#ifdef __cplusplus
}
#endif

#endif
