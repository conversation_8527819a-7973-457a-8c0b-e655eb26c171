# Place this in /etc/systemd/system/ledgersmb_plack-fcgi.service
# systemctl enable ledgersmb_plack-fcgi
# service start ledgersmb_plack-fcgi

[Unit]
Description=LedgerSMB Plack/FCGI
After=network.target

[Service]
WorkingDirectory=WORKING_DIR

# In case you installed dependencies into a 'local::lib'
# make sure you set the PERL5LIB environment variable
# to the correct location by uncommenting the line below
#Environment=PERL5LIB=/path/to/local-lib/lib/perl5
Environment=PLACK_ENV=deployment

# Be sure to set a user and group below
# which don't have write access to the directories
# holding the LedgerSMB sources
User=ledgersmb
Group=ledgersmb
ExecStart=/usr/bin/plackup                     \
    -s FCGI                                    \
    --listen localhost:3244                    \
    -I lib                                     \
    -I old/lib/                                \
    bin/ledgersmb-server.psgi
Restart=always

[Install]
WantedBy=multi-user.target
