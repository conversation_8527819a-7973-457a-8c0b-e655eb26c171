
# This file was automatically generated by Dist::Zilla::Plugin::MakeMaker v5.013.
use strict;
use warnings;

use 5.005;

use ExtUtils::MakeMaker 6.30;



my %WriteMakefileArgs = (
  "ABSTRACT" => "Use other catalog formats in Maketext",
  "AUTHOR" => "<PERSON> <drtech\@cpan.org>, <PERSON> <cpan\@audreyt.org>",
  "BUILD_REQUIRES" => {},
  "CONFIGURE_REQUIRES" => {
    "ExtUtils::MakeMaker" => "6.30"
  },
  "DISTNAME" => "Locale-Maketext-Lexicon",
  "EXE_FILES" => [
    "script/xgettext.pl"
  ],
  "LICENSE" => "mit",
  "NAME" => "Locale::Maketext::Lexicon",
  "PREREQ_PM" => {
    "Locale::Maketext" => "1.17"
  },
  "TEST_REQUIRES" => {},
  "VERSION" => "1.00",
  "test" => {
    "TESTS" => "t/*.t"
  }
);


my %FallbackPrereqs = (
  "Locale::Maketext" => "1.17"
);


unless ( eval { ExtUtils::MakeMaker->VERSION(6.63_03) } ) {
  delete $WriteMakefileArgs{TEST_REQUIRES};
  delete $WriteMakefileArgs{BUILD_REQUIRES};
  $WriteMakefileArgs{PREREQ_PM} = \%FallbackPrereqs;
}

delete $WriteMakefileArgs{CONFIGURE_REQUIRES}
  unless eval { ExtUtils::MakeMaker->VERSION(6.52) };

WriteMakefile(%WriteMakefileArgs);



