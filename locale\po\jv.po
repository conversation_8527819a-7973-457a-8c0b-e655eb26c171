# 
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: LedgerSMB\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-06-14 12:49+0000\n"
"PO-Revision-Date: 2015-12-24 17:27+0000\n"
"Language-Team: Javanese (http://app.transifex.com/ledgersmb/ledgersmb/language/jv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: jv\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:180
msgid "# Invoices"
msgstr ""

#: lib/LedgerSMB/Report/Assets/Net_Book_Value.pm:115
msgid "% Dep."
msgstr ""

#: lib/LedgerSMB/Report/Assets/Net_Book_Value.pm:99
msgid "(+) Salvage Value"
msgstr ""

#: lib/LedgerSMB/Report/Assets/Net_Book_Value.pm:107
msgid "(-) Accum. Dep."
msgstr ""

#: lib/LedgerSMB/Report/Assets/Net_Book_Value.pm:111
msgid "(=) NBV"
msgstr ""

#: UI/templates/widget.html:97
msgid "--FORMAT--"
msgstr ""

#: UI/templates/widget.html:27
msgid "--TEMPLATE--"
msgstr ""

#: UI/templates/widget.html:85
msgid "1099-INT"
msgstr ""

#: UI/templates/widget.html:83
msgid "1099-MISC"
msgstr ""

#: templates/demo/statement.html:68
msgid "30"
msgstr ""

#: templates/demo/statement.html:69
msgid "60"
msgstr ""

#: templates/demo/statement.html:70
msgid "90"
msgstr ""

#: templates/demo/invoice.tex:54 templates/demo/packing_list.tex:37
msgid ""
"A return authorization must be obtained from [_1] before goods are returned."
" Returns must be shipped prepaid and properly insured. [_1] will not be "
"responsible for damages during transit."
msgstr ""

#: old/bin/am.pl:322 UI/Contact/divs/credit.html:216
#: UI/Contact/divs/credit.html:217 UI/Reports/filters/unapproved.html:5
#: UI/setup/complete.html:80 UI/setup/confirm_operation.html:144
msgid "AP"
msgstr ""

#: locale/menu.xml:152
msgid "AP Aging"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Search_Adj.pm:113
msgid "AP Invoice"
msgstr ""

#: UI/Reports/filters/search_goods.html:156
msgid "AP Invoices"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:303
#: UI/Reports/filters/invoice_outstanding.html:4
msgid "AP Outstanding"
msgstr ""

#: UI/Contact/divs/credit.html:364 t/data/04-complex_template.html:325
#: templates/demo/ap_transaction.html:16 templates/demo/ap_transaction.html:31
#: templates/demo/ap_transaction.tex:34
msgid "AP Transaction"
msgstr ""

#: lib/LedgerSMB/Report/Contact/Purchase.pm:130
msgid "AP Transactions"
msgstr ""

#: locale/menu.xml:130
msgid "AP Voucher"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:397 lib/LedgerSMB/Upgrade_Tests.pm:619
msgid "AP account available when vendors defined"
msgstr ""

#: UI/templates/widget.html:33
msgid "AP transaction"
msgstr ""

#: old/bin/am.pl:321 UI/Contact/divs/credit.html:216
#: UI/Contact/divs/credit.html:217 UI/Reports/filters/unapproved.html:6
#: UI/setup/complete.html:82 UI/setup/confirm_operation.html:145
msgid "AR"
msgstr ""

#: locale/menu.xml:86
msgid "AR Aging"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Search_Adj.pm:109
msgid "AR Invoice"
msgstr ""

#: UI/Reports/filters/search_goods.html:135
msgid "AR Invoices"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:305
#: UI/Reports/filters/invoice_outstanding.html:8
msgid "AR Outstanding"
msgstr ""

#: UI/Contact/divs/credit.html:377 t/data/04-complex_template.html:325
#: templates/demo/ar_transaction.html:16 templates/demo/ar_transaction.html:31
#: templates/demo/ar_transaction.tex:49
msgid "AR Transaction"
msgstr ""

#: lib/LedgerSMB/Report/Contact/Purchase.pm:132
msgid "AR Transactions"
msgstr ""

#: locale/menu.xml:64
msgid "AR Voucher"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:376 lib/LedgerSMB/Upgrade_Tests.pm:598
msgid "AR account available when customers defined"
msgstr ""

#: UI/templates/widget.html:35
msgid "AR transaction"
msgstr ""

#: lib/LedgerSMB/Report/Unapproved/Batch_Overview.pm:153
#: lib/LedgerSMB/Report/Unapproved/Drafts.pm:115
msgid "AR/AP/GL Amount"
msgstr ""

#: sql/Pg-database.sql:2407
msgid "Abandonment"
msgstr ""

#: lib/LedgerSMB.pm:537 lib/LedgerSMB.pm:538
#: lib/LedgerSMB/Scripts/configuration.pm:363
msgid "Access Denied"
msgstr ""

#: lib/LedgerSMB/Report/Aging.pm:100
#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:226
#: lib/LedgerSMB/Report/Reconciliation/Summary.pm:135
#: lib/LedgerSMB/Scripts/report_aging.pm:149 old/bin/aa.pl:733
#: old/bin/aa.pl:814 old/bin/aa.pl:988 old/bin/ic.pl:1094 old/bin/ir.pl:409
#: old/bin/ir.pl:816 old/bin/is.pl:467 old/bin/is.pl:931 old/bin/oe.pl:478
#: UI/Contact/contact.html:15 UI/Contact/divs/credit.html:204
#: UI/Contact/divs/credit.html:205 UI/Reports/filters/aging.html:24
#: UI/Reports/filters/gl.html:31 UI/Reports/filters/gl.html:312
#: UI/Reports/filters/invoice_outstanding.html:26
#: UI/Reports/filters/invoice_search.html:29 UI/Reports/filters/orders.html:29
#: UI/Reports/filters/orders.html:177
#: UI/Reports/filters/reconciliation_search.html:40 UI/accounts/edit.html:119
#: UI/budgetting/budget_entry.html:55 UI/journal/journal_entry.html:154
#: UI/payments/payment2.html:84 UI/payments/payment2.html:266
#: UI/payments/payment2.html:343 UI/payments/payments_detail.html:135
#: UI/payments/payments_detail.html:220 UI/payments/payments_filter.html:62
#: UI/payroll/deduction.html:45 UI/payroll/income.html:46
#: UI/reconciliation/new_report.html:18 t/data/04-complex_template.html:27
#: templates/demo/invoice.html:193 templates/demo/printPayment.html:46
msgid "Account"
msgstr ""

#: lib/LedgerSMB/Report/Trial_Balance.pm:152
#: UI/payments/use_overpayment2.html:122
msgid "Account Description"
msgstr ""

#: lib/LedgerSMB/Report/Budget/Variance.pm:79
msgid "Account Label"
msgstr ""

#: lib/LedgerSMB/Report/GL.pm:180 UI/Reports/co/filter_bm.html:84
#: UI/Reports/co/filter_cd.html:68 UI/Reports/filters/gl.html:318
msgid "Account Name"
msgstr ""

#: lib/LedgerSMB/Report/Budget/Search.pm:128
#: lib/LedgerSMB/Report/Budget/Variance.pm:75 lib/LedgerSMB/Report/COA.pm:87
#: lib/LedgerSMB/Report/Contact/History.pm:150
#: lib/LedgerSMB/Report/Contact/History.pm:61
#: lib/LedgerSMB/Report/Contact/Purchase.pm:146
#: lib/LedgerSMB/Report/Contact/Search.pm:106 lib/LedgerSMB/Report/GL.pm:175
#: lib/LedgerSMB/Report/GL.pm:239
#: lib/LedgerSMB/Report/Invoices/Payments.pm:185
#: lib/LedgerSMB/Report/PNL/ECA.pm:88
#: lib/LedgerSMB/Report/Taxform/Details.pm:119
#: lib/LedgerSMB/Report/Taxform/Details.pm:89
#: lib/LedgerSMB/Report/Taxform/Summary.pm:89
#: lib/LedgerSMB/Report/Trial_Balance.pm:146
#: lib/LedgerSMB/Scripts/payment.pm:180 old/bin/ic.pl:1654 old/bin/ic.pl:997
#: UI/Contact/divs/bank_act.html:26 UI/Contact/divs/bank_act.html:75
#: UI/Reports/co/filter_bm.html:77 UI/Reports/co/filter_cd.html:61
#: UI/Reports/filters/taxforms.html:82 UI/accounts/edit.html:39
#: UI/accounts/edit.html:128 UI/payments/use_overpayment2.html:121
#: t/data/04-complex_template.html:558
msgid "Account Number"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:185
msgid "Account Title"
msgstr ""

#: lib/LedgerSMB/Report/Taxform/Details.pm:85
#: lib/LedgerSMB/Report/Taxform/Summary.pm:85
#: UI/Reports/filters/taxforms.html:72 UI/accounts/edit.html:156
msgid "Account Type"
msgstr ""

#: old/bin/gl.pl:665 old/lib/LedgerSMB/GL.pm:233
msgid "Account [_1] not found"
msgstr ""

#: UI/Contact/divs/credit.html:529 UI/Reports/filters/balance_sheet.html:224
#: UI/Reports/filters/income_statement.html:246
msgid "Account category"
msgstr ""

#: UI/Contact/divs/credit.html:480 UI/Reports/filters/balance_sheet.html:175
#: UI/Reports/filters/income_statement.html:197
msgid "Account numbers"
msgstr ""

#: UI/reconciliation/report.html:34
msgid "Account:"
msgstr ""

#: UI/lib/report_base.html:63
msgid "Accounts"
msgstr ""

#: UI/Reports/co/filter_cd.html:23
msgid "Accounts From"
msgstr ""

#: locale/menu.xml:98
msgid "Accounts Payable"
msgstr ""

#: locale/menu.xml:27
msgid "Accounts Receivable"
msgstr ""

#: UI/Reports/co/filter_cd.html:28
msgid "Accounts To"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:683
msgid "Accounts marked for recon -- once"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:699
msgid "Accounts marked for recon exist"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:794
msgid "Accounts without heading"
msgstr ""

#: UI/Reports/filters/income_statement.html:28
msgid "Accrual"
msgstr ""

#: UI/taxform/add_taxform.html:60
msgid "Accrual Basis:"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:1006
msgid "Accum. Depreciation"
msgstr ""

#: t/data/04-complex_template.html:390 t/data/04-complex_template.html:560
msgid "Actions"
msgstr ""

#: t/data/04-complex_template.html:496
msgid "Actions:"
msgstr ""

#: UI/Reports/filters/invoice_outstanding.html:81
#: UI/Reports/filters/invoice_search.html:176
#: UI/Reports/filters/search_goods.html:29
#: UI/business_units/list_classes.html:10
msgid "Active"
msgstr ""

#: UI/business_units/filter.html:31
msgid "Active On"
msgstr ""

#: lib/LedgerSMB/Scripts/admin.pm:141
msgid "Active Sessions"
msgstr ""

#: UI/Reports/filters/contact_search.html:89
msgid "Active after"
msgstr ""

#: UI/Reports/filters/contact_search.html:93
msgid "Active until"
msgstr ""

#: lib/LedgerSMB/Report/File/Incoming.pm:96
#: lib/LedgerSMB/Report/File/Internal.pm:84 old/bin/aa.pl:174
#: old/bin/ic.pl:2079 UI/Configuration/currency.html:35
#: UI/Configuration/ratetype.html:28
msgid "Add"
msgstr ""

#: old/bin/aa.pl:502 old/bin/aa.pl:541
msgid "Add AP Transaction"
msgstr ""

#: old/bin/aa.pl:501
msgid "Add AR Transaction"
msgstr ""

#: old/bin/ic.pl:56 locale/menu.xml:461
msgid "Add Assembly"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:213
msgid "Add Asset"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:65
msgid "Add Asset Class"
msgstr ""

#: locale/menu.xml:626
msgid "Add Assets"
msgstr ""

#: locale/menu.xml:269
msgid "Add Budget"
msgstr ""

#: UI/business_units/list_classes.html:84 locale/menu.xml:614
msgid "Add Class"
msgstr ""

#: UI/Contact/divs/company.html:11
msgid "Add Company"
msgstr ""

#: old/bin/is.pl:104
msgid "Add Credit Invoice"
msgstr ""

#: old/bin/aa.pl:539
msgid "Add Credit Note"
msgstr ""

#: t/data/04-complex_template.html:16
msgid "Add Customer"
msgstr ""

#: old/bin/is.pl:108
msgid "Add Customer Return"
msgstr ""

#: old/bin/ir.pl:139 old/bin/ir.pl:95
msgid "Add Debit Invoice"
msgstr ""

#: old/bin/aa.pl:537
msgid "Add Debit Note"
msgstr ""

#: UI/Contact/divs/employee.html:10 t/data/04-complex_template.html:20
#: locale/menu.xml:282
msgid "Add Employee"
msgstr ""

#: locale/menu.xml:9
msgid "Add Entity"
msgstr ""

#: old/bin/oe.pl:1557
msgid "Add Exchange Rate"
msgstr ""

#: old/bin/pe.pl:87 locale/menu.xml:476
msgid "Add Group"
msgstr ""

#: old/bin/ic.pl:57
msgid "Add Labor/Overhead"
msgstr ""

#: UI/budgetting/budget_entry.html:166
msgid "Add Note"
msgstr ""

#: locale/menu.xml:466
msgid "Add Overhead"
msgstr ""

#: old/bin/ic.pl:54 locale/menu.xml:451
msgid "Add Part"
msgstr ""

#: UI/Contact/divs/person.html:10
msgid "Add Person"
msgstr ""

#: old/bin/io.pl:902 old/bin/oe.pl:63
msgid "Add Purchase Order"
msgstr ""

#: old/bin/io.pl:959 old/bin/oe.pl:75
msgid "Add Quotation"
msgstr ""

#: UI/business_units/edit.html:12
msgid "Add Reporting Unit"
msgstr ""

#: old/bin/io.pl:940 old/bin/oe.pl:71
msgid "Add Request for Quotation"
msgstr ""

#: locale/menu.xml:53
msgid "Add Return"
msgstr ""

#: old/bin/is.pl:113 old/bin/oe.pl:1461
msgid "Add Sales Invoice"
msgstr ""

#: old/bin/io.pl:921 old/bin/oe.pl:67
msgid "Add Sales Order"
msgstr ""

#: old/bin/ic.pl:55 locale/menu.xml:456
msgid "Add Service"
msgstr ""

#: lib/LedgerSMB/Report/Taxform/List.pm:80 locale/menu.xml:596
msgid "Add Tax Form"
msgstr ""

#: locale/menu.xml:560
msgid "Add Timecard"
msgstr ""

#: old/bin/io.pl:1753
msgid "Add To List"
msgstr ""

#: locale/menu.xml:28 locale/menu.xml:99
msgid "Add Transaction"
msgstr ""

#: UI/business_units/list_classes.html:105
msgid "Add Unit"
msgstr ""

#: UI/Contact/divs/employee.html:169 UI/Contact/divs/user.html:140
#: UI/setup/confirm_operation.html:57 UI/setup/edit_user.html:102
msgid "Add User"
msgstr ""

#: t/data/04-complex_template.html:18
msgid "Add Vendor"
msgstr ""

#: old/bin/ir.pl:104 old/bin/oe.pl:1452
msgid "Add Vendor Invoice"
msgstr ""

#: old/bin/ir.pl:99
msgid "Add Vendor Return"
msgstr ""

#: UI/budgetting/budget_entry.html:7
msgid "Add budget"
msgstr ""

#: UI/Contact/divs/wage.html:78
msgid "Add/Change Deductions"
msgstr ""

#: UI/Contact/divs/wage.html:49
msgid "Add/Change Wage"
msgstr ""

#: UI/Configuration/rate.html:58
msgid "Add/Update"
msgstr ""

#: lib/LedgerSMB/Scripts/business_unit.pm:179
msgid "Added id [_1]"
msgstr ""

#: old/bin/aa.pl:738 old/bin/arap.pl:169 old/bin/ic.pl:1657 old/bin/io.pl:1576
#: old/bin/ir.pl:419 old/bin/is.pl:477 old/bin/oe.pl:483 old/bin/pe.pl:575
#: UI/Contact/divs/address.html:85 UI/Contact/divs/address.html:124
#: UI/Contact/divs/address.html:127 UI/Reports/filters/contact_search.html:50
#: UI/Reports/filters/purchase_history.html:67
msgid "Address"
msgstr ""

#: t/data/04-complex_template.html:385
msgid "Address1"
msgstr ""

#: t/data/04-complex_template.html:420
msgid "Address:"
msgstr ""

#: templates/demo/printPayment.html:33
msgid "Address: [_1]"
msgstr ""

#: lib/LedgerSMB/Scripts/contact.pm:208 UI/Contact/divs/address.html:2
#: t/data/04-complex_template.html:30
msgid "Addresses"
msgstr ""

#: old/bin/ic.pl:1252
msgid "Adj"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Activity.pm:142
msgid "Adjusted"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:1012
msgid "Adjusted Basis"
msgstr ""

#: UI/inventory/adjustment_entry.html:28
msgid "Adjustment"
msgstr ""

#: UI/inventory/adjustment_entry.html:9
msgid "Adjustment Date"
msgstr ""

#: UI/inventory/adjustment_setup.html:7
msgid "Adjustment Details"
msgstr ""

#: UI/Reports/filters/aging.html:79
msgid "Aged"
msgstr ""

#: lib/LedgerSMB/Report/Aging.pm:200 UI/Reports/filters/aging.html:6
msgid "Aging Report"
msgstr ""

#: UI/templates/widget.html:75
msgid "Aging statement"
msgstr ""

#: UI/Reports/filters/gl.html:125 UI/Reports/filters/gl.html:143
#: UI/Reports/filters/gl.html:164
#: UI/Reports/filters/invoice_outstanding.html:73
#: UI/Reports/filters/invoice_search.html:190
#: UI/Reports/filters/invoice_search.html:215
#: UI/Reports/filters/invoice_search.html:236
#: UI/Reports/filters/invoice_search.html:257
#: UI/Reports/filters/orders.html:119 UI/Reports/filters/search_goods.html:17
#: UI/Reports/filters/timecards.html:42
#: UI/Reports/filters/trial_balance.html:68
#: UI/payments/payments_detail.html:293
msgid "All"
msgstr ""

#: UI/Reports/filters/trial_balance.html:42
msgid "All accounts"
msgstr ""

#: UI/Reports/co/filter_cd.html:14
msgid "All fields are required"
msgstr ""

#: templates/demo/invoice.html:175
msgid "All prices in [_1]"
msgstr ""

#: templates/demo/sales_order.html:164 templates/demo/sales_quotation.html:146
msgid "All prices in [_1] Funds"
msgstr ""

#: templates/demo/product_receipt.html:153
#: templates/demo/purchase_order.html:155
msgid "All prices in [_1] funds"
msgstr ""

#: templates/demo/invoice.tex:220 templates/demo/product_receipt.tex:163
#: templates/demo/purchase_order.tex:169 templates/demo/sales_order.tex:174
#: templates/demo/sales_quotation.tex:139
msgid "All prices in [_1]."
msgstr ""

#: lib/LedgerSMB/Report/Invoices/COGS.pm:127 UI/timecards/timecard.html:131
msgid "Allocated"
msgstr ""

#: UI/Configuration/sequence.html:8
msgid "Allow Input"
msgstr ""

#: lib/LedgerSMB/Report/Contact/Purchase.pm:84
#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:235
#: lib/LedgerSMB/Report/Invoices/Transactions.pm:280
#: lib/LedgerSMB/Report/Orders.pm:225
#: lib/LedgerSMB/Report/Unapproved/Batch_Detail.pm:130 old/bin/aa.pl:812
#: old/bin/aa.pl:985 old/bin/am.pl:312 old/bin/ir.pl:599 old/bin/ir.pl:813
#: old/bin/is.pl:711 old/bin/is.pl:928 old/bin/pe.pl:945
#: UI/Reports/filters/gl.html:100 UI/Reports/filters/gl.html:106
#: UI/Reports/filters/invoice_outstanding.html:192
#: UI/Reports/filters/invoice_search.html:341
#: UI/Reports/filters/orders.html:190 UI/payments/payment2.html:347
#: templates/demo/ap_transaction.html:155
#: templates/demo/ap_transaction.tex:127
#: templates/demo/ar_transaction.html:151
#: templates/demo/ar_transaction.tex:145 templates/demo/invoice.html:195
#: templates/demo/invoice.tex:172 templates/demo/invoice.tex:236
#: templates/demo/printPayment.html:52 templates/demo/product_receipt.html:101
#: templates/demo/product_receipt.tex:136
#: templates/demo/purchase_order.html:103
#: templates/demo/purchase_order.tex:136 templates/demo/sales_order.html:106
#: templates/demo/sales_order.tex:138 templates/demo/sales_quotation.html:88
#: templates/demo/sales_quotation.tex:106
msgid "Amount"
msgstr ""

#: lib/LedgerSMB/Report/Budget/Variance.pm:84
msgid "Amount Budgeted"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:255
#: lib/LedgerSMB/Scripts/payment.pm:948 lib/LedgerSMB/Scripts/payment.pm:953
#: UI/Reports/filters/invoice_outstanding.html:240
#: UI/Reports/filters/invoice_search.html:387 templates/demo/check_base.tex:67
msgid "Amount Due"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Overpayments.pm:107
#: UI/Reports/filters/reconciliation_search.html:24
msgid "Amount From"
msgstr ""

#: lib/LedgerSMB/Report/Unapproved/Batch_Overview.pm:193
#: lib/LedgerSMB/Report/Unapproved/Drafts.pm:152
msgid "Amount Greater Than"
msgstr ""

#: lib/LedgerSMB/Report/Unapproved/Batch_Overview.pm:195
#: lib/LedgerSMB/Report/Unapproved/Drafts.pm:154
msgid "Amount Less Than"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Overpayments.pm:108
#: UI/Reports/filters/reconciliation_search.html:32
msgid "Amount To"
msgstr ""

#: UI/payments/use_overpayment2.html:153
msgid "Amount to be used"
msgstr ""

#: UI/payments/payment2.html:240
msgid "Amount to pay"
msgstr ""

#: UI/Contact/divs/wage.html:8
msgid "Amount/Rate"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:776
msgid ""
"An account can either be a summary account (which have a\n"
"link of \"AR\", \"AP\" or \"IC\" value) or be linked to dropdowns (having any\n"
"number of \"AR_*\", \"AP_*\" and/or \"IC_*\" links concatenated by colons (:)."
msgstr ""

#: sql/Pg-database.sql:2376
msgid "Annual Straight Line Daily"
msgstr ""

#: sql/Pg-database.sql:2382
msgid "Annual Straight Line Monthly"
msgstr ""

#: old/lib/LedgerSMB/Batch.pm:166
msgid "Any"
msgstr ""

#: templates/demo/check_base.tex:67 templates/demo/receipt.tex:80
msgid "Applied"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:1366
msgid "Applied discount"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:2032
msgid "Applied discount by an overpayment"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:946
msgid "Apply Disc"
msgstr ""

#: UI/Reports/filters/reconciliation_search.html:54
msgid "Approval Status"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Adj_Details.pm:125
#: lib/LedgerSMB/Report/Unapproved/Drafts.pm:228
#: lib/LedgerSMB/Scripts/asset.pm:1039 lib/LedgerSMB/Scripts/asset.pm:717
#: lib/LedgerSMB/Scripts/asset.pm:832 lib/LedgerSMB/Scripts/asset.pm:933
#: lib/LedgerSMB/Scripts/budgets.pm:112 UI/reconciliation/report.html:442
msgid "Approve"
msgstr ""

#: lib/LedgerSMB/Report/Reconciliation/Summary.pm:146
#: UI/Reports/filters/batches.html:23 UI/Reports/filters/gl.html:130
#: UI/Reports/filters/invoice_search.html:224
#: UI/Reports/filters/reconciliation_search.html:49
#: UI/Reports/filters/trial_balance.html:55 UI/reconciliation/report.html:447
msgid "Approved"
msgstr ""

#: lib/LedgerSMB/Report/Budget/Search.pm:95
#: lib/LedgerSMB/Report/Reconciliation/Summary.pm:158
msgid "Approved By"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:674
msgid "Approved at"
msgstr ""

#: templates/demo/printPayment.html:95
msgid "Approved signature"
msgstr ""

#: UI/reconciliation/report.html:446
msgid "Approving..."
msgstr ""

#: old/bin/aa.pl:86 old/bin/gl.pl:77 old/bin/io.pl:78
msgid "Apr"
msgstr ""

#: lib/LedgerSMB.pm:619 old/bin/aa.pl:72 old/bin/gl.pl:63 old/bin/io.pl:64
msgid "April"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:767 lib/LedgerSMB/Scripts/asset.pm:881
#: lib/LedgerSMB/Scripts/asset.pm:989
msgid "Aquired Value"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:905
msgid "Aquired Value Remaining"
msgstr ""

#: old/bin/oe.pl:1329
msgid "Are you sure you want to delete Order Number?"
msgstr ""

#: old/bin/oe.pl:1334
msgid "Are you sure you want to delete Quotation Number?"
msgstr ""

#: UI/Reports/filters/balance_sheet.html:93
msgid "As per"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Activity.pm:137
msgid "Assembled"
msgstr ""

#: UI/Reports/filters/search_goods.html:21
msgid "Assemblies"
msgstr ""

#: old/bin/ic.pl:2199
msgid "Assemblies restocked!"
msgstr ""

#: lib/LedgerSMB/Report/COA.pm:167 UI/Reports/filters/gl.html:172
#: UI/accounts/edit.html:160 sql/Pg-database.sql:2445
msgid "Asset"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Asset_Class.pm:81
#: UI/asset/edit_asset.html:152 UI/asset/edit_class.html:37
#: UI/asset/search_asset.html:141 UI/asset/search_class.html:33
msgid "Asset Account"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:663 UI/asset/begin_approval.html:14
#: UI/asset/begin_report.html:13
msgid "Asset Class"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Asset_Class.pm:114
msgid "Asset Class List"
msgstr ""

#: UI/asset/edit_asset.html:49 UI/asset/search_asset.html:48
msgid "Asset Class:"
msgstr ""

#: locale/menu.xml:613
msgid "Asset Classes"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:546
msgid "Asset Depreciation Report"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:548
msgid "Asset Disposal Report"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Asset.pm:141
msgid "Asset Listing"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:565
msgid "Asset Partial Disposal Report"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:510
msgid "Asset Tag"
msgstr ""

#: lib/LedgerSMB/Report/Balance_Sheet.pm:214 lib/LedgerSMB/Report/PNL.pm:208
#: locale/menu.xml:625
msgid "Assets"
msgstr ""

#: templates/demo/balance_sheet.html:381
msgid "Assets to Equity"
msgstr ""

#: templates/demo/balance_sheet.html:378
msgid "Assets to Liabilities"
msgstr ""

#: lib/LedgerSMB/Report/File/Incoming.pm:102 old/bin/aa.pl:1233
#: old/bin/ic.pl:904 old/bin/ir.pl:1020 old/bin/is.pl:1128 old/bin/oe.pl:930
#: UI/email.html:128 UI/lib/attachments.html:56
msgid "Attach"
msgstr ""

#: UI/file/attachment_screen.html:19
msgid "Attach File"
msgstr ""

#: UI/Contact/divs/address.html:111 UI/Contact/divs/contact_info.html:91
#: UI/Contact/divs/notes.html:40
msgid "Attach To"
msgstr ""

#: UI/Contact/divs/files.html:58
msgid "Attach to Credit Account"
msgstr ""

#: UI/Contact/divs/address.html:114 UI/Contact/divs/contact_info.html:94
#: UI/Contact/divs/files.html:54
msgid "Attach to Entity"
msgstr ""

#: UI/Contact/divs/files.html:13
msgid "Attached At"
msgstr ""

#: UI/Contact/divs/files.html:15
msgid "Attached By"
msgstr ""

#: old/bin/aa.pl:1205 old/bin/ic.pl:876 old/bin/ir.pl:994 old/bin/is.pl:1102
#: old/bin/oe.pl:904 UI/lib/attachments.html:32
msgid "Attached To"
msgstr ""

#: old/bin/aa.pl:1204 old/bin/ic.pl:875 old/bin/ir.pl:993 old/bin/is.pl:1101
#: old/bin/oe.pl:903 UI/lib/attachments.html:31
msgid "Attached To Type"
msgstr ""

#: old/bin/aa.pl:1181 old/bin/ic.pl:852 old/bin/ir.pl:970 old/bin/is.pl:1078
#: old/bin/oe.pl:880 UI/lib/attachments.html:4
msgid "Attached and Linked Files"
msgstr ""

#: old/bin/aa.pl:1185 old/bin/aa.pl:1206 old/bin/ic.pl:856 old/bin/ic.pl:877
#: old/bin/ir.pl:974 old/bin/ir.pl:995 old/bin/is.pl:1082 old/bin/is.pl:1103
#: old/bin/oe.pl:884 old/bin/oe.pl:905 UI/lib/attachments.html:10
#: UI/lib/attachments.html:33
msgid "Attached at"
msgstr ""

#: old/bin/aa.pl:1186 old/bin/aa.pl:1207 old/bin/ic.pl:857 old/bin/ic.pl:878
#: old/bin/ir.pl:975 old/bin/ir.pl:996 old/bin/is.pl:1083 old/bin/is.pl:1104
#: old/bin/oe.pl:885 old/bin/oe.pl:906 UI/lib/attachments.html:11
#: UI/lib/attachments.html:34
msgid "Attached by"
msgstr ""

#: old/bin/printer.pl:123
msgid "Attachment"
msgstr ""

#: UI/setup/complete.html:43 UI/setup/db-patches-log.html:21
msgid "Attention or Action required"
msgstr ""

#: templates/demo/bin_list.html:50 templates/demo/bin_list.html:61
#: templates/demo/packing_list.html:51 templates/demo/pick_list.html:51
#: templates/demo/product_receipt.html:50
#: templates/demo/product_receipt.html:60
#: templates/demo/product_receipt.tex:63 templates/demo/product_receipt.tex:98
#: templates/demo/purchase_order.html:50 templates/demo/purchase_order.html:60
#: templates/demo/purchase_order.tex:63 templates/demo/purchase_order.tex:98
#: templates/demo/request_quotation.html:50
#: templates/demo/request_quotation.html:60 templates/demo/sales_order.html:51
#: templates/demo/sales_quotation.html:47 templates/demo/work_order.html:50
msgid "Attn: [_1]"
msgstr ""

#: old/bin/aa.pl:90 old/bin/gl.pl:81 old/bin/io.pl:82
msgid "Aug"
msgstr ""

#: lib/LedgerSMB.pm:623 old/bin/aa.pl:76 old/bin/gl.pl:67 old/bin/io.pl:68
msgid "August"
msgstr ""

#: UI/Contact/divs/notes.html:69
msgid "Author: [_1]"
msgstr ""

#: old/bin/ir.pl:560 old/bin/is.pl:412 old/bin/is.pl:666
#: UI/Configuration/settings.html:60
msgid "Automatic"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Overpayments.pm:148
#: UI/payments/use_overpayment2.html:123
msgid "Available"
msgstr ""

#: UI/setup/list_databases.html:9
msgid "Available Databases"
msgstr ""

#: UI/setup/list_users.html:8
msgid "Available Users"
msgstr ""

#: lib/LedgerSMB/Scripts/currency.pm:221
msgid "Available exchange rates"
msgstr ""

#: UI/payments/use_overpayment2.html:119
msgid "Available overpayments"
msgstr ""

#: old/bin/ic.pl:421 UI/Reports/filters/search_goods.html:331
msgid "Average Cost"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Search.pm:263
msgid "Avg. Cost"
msgstr ""

#: UI/Contact/divs/bank_act.html:24 UI/Contact/divs/bank_act.html:64
#: UI/Contact/divs/bank_act.html:68 t/data/04-complex_template.html:557
msgid "BIC/SWIFT Code"
msgstr ""

#: t/data/04-complex_template.html:592
msgid "BIC/SWIFT Code:"
msgstr ""

#: old/bin/ic.pl:1251
msgid "BOM"
msgstr ""

#: UI/setup/confirm_operation.html:94
msgid "Backup"
msgstr ""

#: UI/setup/confirm_operation.html:101
msgid "Backup DB"
msgstr ""

#: UI/setup/confirm_operation.html:108
msgid "Backup Roles"
msgstr ""

#: lib/LedgerSMB/Report/GL.pm:190 UI/Reports/filters/gl.html:330
#: templates/demo/invoice.html:159 templates/demo/invoice.tex:204
msgid "Balance"
msgstr ""

#: UI/Reports/balance_sheet.html:23 UI/Reports/filters/balance_sheet.html:7
#: templates/demo/balance_sheet.html:301 templates/demo/balance_sheet.tex:44
#: locale/menu.xml:422
msgid "Balance Sheet"
msgstr ""

#: lib/LedgerSMB/Report/Balance_Sheet.pm:306 UI/templates/widget.html:37
msgid "Balance sheet"
msgstr ""

#: UI/Reports/filters/trial_balance.html:71
msgid "Balances as"
msgstr ""

#: UI/reconciliation/report.html:161
msgid "Bank"
msgstr ""

#: UI/Contact/divs/bank_act.html:71
msgid "Bank Account"
msgstr ""

#: t/data/04-complex_template.html:601
msgid "Bank Account:"
msgstr ""

#: lib/LedgerSMB/Scripts/contact.pm:210 UI/Contact/divs/bank_act.html:2
#: UI/Contact/divs/bank_act.html:6 t/data/04-complex_template.html:32
#: t/data/04-complex_template.html:554
msgid "Bank Accounts"
msgstr ""

#: UI/reconciliation/report.html:84
msgid "Bank transactions upload:"
msgstr ""

#: old/bin/ic.pl:958
msgid "Bar Code"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:131
msgid "Barcode entry on invoices"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:56
msgid "Base Currency"
msgstr ""

#: UI/setup/system_info.html:24
msgid "Base system"
msgstr ""

#: lib/LedgerSMB/Report/Assets/Net_Book_Value.pm:95 old/bin/ir.pl:601
#: old/bin/is.pl:713
msgid "Basis"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/Payments.pm:147
msgid "Batch"
msgstr ""

#: lib/LedgerSMB/Report/Unapproved/Batch_Detail.pm:109
msgid "Batch Class"
msgstr ""

#: old/bin/aa.pl:702
msgid "Batch Control Code"
msgstr ""

#: UI/create_batch.html:28
msgid "Batch Date"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/Payments.pm:144
msgid "Batch Description"
msgstr ""

#: lib/LedgerSMB/Report/Unapproved/Batch_Detail.pm:159
msgid "Batch ID"
msgstr ""

#: old/lib/LedgerSMB/GL.pm:145
msgid "Batch ID Missing"
msgstr ""

#: old/bin/aa.pl:707
msgid "Batch Name"
msgstr ""

#: lib/LedgerSMB/Report/Unapproved/Batch_Overview.pm:142
#: lib/LedgerSMB/Scripts/configuration.pm:109 UI/create_batch.html:10
#: UI/create_batch.html:69
msgid "Batch Number"
msgstr ""

#: lib/LedgerSMB/Report/Unapproved/Batch_Overview.pm:176
msgid "Batch Search"
msgstr ""

#: locale/menu.xml:246
msgid "Batches"
msgstr ""

#: UI/email.html:45
msgid "Bcc"
msgstr ""

#: UI/reconciliation/report.html:41
msgid "Beginning Statement Balance:"
msgstr ""

#: UI/Contact/divs/address.html:45 sql/Pg-database.sql:366
msgid "Billing"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:113
msgid "Billion"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/History.pm:137
#: lib/LedgerSMB/Report/Inventory/Search.pm:239 old/bin/ic.pl:2072
#: old/bin/ic.pl:463 old/bin/io.pl:254 old/bin/oe.pl:1905
#: UI/Reports/filters/search_goods.html:352 templates/demo/bin_list.html:106
#: templates/demo/bin_list.tex:124 templates/demo/pick_list.html:96
#: templates/demo/pick_list.tex:117 templates/demo/work_order.html:103
msgid "Bin"
msgstr ""

#: old/bin/am.pl:1004 old/bin/io.pl:1147 old/bin/oe.pl:265 old/bin/oe.pl:277
#: old/bin/printer.pl:106 old/bin/printer.pl:92
#: templates/demo/bin_list.html:16 templates/demo/bin_list.html:32
#: templates/demo/bin_list.tex:89
msgid "Bin List"
msgstr ""

#: UI/templates/widget.html:41
msgid "Bin list"
msgstr ""

#: lib/LedgerSMB/Report/Contact/EmployeeSearch.pm:70
#: UI/Contact/divs/person.html:122
msgid "Birthdate"
msgstr ""

#: UI/email.html:62
msgid "Body"
msgstr ""

#: UI/reconciliation/report.html:153
msgid "Books"
msgstr ""

#: sql/Pg-database.sql:1785
msgid "Budget"
msgstr ""

#: lib/LedgerSMB/Report/Budget/Variance.pm:118
msgid "Budget Number"
msgstr ""

#: lib/LedgerSMB/Report/Budget/Search.pm:112
msgid "Budget Search Results"
msgstr ""

#: lib/LedgerSMB/Report/Budget/Variance.pm:106
msgid "Budget Variance Report"
msgstr ""

#: locale/menu.xml:268
msgid "Budgets"
msgstr ""

#: old/bin/is.pl:355 old/bin/oe.pl:413 UI/payments/payments_detail.html:171
msgid "Business"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:42
msgid "Business Number"
msgstr ""

#: lib/LedgerSMB/Report/Contact/Search.pm:79 UI/Contact/divs/credit.html:252
#: UI/Contact/divs/credit.html:253
msgid "Business Type"
msgstr ""

#: t/data/04-complex_template.html:294
msgid "Business Type:"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Business_Unit.pm:83
msgid "Business Unit List"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:120
msgid "Business Unit Number"
msgstr ""

#: UI/Reports/filters/invoice_outstanding.html:165
#: UI/Reports/filters/invoice_search.html:314
msgid "Business Units"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/COGS.pm:132 old/bin/ic.pl:501
#: old/bin/ic.pl:670 UI/accounts/edit.html:448
msgid "COGS"
msgstr ""

#: UI/Reports/filters/cogs_lines.html:4
msgid "COGS Lots Report"
msgstr ""

#: UI/Contact/pricelist.html:127 UI/Reports/PNL.html:117
#: UI/Reports/aging_report.html:101 UI/Reports/balance_sheet.html:116
#: UI/Reports/display_report.html:60
msgid "CSV"
msgstr ""

#: old/bin/ir.pl:755 old/bin/is.pl:410 old/bin/is.pl:868
msgid "Calculate Taxes"
msgstr ""

#: old/lib/LedgerSMB/GL.pm:206
msgid "Can't post credits and debits on one line."
msgstr ""

#: lib/LedgerSMB/Scripts/report_aging.pm:165
#: lib/LedgerSMB/Scripts/setup.pm:189 lib/LedgerSMB/Scripts/setup.pm:255
#: old/bin/io.pl:1757 UI/templates/widget.html:150
#: UI/templates/widget.html:166
msgid "Cancel"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:222
msgid "Cancel the <b>whole migration</b>"
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:323
msgid "Cancel?"
msgstr ""

#: old/bin/gl.pl:705
msgid "Cannot Repost Transaction"
msgstr ""

#: old/bin/ic.pl:134
msgid "Cannot create Labor; COGS account does not exist!"
msgstr ""

#: old/bin/ic.pl:131
msgid "Cannot create Labor; Inventory account does not exist!"
msgstr ""

#: old/bin/ic.pl:113
msgid "Cannot create Part; COGS account does not exist!"
msgstr ""

#: old/bin/ic.pl:110
msgid "Cannot create Part; Income account does not exist!"
msgstr ""

#: old/bin/ic.pl:107
msgid "Cannot create Part; Inventory account does not exist!"
msgstr ""

#: old/bin/ic.pl:124
msgid "Cannot create Service; Expense account does not exist!"
msgstr ""

#: old/bin/ic.pl:120
msgid "Cannot create Service; Income account does not exist!"
msgstr ""

#: old/bin/ic.pl:1970
msgid "Cannot delete item!"
msgstr ""

#: old/bin/oe.pl:1372
msgid "Cannot delete order!"
msgstr ""

#: old/bin/oe.pl:1376
msgid "Cannot delete quotation!"
msgstr ""

#: old/bin/ir.pl:1264 old/bin/is.pl:1379
msgid "Cannot post invoice for a closed period!"
msgstr ""

#: old/bin/aa.pl:1512 old/bin/ir.pl:1279 old/bin/is.pl:1394
msgid "Cannot post payment for a closed period!"
msgstr ""

#: old/bin/aa.pl:1498 old/bin/aa.pl:208 old/bin/gl.pl:226 old/bin/gl.pl:723
msgid "Cannot post transaction for a closed period!"
msgstr ""

#: old/bin/gl.pl:765
msgid ""
"Cannot post transaction with a debit and credit entry for the same account!"
msgstr ""

#: old/bin/aa.pl:1565
msgid "Cannot post transaction!"
msgstr ""

#: old/bin/oe.pl:1225
msgid "Cannot save order!"
msgstr ""

#: old/bin/oe.pl:1242
msgid "Cannot save quotation!"
msgstr ""

#: old/bin/am.pl:261
msgid "Cannot save taxes!"
msgstr ""

#: old/bin/ic.pl:2202
msgid "Cannot stock assemblies!"
msgstr ""

#: templates/demo/timecard.html:52 templates/demo/timecard.tex:43
msgid "Card ID"
msgstr ""

#: UI/Reports/filters/income_statement.html:40
msgid "Cash"
msgstr ""

#: locale/menu.xml:164
msgid "Cash & Banking"
msgstr ""

#: UI/Reports/filters/payments.html:54 UI/asset/begin_approval.html:65
#: UI/payments/payment2.html:344
msgid "Cash Account"
msgstr ""

#: UI/templates/widget.html:31
msgid "Cash Transfer transaction"
msgstr ""

#: lib/LedgerSMB/Report/COA.pm:99 UI/timecards/timecard.html:16
msgid "Category"
msgstr ""

#: UI/email.html:36
msgid "Cc"
msgstr ""

#: UI/users/preferences.html:15
msgid "Change Password"
msgstr ""

#: UI/timecards/timecard.html:104
msgid "Chargeable"
msgstr ""

#: lib/LedgerSMB/Report/GL.pm:170
msgid "Chart ID"
msgstr ""

#: lib/LedgerSMB/Report/COA.pm:149 locale/menu.xml:428
msgid "Chart of Accounts"
msgstr ""

#: UI/setup/select_coa_details.html:33
msgid "Chart of accounts"
msgstr ""

#: UI/templates/widget.html:45
msgid "Check (base)"
msgstr ""

#: old/bin/ic.pl:2008
msgid "Check Inventory"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:135
msgid "Check Prefix"
msgstr ""

#: UI/setup/confirm_operation.html:125
msgid "Check consistency"
msgstr ""

#: UI/setup/complete.html:17
msgid "Check database consistency"
msgstr ""

#: UI/payments/payment1_5.html:22
msgid "Choose a company"
msgstr ""

#: sql/Pg-database.sql:630
msgid "Chord"
msgstr ""

#: old/bin/arap.pl:171 old/bin/io.pl:1579 UI/Contact/divs/address.html:86
#: UI/Contact/divs/address.html:146 UI/Reports/filters/contact_search.html:55
#: UI/Reports/filters/purchase_history.html:75
#: t/data/04-complex_template.html:386
msgid "City"
msgstr ""

#: t/data/04-complex_template.html:445
msgid "City:"
msgstr ""

#: UI/Contact/divs/credit.html:80
msgid "Class"
msgstr ""

#: lib/LedgerSMB/Scripts/recon.pm:486
msgid "Clear date"
msgstr ""

#: UI/reconciliation/report.html:204
msgid "Clear date is [*,_1,day] in the future of after posting"
msgstr ""

#: UI/reconciliation/report.html:210
msgid "Clear date over [*,_1,day]"
msgstr ""

#: lib/LedgerSMB/Report/GL.pm:165 UI/Reports/filters/gl.html:303
#: UI/reconciliation/report.html:130 UI/reconciliation/report.html:262
#: UI/reconciliation/report.html:335
msgid "Cleared"
msgstr ""

#: UI/reconciliation/report.html:116
msgid "Cleared Transactions"
msgstr ""

#: UI/timecards/timecard.html:100
msgid "Clocked"
msgstr ""

#: lib/LedgerSMB/Report/Orders.pm:234 old/bin/oe.pl:360
#: UI/Reports/filters/invoice_search.html:209
#: UI/Reports/filters/orders.html:113
#: UI/Reports/filters/purchase_history.html:205
#: UI/Reports/filters/timecards.html:35
msgid "Closed"
msgstr ""

#: lib/LedgerSMB/Report/Balance_Sheet.pm:283
msgid "Closing Balance"
msgstr ""

#: UI/Reports/filters/balance_sheet.html:115
msgid "Closing balance"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Language.pm:49
#: lib/LedgerSMB/Report/Listings/SIC.pm:47
msgid "Code"
msgstr ""

#: sql/Pg-database.sql:244
msgid "Cold Lead"
msgstr ""

#: lib/LedgerSMB/Scripts/order.pm:121 locale/menu.xml:331
msgid "Combine"
msgstr ""

#: lib/LedgerSMB/Scripts/order.pm:76
msgid "Combine Purchase Orders"
msgstr ""

#: lib/LedgerSMB/Scripts/order.pm:68
msgid "Combine Sales Orders"
msgstr ""

#: lib/LedgerSMB/Report/Taxform/Details.pm:81
#: lib/LedgerSMB/Report/Taxform/Summary.pm:81
#: lib/LedgerSMB/Scripts/contact.pm:202 UI/Contact/contact.html:12
#: UI/Contact/divs/company.html:2 UI/Reports/aging_report.html:12
#: UI/Reports/display_report.html:19 UI/Reports/filters/taxforms.html:62
#: templates/demo/display_report.html:157
#: templates/demo/display_report.tex:123
msgid "Company"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:39
msgid "Company Address"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:41
msgid "Company Fax"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:34
msgid "Company Information"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:54
msgid "Company License Number"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/Payments.pm:141
#: lib/LedgerSMB/Scripts/configuration.pm:36
msgid "Company Name"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:40
msgid "Company Phone"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:52
msgid "Company Sales Tax ID"
msgstr ""

#: UI/Reports/filters/balance_sheet.html:138
msgid "Comparison Dates"
msgstr ""

#: UI/Reports/filters/income_statement.html:156
msgid "Comparison Periods"
msgstr ""

#: lib/LedgerSMB/Scripts/report_aging.pm:174
msgid "Complete"
msgstr ""

#: UI/email.html:7
msgid "Compose e-mail"
msgstr ""

#: UI/setup/confirm_operation.html:10
msgid "Confirm Operation"
msgstr ""

#: old/bin/oe.pl:1348
msgid "Confirm!"
msgstr ""

#: lib/LedgerSMB.pm:543
msgid "Conflict with Existing Data.  Perhaps you already entered this?"
msgstr ""

#: UI/setup/consistency_results.html:8
msgid "Consistency check results"
msgstr ""

#: old/bin/io.pl:1672 UI/Reports/filters/contact_search.html:75
#: templates/demo/bin_list.html:77 templates/demo/packing_list.html:68
#: templates/demo/packing_list.tex:103 templates/demo/pick_list.html:68
#: templates/demo/pick_list.tex:96 templates/demo/product_receipt.html:76
#: templates/demo/product_receipt.tex:122
#: templates/demo/purchase_order.html:77 templates/demo/purchase_order.tex:122
#: templates/demo/request_quotation.html:77
#: templates/demo/request_quotation.tex:123
#: templates/demo/sales_quotation.html:62
#: templates/demo/sales_quotation.tex:92 sql/Pg-database.sql:240
msgid "Contact"
msgstr ""

#: lib/LedgerSMB/Scripts/contact.pm:209 UI/Contact/divs/contact_info.html:2
#: UI/Contact/divs/contact_info.html:31 UI/Contact/divs/contact_info.html:113
#: UI/Reports/filters/purchase_history.html:28
#: t/data/04-complex_template.html:31
msgid "Contact Info"
msgstr ""

#: t/data/04-complex_template.html:495 t/data/04-complex_template.html:536
msgid "Contact Info:"
msgstr ""

#: UI/Contact/divs/contact_info.html:6 t/data/04-complex_template.html:491
msgid "Contact Information"
msgstr ""

#: lib/LedgerSMB/Report/Contact/Search.pm:93
#: UI/Reports/filters/contact_search.html:9
msgid "Contact Search"
msgstr ""

#: UI/payments/payments_detail.html:429
msgid "Contact Total (if paying \"some\")"
msgstr ""

#: locale/menu.xml:3
msgid "Contacts"
msgstr ""

#: UI/budgetting/budget_entry.html:156 UI/budgetting/budget_entry.html:179
msgid "Content"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:1576 lib/LedgerSMB/Scripts/payment.pm:777
#: lib/LedgerSMB/Scripts/payment.pm:840 old/bin/arap.pl:263 old/bin/ic.pl:1746
#: old/bin/ic.pl:2027 old/bin/ic.pl:2176 old/bin/ic.pl:931 old/bin/oe.pl:1592
#: old/bin/oe.pl:2118 old/bin/oe.pl:2615 old/bin/pe.pl:1088 old/bin/pe.pl:237
#: old/bin/pe.pl:656 old/bin/pe.pl:821 UI/Contact/divs/credit.html:544
#: UI/Reports/co/filter_bm.html:138 UI/Reports/co/filter_cd.html:114
#: UI/Reports/filters/aging.html:147 UI/Reports/filters/balance_sheet.html:252
#: UI/Reports/filters/gl.html:362 UI/Reports/filters/income_statement.html:280
#: UI/Reports/filters/inventory_activity.html:30
#: UI/Reports/filters/invoice_outstanding.html:283
#: UI/Reports/filters/invoice_search.html:430
#: UI/Reports/filters/orders.html:222
#: UI/Reports/filters/purchase_history.html:356
#: UI/Reports/filters/taxforms.html:145
#: UI/Reports/filters/trial_balance.html:131 UI/asset/begin_approval.html:75
#: UI/asset/begin_report.html:59 UI/create_batch.html:43
#: UI/inventory/adjustment_setup.html:29 UI/oe-save-warn.html:18
#: UI/payments/payments_filter.html:138 UI/timecards/entry_filter.html:36
msgid "Continue"
msgstr ""

#: old/bin/ic.pl:845
msgid "Continue Previous Workflow"
msgstr ""

#: lib/LedgerSMB/Report/COA.pm:104 UI/accounts/edit.html:271
msgid "Contra"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1034
msgid ""
"Contrary to SQL-ledger, LedgerSMB invoices numbers must be unique. Please "
"review suggestions to make all AP invoice numbers unique. Conflicting "
"entries are presented by pairs, with a suffix added to the invoice number"
msgstr ""

#: lib/LedgerSMB/Report/Contact/EmployeeSearch.pm:62
#: lib/LedgerSMB/Report/Contact/Search.pm:66
#: lib/LedgerSMB/Report/Listings/Business_Unit.pm:59
#: lib/LedgerSMB/Report/PNL/ECA.pm:90
#: lib/LedgerSMB/Report/Taxform/Summary.pm:93 old/bin/arap.pl:165
#: UI/Contact/divs/company.html:53 UI/Contact/divs/person.html:53
#: UI/Reports/filters/contact_search.html:35
#: UI/Reports/filters/overpayments.html:9 UI/Reports/filters/taxforms.html:92
#: UI/business_units/edit.html:27 UI/business_units/filter.html:16
#: UI/payroll/income.html:57
msgid "Control Code"
msgstr ""

#: UI/setup/confirm_operation.html:47
msgid "Copy"
msgstr ""

#: UI/setup/confirm_operation.html:40
msgid "Copy to New Name"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Activity.pm:127 old/bin/ic.pl:1250
#: old/bin/ic.pl:999 old/bin/oe.pl:2459
msgid "Cost"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:99
msgid "Cost of Goods Sold"
msgstr ""

#: lib/LedgerSMB/Template/DB.pm:128 lib/LedgerSMB/Template/DB.pm:77
msgid "Could Not Load Template from DB"
msgstr ""

#: old/bin/ir.pl:1243 old/bin/is.pl:1359 old/bin/oe.pl:1258
msgid "Could not save the data.  Please try again"
msgstr ""

#: old/bin/oe.pl:2051
msgid "Could not save!"
msgstr ""

#: old/bin/oe.pl:2321
msgid "Could not transfer Inventory!"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Adj_Details.pm:101
#: UI/inventory/adjustment_entry.html:26
msgid "Counted"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Overpayments.pm:136
#: lib/LedgerSMB/Report/Listings/TemplateTrans.pm:102
msgid "Counterparty"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Overpayments.pm:104
msgid "Counterparty Code"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Country.pm:62
#: lib/LedgerSMB/Report/Payroll/Deduction_Types.pm:34
#: lib/LedgerSMB/Report/Payroll/Income_Types.pm:34 old/bin/io.pl:1588
#: UI/Contact/divs/address.html:89 UI/Contact/divs/address.html:175
#: UI/Contact/divs/company.html:80 UI/Contact/divs/employee.html:101
#: UI/Contact/divs/person.html:106 UI/Reports/filters/contact_search.html:40
#: UI/Reports/filters/purchase_history.html:99 UI/payroll/deduction.html:15
#: UI/payroll/income.html:16 UI/setup/select_coa_country.html:28
#: t/data/04-complex_template.html:389 locale/menu.xml:713
msgid "Country"
msgstr ""

#: lib/LedgerSMB/Report/Taxform/List.pm:55
msgid "Country Name"
msgstr ""

#: UI/taxform/add_taxform.html:28 t/data/04-complex_template.html:477
msgid "Country:"
msgstr ""

#: UI/setup/credentials.html:83 UI/templates/widget.html:124
msgid "Create"
msgstr ""

#: lib/LedgerSMB/Report/COA.pm:211
msgid "Create Account"
msgstr ""

#: UI/create_batch.html:7
msgid "Create Batch"
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:304
msgid "Create Database?"
msgstr ""

#: lib/LedgerSMB/Report/COA.pm:218
msgid "Create Heading"
msgstr ""

#: UI/reconciliation/new_report.html:54
msgid "Create New Report"
msgstr ""

#: UI/taxform/add_taxform.html:6
msgid "Create New Tax Form"
msgstr ""

#: UI/setup/new_user.html:170
msgid "Create User"
msgstr ""

#: UI/Contact/divs/user.html:85 UI/setup/new_user.html:42
msgid "Create new user"
msgstr ""

#: UI/create_batch.html:73
msgid "Created By"
msgstr ""

#: UI/create_batch.html:74
msgid "Created On"
msgstr ""

#: UI/Reports/co/filter_bm.html:105 UI/Reports/filters/gl.html:264
#: UI/Reports/filters/trial_balance.html:5 UI/budgetting/budget_entry.html:60
#: UI/journal/journal_entry.html:157
msgid "Credit"
msgstr ""

#: lib/LedgerSMB/Scripts/contact.pm:317
msgid "Credit Account"
msgstr ""

#: lib/LedgerSMB/Report/Contact/Search.pm:71
msgid "Credit Account Number"
msgstr ""

#: lib/LedgerSMB/Scripts/contact.pm:207 UI/Contact/divs/credit.html:2
msgid "Credit Accounts"
msgstr ""

#: old/bin/am.pl:997 templates/demo/invoice.html:36
#: templates/demo/invoice.tex:140 locale/menu.xml:48
msgid "Credit Invoice"
msgstr ""

#: old/bin/aa.pl:683 old/bin/ir.pl:388 old/bin/is.pl:446 old/bin/oe.pl:458
#: UI/Contact/divs/credit.html:33 UI/Contact/divs/credit.html:162
#: UI/Contact/divs/credit.html:163
msgid "Credit Limit"
msgstr ""

#: t/data/04-complex_template.html:201
msgid "Credit Limit:"
msgstr ""

#: locale/menu.xml:43
msgid "Credit Note"
msgstr ""

#: lib/LedgerSMB/Report/GL.pm:133 lib/LedgerSMB/Report/Trial_Balance.pm:176
#: lib/LedgerSMB/Scripts/payment.pm:207 UI/Reports/co/filter_cd.html:90
#: UI/reconciliation/report.html:171 UI/reconciliation/report.html:173
msgid "Credits"
msgstr ""

#: lib/LedgerSMB/Report/GL.pm:139
#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:260
#: lib/LedgerSMB/Report/Invoices/Transactions.pm:277 old/bin/ic.pl:1083
#: old/bin/ic.pl:988 old/bin/oe.pl:2464
msgid "Curr"
msgstr ""

#: lib/LedgerSMB/Report/Aging.pm:172
#: lib/LedgerSMB/Report/Contact/History.pm:73
#: lib/LedgerSMB/Report/Contact/Purchase.pm:79
#: lib/LedgerSMB/Report/Contact/Search.pm:83
#: lib/LedgerSMB/Report/Inventory/Search.pm:315
#: lib/LedgerSMB/Report/Orders.pm:230 lib/LedgerSMB/Scripts/currency.pm:244
#: old/bin/aa.pl:553 old/bin/ir.pl:294 old/bin/is.pl:315 old/bin/oe.pl:1571
#: old/bin/oe.pl:371 UI/Configuration/rate.html:28
#: UI/Contact/divs/credit.html:238 UI/Contact/divs/credit.html:239
#: UI/Contact/pricelist.csv:46 UI/Contact/pricelist.html:57
#: UI/Contact/pricelist.tex:62 UI/Reports/filters/gl.html:270
#: UI/Reports/filters/invoice_outstanding.html:213
#: UI/Reports/filters/invoice_search.html:360
#: UI/Reports/filters/orders.html:205 UI/Reports/filters/overpayments.html:43
#: UI/Reports/filters/payments.html:98
#: UI/Reports/filters/purchase_history.html:268
#: UI/Reports/filters/search_goods.html:422 UI/payments/payment1.html:64
#: UI/payments/payment2.html:126 UI/payments/payments_filter.html:72
#: UI/payments/use_overpayment1.html:42 UI/payments/use_overpayment2.html:90
#: UI/timecards/timecard-week.html:76 UI/timecards/timecard.html:120
#: templates/demo/printPayment.html:68 templates/demo/statement.html:72
#: templates/demo/statement.tex:58 locale/menu.xml:671
msgid "Currency"
msgstr ""

#: lib/LedgerSMB/Report/Aging.pm:142 old/bin/pe.pl:722
#: UI/Reports/co/filter_bm.html:52 UI/Reports/filters/aging.html:98
#: UI/lib/report_base.html:159 UI/lib/report_base.html:247
#: templates/demo/statement.html:67 templates/demo/statement.tex:57
msgid "Current"
msgstr ""

#: old/bin/io.pl:1677
msgid "Current Attn"
msgstr ""

#: lib/LedgerSMB/Report/Balance_Sheet.pm:241 lib/LedgerSMB/Report/PNL.pm:224
#: lib/LedgerSMB/Scripts/configuration.pm:90
msgid "Current earnings"
msgstr ""

#: UI/accounts/edit.html:311
msgid "Custom Flags"
msgstr ""

#: lib/LedgerSMB/Report/Aging.pm:89
#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:195
#: lib/LedgerSMB/Report/Invoices/Transactions.pm:250
#: lib/LedgerSMB/Report/Orders.pm:179 lib/LedgerSMB/Report/Orders.pm:185
#: old/bin/aa.pl:646 old/bin/ic.pl:1093 old/bin/is.pl:433 old/bin/pe.pl:1067
#: old/bin/pe.pl:917 UI/Contact/divs/credit.html:14
#: UI/Contact/divs/credit.html:15 UI/Reports/filters/aging.html:14
#: UI/Reports/filters/invoice_outstanding.html:9
#: UI/Reports/filters/invoice_search.html:10 UI/Reports/filters/orders.html:7
#: UI/business_units/edit.html:79 UI/payments/payments_detail.html:4
#: UI/payments/payments_filter.html:39 UI/payments/use_overpayment1.html:28
#: UI/payments/use_overpayment2.html:23 UI/payments/use_overpayment2.html:149
#: sql/Pg-database.sql:238
msgid "Customer"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/Transactions.pm:249
msgid "Customer Account"
msgstr ""

#: locale/menu.xml:91
msgid "Customer History"
msgstr ""

#: UI/Contact/divs/credit.html:380 t/data/04-complex_template.html:334
msgid "Customer Invoice"
msgstr ""

#: UI/Reports/filters/orders.html:8 UI/payments/payment1.html:42
#: UI/payments/payment2.html:26
msgid "Customer Name"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/Payments.pm:116
#: lib/LedgerSMB/Report/Invoices/Payments.pm:179
#: lib/LedgerSMB/Scripts/configuration.pm:123 old/bin/io.pl:1529
#: UI/Reports/filters/invoice_outstanding.html:10
#: UI/Reports/filters/invoice_search.html:11
#: UI/Reports/filters/overpayments.html:20 UI/Reports/filters/payments.html:14
#: UI/Reports/filters/purchase_history.html:56
#: UI/payments/payments_filter.html:38
msgid "Customer Number"
msgstr ""

#: old/bin/aa.pl:1481 old/bin/is.pl:1368 old/bin/oe.pl:1193 old/bin/pe.pl:1146
msgid "Customer missing!"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:890
msgid "Customer not in a business"
msgstr ""

#: old/bin/arap.pl:142 old/bin/pe.pl:1121
msgid "Customer not on file!"
msgstr ""

#: UI/setup/complete.html:84 UI/setup/confirm_operation.html:149
msgid "Customer/Vendor Accounts"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:1001
msgid "D M"
msgstr ""

#: UI/setup/credentials.html:46
msgid "DB admin login"
msgstr ""

#: UI/Contact/divs/employee.html:129
msgid "DOB"
msgstr ""

#: t/data/04-complex_template.html:106
msgid "DOB:"
msgstr ""

#: UI/reconciliation/report.html:165
msgid "Data from your bank statement"
msgstr ""

#: UI/reconciliation/report.html:157
msgid "Data from your ledger"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:418 old/bin/aa.pl:1471
msgid "Data not saved.  Please try again."
msgstr ""

#: lib/LedgerSMB/Scripts/recon.pm:152
msgid "Data not saved.  Please update again."
msgstr ""

#: UI/setup/credentials.html:69 UI/setup/list_databases.html:13
#: UI/setup/ui-db-credentials.html:6
msgid "Database"
msgstr ""

#: UI/setup/ui-db-credentials.html:1
msgid "Database Credentials"
msgstr ""

#: UI/setup/begin_backup.html:7 UI/setup/complete.html:9
#: UI/setup/complete_migration_revert.html:8 UI/setup/confirm_operation.html:7
#: UI/setup/db-patches-log.html:9 UI/setup/migration_step.html:9
#: UI/setup/new_user.html:8 UI/setup/select_coa_country.html:7
#: UI/setup/select_coa_details.html:7 UI/setup/template_info.html:7
#: UI/setup/upgrade_info.html:7
msgid "Database Management Console"
msgstr ""

#: UI/setup/complete.html:12 UI/setup/complete_migration_revert.html:9
msgid "Database Operation Complete"
msgstr ""

#: UI/setup/db-patches-log.html:12
msgid "Database Schema Patches Log"
msgstr ""

#: UI/setup/mismatch.html:8
msgid "Database Server version Mismatch"
msgstr ""

#: UI/setup/credentials.html:28
msgid "Database administrator credentials"
msgstr ""

#: UI/setup/system_info.html:13
msgid "Database component"
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:303
msgid "Database does not exist."
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:1148
msgid "Database exists."
msgstr ""

#: lib/LedgerSMB/Report/Aging.pm:130 lib/LedgerSMB/Report/GL.pm:101
#: lib/LedgerSMB/Report/Inventory/History.pm:145
#: lib/LedgerSMB/Report/Inventory/Search_Adj.pm:102
#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:205
#: lib/LedgerSMB/Report/Invoices/Transactions.pm:258
#: lib/LedgerSMB/Report/Listings/Overpayments.pm:140
#: lib/LedgerSMB/Report/Orders.pm:204
#: lib/LedgerSMB/Report/Unapproved/Batch_Detail.pm:114
#: lib/LedgerSMB/Report/Unapproved/Batch_Overview.pm:137
#: lib/LedgerSMB/Report/Unapproved/Drafts.pm:94
#: lib/LedgerSMB/Scripts/asset.pm:652 lib/LedgerSMB/Scripts/payment.pm:190
#: lib/LedgerSMB/Scripts/payment.pm:942 old/bin/aa.pl:984 old/bin/ir.pl:812
#: old/bin/is.pl:927 old/bin/oe.pl:1575 old/bin/pe.pl:930
#: UI/Reports/filters/gl.html:227 UI/Reports/filters/orders.html:147
#: UI/asset/begin_report.html:21 UI/inventory/adjustment_setup.html:14
#: UI/journal/journal_entry.html:43 UI/payments/payment2.html:98
#: UI/payments/payments_detail.html:327 UI/payments/use_overpayment2.html:85
#: templates/demo/ap_transaction.html:60
#: templates/demo/ap_transaction.html:151 templates/demo/ap_transaction.tex:76
#: templates/demo/ap_transaction.tex:127 templates/demo/ar_transaction.html:58
#: templates/demo/ar_transaction.html:148 templates/demo/ar_transaction.tex:93
#: templates/demo/ar_transaction.tex:144 templates/demo/bin_list.html:76
#: templates/demo/invoice.html:84 templates/demo/invoice.html:192
#: templates/demo/invoice.tex:149 templates/demo/invoice.tex:235
#: templates/demo/packing_list.html:67 templates/demo/packing_list.tex:103
#: templates/demo/pick_list.html:67 templates/demo/pick_list.tex:96
#: templates/demo/printPayment.html:70 templates/demo/product_receipt.tex:121
#: templates/demo/purchase_order.tex:121
#: templates/demo/request_quotation.html:75
#: templates/demo/request_quotation.tex:122
#: templates/demo/sales_quotation.html:60
#: templates/demo/sales_quotation.tex:91 templates/demo/statement.html:65
#: templates/demo/statement.tex:56 templates/demo/timecard.html:56
#: templates/demo/timecard.tex:44
msgid "Date"
msgstr ""

#: UI/users/preferences.html:77
msgid "Date Format"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Overpayments.pm:105 old/bin/ic.pl:923
msgid "Date From"
msgstr ""

#: lib/LedgerSMB/Report/Contact/Purchase.pm:103
#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:264
#: lib/LedgerSMB/Report/Invoices/Payments.pm:128
#: lib/LedgerSMB/Report/Invoices/Transactions.pm:300
#: UI/Reports/filters/invoice_outstanding.html:219
#: UI/Reports/filters/invoice_search.html:366
msgid "Date Paid"
msgstr ""

#: UI/lib/report_base.html:191
msgid "Date Range"
msgstr ""

#: old/bin/oe.pl:1780
msgid "Date Received"
msgstr ""

#: UI/Reports/filters/payments.html:80
msgid "Date Reversed"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Overpayments.pm:106 old/bin/ic.pl:926
msgid "Date To"
msgstr ""

#: UI/timecards/timecard.html:55
msgid "Date Worked"
msgstr ""

#: UI/payments/payment1.html:71
msgid "Date from"
msgstr ""

#: UI/setup/new_user.html:136
msgid "Date of Birth"
msgstr ""

#: old/bin/oe.pl:2036
msgid "Date received missing!"
msgstr ""

#: UI/Reports/filters/balance_sheet.html:45
msgid "Date selection"
msgstr ""

#: UI/payments/payment1.html:82
msgid "Date to"
msgstr ""

#: UI/payments/payment2.html:105
msgid "Date to register payment"
msgstr ""

#: UI/reconciliation/report.html:134
msgid "Date when the transaction was cleared at your bank"
msgstr ""

#: UI/reconciliation/report.html:146
msgid "Date when the transaction was posted in the ledger"
msgstr ""

#: UI/Contact/divs/credit.html:426 UI/Reports/filters/balance_sheet.html:68
#: UI/Reports/filters/income_statement.html:101
#: UI/Reports/filters/search_goods.html:198
msgid "Dates"
msgstr ""

#: old/bin/am.pl:268 UI/timecards/entry_filter.html:19
msgid "Day"
msgstr ""

#: old/bin/arap.pl:526
msgid "Day(s)"
msgstr ""

#: old/bin/am.pl:269
msgid "Days"
msgstr ""

#: UI/Reports/filters/gl.html:257 UI/Reports/filters/trial_balance.html:4
#: UI/budgetting/budget_entry.html:59 UI/journal/journal_entry.html:156
msgid "Debit"
msgstr ""

#: old/bin/am.pl:998 locale/menu.xml:119
msgid "Debit Invoice"
msgstr ""

#: locale/menu.xml:114
msgid "Debit Note"
msgstr ""

#: lib/LedgerSMB/Report/GL.pm:127 lib/LedgerSMB/Report/Trial_Balance.pm:170
#: lib/LedgerSMB/Scripts/payment.pm:201 UI/Reports/co/filter_cd.html:82
#: UI/reconciliation/report.html:170 UI/reconciliation/report.html:172
msgid "Debits"
msgstr ""

#: old/bin/aa.pl:94 old/bin/gl.pl:85 old/bin/io.pl:86
msgid "Dec"
msgstr ""

#: lib/LedgerSMB.pm:627 old/bin/aa.pl:80 old/bin/gl.pl:71 old/bin/io.pl:72
msgid "December"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:140
msgid "Decimal Places for Money"
msgstr ""

#: lib/LedgerSMB/Report/Payroll/Deduction_Types.pm:38
#: UI/payroll/deduction.html:28
msgid "Deduction Class"
msgstr ""

#: UI/payroll/deduction.html:3
msgid "Deduction Type"
msgstr ""

#: lib/LedgerSMB/Report/Payroll/Deduction_Types.pm:53
msgid "Deduction Types"
msgstr ""

#: UI/setup/upgrade_info.html:80
msgid "Default AP"
msgstr ""

#: UI/setup/upgrade_info.html:58
msgid "Default AR"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:86
msgid "Default Accounts"
msgstr ""

#: UI/setup/upgrade_info.html:44
msgid "Default Country"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:48
msgid "Default Email BCC"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:46
msgid "Default Email CC"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:50
msgid "Default Email From"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:44
msgid "Default Email To"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:61
msgid "Default Format"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:64
msgid "Default Paper Size"
msgstr ""

#: lib/LedgerSMB/Report/Taxform/List.pm:59
msgid "Default Reportable"
msgstr ""

#: UI/payroll/income.html:79
msgid "Default amount"
msgstr ""

#: locale/menu.xml:693
msgid "Defaults"
msgstr ""

#: lib/LedgerSMB/Scripts/currency.pm:76
msgid "Defined currencies"
msgstr ""

#: lib/LedgerSMB/Scripts/currency.pm:166
msgid "Defined exchange rate types"
msgstr ""

#: lib/LedgerSMB/Report/COA.pm:134 lib/LedgerSMB/Report/COA.pm:180
#: lib/LedgerSMB/Report/Inventory/Adj_Details.pm:131
#: lib/LedgerSMB/Report/Listings/TemplateTrans.pm:121
#: lib/LedgerSMB/Report/Listings/User.pm:99
#: lib/LedgerSMB/Report/Unapproved/Batch_Overview.pm:244
#: lib/LedgerSMB/Report/Unapproved/Drafts.pm:234 old/bin/aa.pl:1111
#: old/bin/am.pl:67 old/bin/am.pl:79 old/bin/ic.pl:813 old/bin/ic.pl:833
#: old/bin/ir.pl:923 old/bin/is.pl:1031 old/bin/pe.pl:154 old/bin/pe.pl:515
#: UI/Contact/divs/address.html:57 UI/Contact/divs/bank_act.html:14
#: UI/Contact/divs/company.html:146 UI/Contact/divs/contact_info.html:11
#: UI/Contact/divs/credit.html:22 UI/Contact/divs/person.html:153
#: UI/Contact/pricelist.html:66 UI/accounts/edit.html:104
#: UI/accounts/edit.html:580 UI/business_units/list_classes.html:94
#: UI/file/internal-file-list.html:31 UI/reconciliation/report.html:461
#: t/data/04-complex_template.html:510
msgid "Delete"
msgstr ""

#: lib/LedgerSMB/Report/Unapproved/Batch_Detail.pm:202
msgid "Delete Batch"
msgstr ""

#: old/bin/arap.pl:628 old/bin/arap.pl:634
msgid "Delete Schedule"
msgstr ""

#: UI/Contact/divs/user.html:42
msgid "Delete User"
msgstr ""

#: lib/LedgerSMB/Report/Unapproved/Batch_Detail.pm:209
msgid "Delete Vouchers"
msgstr ""

#: UI/accounts/edit.html:106 UI/accounts/edit.html:582
#: UI/file/internal-file-list.html:33 UI/reconciliation/report.html:466
msgid "Deleted"
msgstr ""

#: UI/accounts/edit.html:105 UI/accounts/edit.html:581
#: UI/file/internal-file-list.html:32 UI/reconciliation/report.html:465
msgid "Deleting..."
msgstr ""

#: templates/demo/invoice.tex:167 templates/demo/request_quotation.html:110
#: templates/demo/request_quotation.tex:139
msgid "Delivery"
msgstr ""

#: lib/LedgerSMB/Report/Contact/History.pm:107 old/bin/io.pl:275
#: UI/Reports/filters/purchase_history.html:310
msgid "Delivery Date"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:784
msgid "Dep. Basis"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:773
msgid "Dep. Method"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:761 lib/LedgerSMB/Scripts/asset.pm:875
#: lib/LedgerSMB/Scripts/asset.pm:983
msgid "Dep. Starts"
msgstr ""

#: lib/LedgerSMB/Report/Assets/Net_Book_Value.pm:103
msgid "Dep. Through"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:808
msgid "Dep. YTD"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:802
msgid "Dep. this run"
msgstr ""

#: old/bin/aa.pl:583 old/bin/ir.pl:325 old/bin/is.pl:344 old/bin/oe.pl:557
#: UI/asset/edit_asset.html:145 UI/asset/search_asset.html:133
#: UI/payments/payment2.html:74 UI/payments/payments_detail.html:129
msgid "Department"
msgstr ""

#: UI/payments/payment1.html:28
msgid "Departments"
msgstr ""

#: locale/menu.xml:641
msgid "Depreciate"
msgstr ""

#: UI/asset/import_asset.html:10
msgid "Depreciate Through"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:700 UI/accounts/edit.html:517
#: locale/menu.xml:657
msgid "Depreciation"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Asset_Class.pm:84
#: UI/asset/edit_asset.html:165 UI/asset/edit_class.html:48
#: UI/asset/search_asset.html:152 UI/asset/search_class.html:44
msgid "Depreciation Account"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Asset_Class.pm:101
#: lib/LedgerSMB/Report/Listings/Asset_Class.pm:78 UI/asset/edit_class.html:24
#: UI/asset/search_class.html:20
msgid "Depreciation Method"
msgstr ""

#: UI/asset/edit_asset.html:118 UI/asset/search_asset.html:106
msgid "Depreciation Starts"
msgstr ""

#: lib/LedgerSMB/Report/Aging.pm:125
#: lib/LedgerSMB/Report/Assets/Net_Book_Value.pm:79
#: lib/LedgerSMB/Report/Budget/Search.pm:87
#: lib/LedgerSMB/Report/Budget/Variance.pm:120
#: lib/LedgerSMB/Report/Budget/Variance.pm:71 lib/LedgerSMB/Report/COA.pm:93
#: lib/LedgerSMB/Report/Contact/History.pm:84
#: lib/LedgerSMB/Report/Contact/Search.pm:75
#: lib/LedgerSMB/Report/File/Incoming.pm:53
#: lib/LedgerSMB/Report/File/Internal.pm:51 lib/LedgerSMB/Report/GL.pm:117
#: lib/LedgerSMB/Report/Inventory/Activity.pm:166
#: lib/LedgerSMB/Report/Inventory/Activity.pm:97
#: lib/LedgerSMB/Report/Inventory/Adj_Details.pm:98
#: lib/LedgerSMB/Report/Inventory/History.pm:125
#: lib/LedgerSMB/Report/Inventory/Search.pm:223
#: lib/LedgerSMB/Report/Listings/Asset.pm:127
#: lib/LedgerSMB/Report/Listings/Asset.pm:92
#: lib/LedgerSMB/Report/Listings/Business_Type.pm:50
#: lib/LedgerSMB/Report/Listings/Business_Unit.pm:63
#: lib/LedgerSMB/Report/Listings/GIFI.pm:52
#: lib/LedgerSMB/Report/Listings/Language.pm:53
#: lib/LedgerSMB/Report/Listings/SIC.pm:51
#: lib/LedgerSMB/Report/Listings/TemplateTrans.pm:97
#: lib/LedgerSMB/Report/Listings/Warehouse.pm:46
#: lib/LedgerSMB/Report/PNL/Product.pm:79
#: lib/LedgerSMB/Report/Timecards.pm:106 lib/LedgerSMB/Report/Timecards.pm:93
#: lib/LedgerSMB/Report/Unapproved/Batch_Detail.pm:125
#: lib/LedgerSMB/Report/Unapproved/Batch_Overview.pm:148
#: lib/LedgerSMB/Report/Unapproved/Batch_Overview.pm:191
#: lib/LedgerSMB/Report/Unapproved/Drafts.pm:110
#: lib/LedgerSMB/Scripts/asset.pm:515 lib/LedgerSMB/Scripts/asset.pm:870
#: lib/LedgerSMB/Scripts/asset.pm:978 lib/LedgerSMB/Scripts/currency.pm:140
#: lib/LedgerSMB/Scripts/currency.pm:50 lib/LedgerSMB/Scripts/file.pm:137
#: old/bin/aa.pl:755 old/bin/aa.pl:815 old/bin/am.pl:303 old/bin/ic.pl:1244
#: old/bin/ic.pl:2001 old/bin/ic.pl:2068 old/bin/ic.pl:724 old/bin/io.pl:1669
#: old/bin/io.pl:242 old/bin/ir.pl:436 old/bin/is.pl:504 old/bin/oe.pl:1898
#: old/bin/oe.pl:2099 old/bin/oe.pl:2172 old/bin/oe.pl:2206 old/bin/oe.pl:2451
#: old/bin/pe.pl:218 old/bin/pe.pl:264 old/bin/pe.pl:293 old/bin/pe.pl:940
#: UI/Contact/divs/contact_info.html:30 UI/Contact/divs/contact_info.html:105
#: UI/Contact/divs/credit.html:32 UI/Contact/divs/credit.html:105
#: UI/Contact/divs/credit.html:106 UI/Contact/pricelist.csv:11
#: UI/Contact/pricelist.html:25 UI/Contact/pricelist.tex:17
#: UI/Reports/filters/batches.html:35 UI/Reports/filters/budget_search.html:25
#: UI/Reports/filters/cogs_lines.html:18 UI/Reports/filters/gl.html:82
#: UI/Reports/filters/gl.html:248
#: UI/Reports/filters/inventory_activity.html:18
#: UI/Reports/filters/invoice_search.html:140
#: UI/Reports/filters/orders.html:82
#: UI/Reports/filters/purchase_history.html:249
#: UI/Reports/filters/search_goods.html:63
#: UI/Reports/filters/search_goods.html:271 UI/accounts/edit.html:53
#: UI/accounts/edit.html:142 UI/accounts/yearend.html:43
#: UI/budgetting/budget_entry.html:26 UI/budgetting/budget_entry.html:61
#: UI/create_batch.html:19 UI/create_batch.html:72
#: UI/file/internal-file-list.html:42 UI/inventory/adjustment_entry.html:25
#: UI/journal/journal_entry.html:57 UI/payments/payment2.html:186
#: UI/reconciliation/report.html:150 UI/reconciliation/report.html:265
#: UI/reconciliation/report.html:338 UI/timecards/timecard-week.html:111
#: UI/timecards/timecard.html:45 templates/demo/bin_list.html:100
#: templates/demo/bin_list.tex:121 templates/demo/invoice.html:109
#: templates/demo/invoice.tex:166 templates/demo/packing_list.html:92
#: templates/demo/packing_list.tex:129 templates/demo/pick_list.html:92
#: templates/demo/pick_list.tex:115 templates/demo/printPayment.html:48
#: templates/demo/product_receipt.html:96
#: templates/demo/product_receipt.tex:133
#: templates/demo/purchase_order.html:98 templates/demo/purchase_order.tex:133
#: templates/demo/request_quotation.html:107
#: templates/demo/request_quotation.tex:138
#: templates/demo/sales_order.html:101 templates/demo/sales_order.tex:135
#: templates/demo/sales_quotation.html:83
#: templates/demo/sales_quotation.tex:103 templates/demo/timecard.html:91
#: templates/demo/timecard.html:99 templates/demo/timecard.tex:54
#: templates/demo/timecard.tex:56 templates/demo/work_order.html:100
#: templates/demo/work_order.tex:140 locale/menu.xml:514 locale/menu.xml:578
msgid "Description"
msgstr ""

#: old/bin/pe.pl:170
msgid "Description Translations"
msgstr ""

#: UI/asset/edit_asset.html:57 UI/asset/search_asset.html:54
#: UI/business_units/edit.html:69 UI/business_units/filter.html:22
#: UI/taxform/add_taxform.html:36
msgid "Description:"
msgstr ""

#: old/bin/pe.pl:803 UI/Reports/filters/aging.html:63
#: UI/Reports/filters/purchase_history.html:224
msgid "Detail"
msgstr ""

#: UI/Reports/filters/invoice_outstanding.html:117
#: UI/payments/payments_detail.html:225
msgid "Details"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:69
msgid "Disable Back Button"
msgstr ""

#: lib/LedgerSMB/Report/Contact/History.pm:103
msgid "Disc"
msgstr ""

#: templates/demo/invoice.html:114 templates/demo/invoice.tex:171
#: templates/demo/sales_order.html:105 templates/demo/sales_order.tex:137
#: templates/demo/sales_quotation.html:87
#: templates/demo/sales_quotation.tex:105
msgid "Disc %"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:945 old/bin/ic.pl:1096
#: UI/Contact/divs/credit.html:184 UI/Contact/divs/credit.html:185
#: UI/Reports/filters/purchase_history.html:299 UI/accounts/edit.html:377
#: UI/accounts/edit.html:427 UI/payments/payments_detail.html:334
msgid "Discount"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Business_Type.pm:57
msgid "Discount (%)"
msgstr ""

#: t/data/04-complex_template.html:242
msgid "Discount:"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:893
msgid "Disp. Aquired Value"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:701 locale/menu.xml:646 locale/menu.xml:662
msgid "Disposal"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:995
msgid "Disposal Date"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:552
msgid "Disposal Method"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:1046
msgid "Disposal Report [_1] on date [_2]"
msgstr ""

#: lib/LedgerSMB.pm:540
msgid "Division by 0 error"
msgstr ""

#: old/bin/io.pl:1859
msgid "Do not keep field empty [_1]"
msgstr ""

#: UI/Reports/co/filter_cd.html:75
msgid "Document Type"
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:537
msgid "Don't know what to do with backup"
msgstr ""

#: old/bin/oe.pl:1997 old/bin/oe.pl:2005
msgid "Done"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:914
msgid "Double customernumbers"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:949
msgid "Double vendornumbers"
msgstr ""

#: old/bin/aa.pl:1615
msgid "Draft Posted"
msgstr ""

#: lib/LedgerSMB/Report/Unapproved/Drafts.pm:132
msgid "Draft Search"
msgstr ""

#: lib/LedgerSMB/Report/Unapproved/Drafts.pm:144
msgid "Draft Type"
msgstr ""

#: old/bin/aa.pl:151 old/bin/ir.pl:121 old/bin/is.pl:130
msgid "Draft deleted"
msgstr ""

#: locale/menu.xml:251
msgid "Drafts"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Search.pm:287 old/bin/ic.pl:480
#: UI/Reports/filters/search_goods.html:106
#: UI/Reports/filters/search_goods.html:387
msgid "Drawing"
msgstr ""

#: lib/LedgerSMB/Report/COA.pm:129
msgid "Dropdowns"
msgstr ""

#: lib/LedgerSMB/Report/Contact/Purchase.pm:99
#: lib/LedgerSMB/Report/Invoices/Transactions.pm:296
#: templates/demo/ap_transaction.html:64 templates/demo/ap_transaction.tex:77
#: templates/demo/ar_transaction.html:62 templates/demo/ar_transaction.tex:94
#: templates/demo/invoice.html:85 templates/demo/invoice.tex:150
#: templates/demo/receipt.tex:80 templates/demo/statement.html:66
#: templates/demo/statement.tex:56
msgid "Due"
msgstr ""

#: lib/LedgerSMB/Report/Aging.pm:135
#: lib/LedgerSMB/Report/Contact/Purchase.pm:107
#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:268
#: lib/LedgerSMB/Report/Invoices/Transactions.pm:303 old/bin/aa.pl:787
#: old/bin/ir.pl:467 old/bin/is.pl:559
#: UI/Reports/filters/invoice_outstanding.html:234
#: UI/Reports/filters/invoice_search.html:381
msgid "Due Date"
msgstr ""

#: old/bin/aa.pl:1486
msgid "Due Date missing!"
msgstr ""

#: UI/Contact/divs/user.html:14 UI/setup/edit_user.html:11
msgid "Duplicate User Found: Importing User"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:485
msgid "Duplicate employee numbers"
msgstr ""

#: old/bin/am.pl:316 old/bin/arap.pl:406 old/bin/is.pl:1030 old/bin/oe.pl:2004
#: UI/Reports/filters/contact_search.html:45
msgid "E-mail"
msgstr ""

#: old/bin/am.pl:969
msgid "E-mail address missing!"
msgstr ""

#: lib/LedgerSMB/Scripts/report_aging.pm:206
msgid "E-mail aging reminder status"
msgstr ""

#: old/bin/arap.pl:448
msgid "E-mail message"
msgstr ""

#: lib/LedgerSMB/Report/PNL/ECA.pm:77
msgid "ECA Income Statement"
msgstr ""

#: old/bin/aa.pl:157 old/bin/arapprn.pl:67 UI/Contact/divs/address.html:56
#: UI/Contact/divs/contact_info.html:10 UI/templates/widget.html:129
#: t/data/04-complex_template.html:506
msgid "Edit"
msgstr ""

#: old/bin/aa.pl:504 old/bin/aa.pl:542
msgid "Edit AP Transaction"
msgstr ""

#: old/bin/aa.pl:503
msgid "Edit AR Transaction"
msgstr ""

#: old/bin/ic.pl:691 old/bin/ic.pl:83
msgid "Edit Assembly"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:63
msgid "Edit Asset Class"
msgstr ""

#: UI/Contact/divs/company.html:9
msgid "Edit Company"
msgstr ""

#: old/bin/is.pl:148
msgid "Edit Credit Invoice"
msgstr ""

#: old/bin/aa.pl:540
msgid "Edit Credit Note"
msgstr ""

#: t/data/04-complex_template.html:17
msgid "Edit Customer"
msgstr ""

#: old/bin/is.pl:145
msgid "Edit Customer Return"
msgstr ""

#: old/bin/aa.pl:538
msgid "Edit Debit Note"
msgstr ""

#: old/bin/pe.pl:417
msgid "Edit Description Translations"
msgstr ""

#: UI/Contact/divs/employee.html:8 t/data/04-complex_template.html:21
msgid "Edit Employee"
msgstr ""

#: old/bin/pe.pl:88
msgid "Edit Group"
msgstr ""

#: old/bin/ic.pl:84
msgid "Edit Labor/Overhead"
msgstr ""

#: old/bin/ic.pl:689 old/bin/ic.pl:81
msgid "Edit Part"
msgstr ""

#: UI/Contact/divs/person.html:8
msgid "Edit Person"
msgstr ""

#: old/bin/oe.pl:100
msgid "Edit Purchase Order"
msgstr ""

#: old/bin/oe.pl:114
msgid "Edit Quotation"
msgstr ""

#: UI/business_units/edit.html:10
msgid "Edit Reporting Unit"
msgstr ""

#: old/bin/oe.pl:110
msgid "Edit Request for Quotation"
msgstr ""

#: old/bin/is.pl:151
msgid "Edit Sales Invoice"
msgstr ""

#: old/bin/oe.pl:105
msgid "Edit Sales Order"
msgstr ""

#: old/bin/ic.pl:690 old/bin/ic.pl:82
msgid "Edit Service"
msgstr ""

#: UI/taxform/add_taxform.html:4
msgid "Edit Tax Form"
msgstr ""

#: UI/Contact/divs/employee.html:162
msgid "Edit User"
msgstr ""

#: t/data/04-complex_template.html:19
msgid "Edit Vendor"
msgstr ""

#: old/bin/ir.pl:142
msgid "Edit Vendor Invoice"
msgstr ""

#: old/bin/ir.pl:136
msgid "Edit Vendor Return"
msgstr ""

#: UI/budgetting/budget_entry.html:7
msgid "Edit budget"
msgstr ""

#: locale/menu.xml:672
msgid "Edit currencies"
msgstr ""

#: locale/menu.xml:677
msgid "Edit rate types"
msgstr ""

#: locale/menu.xml:682
msgid "Edit rates"
msgstr ""

#: UI/Contact/divs/user.html:17 UI/setup/edit_user.html:14
msgid "Editing User"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:76
msgid "Eight"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:87
msgid "Eighteen"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:105
msgid "Eighty"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:79
msgid "Eleven"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:80
msgid "Eleven-o"
msgstr ""

#: UI/Reports/aging_report.html:54
msgid "Email"
msgstr ""

#: lib/LedgerSMB/Report/Orders.pm:254 lib/LedgerSMB/Scripts/contact.pm:204
#: old/bin/aa.pl:610 old/bin/oe.pl:581 old/bin/pe.pl:767
#: UI/Contact/divs/employee.html:2 UI/timecards/entry_filter.html:11
#: t/data/04-complex_template.html:43 templates/demo/ap_transaction.html:76
#: templates/demo/ap_transaction.tex:84 templates/demo/ar_transaction.tex:101
#: templates/demo/timecard.html:40 templates/demo/timecard.tex:38
#: sql/Pg-database.sql:239
msgid "Employee"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:121
#: UI/Contact/divs/employee.html:105 UI/setup/new_user.html:128
#: t/data/04-complex_template.html:92
msgid "Employee Number"
msgstr ""

#: lib/LedgerSMB/Report/Contact/EmployeeSearch.pm:88
#: UI/Reports/filters/contact_search.html:13
msgid "Employee Search"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:467
msgid ""
"Employee name doesn't meet minimal requirements (e.g. non-empty, "
"alphanumeric)"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:156
msgid "Employee wage screen"
msgstr ""

#: UI/lib/report_base.html:296 locale/menu.xml:281
msgid "Employees"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:737
msgid "Empty AP account"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:718
msgid "Empty AR account"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:833
msgid "Empty businesses"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:810
msgid "Empty customernumbers"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:930
msgid "Empty vendornumbers"
msgstr ""

#: lib/LedgerSMB/Report/Budget/Search.pm:126
#: lib/LedgerSMB/Report/Budget/Search.pm:77
#: lib/LedgerSMB/Report/Budget/Variance.pm:124
#: lib/LedgerSMB/Report/Contact/History.pm:155 lib/LedgerSMB/Report/GL.pm:237
#: lib/LedgerSMB/Report/Inventory/Search_Adj.pm:85
#: lib/LedgerSMB/Report/Invoices/COGS.pm:160
#: lib/LedgerSMB/Report/Listings/Business_Unit.pm:71
#: UI/Contact/divs/credit.html:35 UI/Contact/divs/credit.html:140
#: UI/Contact/divs/credit.html:141 UI/Contact/divs/employee.html:143
#: UI/Reports/filters/budget_search.html:40 UI/budgetting/budget_entry.html:46
#: UI/business_units/edit.html:62
msgid "End Date"
msgstr ""

#: t/data/04-complex_template.html:122 t/data/04-complex_template.html:188
msgid "End Date:"
msgstr ""

#: lib/LedgerSMB/Report/Contact/EmployeeSearch.pm:78
msgid "End date"
msgstr ""

#: UI/Reports/filters/trial_balance.html:10
msgid "Ending"
msgstr ""

#: lib/LedgerSMB/Report/Trial_Balance.pm:194
msgid "Ending Balance"
msgstr ""

#: lib/LedgerSMB/Report/Trial_Balance.pm:188
msgid "Ending Balance Credit"
msgstr ""

#: lib/LedgerSMB/Report/Trial_Balance.pm:182
msgid "Ending Balance Debit"
msgstr ""

#: UI/reconciliation/report.html:71
msgid "Ending GL Balance:"
msgstr ""

#: UI/reconciliation/report.html:45
msgid "Ending Statement Balance:"
msgstr ""

#: old/bin/am.pl:309
msgid "Ends"
msgstr ""

#: UI/inventory/adjustment_entry.html:7 locale/menu.xml:547
msgid "Enter Inventory"
msgstr ""

#: UI/setup/new_user.html:9
msgid "Enter User"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:453
msgid "Enter employee numbers where they are missing"
msgstr ""

#: lib/LedgerSMB/Report/Budget/Search.pm:91
#: lib/LedgerSMB/Report/Reconciliation/Summary.pm:155
msgid "Entered By"
msgstr ""

#: UI/timecards/timecard.html:20
msgid "Entered For"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:668
msgid "Entered at"
msgstr ""

#: UI/Contact/divs/notes.html:68 t/data/04-complex_template.html:629
msgid "Entered at: [_1]"
msgstr ""

#: lib/LedgerSMB/Scripts/contact.pm:315
#: lib/LedgerSMB/Scripts/report_aging.pm:144 UI/Contact/divs/notes.html:44
#: sql/Pg-database.sql:728
msgid "Entity"
msgstr ""

#: UI/lib/report_base.html:47
msgid "Entity Class"
msgstr ""

#: old/bin/ir.pl:406 old/bin/is.pl:464 old/bin/oe.pl:475
msgid "Entity Code"
msgstr ""

#: old/bin/aa.pl:723
msgid "Entity Control Code"
msgstr ""

#: sql/Pg-database.sql:730
msgid "Entity Credit Account"
msgstr ""

#: UI/Reports/filters/purchase_history.html:20
msgid "Entity Name"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:122
msgid "Entity number"
msgstr ""

#: lib/LedgerSMB/Report/GL.pm:122
msgid "Entry ID"
msgstr ""

#: old/bin/printer.pl:111 UI/templates/widget.html:49
msgid "Envelope"
msgstr ""

#: UI/setup/system_info.html:35
msgid "Environment"
msgstr ""

#: lib/LedgerSMB/Report/Balance_Sheet.pm:234 lib/LedgerSMB/Report/COA.pm:169
#: lib/LedgerSMB/Report/PNL.pm:218 UI/Reports/filters/gl.html:188
#: UI/accounts/edit.html:162
msgid "Equity"
msgstr ""

#: lib/LedgerSMB/Report/Balance_Sheet.pm:220
msgid "Equity & Liabilities"
msgstr ""

#: UI/accounts/edit.html:163
msgid "Equity (Temporary)"
msgstr ""

#: templates/demo/balance_sheet.html:384
msgid "Equity to Liabilities"
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:485
msgid "Error creating backup file"
msgstr ""

#: lib/LedgerSMB/Scripts/vouchers.pm:113
msgid "Error creating batch.  Please try again."
msgstr ""

#: lib/LedgerSMB.pm:544
msgid "Error from Function:"
msgstr ""

#: lib/LedgerSMB/Scripts/account.pm:162
msgid "Error: Cannot include summary account in other dropdown menus"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:778
msgid "Est. Life"
msgstr ""

#: old/bin/am.pl:314 old/bin/arap.pl:549
msgid "Every"
msgstr ""

#: old/bin/aa.pl:986 old/bin/ir.pl:814 old/bin/is.pl:929
msgid "Exch"
msgstr ""

#: lib/LedgerSMB/Report/Contact/History.pm:117
#: lib/LedgerSMB/Scripts/payment.pm:952 old/bin/aa.pl:560 old/bin/ir.pl:303
#: old/bin/is.pl:324 old/bin/oe.pl:1579 old/bin/oe.pl:381 old/bin/oe.pl:389
#: UI/Reports/filters/overpayments.html:50
#: UI/Reports/filters/payments.html:103 UI/payments/payment2.html:134
#: UI/payments/payments_detail.html:148 UI/payments/payments_detail.html:157
#: UI/payments/use_overpayment2.html:98
msgid "Exchange Rate"
msgstr ""

#: old/bin/aa.pl:1519 old/bin/ir.pl:1287 old/bin/is.pl:1402
msgid "Exchange rate for payment missing!"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:1262
msgid "Exchange rate hasn't been defined!"
msgstr ""

#: old/bin/aa.pl:1501 old/bin/ir.pl:1267 old/bin/is.pl:1382 old/bin/oe.pl:1197
msgid "Exchange rate missing!"
msgstr ""

#: UI/email.html:143
msgid "Expansion"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Adj_Details.pm:104
msgid "Expected"
msgstr ""

#: lib/LedgerSMB/Report/COA.pm:171 old/bin/ic.pl:634
#: UI/Reports/filters/gl.html:204 UI/accounts/edit.html:165
#: UI/accounts/edit.html:487 UI/accounts/edit.html:526
msgid "Expense"
msgstr ""

#: UI/asset/edit_asset.html:187
msgid "Expense Account"
msgstr ""

#: UI/accounts/edit.html:390
msgid "Expense/Asset"
msgstr ""

#: lib/LedgerSMB/Report/Balance_Sheet.pm:203 lib/LedgerSMB/Report/PNL.pm:197
msgid "Expenses"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:153
msgid "Experimental features"
msgstr ""

#: old/bin/io.pl:252 templates/demo/invoice.html:115
#: templates/demo/request_quotation.tex:140
msgid "Extended"
msgstr ""

#: UI/timecards/timecard-week.html:105
msgid "Extra Used"
msgstr ""

#: UI/budgetting/budget_entry.html:57 UI/journal/journal_entry.html:110
#: UI/journal/journal_entry.html:155
msgid "FX"
msgstr ""

#: UI/Reports/filters/gl.html:282
msgid "FX Credit"
msgstr ""

#: lib/LedgerSMB/Report/GL.pm:149
msgid "FX Credits"
msgstr ""

#: UI/Reports/filters/gl.html:276
msgid "FX Debit"
msgstr ""

#: lib/LedgerSMB/Report/GL.pm:143
msgid "FX Debits"
msgstr ""

#: old/bin/pe.pl:1213
msgid "Failed to save order!"
msgstr ""

#: templates/demo/ar_transaction.html:48 templates/demo/ar_transaction.tex:43
#: templates/demo/letterhead.tex:49
msgid "Fax:"
msgstr ""

#: templates/demo/ap_transaction.html:49 templates/demo/ar_transaction.tex:81
#: templates/demo/bin_list.html:52 templates/demo/bin_list.html:63
#: templates/demo/invoice.html:57 templates/demo/invoice.html:70
#: templates/demo/letterhead.html:48 templates/demo/packing_list.html:53
#: templates/demo/pick_list.html:53 templates/demo/product_receipt.html:52
#: templates/demo/product_receipt.html:62
#: templates/demo/product_receipt.tex:72
#: templates/demo/product_receipt.tex:107
#: templates/demo/purchase_order.html:52 templates/demo/purchase_order.html:62
#: templates/demo/purchase_order.tex:72 templates/demo/purchase_order.tex:107
#: templates/demo/request_quotation.html:52
#: templates/demo/request_quotation.html:62
#: templates/demo/request_quotation.tex:72
#: templates/demo/request_quotation.tex:107 templates/demo/sales_order.html:53
#: templates/demo/sales_order.html:64 templates/demo/sales_order.tex:71
#: templates/demo/sales_order.tex:106 templates/demo/sales_quotation.html:49
#: templates/demo/sales_quotation.tex:77 templates/demo/work_order.html:52
#: templates/demo/work_order.html:63 templates/demo/work_order.tex:80
#: templates/demo/work_order.tex:115
msgid "Fax: [_1]"
msgstr ""

#: old/bin/aa.pl:84 old/bin/gl.pl:75 old/bin/io.pl:76
msgid "Feb"
msgstr ""

#: lib/LedgerSMB.pm:617 old/bin/aa.pl:70 old/bin/gl.pl:61 old/bin/io.pl:62
msgid "February"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:84
msgid "Fifteen"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:102
msgid "Fifty"
msgstr ""

#: UI/file/attachment_screen.html:36 UI/file/internal-file-list.html:59
msgid "File"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:1282
msgid "File Imported"
msgstr ""

#: lib/LedgerSMB/Report/File/Incoming.pm:50
#: lib/LedgerSMB/Report/File/Internal.pm:48
#: lib/LedgerSMB/Report/Listings/Templates.pm:53
msgid "File Name"
msgstr ""

#: UI/Contact/divs/files.html:11
msgid "File Type"
msgstr ""

#: lib/LedgerSMB/Scripts/file.pm:136 old/bin/aa.pl:1183 old/bin/aa.pl:1202
#: old/bin/ic.pl:854 old/bin/ic.pl:873 old/bin/ir.pl:972 old/bin/ir.pl:991
#: old/bin/is.pl:1080 old/bin/is.pl:1099 old/bin/oe.pl:882 old/bin/oe.pl:901
#: UI/Contact/divs/files.html:9 UI/lib/attachments.html:8
#: UI/lib/attachments.html:29
msgid "File name"
msgstr ""

#: old/bin/aa.pl:1184 old/bin/aa.pl:1203 old/bin/ic.pl:855 old/bin/ic.pl:874
#: old/bin/ir.pl:973 old/bin/ir.pl:992 old/bin/is.pl:1081 old/bin/is.pl:1100
#: old/bin/oe.pl:883 old/bin/oe.pl:902 UI/lib/attachments.html:9
#: UI/lib/attachments.html:30
msgid "File type"
msgstr ""

#: lib/LedgerSMB/Scripts/contact.pm:212 UI/Contact/divs/files.html:2
#: locale/menu.xml:730
msgid "Files"
msgstr ""

#: UI/Contact/divs/files.html:35
msgid "Files attached to Credit Account"
msgstr ""

#: UI/Contact/divs/files.html:17
msgid "Files attached to Entity"
msgstr ""

#: UI/payments/payments_detail.html:115
msgid "Filtering From"
msgstr ""

#: UI/payments/payments_filter.html:7 UI/payments/payments_filter.html:8
msgid "Filtering Payments"
msgstr ""

#: UI/payments/payments_filter.html:6 UI/payments/payments_filter.html:9
msgid "Filtering Receipts"
msgstr ""

#: t/data/04-complex_template.html:53
msgid "First"
msgstr ""

#: UI/Contact/divs/employee.html:47 UI/setup/new_user.html:111
msgid "First Name"
msgstr ""

#: templates/demo/balance_sheet.html:376
msgid "First column only"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:73
msgid "Five"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:108
msgid "Five Hundred"
msgstr ""

#: UI/accounts/edit.html:508
msgid "Fixed Asset"
msgstr ""

#: UI/accounts/edit.html:502 locale/menu.xml:612
msgid "Fixed Assets"
msgstr ""

#: old/bin/arap.pl:554
msgid "For"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:102
msgid "Foreign Exchange Gain"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:105
msgid "Foreign Exchange Loss"
msgstr ""

#: UI/Reports/filters/taxforms.html:22
msgid "Form"
msgstr ""

#: lib/LedgerSMB/Report/Taxform/List.pm:51
msgid "Form Name"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Templates.pm:57 UI/templates/widget.html:91
msgid "Format"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:101
msgid "Forty"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:72
msgid "Four"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:83
msgid "Fourteen"
msgstr ""

#: lib/LedgerSMB/Report/Timecards.pm:130
msgid "Fri"
msgstr ""

#: old/bin/ic.pl:1099 old/bin/oe.pl:2180 old/bin/pe.pl:738
#: UI/Contact/divs/credit.html:450 UI/Reports/co/filter_bm.html:20
#: UI/Reports/co/filter_cd.html:38
#: UI/Reports/filters/income_statement.html:125
#: UI/Reports/filters/income_statement.html:172
#: UI/Reports/filters/purchase_history.html:116
#: UI/Reports/filters/purchase_history.html:134
#: UI/Reports/filters/search_goods.html:222 UI/asset/begin_approval.html:22
#: UI/email.html:17 UI/lib/report_base.html:112 UI/lib/report_base.html:202
#: templates/demo/bin_list.html:40 templates/demo/bin_list.tex:50
msgid "From"
msgstr ""

#: lib/LedgerSMB/Report/Reconciliation/Summary.pm:184
msgid "From Amount"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Activity.pm:167
#: lib/LedgerSMB/Report/Invoices/Payments.pm:187
#: lib/LedgerSMB/Report/Reconciliation/Summary.pm:180
#: lib/LedgerSMB/Report/Taxform/Details.pm:116
#: lib/LedgerSMB/Report/Taxform/Summary.pm:125
#: lib/LedgerSMB/Report/Timecards.pm:147
#: lib/LedgerSMB/Report/Unapproved/Drafts.pm:148
#: UI/Reports/filters/unapproved.html:35
msgid "From Date"
msgstr ""

#: UI/Configuration/rate.html:80 UI/asset/import_asset.html:19
msgid "From File"
msgstr ""

#: old/bin/oe.pl:2193
msgid "From Warehouse"
msgstr ""

#: lib/LedgerSMB/Report/Trial_Balance.pm:207
msgid "From date"
msgstr ""

#: UI/Contact/divs/credit.html:508 UI/Reports/filters/balance_sheet.html:203
#: UI/Reports/filters/income_statement.html:225
#: UI/Reports/filters/trial_balance.html:9 UI/asset/begin_report.html:31
msgid "Full"
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:1289
msgid "Full Permissions"
msgstr ""

#: lib/LedgerSMB/Report/COA.pm:124 lib/LedgerSMB/Report/GL.pm:185
#: lib/LedgerSMB/Report/Listings/GIFI.pm:48
#: lib/LedgerSMB/Report/Listings/GIFI.pm:65
#: lib/LedgerSMB/Report/Trial_Balance.pm:158
#: UI/Reports/filters/balance_sheet.html:13
#: UI/Reports/filters/balance_sheet.html:30 UI/Reports/filters/gl.html:324
#: UI/Reports/filters/income_statement.html:49
#: UI/Reports/filters/income_statement.html:66 UI/accounts/edit.html:241
#: UI/lib/report_base.html:78 UI/setup/select_coa_details.html:45
#: locale/menu.xml:438
msgid "GIFI"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:580 lib/LedgerSMB/Upgrade_Tests.pm:642
#: lib/LedgerSMB/Upgrade_Tests.pm:662
msgid "GIFI accounts not in \"gifi\" table"
msgstr ""

#: old/bin/am.pl:323 UI/Reports/filters/unapproved.html:7
#: UI/setup/complete.html:86 UI/setup/confirm_operation.html:146
msgid "GL"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:110
msgid "GL Reference Number"
msgstr ""

#: UI/templates/widget.html:29
msgid "GL transaction"
msgstr ""

#: UI/accounts/edit.html:535
msgid "Gain"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:1024
msgid "Gain (Loss)"
msgstr ""

#: UI/asset/begin_approval.html:45
msgid "Gain Account"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:912
msgid "Gain/Loss"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:83
msgid "Gapless AR"
msgstr ""

#: locale/menu.xml:385
msgid "General Journal"
msgstr ""

#: lib/LedgerSMB/Report/GL.pm:223
msgid "General Ledger Report"
msgstr ""

#: UI/Reports/filters/gl.html:15
msgid "General Ledger Reports"
msgstr ""

#: lib/LedgerSMB/Scripts/order.pm:129 UI/Reports/aging_report.html:83
#: locale/menu.xml:319 locale/menu.xml:570
msgid "Generate"
msgstr ""

#: UI/Contact/divs/company.html:121 UI/Contact/divs/person.html:129
msgid "Generate Control Code"
msgstr ""

#: old/bin/oe.pl:2561
msgid "Generate Orders"
msgstr ""

#: old/bin/oe.pl:2466
msgid "Generate Purchase Orders"
msgstr ""

#: lib/LedgerSMB/Scripts/order.pm:66
msgid "Generate Purchase Orders from Sales Orders"
msgstr ""

#: old/bin/pe.pl:1038 old/bin/pe.pl:773 old/bin/pe.pl:948
msgid "Generate Sales Orders"
msgstr ""

#: lib/LedgerSMB/Scripts/order.pm:79
msgid "Generate Sales Orders from Purchase Orders"
msgstr ""

#: UI/Contact/divs/person.html:72
msgid "Given Name"
msgstr ""

#: locale/menu.xml:526
msgid "Goods"
msgstr ""

#: t/data/04-strings-for-translation.html:8 locale/menu.xml:445
msgid "Goods & Services"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Search.pm:341
msgid "Goods and Services"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/History.pm:180
msgid "Goods and Services History"
msgstr ""

#: UI/payments/payments_detail.html:452
msgid "Grand Total"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Partsgroups.pm:55 old/bin/ic.pl:1253
#: old/bin/ic.pl:396 old/bin/io.pl:278 old/bin/oe.pl:2103 old/bin/oe.pl:2176
#: old/bin/oe.pl:2211 old/bin/pe.pl:126
msgid "Group"
msgstr ""

#: old/bin/pe.pl:182
msgid "Group Translations"
msgstr ""

#: old/bin/pe.pl:78
msgid "Group deleted!"
msgstr ""

#: old/bin/pe.pl:60
msgid "Group missing!"
msgstr ""

#: old/bin/pe.pl:62
msgid "Group saved!"
msgstr ""

#: t/data/04-complex_template.html:25 locale/menu.xml:280
msgid "HR"
msgstr ""

#: UI/Reports/PNL.html:113 UI/Reports/balance_sheet.html:113
msgid "HTML"
msgstr ""

#: UI/accounts/edit.html:21 UI/accounts/edit.html:67 UI/accounts/edit.html:176
msgid "Heading"
msgstr ""

#: UI/accounts/edit.html:191
msgid ""
"Heading for negative assets reclassified as (positive) liabilities or the "
"other way around"
msgstr ""

#: UI/Contact/divs/credit.html:490 UI/Reports/filters/balance_sheet.html:185
#: UI/Reports/filters/income_statement.html:207
msgid "Hierarchy type"
msgstr ""

#: t/data/04-complex_template.html:35
msgid "History"
msgstr ""

#: sql/Pg-database.sql:243
msgid "Hot Lead"
msgstr ""

#: sql/Pg-database.sql:629
msgid "Hourly"
msgstr ""

#: templates/demo/timecard.html:68 templates/demo/timecard.tex:47
msgid "Hours"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:107
msgid "Hundred"
msgstr ""

#: lib/LedgerSMB/Report/Assets/Net_Book_Value.pm:70
#: lib/LedgerSMB/Report/Contact/Purchase.pm:59 lib/LedgerSMB/Report/GL.pm:96
#: lib/LedgerSMB/Report/Inventory/History.pm:116
#: lib/LedgerSMB/Report/Inventory/Search.pm:214
#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:209
#: lib/LedgerSMB/Report/Invoices/Transactions.pm:255
#: lib/LedgerSMB/Report/Listings/Asset_Class.pm:71
#: lib/LedgerSMB/Report/Listings/TemplateTrans.pm:88
#: lib/LedgerSMB/Report/Orders.pm:200 lib/LedgerSMB/Report/Timecards.pm:97
#: lib/LedgerSMB/Report/Unapproved/Batch_Detail.pm:104
#: lib/LedgerSMB/Report/Unapproved/Batch_Overview.pm:132
#: lib/LedgerSMB/Report/Unapproved/Drafts.pm:89
#: lib/LedgerSMB/Scripts/asset.pm:646
#: lib/LedgerSMB/Scripts/report_aging.pm:138 old/bin/am.pl:302
#: UI/Reports/filters/gl.html:220
#: UI/Reports/filters/invoice_outstanding.html:131
#: UI/Reports/filters/invoice_search.html:279
#: UI/Reports/filters/orders.html:134 UI/Reports/filters/search_goods.html:255
#: UI/business_units/list_classes.html:8 templates/demo/timecard.html:44
#: templates/demo/timecard.tex:39
msgid "ID"
msgstr ""

#: templates/demo/printPayment.html:80
msgid "Identification"
msgstr ""

#: UI/lib/report_base.html:86
msgid "Ignore Year-ends"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Search.pm:283 old/bin/ic.pl:474
#: UI/Reports/filters/search_goods.html:380
msgid "Image"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:142
msgid "Images in Templates"
msgstr ""

#: UI/setup/edit_user.html:64 locale/menu.xml:391 locale/menu.xml:525
#: locale/menu.xml:565 locale/menu.xml:636
msgid "Import"
msgstr ""

#: locale/menu.xml:140
msgid "Import AP Batch"
msgstr ""

#: locale/menu.xml:74
msgid "Import AR Batch"
msgstr ""

#: locale/menu.xml:33 locale/menu.xml:104 locale/menu.xml:396
msgid "Import Batch"
msgstr ""

#: locale/menu.xml:433
msgid "Import Chart"
msgstr ""

#: UI/asset/import_asset.html:4
msgid "Import assets"
msgstr ""

#: UI/Contact/divs/user.html:97 UI/setup/new_user.html:53
msgid "Import existing user"
msgstr ""

#: templates/demo/timecard.html:60 templates/demo/timecard.tex:45
msgid "In"
msgstr ""

#: lib/LedgerSMB/Report/Assets/Net_Book_Value.pm:83
msgid "In Svc"
msgstr ""

#: UI/setup/upgrade_info.html:34
msgid ""
"In addition to these new defaults LedgerSMB 1.3 adds stricter checks on data"
" validity in the database. Because of these stricter checks it's no longer "
"valid to leave companies without a country or customers without accounts "
"receivable reference. The defaults you choose below will be used to add "
"values where these are currently missing but required."
msgstr ""

#: old/bin/printer.pl:125
msgid "In-line"
msgstr ""

#: old/bin/arap.pl:376
msgid "Include Payment"
msgstr ""

#: UI/Reports/co/filter_bm.html:67 UI/Reports/co/filter_cd.html:53
#: UI/Reports/filters/aging.html:70 UI/Reports/filters/gl.html:211
#: UI/Reports/filters/invoice_outstanding.html:97
#: UI/Reports/filters/invoice_search.html:265
#: UI/Reports/filters/orders.html:94
#: UI/Reports/filters/purchase_history.html:156
#: UI/Reports/filters/search_goods.html:249
#: UI/Reports/filters/taxforms.html:48
msgid "Include in Report"
msgstr ""

#: UI/accounts/edit.html:328
msgid "Include in drop-down menus"
msgstr ""

#: UI/Reports/filters/inventory_adj.html:23
msgid "Includes Part"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Search_Adj.pm:87
msgid "Including partnumber"
msgstr ""

#: lib/LedgerSMB/Report/Balance_Sheet.pm:208 lib/LedgerSMB/Report/COA.pm:170
#: lib/LedgerSMB/Report/PNL.pm:203 lib/LedgerSMB/Scripts/configuration.pm:96
#: old/bin/ic.pl:497 old/bin/ic.pl:630 UI/Reports/filters/gl.html:196
#: UI/accounts/edit.html:164 UI/accounts/edit.html:337
#: UI/accounts/edit.html:439 UI/accounts/edit.html:478
msgid "Income"
msgstr ""

#: lib/LedgerSMB/Report/Payroll/Income_Types.pm:38 UI/payroll/income.html:29
msgid "Income Class"
msgstr ""

#: lib/LedgerSMB/Report/PNL/Income_Statement.pm:47
#: UI/Reports/filters/income_statement.html:15 templates/demo/PNL.tex:44
#: locale/menu.xml:417
msgid "Income Statement"
msgstr ""

#: UI/payroll/income.html:3
msgid "Income Type"
msgstr ""

#: lib/LedgerSMB/Report/Payroll/Income_Types.pm:53
msgid "Income Types"
msgstr ""

#: UI/templates/widget.html:39
msgid "Income statement"
msgstr ""

#: lib/LedgerSMB/Report/File/Incoming.pm:72
msgid "Incoming Files"
msgstr ""

#: old/lib/LedgerSMB/User.pm:155
msgid "Incorrect Password"
msgstr ""

#: old/bin/ic.pl:1260
msgid "Individual Items"
msgstr ""

#: templates/demo/invoice.tex:53
msgid ""
"Interest on overdue amounts will accrue at the rate of 12% per annum "
"starting from [_1] until paid in full. Items returned are subject to a 10% "
"restocking charge."
msgstr ""

#: lib/LedgerSMB.pm:536
msgid "Internal Database Error"
msgstr ""

#: lib/LedgerSMB/Report/File/Internal.pm:70
msgid "Internal Files"
msgstr ""

#: old/bin/aa.pl:955 old/bin/ir.pl:729 old/bin/ir.pl:737 old/bin/is.pl:843
#: old/bin/is.pl:851 old/bin/oe.pl:794
msgid "Internal Notes"
msgstr ""

#: lib/LedgerSMB/Report/PNL/Income_Statement.pm:75
msgid "Invalid Reporting Basis"
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:481
msgid "Invalid backup request"
msgstr ""

#: lib/LedgerSMB.pm:539
msgid "Invalid date/time entered"
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:1193 lib/LedgerSMB/Scripts/setup.pm:1200
msgid "Invalid request"
msgstr ""

#: lib/LedgerSMB/Scripts/recon.pm:353
msgid "Invalid statement balance.  Hint: Try entering a number"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:93 old/bin/ic.pl:493
#: UI/accounts/edit.html:217 locale/menu.xml:262 locale/menu.xml:541
msgid "Inventory"
msgstr ""

#: locale/menu.xml:499
msgid "Inventory & COGS"
msgstr ""

#: UI/Reports/filters/inventory_activity.html:5 locale/menu.xml:505
msgid "Inventory Activity"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Activity.pm:181
msgid "Inventory Activity Report"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Adj_Details.pm:72
msgid "Inventory Adjustment Details"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Search_Adj.pm:73
msgid "Inventory Adjustments"
msgstr ""

#: old/bin/ic.pl:1788
msgid ""
"Inventory quantity must be zero before you can set this assembly obsolete!"
msgstr ""

#: old/bin/ic.pl:1787
msgid "Inventory quantity must be zero before you can set this part obsolete!"
msgstr ""

#: old/bin/oe.pl:2048
msgid "Inventory saved!"
msgstr ""

#: old/bin/oe.pl:2318
msgid "Inventory transferred!"
msgstr ""

#: lib/LedgerSMB/Report/Aging.pm:119
#: lib/LedgerSMB/Report/Inventory/Search.pm:303
#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:176
#: lib/LedgerSMB/Report/Invoices/Transactions.pm:268
#: lib/LedgerSMB/Scripts/payment.pm:941 old/bin/am.pl:996 old/bin/io.pl:1087
#: old/bin/is.pl:1406 old/bin/is.pl:271 old/bin/printer.pl:48
#: UI/templates/widget.html:51 templates/demo/invoice.html:20
#: templates/demo/invoice.html:36 templates/demo/invoice.tex:142
#: templates/demo/printPayment.html:44 sql/Pg-database.sql:729
msgid "Invoice"
msgstr ""

#: templates/demo/ap_transaction.html:56 templates/demo/ap_transaction.tex:75
#: templates/demo/ar_transaction.html:54 templates/demo/ar_transaction.tex:92
#: templates/demo/check_base.tex:66 templates/demo/invoice.html:83
#: templates/demo/invoice.tex:149 templates/demo/packing_list.html:65
#: templates/demo/packing_list.tex:102 templates/demo/pick_list.html:65
#: templates/demo/pick_list.tex:95 templates/demo/product_receipt.html:74
#: templates/demo/product_receipt.tex:121 templates/demo/statement.html:63
#: templates/demo/statement.tex:55
msgid "Invoice #"
msgstr ""

#: old/bin/aa.pl:779 old/bin/ir.pl:459 old/bin/is.pl:551
msgid "Invoice Created"
msgstr ""

#: old/bin/aa.pl:1487
msgid "Invoice Created Date missing!"
msgstr ""

#: lib/LedgerSMB/Report/Contact/History.pm:125
#: lib/LedgerSMB/Report/Invoices/COGS.pm:96 old/bin/aa.pl:783
#: old/bin/ir.pl:463 old/bin/is.pl:555
#: UI/Reports/filters/invoice_outstanding.html:158
#: UI/Reports/filters/invoice_search.html:307 templates/demo/check_base.tex:66
#: templates/demo/receipt.tex:79
msgid "Invoice Date"
msgstr ""

#: old/bin/aa.pl:1485 old/bin/io.pl:1200 old/bin/ir.pl:1252 old/bin/is.pl:1367
msgid "Invoice Date missing!"
msgstr ""

#: UI/setup/complete.html:88
msgid "Invoice Lines"
msgstr ""

#: templates/demo/receipt.tex:79
msgid "Invoice No."
msgstr ""

#: lib/LedgerSMB/Report/Contact/History.pm:69
#: lib/LedgerSMB/Report/Contact/Purchase.pm:67
#: lib/LedgerSMB/Report/Invoices/COGS.pm:90
#: lib/LedgerSMB/Report/PNL/Invoice.pm:81
#: lib/LedgerSMB/Report/Taxform/Details.pm:93 old/bin/aa.pl:770
#: old/bin/ir.pl:449 old/bin/is.pl:542
#: UI/Reports/filters/invoice_outstanding.html:137
#: UI/Reports/filters/invoice_search.html:89
#: UI/Reports/filters/invoice_search.html:286 UI/asset/edit_asset.html:203
#: UI/asset/search_asset.html:175 UI/payments/payments_detail.html:330
#: UI/payments/use_overpayment2.html:148
msgid "Invoice Number"
msgstr ""

#: old/bin/io.pl:1210
msgid "Invoice Number missing!"
msgstr ""

#: lib/LedgerSMB/Report/PNL/Invoice.pm:72
msgid "Invoice Profit/Loss"
msgstr ""

#: UI/Reports/filters/invoice_outstanding.html:71
#: UI/Reports/filters/invoice_search.html:171
msgid "Invoice Status"
msgstr ""

#: UI/payments/payments_detail.html:223
msgid "Invoice Total"
msgstr ""

#: locale/menu.xml:69 locale/menu.xml:135
msgid "Invoice Vouchers"
msgstr ""

#: UI/payments/use_overpayment2.html:150
msgid "Invoice date"
msgstr ""

#: UI/payments/use_overpayment2.html:151
msgid "Invoice due"
msgstr ""

#: lib/LedgerSMB/Report/Taxform/Details.pm:101
msgid "Invoice sum"
msgstr ""

#: lib/LedgerSMB/Report/Taxform/Summary.pm:109
#: UI/Reports/filters/purchase_history.html:167
msgid "Invoices"
msgstr ""

#: old/bin/ic.pl:1234 old/bin/io.pl:237 UI/payments/payment2.html:342
#: templates/demo/bin_list.html:98 templates/demo/bin_list.tex:120
#: templates/demo/invoice.html:107 templates/demo/invoice.tex:164
#: templates/demo/packing_list.html:90 templates/demo/packing_list.tex:128
#: templates/demo/pick_list.html:90 templates/demo/pick_list.tex:114
#: templates/demo/product_receipt.html:94
#: templates/demo/purchase_order.html:96
#: templates/demo/request_quotation.html:105
#: templates/demo/sales_order.html:99 templates/demo/sales_order.tex:134
#: templates/demo/sales_quotation.html:81 templates/demo/work_order.html:98
#: templates/demo/work_order.tex:139
msgid "Item"
msgstr ""

#: old/bin/ic.pl:1967
msgid "Item deleted!"
msgstr ""

#: UI/Reports/filters/search_goods.html:128
msgid "Items Found In"
msgstr ""

#: templates/demo/packing_list.tex:36
msgid "Items returned are subject to a 10% restocking charge."
msgstr ""

#: templates/demo/invoice.html:217 templates/demo/packing_list.html:135
msgid ""
"Items returned are subject to a 10% restocking charge. A return "
"authorization must be obtained from [_1] before goods are returned. Returns "
"must be shipped prepaid and properly insured. [_1] will not be responsible "
"for damages during transit."
msgstr ""

#: old/bin/aa.pl:83 old/bin/gl.pl:74 old/bin/io.pl:75
msgid "Jan"
msgstr ""

#: lib/LedgerSMB.pm:616 old/bin/aa.pl:69 old/bin/gl.pl:60 old/bin/io.pl:61
msgid "January"
msgstr ""

#: lib/LedgerSMB/Report/Contact/EmployeeSearch.pm:66
#: UI/Contact/divs/employee.html:70
msgid "Job Title"
msgstr ""

#: t/data/04-complex_template.html:78
msgid "Job Title:"
msgstr ""

#: templates/demo/timecard.html:87 templates/demo/timecard.tex:53
msgid "Job/Project #"
msgstr ""

#: sql/Pg-database.sql:731 locale/menu.xml:386
msgid "Journal Entry"
msgstr ""

#: UI/setup/complete.html:90 UI/setup/confirm_operation.html:147
msgid "Journal Lines"
msgstr ""

#: old/bin/aa.pl:89 old/bin/gl.pl:80 old/bin/io.pl:81
msgid "Jul"
msgstr ""

#: lib/LedgerSMB.pm:622 old/bin/aa.pl:75 old/bin/gl.pl:66 old/bin/io.pl:67
msgid "July"
msgstr ""

#: old/bin/aa.pl:88 old/bin/gl.pl:79 old/bin/io.pl:80
msgid "Jun"
msgstr ""

#: lib/LedgerSMB.pm:621 old/bin/aa.pl:74 old/bin/gl.pl:65 old/bin/io.pl:66
#: t/data/04-strings-for-translation.html:6
msgid "June"
msgstr ""

#: templates/demo/balance_sheet.html:375
msgid "Key Ratios"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Asset_Class.pm:74
#: lib/LedgerSMB/Report/Listings/Asset_Class.pm:99
#: lib/LedgerSMB/Report/Payroll/Deduction_Types.pm:41
#: lib/LedgerSMB/Report/Payroll/Income_Types.pm:41
#: UI/business_units/list_classes.html:9
msgid "Label"
msgstr ""

#: UI/asset/edit_class.html:18 UI/asset/search_class.html:13
msgid "Label:"
msgstr ""

#: old/bin/ic.pl:665
msgid "Labor/Overhead"
msgstr ""

#: templates/demo/timecard.html:95 templates/demo/timecard.tex:55
msgid "Labor/Service Code"
msgstr ""

#: lib/LedgerSMB/Report/Aging.pm:105
#: lib/LedgerSMB/Report/Listings/Language.pm:65
#: lib/LedgerSMB/Report/Listings/Templates.pm:72 old/bin/pe.pl:297
#: old/bin/pe.pl:470 UI/Contact/divs/credit.html:293
#: UI/Contact/divs/credit.html:294 UI/Reports/filters/balance_sheet.html:239
#: UI/Reports/filters/income_statement.html:261 UI/users/preferences.html:97
#: locale/menu.xml:719
msgid "Language"
msgstr ""

#: old/bin/pe.pl:393
msgid "Languages not defined!"
msgstr ""

#: t/data/04-complex_template.html:67
msgid "Last"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Search.pm:258 old/bin/ic.pl:430
#: old/bin/ic.pl:609 UI/Contact/pricelist.csv:17 UI/Contact/pricelist.html:31
#: UI/Contact/pricelist.tex:25 UI/Reports/filters/search_goods.html:324
msgid "Last Cost"
msgstr ""

#: UI/Contact/divs/employee.html:62
msgid "Last Name"
msgstr ""

#: lib/LedgerSMB/Report/Reconciliation/Summary.pm:152
msgid "Last Updated"
msgstr ""

#: UI/setup/new_user.html:120
msgid "Last name"
msgstr ""

#: old/bin/oe.pl:2462 sql/Pg-database.sql:241
msgid "Lead"
msgstr ""

#: UI/Contact/pricelist.csv:23 UI/Contact/pricelist.html:37
#: UI/Contact/pricelist.tex:33
msgid "Lead Time"
msgstr ""

#: old/bin/ic.pl:1001
msgid "Leadtime"
msgstr ""

#: lib/LedgerSMB/Report/Taxform/Details.pm:97
msgid "Ledger sum"
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:234
msgid "LedgerSMB 1.10 db found."
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:239
msgid "LedgerSMB 1.11 db found."
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:244
msgid "LedgerSMB 1.12 db found."
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:249
msgid "LedgerSMB 1.13 db found."
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:193
msgid "LedgerSMB 1.2 db found."
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:198
msgid "LedgerSMB 1.3 db found."
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:203
msgid "LedgerSMB 1.4 db found."
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:209
msgid "LedgerSMB 1.5 db found."
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:214
msgid "LedgerSMB 1.6 db found."
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:219
msgid "LedgerSMB 1.7 db found."
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:224
msgid "LedgerSMB 1.8 db found."
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:229
msgid "LedgerSMB 1.9 db found."
msgstr ""

#: UI/setup/complete.html:76 UI/setup/confirm_operation.html:140
msgid "LedgerSMB Database Statistics"
msgstr ""

#: UI/setup/consistency_results.html:13
msgid "LedgerSMB consistency check results"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:895
msgid ""
"LedgerSMB customers must be assigned to a valid business.<br>\n"
"Please review the selection or select the proper business from the list"
msgstr ""

#: UI/setup/system_info.html:10
msgid "LedgerSMB has detected the following system components"
msgstr ""

#: UI/setup/upgrade_info.html:30
msgid ""
"LedgerSMB has introduced three new system wide default values which you will"
" need to set as part of the upgrade process."
msgstr ""

#: UI/setup/upgrade_info.html:73
msgid ""
"LedgerSMB supports multiple <em>Accounts payable (AP)</em> accounts per "
"company. One of those must be the system default. Please select your default"
" below in case of multiple. If the list below is empty, your database is in "
"an inconsistent state and needs to be fixed first."
msgstr ""

#: UI/setup/upgrade_info.html:51
msgid ""
"LedgerSMB supports multiple <em>Accounts receivable (AR)</em> accounts per "
"company. One of those must be the system default. Please select your default"
" below in case of multiple. If the list below is empty, your database is in "
"an inconsistent state and needs to be fixed first."
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:872
msgid ""
"LedgerSMB vendors must be assigned to a valid business.<br>\n"
"Please review the selection or select the proper business from the list"
msgstr ""

#: UI/reconciliation/report.html:67
msgid "Less Outstanding Checks:"
msgstr ""

#: UI/templates/widget.html:53
msgid "Letterhead"
msgstr ""

#: lib/LedgerSMB/Report/Balance_Sheet.pm:227 lib/LedgerSMB/Report/PNL.pm:213
msgid "Liabilities"
msgstr ""

#: lib/LedgerSMB/Report/COA.pm:168 UI/Reports/filters/gl.html:180
#: UI/accounts/edit.html:161
msgid "Liability"
msgstr ""

#: UI/Contact/divs/company.html:110
msgid "License Number"
msgstr ""

#: old/bin/io.pl:1593
msgid "Line 1"
msgstr ""

#: old/bin/io.pl:1596
msgid "Line 2"
msgstr ""

#: old/bin/io.pl:1600
msgid "Line 3"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/COGS.pm:176
msgid "Line Item COGS Report"
msgstr ""

#: UI/Reports/filters/search_goods.html:338
msgid "Line Total"
msgstr ""

#: old/bin/ic.pl:744
msgid "Link Accounts"
msgstr ""

#: old/bin/ic.pl:1248
msgid "List"
msgstr ""

#: locale/menu.xml:619
msgid "List Classes"
msgstr ""

#: UI/Reports/filters/overpayments.html:4
msgid "List Overpayments"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Search.pm:248 old/bin/ic.pl:413
#: UI/Reports/filters/search_goods.html:308
msgid "List Price"
msgstr ""

#: locale/menu.xml:601
msgid "List Tax Forms"
msgstr ""

#: UI/business_units/list_classes.html:113
msgid "List Units"
msgstr ""

#: UI/setup/confirm_operation.html:64
msgid "List Users"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Business_Type.pm:67
msgid "List of Business Types"
msgstr ""

#: UI/setup/confirm_operation.html:84 UI/setup/template_info.html:30
msgid "Load Templates"
msgstr ""

#: UI/asset/edit_asset.html:135 UI/asset/search_asset.html:122
#: UI/payments/payment2.html:34 UI/payments/use_overpayment2.html:32
msgid "Location"
msgstr ""

#: UI/Contact/divs/address.html:6 t/data/04-complex_template.html:368
msgid "Locations"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:80
msgid "Lock Item Description"
msgstr ""

#: UI/payments/payments_detail.html:414
msgid "Locked by [_1]"
msgstr ""

#: UI/logout.html:6
msgid "Logged out due to inactivity"
msgstr ""

#: UI/setup/credentials.html:78
msgid "Login"
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:1150
msgid "Login?"
msgstr ""

#: locale/menu.xml:747
msgid "Logout"
msgstr ""

#: UI/logout.html:8
msgid "Logout Successful"
msgstr ""

#: UI/accounts/edit.html:544
msgid "Loss"
msgstr ""

#: UI/asset/begin_approval.html:55
msgid "Loss Account"
msgstr ""

#: sql/Pg-database.sql:370
msgid "Mailing"
msgstr ""

#: UI/setup/confirm_operation.html:68
msgid "Maintenance"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Search.pm:275 old/bin/ic.pl:956
#: UI/Reports/filters/search_goods.html:86
#: UI/Reports/filters/search_goods.html:401
msgid "Make"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:487
msgid "Make employee numbers unique"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:523
msgid "Make invoice numbers unique"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1052 lib/LedgerSMB/Upgrade_Tests.pm:507
msgid "Make non-obsolete partnumbers unique"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:567
msgid "Make sure all meta numbers are unique."
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:469
msgid ""
"Make sure every name consists of alphanumeric characters (and underscores) "
"only and is at least one character long"
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:1288
msgid "Manage Users"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:280
#: lib/LedgerSMB/Report/Invoices/Transactions.pm:312
#: lib/LedgerSMB/Report/Orders.pm:258 UI/Contact/divs/employee.html:88
#: UI/Contact/divs/employee.html:112
#: UI/Reports/filters/invoice_outstanding.html:184
#: UI/Reports/filters/invoice_search.html:333
msgid "Manager"
msgstr ""

#: t/data/04-complex_template.html:98
msgid "Manager:"
msgstr ""

#: old/bin/ir.pl:564 old/bin/is.pl:416 old/bin/is.pl:670
msgid "Manual"
msgstr ""

#: old/bin/aa.pl:85 old/bin/gl.pl:76 old/bin/io.pl:77
msgid "Mar"
msgstr ""

#: lib/LedgerSMB.pm:618 old/bin/aa.pl:71 old/bin/gl.pl:62 old/bin/io.pl:63
msgid "March"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Search.pm:267 old/bin/ic.pl:436
#: old/bin/ic.pl:615 UI/Reports/filters/search_goods.html:345
msgid "Markup"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:138
msgid "Max Invoices per Check Stub"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:136
msgid "Max per dropdown"
msgstr ""

#: lib/LedgerSMB.pm:620 old/bin/aa.pl:73 old/bin/aa.pl:87 old/bin/gl.pl:64
#: old/bin/gl.pl:78 old/bin/io.pl:65 old/bin/io.pl:79
msgid "May"
msgstr ""

#: lib/LedgerSMB/Report/GL.pm:160 lib/LedgerSMB/Scripts/payment.pm:947
#: old/bin/aa.pl:990 old/bin/ir.pl:603 old/bin/ir.pl:818 old/bin/is.pl:715
#: old/bin/is.pl:933 UI/Reports/filters/gl.html:73
#: UI/Reports/filters/gl.html:297 UI/journal/journal_entry.html:159
#: UI/payments/payment2.html:346 templates/demo/ap_transaction.html:154
#: templates/demo/ap_transaction.tex:127
msgid "Memo"
msgstr ""

#: UI/payments/payment2.html:228
msgid "Memo to help retrieval"
msgstr ""

#: lib/LedgerSMB/Report/Assets/Net_Book_Value.pm:87
msgid "Method"
msgstr ""

#: UI/asset/edit_asset.html:100
msgid "Method Default"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Search.pm:291 old/bin/ic.pl:476
#: UI/Reports/filters/search_goods.html:116
#: UI/Reports/filters/search_goods.html:394
msgid "Microfiche"
msgstr ""

#: t/data/04-complex_template.html:60
msgid "Middle"
msgstr ""

#: UI/Contact/divs/employee.html:55 UI/Contact/divs/person.html:80
msgid "Middle Name"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:112
msgid "Million"
msgstr ""

#: lib/LedgerSMB/Report/File/Incoming.pm:56
#: lib/LedgerSMB/Report/File/Internal.pm:54
msgid "Mime Type"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:145
msgid "Min Empty Lines"
msgstr ""

#: old/bin/ic.pl:1101 UI/Contact/pricelist.csv:40 UI/Contact/pricelist.html:52
#: UI/Contact/pricelist.tex:54
msgid "Min Qty"
msgstr ""

#: UI/am-taxes.html:15
msgid "Min Taxable"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:126
msgid "Misc Settings"
msgstr ""

#: UI/reconciliation/report.html:252
msgid "Mismatched Transactions (from upload)"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Search.pm:279 old/bin/ic.pl:957
#: UI/Reports/filters/search_goods.html:96
#: UI/Reports/filters/search_goods.html:408
msgid "Model"
msgstr ""

#: lib/LedgerSMB/Report/Timecards.pm:114
msgid "Mon"
msgstr ""

#: old/bin/am.pl:270 old/bin/pe.pl:724 UI/Reports/co/filter_bm.html:57
#: UI/lib/report_base.html:167 UI/lib/report_base.html:254
msgid "Month"
msgstr ""

#: old/bin/arap.pl:528
msgid "Month(s)"
msgstr ""

#: old/bin/am.pl:271
msgid "Months"
msgstr ""

#: lib/LedgerSMB.pm:552
msgid "More information has been reported in the error logs"
msgstr ""

#: UI/templates/widget.html:47
msgid "Multiple checks"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1197
msgid ""
"Multiple tax rates with the same end date have been detected for a tax "
"account;"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/Payments.pm:218
msgid "Must have cash account in batch"
msgstr ""

#: UI/accounts/edit.html:11
msgid "NONE"
msgstr ""

#: lib/LedgerSMB/Report/Contact/EmployeeSearch.pm:57
#: lib/LedgerSMB/Report/Contact/EmployeeSearch.pm:99
#: lib/LedgerSMB/Report/Contact/History.pm:147
#: lib/LedgerSMB/Report/Contact/History.pm:57
#: lib/LedgerSMB/Report/Contact/Purchase.pm:144
#: lib/LedgerSMB/Report/Contact/Purchase.pm:62
#: lib/LedgerSMB/Report/Contact/Search.pm:104
#: lib/LedgerSMB/Report/Contact/Search.pm:61
#: lib/LedgerSMB/Report/Listings/Country.pm:50
#: lib/LedgerSMB/Report/Listings/User.pm:62 lib/LedgerSMB/Report/Orders.pm:221
#: lib/LedgerSMB/Report/PNL/ECA.pm:86 old/bin/arap.pl:163
#: UI/Contact/divs/company.html:61 UI/Reports/filters/contact_search.html:30
#: UI/Reports/filters/overpayments.html:31
#: UI/payments/payments_detail.html:222
msgid "Name"
msgstr ""

#: t/data/04-complex_template.html:50 t/data/04-complex_template.html:160
msgid "Name:"
msgstr ""

#: UI/accounts/edit.html:191
msgid "Negative balance heading"
msgstr ""

#: lib/LedgerSMB/Report/Assets/Net_Book_Value.pm:127 locale/menu.xml:652
msgid "Net Book Value"
msgstr ""

#: UI/payments/payments_detail.html:335
msgid "Net Due"
msgstr ""

#: old/bin/aa.pl:600 old/bin/ir.pl:320 old/bin/is.pl:339 old/bin/oe.pl:552
msgid "New"
msgstr ""

#: UI/asset/begin_report.html:5
msgid "New Asset Report"
msgstr ""

#: UI/asset/begin_approval.html:5
msgid "New Asset Report Search"
msgstr ""

#: UI/users/preferences.html:15
msgid "New Password"
msgstr ""

#: UI/reconciliation/new_report.html:8
msgid "New Reconciliation Report"
msgstr ""

#: UI/Contact/divs/user.html:20 UI/setup/edit_user.html:17
msgid "New User"
msgstr ""

#: locale/menu.xml:744
msgid "New Window"
msgstr ""

#: old/bin/am.pl:305 UI/inventory/adjustment_entry.html:82
#: UI/setup/select_coa_country.html:45 UI/setup/select_coa_details.html:76
msgid "Next"
msgstr ""

#: old/bin/arap.pl:385
msgid "Next Date"
msgstr ""

#: old/bin/am.pl:377
msgid "Next Number"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:107
#: lib/LedgerSMB/Scripts/configuration.pm:339
msgid "Next in Sequence"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:77
msgid "Nine"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:110
msgid "Nine Hundred"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:88
msgid "Nineteen"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:106
msgid "Ninety"
msgstr ""

#: lib/LedgerSMB/Report/Taxform/List.pm:101 UI/Configuration/settings.html:51
msgid "No"
msgstr ""

#: lib/LedgerSMB/Scripts/contact.pm:657
msgid "No AR or AP Account Selected"
msgstr ""

#: lib/LedgerSMB/Report/File.pm:51
msgid "No File Class Specified"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:538
msgid "No NULL Amounts"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:451
msgid "No Null employeenumber"
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:1290
msgid "No changes"
msgstr ""

#: old/bin/aa.pl:311 old/bin/ir.pl:162 old/bin/is.pl:172
msgid "No currencies defined.  Please set these up under System/Defaults."
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:562
msgid "No duplicate meta_numbers"
msgstr ""

#: lib/LedgerSMB/Scripts/file.pm:190
msgid "No file uploaded"
msgstr ""

#: old/bin/pe.pl:755
msgid "No open Projects!"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:1420
msgid "No overpayment account selected.  Was one set up?"
msgstr ""

#: lib/LedgerSMB/Report/File.pm:38
msgid "No ref key set and no override provided"
msgstr ""

#: lib/LedgerSMB/Scripts/reports.pm:97
msgid "No report specified"
msgstr ""

#: UI/setup/complete.html:50 UI/setup/db-patches-log.html:28
msgid "No schema upgrade scripts run"
msgstr ""

#: lib/LedgerSMB/Scripts/taxform.pm:124
msgid "No tax form selected"
msgstr ""

#: UI/Reports/filters/invoice_outstanding.html:125
#: UI/Reports/filters/invoice_search.html:273
#: UI/Reports/filters/orders.html:128
msgid "No."
msgstr ""

#: UI/timecards/timecard.html:112
msgid "Non-Chargeable"
msgstr ""

#: sql/Pg-database.sql:631
msgid "Non-cash"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1103
msgid "Non-existing customer pricegroups in partscustomer"
msgstr ""

#: UI/Reports/filters/gl.html:153
msgid "Non-reversed"
msgstr ""

#: UI/accounts/edit.html:472
msgid "Non-tracking Items"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1005
msgid "Non-unique invoice numbers"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1028
msgid "Non-unique invoice numbers detected"
msgstr ""

#: UI/templates/widget.html:109
msgid "None/Generic"
msgstr ""

#: UI/Reports/filters/invoice_search.html:67
msgid "Nontaxable"
msgstr ""

#: UI/Reports/filters/trial_balance.html:6
msgid "Normal"
msgstr ""

#: UI/Reports/filters/reconciliation_search.html:50
msgid "Not Approved"
msgstr ""

#: UI/Reports/filters/reconciliation_search.html:63
msgid "Not Submitted"
msgstr ""

#: UI/Contact/divs/credit.html:522 UI/Reports/filters/balance_sheet.html:217
#: UI/Reports/filters/income_statement.html:239
msgid "Not set up for hierarchy reporting, please see linked instructions"
msgstr ""

#: UI/Contact/divs/notes.html:44
msgid "Note Class"
msgstr ""

#: UI/setup/upgrade_info.html:94
msgid ""
"Note that the process invoked by hitting the button below might take long to"
" complete as it will run the upgrade process and will copy all data from the"
" 1.2 tables into the 1.3 tables."
msgstr ""

#: UI/setup/consistency_results.html:10
msgid "Note: "
msgstr ""

#: lib/LedgerSMB/Report/Contact/Purchase.pm:111
#: lib/LedgerSMB/Report/Inventory/Search.pm:295
#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:272
#: lib/LedgerSMB/Report/Invoices/Transactions.pm:306
#: lib/LedgerSMB/Scripts/contact.pm:211 old/bin/aa.pl:954 old/bin/ic.pl:749
#: old/bin/ir.pl:726 old/bin/is.pl:838 old/bin/oe.pl:793
#: UI/Contact/divs/notes.html:2 UI/Contact/divs/notes.html:6
#: UI/Contact/divs/notes.html:51 UI/Contact/divs/notes.html:52
#: UI/Reports/filters/contact_search.html:85 UI/Reports/filters/gl.html:90
#: UI/Reports/filters/invoice_outstanding.html:247
#: UI/Reports/filters/invoice_search.html:150
#: UI/Reports/filters/invoice_search.html:394
#: UI/Reports/filters/search_goods.html:373 UI/journal/journal_entry.html:83
#: UI/payments/payment2.html:48 UI/payments/use_overpayment2.html:70
#: UI/timecards/timecard.html:134 t/data/04-complex_template.html:33
#: t/data/04-complex_template.html:626 templates/demo/printPayment.html:99
#: templates/demo/product_receipt.html:162
#: templates/demo/purchase_order.html:164 templates/demo/sales_order.html:159
#: templates/demo/sales_quotation.html:141
msgid "Notes"
msgstr ""

#: t/data/04-complex_template.html:639
msgid "Notes:<br />"
msgstr ""

#: old/bin/oe.pl:2045
msgid "Nothing entered!"
msgstr ""

#: old/bin/oe.pl:2336 old/bin/oe.pl:2595 old/bin/pe.pl:1063
msgid "Nothing selected!"
msgstr ""

#: old/bin/oe.pl:2062
msgid "Nothing to transfer!"
msgstr ""

#: old/bin/aa.pl:93 old/bin/gl.pl:84 old/bin/io.pl:85
msgid "Nov"
msgstr ""

#: lib/LedgerSMB.pm:626 old/bin/aa.pl:79 old/bin/gl.pl:70 old/bin/io.pl:71
msgid "November"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:965 lib/LedgerSMB/Upgrade_Tests.pm:984
msgid "Null employee numbers"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1083
msgid "Null make numbers"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1066
msgid "Null model numbers"
msgstr ""

#: old/bin/ic.pl:1241 old/bin/ic.pl:1995 old/bin/ic.pl:2064 old/bin/ic.pl:723
#: old/bin/io.pl:239 old/bin/oe.pl:1895 old/bin/pe.pl:175 old/bin/pe.pl:257
#: old/bin/pe.pl:289 UI/Contact/divs/credit.html:30
#: UI/Contact/divs/credit.html:97 UI/Contact/divs/credit.html:98
#: UI/am-taxes.html:16 templates/demo/bin_list.html:99
#: templates/demo/bin_list.tex:120 templates/demo/invoice.html:108
#: templates/demo/invoice.tex:165 templates/demo/packing_list.html:91
#: templates/demo/packing_list.tex:128 templates/demo/pick_list.html:91
#: templates/demo/pick_list.tex:114 templates/demo/printPayment.html:44
#: templates/demo/product_receipt.html:95
#: templates/demo/product_receipt.tex:133
#: templates/demo/purchase_order.html:97 templates/demo/purchase_order.tex:133
#: templates/demo/request_quotation.html:106
#: templates/demo/request_quotation.tex:138
#: templates/demo/sales_order.html:100 templates/demo/sales_order.tex:134
#: templates/demo/sales_quotation.html:59
#: templates/demo/sales_quotation.html:82
#: templates/demo/sales_quotation.tex:103 templates/demo/work_order.html:99
#: templates/demo/work_order.tex:139
msgid "Number"
msgstr ""

#: UI/users/preferences.html:87
msgid "Number Format"
msgstr ""

#: old/bin/io.pl:890
msgid "Number missing in Row [_1]"
msgstr ""

#: t/data/04-complex_template.html:167
msgid "Number:"
msgstr ""

#: UI/Contact/pricelist.html:123 UI/Reports/aging_report.html:113
#: UI/Reports/display_report.html:64
msgid "ODS"
msgstr ""

#: old/bin/io.pl:256
msgid "OH"
msgstr ""

#: UI/payments/payment2.html:339
msgid "OVERPAYMENT / ADVANCE PAYMENT / PREPAYMENT"
msgstr ""

#: lib/LedgerSMB/Report/COA.pm:119 lib/LedgerSMB/Scripts/budgets.pm:127
#: old/bin/ic.pl:681 UI/Reports/filters/search_goods.html:32
#: UI/accounts/edit.html:302
msgid "Obsolete"
msgstr ""

#: lib/LedgerSMB/Report/Budget/Search.pm:99
msgid "Obsolete By"
msgstr ""

#: old/bin/aa.pl:92 old/bin/gl.pl:83 old/bin/io.pl:84
msgid "Oct"
msgstr ""

#: lib/LedgerSMB.pm:625 old/bin/aa.pl:78 old/bin/gl.pl:69 old/bin/io.pl:70
msgid "October"
msgstr ""

#: old/bin/is.pl:811
msgid "Off Hold"
msgstr ""

#: UI/users/preferences.html:15
msgid "Old Password"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/History.pm:129
#: lib/LedgerSMB/Report/Inventory/Search.pm:227
#: lib/LedgerSMB/Report/Invoices/COGS.pm:112 old/bin/ic.pl:447
#: old/bin/ic.pl:656 UI/inventory/adjustment_entry.html:27
msgid "On Hand"
msgstr ""

#: old/bin/is.pl:813 UI/Reports/filters/invoice_outstanding.html:89
#: UI/Reports/filters/invoice_search.html:183
msgid "On Hold"
msgstr ""

#: UI/Reports/filters/search_goods.html:30
#: UI/Reports/filters/search_goods.html:279
msgid "On hand"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:68
msgid "One"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:69
msgid "One-o"
msgstr ""

#: UI/Reports/filters/contact_search.html:80
msgid "Only Users"
msgstr ""

#: old/bin/oe.pl:357 UI/Reports/filters/invoice_search.html:203
#: UI/Reports/filters/orders.html:107
#: UI/Reports/filters/purchase_history.html:196
#: UI/Reports/filters/timecards.html:28
msgid "Open"
msgstr ""

#: lib/LedgerSMB/Report/Balance_Sheet.pm:284
msgid "Opening Balance"
msgstr ""

#: UI/Reports/filters/balance_sheet.html:119
msgid "Opening balance"
msgstr ""

#: UI/accounts/edit.html:262
msgid "Options"
msgstr ""

#: UI/file/attachment_screen.html:32 UI/file/internal-file-list.html:56
msgid "Or"
msgstr ""

#: UI/create_batch.html:58
msgid "Or Add To Batch"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Search.pm:307
#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:218
#: lib/LedgerSMB/Report/Invoices/Transactions.pm:271 old/bin/oe.pl:2455
msgid "Order"
msgstr ""

#: templates/demo/ap_transaction.html:72 templates/demo/ap_transaction.tex:82
#: templates/demo/ar_transaction.html:70 templates/demo/ar_transaction.tex:99
#: templates/demo/bin_list.html:75 templates/demo/invoice.html:86
#: templates/demo/invoice.tex:150 templates/demo/packing_list.html:66
#: templates/demo/packing_list.tex:102 templates/demo/pick_list.html:66
#: templates/demo/pick_list.tex:95 templates/demo/purchase_order.html:74
#: templates/demo/purchase_order.tex:121 templates/demo/sales_order.html:77
#: templates/demo/sales_order.tex:121 templates/demo/statement.html:64
#: templates/demo/statement.tex:55 templates/demo/work_order.html:76
msgid "Order #"
msgstr ""

#: UI/reconciliation/report.html:404
msgid "Order By"
msgstr ""

#: old/bin/oe.pl:1850 old/bin/oe.pl:435 templates/demo/purchase_order.html:75
#: templates/demo/sales_order.html:78 templates/demo/sales_order.tex:121
#: templates/demo/work_order.html:77
msgid "Order Date"
msgstr ""

#: old/bin/io.pl:1202 old/bin/oe.pl:1183 old/bin/oe.pl:1394
msgid "Order Date missing!"
msgstr ""

#: locale/menu.xml:296
msgid "Order Entry"
msgstr ""

#: UI/setup/complete.html:94
msgid "Order Lines"
msgstr ""

#: lib/LedgerSMB/Report/Contact/Purchase.pm:71 old/bin/aa.pl:775
#: old/bin/ir.pl:454 old/bin/is.pl:546 old/bin/oe.pl:1845 old/bin/oe.pl:429
#: UI/Reports/filters/invoice_outstanding.html:144
#: UI/Reports/filters/invoice_search.html:99
#: UI/Reports/filters/invoice_search.html:293
#: UI/Reports/filters/orders.html:40 UI/Reports/filters/orders.html:141
msgid "Order Number"
msgstr ""

#: old/bin/io.pl:1212 old/bin/oe.pl:1393
msgid "Order Number missing!"
msgstr ""

#: old/bin/oe.pl:1371
msgid "Order deleted!"
msgstr ""

#: old/bin/oe.pl:2584
msgid "Order generation failed!"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/History.pm:141
msgid "Order/Invoice"
msgstr ""

#: UI/am-taxes.html:18 UI/business_units/list_classes.html:11
msgid "Ordering"
msgstr ""

#: UI/Reports/filters/purchase_history.html:176 UI/setup/complete.html:92
#: UI/setup/confirm_operation.html:148
msgid "Orders"
msgstr ""

#: old/bin/pe.pl:1220
msgid "Orders generated!"
msgstr ""

#: UI/setup/confirm_operation.html:33
msgid "Other Actions"
msgstr ""

#: lib/LedgerSMB/Scripts/recon.pm:498
msgid "Our Balance"
msgstr ""

#: UI/reconciliation/report.html:267 UI/reconciliation/report.html:340
msgid "Our Credits"
msgstr ""

#: UI/reconciliation/report.html:266 UI/reconciliation/report.html:339
msgid "Our Debits"
msgstr ""

#: templates/demo/timecard.html:64 templates/demo/timecard.tex:46
msgid "Out"
msgstr ""

#: old/bin/gl.pl:774
msgid "Out of balance transaction!"
msgstr ""

#: locale/menu.xml:81 locale/menu.xml:147
msgid "Outstanding"
msgstr ""

#: UI/reconciliation/report.html:327
msgid "Outstanding Transactions"
msgstr ""

#: UI/Reports/filters/aging.html:88
msgid "Overdue"
msgstr ""

#: UI/Reports/filters/search_goods.html:20 locale/menu.xml:536
msgid "Overhead"
msgstr ""

#: UI/accounts/edit.html:367 UI/accounts/edit.html:418
msgid "Overpayment"
msgstr ""

#: UI/payments/use_overpayment2.html:152
msgid "Overpayment Account"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Overpayments.pm:80
msgid "Overpayments"
msgstr ""

#: UI/Contact/pricelist.html:105 UI/Reports/PNL.html:109
#: UI/Reports/aging_report.html:96 UI/Reports/balance_sheet.html:110
#: UI/Reports/display_report.html:56
msgid "PDF"
msgstr ""

#: templates/demo/ap_transaction.html:68 templates/demo/ap_transaction.tex:79
#: templates/demo/ar_transaction.html:66
msgid "PO #"
msgstr ""

#: lib/LedgerSMB/Report/Contact/Purchase.pm:75
#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:222
#: lib/LedgerSMB/Report/Invoices/Transactions.pm:274
#: lib/LedgerSMB/Report/Orders.pm:238 old/bin/aa.pl:791 old/bin/is.pl:563
#: old/bin/oe.pl:1856 old/bin/oe.pl:444
#: UI/Reports/filters/invoice_outstanding.html:150
#: UI/Reports/filters/invoice_search.html:109
#: UI/Reports/filters/invoice_search.html:299
#: UI/Reports/filters/orders.html:62 UI/Reports/filters/orders.html:154
msgid "PO Number"
msgstr ""

#: UI/payments/use_overpayment2.html:353
msgid "POST"
msgstr ""

#: UI/payments/payment2.html:512 UI/payments/use_overpayment2.html:359
msgid "POST AND PRINT"
msgstr ""

#: old/bin/am.pl:999 old/bin/io.pl:1106 old/bin/is.pl:273 old/bin/oe.pl:258
#: old/bin/oe.pl:272 old/bin/printer.pl:101 old/bin/printer.pl:83
#: templates/demo/packing_list.html:16 templates/demo/packing_list.html:33
#: templates/demo/packing_list.tex:96
msgid "Packing List"
msgstr ""

#: old/bin/io.pl:1201
msgid "Packing List Date missing!"
msgstr ""

#: old/bin/io.pl:1211
msgid "Packing List Number missing!"
msgstr ""

#: UI/templates/widget.html:55
msgid "Packing list"
msgstr ""

#: lib/LedgerSMB/Report/Contact/Purchase.pm:94
#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:250
#: lib/LedgerSMB/Report/Invoices/Transactions.pm:292
#: lib/LedgerSMB/Report/Listings/Overpayments.pm:144
#: lib/LedgerSMB/Scripts/payment.pm:944
#: UI/Reports/filters/invoice_outstanding.html:227
#: UI/Reports/filters/invoice_search.html:374
#: UI/payments/payments_detail.html:333 templates/demo/invoice.html:148
#: templates/demo/invoice.tex:201
msgid "Paid"
msgstr ""

#: old/bin/pe.pl:114 UI/business_units/edit.html:41
msgid "Parent"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/COGS.pm:107
msgid "Part"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/COGS.pm:164
msgid "Part Description"
msgstr ""

#: UI/Reports/filters/search_goods.html:38
#: UI/Reports/filters/search_goods.html:301
msgid "Part Group"
msgstr ""

#: lib/LedgerSMB/Report/Contact/History.pm:80
#: lib/LedgerSMB/Report/Inventory/Adj_Details.pm:95
#: lib/LedgerSMB/Report/Inventory/History.pm:121
#: lib/LedgerSMB/Report/Inventory/Search.pm:219
#: lib/LedgerSMB/Report/Invoices/COGS.pm:101
#: lib/LedgerSMB/Report/Invoices/COGS.pm:162
#: lib/LedgerSMB/Report/PNL/Product.pm:77
#: lib/LedgerSMB/Scripts/configuration.pm:119 old/bin/oe.pl:2095
#: old/bin/oe.pl:2168 old/bin/oe.pl:2202 old/bin/oe.pl:2449 old/bin/pe.pl:935
#: UI/Reports/filters/cogs_lines.html:8
#: UI/Reports/filters/inventory_activity.html:8
#: UI/Reports/filters/purchase_history.html:239
#: UI/Reports/filters/search_goods.html:53
#: UI/Reports/filters/search_goods.html:263
msgid "Part Number"
msgstr ""

#: old/bin/ic.pl:1475
msgid "Part [_1] not on file!"
msgstr ""

#: UI/Reports/filters/search_goods.html:11
msgid "Part criteria"
msgstr ""

#: UI/Reports/filters/search_goods.html:84
msgid "Part details"
msgstr ""

#: UI/asset/begin_report.html:43
msgid "Partial"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:917
msgid "Partial Disposal Report [_1] on date [_2]"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Activity.pm:165
#: lib/LedgerSMB/Report/Inventory/Activity.pm:93
#: lib/LedgerSMB/Report/Timecards.pm:102 lib/LedgerSMB/Report/Timecards.pm:151
#: UI/Contact/pricelist.csv:7 UI/Contact/pricelist.html:21
#: UI/Contact/pricelist.tex:12 UI/Reports/filters/invoice_search.html:119
#: UI/Reports/filters/timecards.html:9 UI/inventory/adjustment_entry.html:24
#: UI/timecards/timecard-week.html:88 UI/timecards/timecard.html:33
msgid "Partnumber"
msgstr ""

#: UI/Reports/filters/search_goods.html:18 UI/setup/complete.html:96
msgid "Parts"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Partsgroups.pm:71
#: lib/LedgerSMB/Report/Inventory/Search.pm:299
#: UI/Reports/filters/search_partsgroups.html:11 locale/menu.xml:519
msgid "Partsgroup"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Partsgroups.pm:82
msgid "Partsgroups"
msgstr ""

#: UI/Contact/divs/user.html:60 UI/setup/credentials.html:59
#: UI/setup/edit_user.html:50 UI/setup/new_user.html:28
#: UI/users/preferences.html:151
msgid "Password"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:72
msgid "Password Duration (days)"
msgstr ""

#: old/lib/LedgerSMB/User.pm:145
msgid "Passwords must match."
msgstr ""

#: UI/payments/payments_detail.html:195 UI/payments/payments_filter.html:90
msgid "Pay From"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:175 UI/Contact/divs/credit.html:116
#: UI/Contact/divs/credit.html:117 UI/payments/payments_filter.html:91
msgid "Pay To"
msgstr ""

#: templates/demo/printPayment.html:83
msgid "Pay in behalf of"
msgstr ""

#: UI/accounts/edit.html:384
msgid "Payables"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:1175 UI/Contact/divs/credit.html:226
#: UI/Contact/divs/credit.html:227 UI/accounts/edit.html:347
#: UI/accounts/edit.html:399 UI/payments/payments_detail.html:224
#: UI/templates/widget.html:59 t/data/04-complex_template.html:282
#: locale/menu.xml:175
msgid "Payment"
msgstr ""

#: lib/LedgerSMB/Report/Unapproved/Batch_Overview.pm:159
msgid "Payment Amount"
msgstr ""

#: templates/demo/printPayment.html:37
msgid "Payment Order Number [_1]"
msgstr ""

#: UI/payments/payments_filter.html:87
msgid "Payment Processing"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/Payments.pm:201
msgid "Payment Results"
msgstr ""

#: UI/Reports/filters/payments.html:69
msgid "Payment Reversal Information"
msgstr ""

#: UI/Contact/divs/credit.html:174 UI/Contact/divs/credit.html:175
msgid "Payment Terms"
msgstr ""

#: UI/payments/payments_detail.html:184 UI/payments/payments_filter.html:112
msgid "Payment Type"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:288
msgid "Payment batch"
msgstr ""

#: old/bin/aa.pl:1509 old/bin/ir.pl:1276 old/bin/is.pl:1391
msgid "Payment date missing!"
msgstr ""

#: templates/demo/invoice.tex:52
msgid "Payment due NET [_1] Days from date of Invoice."
msgstr ""

#: templates/demo/invoice.html:216
msgid "Payment due by [_1]."
msgstr ""

#: old/bin/aa.pl:968 old/bin/ir.pl:800 old/bin/is.pl:914
#: UI/payments/payment1.html:20 UI/payments/payments_detail.html:6
#: templates/demo/ap_transaction.html:140
#: templates/demo/ap_transaction.tex:125
#: templates/demo/ar_transaction.html:137
#: templates/demo/ar_transaction.tex:142 templates/demo/invoice.html:184
#: templates/demo/invoice.tex:233 locale/menu.xml:186 locale/menu.xml:233
msgid "Payments"
msgstr ""

#: old/bin/ir.pl:523 old/bin/is.pl:631
msgid "Payments associated with voided invoice may need to be reversed."
msgstr ""

#: old/bin/aa.pl:1016 old/bin/ir.pl:840 old/bin/is.pl:952
msgid "Pending approval"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:568
msgid "Percent"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:887
msgid "Percent Disposed"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:899
msgid "Percent Remaining"
msgstr ""

#: old/bin/pe.pl:717 UI/lib/report_base.html:133 UI/lib/report_base.html:225
msgid "Period"
msgstr ""

#: UI/Contact/divs/credit.html:406 UI/Reports/filters/income_statement.html:81
#: UI/Reports/filters/search_goods.html:178
msgid "Period selection"
msgstr ""

#: UI/Contact/divs/credit.html:439 UI/Reports/filters/balance_sheet.html:81
#: UI/Reports/filters/income_statement.html:114
#: UI/Reports/filters/search_goods.html:211
msgid "Periods"
msgstr ""

#: lib/LedgerSMB/Scripts/contact.pm:203 UI/Contact/divs/person.html:2
msgid "Person"
msgstr ""

#: UI/Contact/divs/person.html:114
msgid "Personal ID"
msgstr ""

#: UI/Reports/filters/contact_search.html:70
msgid "Phone"
msgstr ""

#: sql/Pg-database.sql:369
msgid "Physical"
msgstr ""

#: old/bin/am.pl:1000 old/bin/io.pl:1129 old/bin/is.pl:272 old/bin/oe.pl:257
#: old/bin/oe.pl:271 old/bin/printer.pl:79 old/bin/printer.pl:97
#: templates/demo/pick_list.html:16 templates/demo/pick_list.html:32
#: templates/demo/pick_list.tex:89
msgid "Pick List"
msgstr ""

#: UI/templates/widget.html:57
msgid "Pick list"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1177
msgid ""
"Please add a header to the CoA which sorts before the listed accounts "
"(usually \"0000\" works) (in the standard SL UI)"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1158
msgid ""
"Please add at least one header to your CoA which sorts before all other "
"account numbers (in the standard SL UI)"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:649 lib/LedgerSMB/Upgrade_Tests.pm:669
msgid "Please add the missing GIFI accounts"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1123
msgid "Please fix the presented rows to either be \"H\" or \"A\""
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1107 lib/LedgerSMB/Upgrade_Tests.pm:1141
msgid ""
"Please fix the pricegroup data in your partscustomer table (no UI available)"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:798
msgid ""
"Please go into the SQL-Ledger UI and create/rename a\n"
"heading which sorts alphanumerically before the first account by accno"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1011
msgid "Please make all AR invoice numbers unique"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:420 lib/LedgerSMB/Upgrade_Tests.pm:918
msgid "Please make all customer numbers unique"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:989
msgid "Please make all employee numbers unique"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:439 lib/LedgerSMB/Upgrade_Tests.pm:953
msgid "Please make all vendor numbers unique"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:756
msgid ""
"Please make sure all accounts have a category of\n"
"(A)sset, (L)iability, e(Q)uity, (I)ncome or (E)xpense."
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:969
msgid "Please make sure all employees have an employee number"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1088
msgid "Please make sure all make numbers are non-empty"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1070
msgid "Please make sure all modelsnumbers are non-empty"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:814
msgid "Please make sure there are no empty customer numbers."
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:934
msgid "Please make sure there are no empty vendor numbers."
msgstr ""

#: templates/demo/request_quotation.html:96
#: templates/demo/request_quotation.tex:133
msgid "Please provide price and delivery time for the following items:"
msgstr ""

#: UI/payments/use_overpayment1.html:17
msgid "Please select a customer with unused overpayments"
msgstr ""

#: lib/LedgerSMB/Scripts/account.pm:137
msgid "Please select a valid heading"
msgstr ""

#: UI/payments/use_overpayment1.html:15
msgid "Please select a vendor with unused overpayments"
msgstr ""

#: lib/LedgerSMB/Scripts/timecard.pm:142
msgid "Please submit a start/end time or a qty"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:703
msgid ""
"Please use pgAdmin3 or psql to look up the 'chart_id' value in the 'account'"
" table and change it to an existing value"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:687
msgid "Please use pgAdmin3 or psql to remove the duplicates"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:584
msgid "Please use the 1.2 UI to add the GIFI accounts"
msgstr ""

#: lib/LedgerSMB/Report/Unapproved/Batch_Overview.pm:237 old/bin/aa.pl:1107
#: old/bin/ir.pl:919 old/bin/is.pl:1026 UI/payments/payment2.html:506
msgid "Post"
msgstr ""

#: lib/LedgerSMB/Report/Unapproved/Batch_Detail.pm:195
msgid "Post Batch"
msgstr ""

#: lib/LedgerSMB/Scripts/recon.pm:494 UI/create_batch.html:75
msgid "Post Date"
msgstr ""

#: UI/accounts/yearend.html:75
msgid "Post Yearend"
msgstr ""

#: old/bin/aa.pl:1110 old/bin/ir.pl:920 old/bin/is.pl:1029
msgid "Post as new"
msgstr ""

#: UI/reconciliation/report.html:142 UI/reconciliation/report.html:264
#: UI/reconciliation/report.html:337
msgid "Posted"
msgstr ""

#: UI/payments/payments_detail.html:98
msgid "Posting Date"
msgstr ""

#: old/bin/am.pl:859
msgid "Posting GL Transaction [_1]"
msgstr ""

#: old/bin/am.pl:670
msgid "Posting Sales Invoice [_1]"
msgstr ""

#: old/bin/am.pl:692 old/bin/am.pl:701
msgid "Posting Transaction [_1]"
msgstr ""

#: old/bin/am.pl:680
msgid "Posting Vendor Invoice [_1]"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1227
msgid ""
"Pre-migration checks found reconciliations on income or expense accounts or accounts that have not been marked for receipts/payment. Reconciliations should be on asset, liability or equity accounts only.<br>\n"
"Void the clearing date in the dialog shown or go back to SQL-Ledger if you feel that you need to adjust more before migrating."
msgstr ""

#: UI/users/preferences.html:23 locale/menu.xml:741
msgid "Preferences"
msgstr ""

#: UI/users/preferences.html:45
msgid "Preferences Saved"
msgstr ""

#: UI/users/preferences.html:42
msgid "Preferences for [_1]"
msgstr ""

#: UI/Configuration/sequence.html:11
msgid "Prefix"
msgstr ""

#: old/bin/io.pl:249 templates/demo/invoice.tex:170
#: templates/demo/product_receipt.html:99
#: templates/demo/product_receipt.tex:135
#: templates/demo/purchase_order.html:101
#: templates/demo/purchase_order.tex:135 templates/demo/sales_order.html:104
#: templates/demo/sales_order.tex:136 templates/demo/sales_quotation.html:86
#: templates/demo/sales_quotation.tex:105
msgid "Price"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Pricegroups.pm:55
#: lib/LedgerSMB/Report/Inventory/Pricegroups.pm:71
#: UI/Reports/filters/search_pricegroups.html:11
msgid "Price Group"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Pricegroups.pm:82
msgid "Price Groups"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Search.pm:271
#: UI/Reports/filters/search_goods.html:294
msgid "Price Updated"
msgstr ""

#: old/bin/ic.pl:1078 UI/Contact/divs/credit.html:264
#: UI/Contact/divs/credit.html:265
msgid "Pricegroup"
msgstr ""

#: locale/menu.xml:481
msgid "Pricegroups"
msgstr ""

#: UI/Contact/divs/credit.html:390 UI/Contact/pricelist.html:15
#: t/data/04-complex_template.html:361
msgid "Pricelist"
msgstr ""

#: UI/Contact/pricelist.tex:69
msgid "Pricelist for [_1]"
msgstr ""

#: lib/LedgerSMB/Report/Taxform/Details.pm:142
#: lib/LedgerSMB/Report/Taxform/Summary.pm:150 old/bin/aa.pl:1106
#: old/bin/am.pl:317 old/bin/arap.pl:481 old/bin/is.pl:1025 old/bin/oe.pl:2001
#: UI/payments/payments_detail.html:505 UI/timecards/timecard.html:159
msgid "Print"
msgstr ""

#: lib/LedgerSMB/Report/Unapproved/Batch_Detail.pm:223
msgid "Print Batch"
msgstr ""

#: UI/users/preferences.html:120
msgid "Printer"
msgstr ""

#: old/bin/am.pl:908
msgid "Printing Bin List [_1]"
msgstr ""

#: old/bin/am.pl:901
msgid "Printing Credit Invoice [_1]"
msgstr ""

#: old/bin/am.pl:902
msgid "Printing Debit Invoice [_1]"
msgstr ""

#: old/bin/am.pl:900
msgid "Printing Invoice [_1]"
msgstr ""

#: old/bin/am.pl:903
msgid "Printing Packing List [_1]"
msgstr ""

#: old/bin/am.pl:904
msgid "Printing Pick List [_1]"
msgstr ""

#: old/bin/am.pl:907
msgid "Printing Purchase Order [_1]"
msgstr ""

#: old/bin/am.pl:905
msgid "Printing Sales Order [_1]"
msgstr ""

#: old/bin/am.pl:899
msgid "Printing Transaction [_1]"
msgstr ""

#: old/bin/am.pl:906
msgid "Printing Work Order [_1]"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:796
msgid "Prior Dep."
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:790
msgid "Prior Through"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:1018 lib/LedgerSMB/Scripts/asset.pm:558
msgid "Proceeds"
msgstr ""

#: old/bin/am.pl:437 UI/am-list-recurring.html:64
msgid "Process Transactions"
msgstr ""

#: old/bin/printer.pl:54 templates/demo/product_receipt.html:16
#: templates/demo/product_receipt.html:32
#: templates/demo/product_receipt.tex:116
msgid "Product Receipt"
msgstr ""

#: UI/templates/widget.html:61
msgid "Product receipt"
msgstr ""

#: UI/Contact/divs/credit.html:395
msgid "Profit and Loss"
msgstr ""

#: old/bin/aa.pl:1177 old/bin/ic.pl:920 old/bin/ir.pl:967 old/bin/is.pl:1075
msgid "Profit/Loss"
msgstr ""

#: lib/LedgerSMB/Report/PNL/Product.pm:67
msgid "Proft/Loss on Inventory Sales"
msgstr ""

#: old/bin/io.pl:277 old/bin/pe.pl:793
msgid "Project"
msgstr ""

#: old/bin/pe.pl:187
msgid "Project Description Translations"
msgstr ""

#: old/bin/pe.pl:192 old/bin/pe.pl:938
#: UI/Reports/filters/purchase_history.html:319
msgid "Project Number"
msgstr ""

#: lib/LedgerSMB/Report/Timecards.pm:89
msgid "Project/Department Number"
msgstr ""

#: UI/payments/payment2.html:64
msgid "Projects"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Asset.pm:128
#: lib/LedgerSMB/Report/Listings/Asset.pm:95
#: lib/LedgerSMB/Scripts/asset.pm:520
msgid "Purchase Date"
msgstr ""

#: UI/asset/edit_asset.html:67 UI/asset/search_asset.html:64
msgid "Purchase Date:"
msgstr ""

#: lib/LedgerSMB/Report/Contact/History.pm:136
#: UI/Reports/filters/purchase_history.html:9
msgid "Purchase History"
msgstr ""

#: old/bin/am.pl:1003 old/bin/am.pl:758 old/bin/io.pl:1140 old/bin/ir.pl:922
#: old/bin/oe.pl:1219 old/bin/oe.pl:264 old/bin/printer.pl:88
#: UI/Contact/divs/credit.html:370 t/data/04-complex_template.html:343
#: templates/demo/purchase_order.html:16 templates/demo/purchase_order.html:32
#: templates/demo/purchase_order.tex:116 sql/Pg-database.sql:1575
#: locale/menu.xml:302
msgid "Purchase Order"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:117
msgid "Purchase Order Number"
msgstr ""

#: lib/LedgerSMB/Report/Orders.pm:181 lib/LedgerSMB/Report/Orders.pm:273
#: old/bin/am.pl:325 UI/Reports/filters/search_goods.html:163
#: locale/menu.xml:313 locale/menu.xml:325 locale/menu.xml:337
msgid "Purchase Orders"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Asset.pm:129
#: lib/LedgerSMB/Report/Listings/Asset.pm:98
#: lib/LedgerSMB/Scripts/asset.pm:525
msgid "Purchase Value"
msgstr ""

#: UI/asset/edit_asset.html:77 UI/asset/search_asset.html:73
msgid "Purchase Value:"
msgstr ""

#: UI/templates/widget.html:63
msgid "Purchase order"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Activity.pm:117
msgid "Purchased"
msgstr ""

#: lib/LedgerSMB/Report/Contact/History.pm:88
#: lib/LedgerSMB/Report/Inventory/History.pm:158
#: lib/LedgerSMB/Report/Inventory/Search.pm:319 old/bin/ic.pl:1236
#: old/bin/ic.pl:2075 old/bin/io.pl:245 old/bin/oe.pl:1901 old/bin/oe.pl:2185
#: old/bin/pe.pl:943 UI/Reports/filters/purchase_history.html:279
#: UI/timecards/timecard-week.html:99 templates/demo/bin_list.html:103
#: templates/demo/bin_list.tex:123 templates/demo/invoice.html:111
#: templates/demo/invoice.tex:168 templates/demo/packing_list.html:95
#: templates/demo/packing_list.tex:131 templates/demo/pick_list.html:93
#: templates/demo/pick_list.tex:116 templates/demo/product_receipt.html:97
#: templates/demo/product_receipt.tex:134
#: templates/demo/purchase_order.html:99 templates/demo/purchase_order.tex:134
#: templates/demo/request_quotation.html:108
#: templates/demo/request_quotation.tex:139
#: templates/demo/sales_order.html:102 templates/demo/sales_order.tex:135
#: templates/demo/sales_quotation.html:84
#: templates/demo/sales_quotation.tex:104 templates/demo/work_order.html:101
#: templates/demo/work_order.tex:140
msgid "Qty"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/COGS.pm:117
msgid "Quantity"
msgstr ""

#: old/bin/ic.pl:2192
msgid "Quantity exceeds available units to stock!"
msgstr ""

#: old/bin/pe.pl:726 UI/Reports/co/filter_bm.html:62
#: UI/lib/report_base.html:174 UI/lib/report_base.html:261
msgid "Quarter"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Search.pm:311
#: lib/LedgerSMB/Report/Orders.pm:242 old/bin/io.pl:1154 old/bin/io.pl:1161
#: old/bin/oe.pl:1230 old/bin/oe.pl:244 old/bin/printer.pl:61
#: UI/Contact/divs/credit.html:386 t/data/04-complex_template.html:352
#: templates/demo/sales_quotation.html:16
#: templates/demo/sales_quotation.html:33
#: templates/demo/sales_quotation.tex:85 sql/Pg-database.sql:1576
#: locale/menu.xml:362
msgid "Quotation"
msgstr ""

#: templates/demo/sales_quotation.tex:91
msgid "Quotation #"
msgstr ""

#: old/bin/oe.pl:526
msgid "Quotation Date"
msgstr ""

#: old/bin/io.pl:1203 old/bin/oe.pl:1186 old/bin/oe.pl:1400
msgid "Quotation Date missing!"
msgstr ""

#: old/bin/oe.pl:503
msgid "Quotation Number"
msgstr ""

#: old/bin/io.pl:1213 old/bin/oe.pl:1399
msgid "Quotation Number missing!"
msgstr ""

#: old/bin/oe.pl:1375
msgid "Quotation deleted!"
msgstr ""

#: lib/LedgerSMB/Report/Orders.pm:184 lib/LedgerSMB/Report/Orders.pm:275
#: UI/Reports/filters/purchase_history.html:185
#: UI/Reports/filters/search_goods.html:149 locale/menu.xml:361
#: locale/menu.xml:373
msgid "Quotations"
msgstr ""

#: UI/Reports/filters/orders.html:51
msgid "Quote Number"
msgstr ""

#: old/bin/oe.pl:249 old/bin/printer.pl:66 UI/Contact/divs/credit.html:373
#: t/data/04-complex_template.html:352 sql/Pg-database.sql:1577
#: locale/menu.xml:367
msgid "RFQ"
msgstr ""

#: templates/demo/request_quotation.html:74
#: templates/demo/request_quotation.tex:122
msgid "RFQ #"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:118 old/bin/oe.pl:514
msgid "RFQ Number"
msgstr ""

#: lib/LedgerSMB/Report/Orders.pm:187 lib/LedgerSMB/Report/Orders.pm:277
#: UI/Reports/filters/search_goods.html:170 locale/menu.xml:378
msgid "RFQs"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Search.pm:235 old/bin/ic.pl:2077
#: old/bin/ic.pl:456 UI/Reports/filters/search_goods.html:359
msgid "ROP"
msgstr ""

#: lib/LedgerSMB/Scripts/currency.pm:260 old/bin/ir.pl:600 old/bin/is.pl:712
#: UI/Configuration/rate.html:49 templates/demo/timecard.html:107
#: templates/demo/timecard.tex:60
msgid "Rate"
msgstr ""

#: UI/am-taxes.html:14
msgid "Rate (%)"
msgstr ""

#: lib/LedgerSMB/Scripts/currency.pm:248
msgid "Rate Type"
msgstr ""

#: UI/Configuration/rate.html:36
msgid "Rate type"
msgstr ""

#: UI/email.html:72
msgid "Read receipt"
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:210 lib/LedgerSMB/Scripts/setup.pm:215
#: lib/LedgerSMB/Scripts/setup.pm:220 lib/LedgerSMB/Scripts/setup.pm:225
#: lib/LedgerSMB/Scripts/setup.pm:230 lib/LedgerSMB/Scripts/setup.pm:235
#: lib/LedgerSMB/Scripts/setup.pm:240 lib/LedgerSMB/Scripts/setup.pm:245
#: lib/LedgerSMB/Scripts/setup.pm:250
msgid "Rebuild/Upgrade?"
msgstr ""

#: old/bin/io.pl:185 old/bin/oe.pl:1885 templates/demo/bin_list.html:104
#: templates/demo/bin_list.tex:123
msgid "Recd"
msgstr ""

#: UI/templates/widget.html:65 locale/menu.xml:165
msgid "Receipt"
msgstr ""

#: templates/demo/product_receipt.html:75
msgid "Receipt Date"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/Payments.pm:202
msgid "Receipt Results"
msgstr ""

#: UI/payments/payment1.html:19 UI/payments/payments_detail.html:6
#: locale/menu.xml:201 locale/menu.xml:228
msgid "Receipts"
msgstr ""

#: UI/accounts/edit.html:331
msgid "Receivables"
msgstr ""

#: lib/LedgerSMB/Scripts/order.pm:81 locale/menu.xml:350
msgid "Receive"
msgstr ""

#: old/bin/oe.pl:1778
msgid "Receive Merchandise"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:1009
msgid "Received"
msgstr ""

#: lib/LedgerSMB/Report/COA.pm:109 UI/accounts/edit.html:281
msgid "Recon"
msgstr ""

#: UI/reconciliation/new_report.html:39
msgid "Reconcile as FX"
msgstr ""

#: locale/menu.xml:222 locale/menu.xml:238 locale/menu.xml:257
msgid "Reconciliation"
msgstr ""

#: UI/reconciliation/report.html:31
msgid "Reconciliation Report"
msgstr ""

#: lib/LedgerSMB/Report/Reconciliation/Summary.pm:198
msgid "Reconciliation Reports"
msgstr ""

#: old/bin/ir.pl:430 old/bin/is.pl:498
msgid "Record in"
msgstr ""

#: UI/timecards/entry_filter.html:15
msgid "Recording time for"
msgstr ""

#: old/bin/arap.pl:561
msgid "Recurring Transaction for [_1]"
msgstr ""

#: old/bin/am.pl:281 UI/am-list-recurring.html:11 locale/menu.xml:585
msgid "Recurring Transactions"
msgstr ""

#: lib/LedgerSMB/Report/Budget/Search.pm:130
#: lib/LedgerSMB/Report/Budget/Search.pm:82 lib/LedgerSMB/Report/GL.pm:106
#: lib/LedgerSMB/Report/GL.pm:241
#: lib/LedgerSMB/Report/Inventory/Search_Adj.pm:106
#: lib/LedgerSMB/Report/Unapproved/Batch_Detail.pm:119
#: lib/LedgerSMB/Report/Unapproved/Drafts.pm:146
#: lib/LedgerSMB/Report/Unapproved/Drafts.pm:99 old/bin/am.pl:298
#: old/bin/arap.pl:582 UI/Reports/filters/budget_search.html:17
#: UI/Reports/filters/gl.html:23 UI/Reports/filters/gl.html:234
#: UI/Reports/filters/unapproved.html:29 UI/accounts/yearend.html:33
#: UI/budgetting/budget_entry.html:16 UI/journal/journal_entry.html:22
msgid "Reference"
msgstr ""

#: old/bin/io.pl:1214
msgid "Reference Number Missing"
msgstr ""

#: sql/Pg-database.sql:242
msgid "Referral"
msgstr ""

#: templates/demo/ar_transaction.html:171
#: templates/demo/ar_transaction.tex:159
msgid "Registration"
msgstr ""

#: templates/demo/invoice.html:229
msgid "Registration [_1]"
msgstr ""

#: UI/Reports/filters/balance_sheet.html:18
#: UI/Reports/filters/income_statement.html:54
msgid "Regular"
msgstr ""

#: lib/LedgerSMB/Scripts/budgets.pm:118 UI/reconciliation/report.html:451
msgid "Reject"
msgstr ""

#: UI/reconciliation/report.html:455
msgid "Rejected"
msgstr ""

#: UI/reconciliation/report.html:454
msgid "Rejecting..."
msgstr ""

#: UI/setup/confirm_operation.html:74
msgid "Reload menu"
msgstr ""

#: lib/LedgerSMB/Report/Assets/Net_Book_Value.pm:91
msgid "Rem. Life"
msgstr ""

#: old/bin/aa.pl:685 old/bin/ir.pl:393 old/bin/is.pl:451 old/bin/oe.pl:463
msgid "Remaining"
msgstr ""

#: old/bin/aa.pl:949 old/bin/ir.pl:779 old/bin/is.pl:891
msgid "Remaining balance"
msgstr ""

#: UI/Contact/divs/bank_act.html:27 UI/Contact/divs/bank_act.html:78
#: UI/Contact/divs/bank_act.html:82 t/data/04-complex_template.html:559
msgid "Remark"
msgstr ""

#: t/data/04-complex_template.html:610
msgid "Remark:"
msgstr ""

#: old/bin/arap.pl:545
msgid "Repeat"
msgstr ""

#: UI/reconciliation/approved.html:3
msgid "Report Approved"
msgstr ""

#: UI/reconciliation/report.html:76
msgid "Report Generated By:"
msgstr ""

#: UI/Reports/aging_report.html:9 UI/Reports/display_report.html:16
#: templates/demo/display_report.html:153
#: templates/demo/display_report.tex:122
msgid "Report Name"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:730
msgid "Report Results"
msgstr ""

#: UI/reconciliation/submitted.html:3
msgid "Report Submitted"
msgstr ""

#: UI/Reports/filters/trial_balance.html:79
msgid "Report Type"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:821
msgid "Report [_1] on date [_2]"
msgstr ""

#: UI/timecards/timecard-week.html:65
msgid "Report in"
msgstr ""

#: UI/Reports/filters/balance_sheet.html:11
#: UI/Reports/filters/income_statement.html:18
msgid "Report type"
msgstr ""

#: lib/LedgerSMB/Report/PNL/Income_Statement.pm:56
msgid "Reporting Basis"
msgstr ""

#: UI/business_units/list_classes.html:3 locale/menu.xml:708
msgid "Reporting Units"
msgstr ""

#: UI/Reports/filters/income_statement.html:273
msgid "Reporting unit filters"
msgstr ""

#: UI/templates/widget.html:81 locale/menu.xml:80 locale/menu.xml:146
#: locale/menu.xml:227 locale/menu.xml:307 locale/menu.xml:372
#: locale/menu.xml:411 locale/menu.xml:498 locale/menu.xml:606
#: locale/menu.xml:651
msgid "Reports"
msgstr ""

#: old/bin/arap.pl:348
msgid "Reposting Not Allowed"
msgstr ""

#: old/bin/oe.pl:2457
msgid "Req"
msgstr ""

#: old/bin/oe.pl:1236 templates/demo/request_quotation.html:16
#: templates/demo/request_quotation.html:32
#: templates/demo/request_quotation.tex:116
msgid "Request for Quotation"
msgstr ""

#: UI/templates/widget.html:67
msgid "Request for quotation"
msgstr ""

#: UI/Reports/filters/orders.html:163
msgid "Required By"
msgstr ""

#: lib/LedgerSMB/Report/Orders.pm:213
msgid "Required Date"
msgstr ""

#: old/bin/io.pl:284 old/bin/oe.pl:439 old/bin/oe.pl:498
#: templates/demo/product_receipt.tex:122
#: templates/demo/purchase_order.html:76 templates/demo/purchase_order.tex:122
#: templates/demo/request_quotation.html:76
#: templates/demo/request_quotation.tex:123 templates/demo/sales_order.html:79
#: templates/demo/sales_order.tex:122 templates/demo/work_order.html:78
msgid "Required by"
msgstr ""

#: lib/LedgerSMB.pm:541 lib/LedgerSMB.pm:542
msgid "Required input not provided"
msgstr ""

#: lib/LedgerSMB/Report/Balance_Sheet.pm:94
#: lib/LedgerSMB/Report/PNL/Income_Statement.pm:77
msgid "Required period type"
msgstr ""

#: UI/Contact/divs/user.html:132 UI/setup/edit_user.html:93
msgid "Reset Password"
msgstr ""

#: UI/accounts/yearend.html:53
msgid "Retained Earnings"
msgstr ""

#: UI/Contact/divs/company.html:130 UI/Contact/divs/person.html:137
msgid "Retrieve"
msgstr ""

#: UI/email.html:6
msgid "Return"
msgstr ""

#: UI/setup/complete.html:21 UI/setup/complete_migration_revert.html:11
msgid "Return to setup"
msgstr ""

#: UI/logout.html:10
msgid "Return to the login screen."
msgstr ""

#: old/bin/ic.pl:505 UI/accounts/edit.html:466
msgid "Returns"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Activity.pm:112
msgid "Revenue"
msgstr ""

#: UI/Reports/filters/overpayments.html:41
msgid "Reversal Information"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Overpayments.pm:163
msgid "Reverse"
msgstr ""

#: locale/menu.xml:211
msgid "Reverse AR Overpay"
msgstr ""

#: locale/menu.xml:196
msgid "Reverse Overpay"
msgstr ""

#: locale/menu.xml:191
msgid "Reverse Payment"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/Payments.pm:229
msgid "Reverse Payments"
msgstr ""

#: locale/menu.xml:206
msgid "Reverse Receipts"
msgstr ""

#: UI/Reports/filters/gl.html:148
msgid "Reversed"
msgstr ""

#: sql/Pg-database.sql:2089
msgid "Rounded"
msgstr ""

#: UI/setup/complete.html:77 UI/setup/confirm_operation.html:141
msgid "Row counts"
msgstr ""

#: UI/setup/begin_backup.html:71
msgid "Run Backup"
msgstr ""

#: UI/setup/db-patches-log.html:19
msgid "Run ID"
msgstr ""

#: UI/Reports/filters/inventory_adj.html:38
msgid "Run Report"
msgstr ""

#: UI/Contact/divs/company.html:93 UI/setup/select_coa_details.html:58
#: locale/menu.xml:20
msgid "SIC"
msgstr ""

#: t/data/04-complex_template.html:259
msgid "SIC:"
msgstr ""

#: old/bin/io.pl:279 old/bin/oe.pl:1887 old/bin/oe.pl:2447
msgid "SKU"
msgstr ""

#: old/bin/ir.pl:471
msgid "SO Number"
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:180
msgid "SQL-Ledger 3.0 database detected."
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:156
msgid "SQL-Ledger database detected."
msgstr ""

#: UI/payments/payment1.html:53
msgid "SSN"
msgstr ""

#: sql/Pg-database.sql:628
msgid "Salary"
msgstr ""

#: sql/Pg-database.sql:2409
msgid "Sale"
msgstr ""

#: UI/Contact/divs/address.html:47 UI/Contact/divs/employee.html:79
#: sql/Pg-database.sql:367
msgid "Sales"
msgstr ""

#: locale/menu.xml:38
msgid "Sales Invoice"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:112
msgid "Sales Invoice/AR Transaction Number"
msgstr ""

#: old/bin/am.pl:1001 old/bin/am.pl:752 old/bin/io.pl:1092 old/bin/is.pl:1032
#: old/bin/oe.pl:1213 old/bin/oe.pl:255 old/bin/printer.pl:71
#: UI/Contact/divs/credit.html:383 t/data/04-complex_template.html:343
#: templates/demo/sales_order.html:16 templates/demo/sales_order.html:33
#: templates/demo/sales_order.tex:115 sql/Pg-database.sql:1574
#: locale/menu.xml:297
msgid "Sales Order"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:113
msgid "Sales Order Number"
msgstr ""

#: lib/LedgerSMB/Report/Orders.pm:178 lib/LedgerSMB/Report/Orders.pm:271
#: old/bin/am.pl:324 UI/Reports/filters/search_goods.html:142
#: locale/menu.xml:308 locale/menu.xml:320 locale/menu.xml:332
#: locale/menu.xml:571
msgid "Sales Orders"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:114
msgid "Sales Quotation Number"
msgstr ""

#: lib/LedgerSMB/Report/Taxform/Summary.pm:101
#: UI/Contact/divs/company.html:102 UI/Reports/filters/taxforms.html:110
msgid "Sales Tax ID"
msgstr ""

#: UI/templates/widget.html:69
msgid "Sales order"
msgstr ""

#: UI/templates/widget.html:71
msgid "Sales quotation"
msgstr ""

#: t/data/04-complex_template.html:85
msgid "Sales:"
msgstr ""

#: lib/LedgerSMB/Report/Contact/History.pm:121
#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:276
#: lib/LedgerSMB/Report/Invoices/Transactions.pm:309 old/bin/aa.pl:609
#: old/bin/is.pl:372 old/bin/oe.pl:571
#: UI/Reports/filters/invoice_outstanding.html:178
#: UI/Reports/filters/invoice_search.html:327
#: templates/demo/ar_transaction.html:74 templates/demo/invoice.html:87
#: templates/demo/invoice.tex:151 templates/demo/sales_order.html:80
#: templates/demo/sales_order.tex:123 templates/demo/work_order.html:79
msgid "Salesperson"
msgstr ""

#: UI/Contact/divs/employee.html:37 UI/Contact/divs/person.html:61
msgid "Salutation"
msgstr ""

#: UI/asset/edit_asset.html:105 UI/asset/search_asset.html:94
msgid "Salvage Value:"
msgstr ""

#: lib/LedgerSMB/Report/Timecards.pm:134
msgid "Sat"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:538 lib/LedgerSMB/Scripts/budgets.pm:103
#: old/bin/am.pl:65 old/bin/am.pl:73 old/bin/am.pl:84 old/bin/ic.pl:811
#: old/bin/ic.pl:818 old/bin/pe.pl:147 old/bin/pe.pl:514
#: UI/Configuration/sequence.html:7 UI/Configuration/sequence.html:117
#: UI/Configuration/settings.html:107 UI/Contact/divs/bank_act.html:90
#: UI/Contact/divs/company.html:137 UI/Contact/divs/employee.html:152
#: UI/Contact/divs/notes.html:59 UI/Contact/divs/person.html:144
#: UI/Contact/pricelist.html:96 UI/accounts/edit.html:87
#: UI/accounts/edit.html:563 UI/am-taxes.html:76 UI/asset/edit_asset.html:214
#: UI/asset/edit_class.html:60 UI/asset/import_asset.html:58
#: UI/business_units/edit.html:91 UI/business_units/edit.html:103
#: UI/business_units/list_classes.html:84
#: UI/inventory/adjustment_entry.html:89 UI/payroll/deduction.html:93
#: UI/payroll/income.html:98 UI/reconciliation/report.html:425
#: UI/taxform/add_taxform.html:71 UI/templates/widget.html:146
#: UI/templates/widget.html:160 UI/timecards/timecard-week.html:125
#: UI/timecards/timecard.html:168 UI/users/preferences.html:148
#: t/data/04-complex_template.html:131 t/data/04-complex_template.html:310
#: t/data/04-complex_template.html:620 t/data/04-complex_template.html:644
msgid "Save"
msgstr ""

#: UI/Contact/divs/contact_info.html:131
msgid "Save As New"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:273
msgid "Save Batch"
msgstr ""

#: UI/Contact/divs/credit.html:345
msgid "Save Changes"
msgstr ""

#: UI/Contact/divs/contact_info.html:124 t/data/04-complex_template.html:546
msgid "Save Contact"
msgstr ""

#: UI/Contact/divs/user.html:202 UI/setup/edit_user.html:153
msgid "Save Groups"
msgstr ""

#: UI/Contact/divs/address.html:187 t/data/04-complex_template.html:485
msgid "Save Location"
msgstr ""

#: UI/Contact/divs/credit.html:353
msgid "Save New"
msgstr ""

#: UI/Contact/divs/address.html:195
msgid "Save New Location"
msgstr ""

#: old/bin/arap.pl:627 old/bin/arap.pl:632
msgid "Save Schedule"
msgstr ""

#: UI/accounts/edit.html:646
msgid "Save Translations"
msgstr ""

#: old/bin/am.pl:66 old/bin/am.pl:75 old/bin/ic.pl:812 old/bin/ic.pl:827
#: UI/accounts/edit.html:96 UI/accounts/edit.html:572
msgid "Save as new"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:221
msgid "Save the fixes provided and attempt to continue migration"
msgstr ""

#: UI/Configuration/sequence.html:119 UI/Contact/pricelist.html:98
#: UI/accounts/edit.html:89 UI/accounts/edit.html:565 UI/am-taxes.html:78
#: UI/asset/edit_asset.html:220 UI/asset/edit_class.html:66
#: UI/asset/import_asset.html:64 UI/business_units/edit.html:97
#: UI/business_units/edit.html:109 UI/business_units/list_classes.html:86
#: UI/inventory/adjustment_entry.html:95 UI/payments/payments_detail.html:489
#: UI/payroll/deduction.html:95 UI/payroll/income.html:100
#: UI/reconciliation/report.html:429 UI/setup/edit_user.html:158
#: UI/taxform/add_taxform.html:76 UI/timecards/timecard-week.html:131
#: UI/timecards/timecard.html:170
msgid "Saved"
msgstr ""

#: UI/accounts/edit.html:648
msgid "Saved Translations"
msgstr ""

#: lib/LedgerSMB/Scripts/business_unit.pm:193
msgid "Saved id [_1]"
msgstr ""

#: UI/accounts/edit.html:647
msgid "Saving Translations..."
msgstr ""

#: old/bin/am.pl:796
msgid "Saving [_1] [_2]"
msgstr ""

#: UI/oe-save-warn.html:12
msgid "Saving over an existing document.  Continue?"
msgstr ""

#: UI/Configuration/sequence.html:118 UI/Contact/pricelist.html:97
#: UI/accounts/edit.html:88 UI/accounts/edit.html:564 UI/am-taxes.html:77
#: UI/asset/edit_asset.html:219 UI/asset/edit_class.html:65
#: UI/asset/import_asset.html:63 UI/business_units/edit.html:96
#: UI/business_units/edit.html:108 UI/business_units/list_classes.html:85
#: UI/inventory/adjustment_entry.html:94 UI/payments/payments_detail.html:488
#: UI/payroll/deduction.html:94 UI/payroll/income.html:99
#: UI/reconciliation/report.html:428 UI/setup/edit_user.html:157
#: UI/taxform/add_taxform.html:75 UI/timecards/timecard-week.html:130
#: UI/timecards/timecard.html:169
msgid "Saving..."
msgstr ""

#: old/bin/aa.pl:1108 old/bin/ir.pl:921 old/bin/is.pl:1027
msgid "Schedule"
msgstr ""

#: UI/journal/journal_entry.html:351
msgid "Scheduled"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:701 lib/LedgerSMB/Template/UI.pm:127
#: old/bin/printer.pl:136 UI/lib/utilities.html:44
msgid "Screen"
msgstr ""

#: UI/setup/complete.html:42 UI/setup/db-patches-log.html:20
msgid "Script path"
msgstr ""

#: UI/Reports/filters/batches.html:58 UI/Reports/filters/budget_search.html:61
#: UI/Reports/filters/cogs_lines.html:35
#: UI/Reports/filters/contact_search.html:101
#: UI/Reports/filters/overpayments.html:60
#: UI/Reports/filters/payments.html:121
#: UI/Reports/filters/reconciliation_search.html:80
#: UI/Reports/filters/search_goods.html:446
#: UI/Reports/filters/search_partsgroups.html:17
#: UI/Reports/filters/search_pricegroups.html:17
#: UI/Reports/filters/timecards.html:48 UI/Reports/filters/unapproved.html:67
#: UI/asset/search_asset.html:186 UI/asset/search_class.html:55
#: UI/business_units/filter.html:40 locale/menu.xml:4 locale/menu.xml:58
#: locale/menu.xml:124 locale/menu.xml:274 locale/menu.xml:288
#: locale/menu.xml:406 locale/menu.xml:446 locale/menu.xml:554
msgid "Search"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/Transactions.pm:331
msgid "Search AP"
msgstr ""

#: UI/Reports/filters/invoice_search.html:4
msgid "Search AP Invoices"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/Transactions.pm:332
msgid "Search AR"
msgstr ""

#: UI/Reports/filters/invoice_search.html:9
msgid "Search AR Invoices"
msgstr ""

#: UI/asset/search_asset.html:4
msgid "Search Asset"
msgstr ""

#: UI/asset/search_class.html:4
msgid "Search Asset Class"
msgstr ""

#: locale/menu.xml:631
msgid "Search Assets"
msgstr ""

#: UI/Reports/filters/batches.html:9
msgid "Search Batches"
msgstr ""

#: UI/Reports/filters/budget_search.html:8
msgid "Search Budgets"
msgstr ""

#: UI/Reports/filters/search_goods.html:9
msgid "Search Goods and Services"
msgstr ""

#: locale/menu.xml:471
msgid "Search Groups"
msgstr ""

#: UI/Reports/filters/inventory_adj.html:9
msgid "Search Inventory Entry"
msgstr ""

#: UI/Reports/filters/search_partsgroups.html:3
msgid "Search Partsgroups"
msgstr ""

#: UI/Reports/filters/payments.html:10
msgid "Search Payments"
msgstr ""

#: UI/Reports/filters/search_pricegroups.html:3
msgid "Search Price Groups"
msgstr ""

#: lib/LedgerSMB/Scripts/order.pm:74
msgid "Search Purchase Orders"
msgstr ""

#: lib/LedgerSMB/Scripts/order.pm:85
msgid "Search Quotations"
msgstr ""

#: UI/Reports/filters/payments.html:7
msgid "Search Receipts"
msgstr ""

#: UI/Reports/filters/reconciliation_search.html:6
msgid "Search Reconciliation Reports"
msgstr ""

#: lib/LedgerSMB/Scripts/order.pm:89
msgid "Search Requests for Quotation"
msgstr ""

#: lib/LedgerSMB/Scripts/order.pm:63
msgid "Search Sales Orders"
msgstr ""

#: UI/Reports/filters/unapproved.html:17
msgid "Search Unapproved Transactions"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:603
msgid "Search reports"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:66
msgid "Security Settings"
msgstr ""

#: UI/reconciliation/report.html:419
msgid "Select All"
msgstr ""

#: old/bin/pe.pl:1043
msgid "Select Customer"
msgstr ""

#: UI/setup/template_info.html:10
msgid "Select Templates to Load"
msgstr ""

#: old/bin/oe.pl:2566
msgid "Select Vendor"
msgstr ""

#: old/bin/arapprn.pl:300 old/bin/is.pl:1447 old/bin/oe.pl:1300
msgid "Select a Printer!"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:733
msgid "Select and assign the missing AP accounts"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:714
msgid "Select and assign the missing AR accounts"
msgstr ""

#: UI/taxform/add_taxform.html:48
msgid "Select by Default:"
msgstr ""

#: old/bin/arap.pl:178 old/bin/ic.pl:1663 old/bin/pe.pl:579
msgid "Select from one of the names below"
msgstr ""

#: UI/payments/use_overpayment2.html:146
msgid "Select invoices"
msgstr ""

#: UI/setup/credentials.html:36
msgid "Select or Enter User"
msgstr ""

#: old/bin/arapprn.pl:298 old/bin/arapprn.pl:86 old/bin/is.pl:1445
#: old/bin/oe.pl:1298
msgid "Select postscript or PDF!"
msgstr ""

#: old/bin/io.pl:1059
msgid "Select txt, postscript or PDF!"
msgstr ""

#: UI/Contact/divs/credit.html:409 UI/Reports/filters/balance_sheet.html:48
#: UI/Reports/filters/income_statement.html:84
#: UI/Reports/filters/search_goods.html:181
msgid "Select using"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/Payments.pm:122
msgid "Selected"
msgstr ""

#: old/bin/ic.pl:1246
msgid "Sell"
msgstr ""

#: lib/LedgerSMB/Report/Contact/History.pm:98
#: lib/LedgerSMB/Report/Inventory/History.pm:154
#: lib/LedgerSMB/Report/Inventory/Search.pm:253 old/bin/ic.pl:1097
#: old/bin/ic.pl:409 UI/Contact/pricelist.csv:31 UI/Contact/pricelist.html:43
#: UI/Contact/pricelist.tex:42 UI/Reports/filters/purchase_history.html:259
#: UI/Reports/filters/search_goods.html:316
msgid "Sell Price"
msgstr ""

#: old/bin/am.pl:956
msgid "Sending Bin List [_1]"
msgstr ""

#: old/bin/am.pl:949
msgid "Sending Credit Invoice [_1]"
msgstr ""

#: old/bin/am.pl:950
msgid "Sending Debit Invoice [_1]"
msgstr ""

#: old/bin/am.pl:948
msgid "Sending Invoice [_1]"
msgstr ""

#: old/bin/am.pl:951
msgid "Sending Packing List [_1]"
msgstr ""

#: old/bin/am.pl:952
msgid "Sending Pick List [_1]"
msgstr ""

#: old/bin/am.pl:955
msgid "Sending Purchase Order [_1]"
msgstr ""

#: old/bin/am.pl:953
msgid "Sending Sales Order [_1]"
msgstr ""

#: old/bin/am.pl:947
msgid "Sending Transaction [_1]"
msgstr ""

#: old/bin/am.pl:954
msgid "Sending Work Order [_1]"
msgstr ""

#: old/bin/aa.pl:91 old/bin/gl.pl:82 old/bin/io.pl:83
msgid "Sep"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:77
msgid "Separate Duties"
msgstr ""

#: lib/LedgerSMB.pm:624 old/bin/aa.pl:77 old/bin/gl.pl:68 old/bin/io.pl:69
msgid "September"
msgstr ""

#: UI/Configuration/sequence.html:12
msgid "Sequence"
msgstr ""

#: UI/Configuration/sequence.html:3 locale/menu.xml:698
msgid "Sequences"
msgstr ""

#: templates/demo/packing_list.html:93 templates/demo/work_order.html:104
msgid "Serial #"
msgstr ""

#: old/bin/io.pl:276 old/bin/oe.pl:1908
msgid "Serial No."
msgstr ""

#: lib/LedgerSMB/Report/Contact/History.pm:111
#: lib/LedgerSMB/Report/Inventory/History.pm:167
#: lib/LedgerSMB/Report/Inventory/Search.pm:328
#: UI/Reports/filters/purchase_history.html:328
#: UI/Reports/filters/search_goods.html:73
#: UI/Reports/filters/search_goods.html:429 templates/demo/bin_list.tex:122
#: templates/demo/packing_list.tex:130 templates/demo/work_order.tex:141
msgid "Serial Number"
msgstr ""

#: templates/demo/bin_list.html:101
msgid "Serialnumber"
msgstr ""

#: old/bin/pe.pl:933
msgid "Service Code"
msgstr ""

#: UI/Reports/filters/search_goods.html:19 locale/menu.xml:531
msgid "Services"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:75
msgid "Session Timeout (e.g. \"90 minutes\")"
msgstr ""

#: locale/menu.xml:703
msgid "Sessions"
msgstr ""

#: UI/Configuration/sequence.html:10
msgid "Setting"
msgstr ""

#: UI/users/preferences.html:74
msgid "Settings"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:75
msgid "Seven"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:109
msgid "Seven Hundred"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:86
msgid "Seventeen"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:104
msgid "Seventy"
msgstr ""

#: lib/LedgerSMB/Scripts/order.pm:70 old/bin/io.pl:177 old/bin/oe.pl:1881
#: templates/demo/packing_list.html:96 templates/demo/packing_list.tex:131
#: templates/demo/pick_list.html:94 templates/demo/pick_list.tex:116
#: locale/menu.xml:345
msgid "Ship"
msgstr ""

#: old/bin/oe.pl:1773
msgid "Ship Merchandise"
msgstr ""

#: templates/demo/bin_list.html:41 templates/demo/bin_list.tex:68
#: templates/demo/invoice.html:44 templates/demo/invoice.tex:98
#: templates/demo/sales_order.html:41 templates/demo/work_order.html:40
msgid "Ship To"
msgstr ""

#: templates/demo/packing_list.html:40 templates/demo/pick_list.html:39
#: templates/demo/product_receipt.html:40
#: templates/demo/purchase_order.html:40
#: templates/demo/request_quotation.html:40
msgid "Ship To:"
msgstr ""

#: lib/LedgerSMB/Report/Contact/Purchase.pm:119
#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:288
#: lib/LedgerSMB/Report/Invoices/Transactions.pm:318
#: lib/LedgerSMB/Report/Orders.pm:250
#: UI/Reports/filters/invoice_outstanding.html:60
#: UI/Reports/filters/invoice_outstanding.html:261
#: UI/Reports/filters/invoice_search.html:160
#: UI/Reports/filters/invoice_search.html:408
#: UI/Reports/filters/orders.html:72 UI/Reports/filters/orders.html:183
#: templates/demo/product_receipt.html:78
#: templates/demo/purchase_order.html:79 templates/demo/sales_order.html:82
#: templates/demo/sales_order.tex:125 templates/demo/work_order.html:81
msgid "Ship Via"
msgstr ""

#: old/bin/aa.pl:1109 old/bin/io.pl:1515 old/bin/is.pl:1028 old/bin/oe.pl:2003
msgid "Ship to"
msgstr ""

#: old/bin/is.pl:527 old/bin/oe.pl:1834 old/bin/oe.pl:656
#: templates/demo/bin_list.html:80 templates/demo/bin_list.tex:99
#: templates/demo/invoice.html:89 templates/demo/invoice.tex:153
#: templates/demo/packing_list.html:71 templates/demo/packing_list.tex:108
#: templates/demo/pick_list.html:71 templates/demo/pick_list.tex:99
#: templates/demo/product_receipt.tex:124
#: templates/demo/purchase_order.tex:124
#: templates/demo/request_quotation.html:79
#: templates/demo/request_quotation.tex:125
#: templates/demo/sales_quotation.html:64
#: templates/demo/sales_quotation.tex:94
msgid "Ship via"
msgstr ""

#: UI/Contact/divs/address.html:49 sql/Pg-database.sql:368 locale/menu.xml:344
msgid "Shipping"
msgstr ""

#: old/bin/io.pl:1571 old/bin/is.pl:518 old/bin/oe.pl:645
msgid "Shipping Address"
msgstr ""

#: old/bin/io.pl:1662 old/bin/is.pl:514 old/bin/oe.pl:641
msgid "Shipping Attn"
msgstr ""

#: old/bin/oe.pl:1775
msgid "Shipping Date"
msgstr ""

#: old/bin/oe.pl:2032
msgid "Shipping Date missing!"
msgstr ""

#: old/bin/printer.pl:115
msgid "Shipping Label"
msgstr ""

#: lib/LedgerSMB/Report/Contact/Purchase.pm:115
#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:284
#: lib/LedgerSMB/Report/Invoices/Transactions.pm:315
#: lib/LedgerSMB/Report/Orders.pm:246 old/bin/is.pl:510 old/bin/oe.pl:1829
#: old/bin/oe.pl:636 UI/Reports/filters/invoice_outstanding.html:255
#: UI/Reports/filters/invoice_search.html:402 templates/demo/bin_list.html:79
#: templates/demo/bin_list.tex:99 templates/demo/invoice.html:88
#: templates/demo/invoice.tex:152 templates/demo/packing_list.html:70
#: templates/demo/packing_list.tex:107 templates/demo/pick_list.html:70
#: templates/demo/pick_list.tex:98 templates/demo/product_receipt.html:77
#: templates/demo/product_receipt.tex:123
#: templates/demo/purchase_order.html:78 templates/demo/purchase_order.tex:123
#: templates/demo/request_quotation.html:78
#: templates/demo/request_quotation.tex:124 templates/demo/sales_order.html:81
#: templates/demo/sales_order.tex:124 templates/demo/sales_quotation.html:63
#: templates/demo/sales_quotation.tex:93 templates/demo/work_order.html:80
msgid "Shipping Point"
msgstr ""

#: UI/templates/widget.html:73
msgid "Shipping label"
msgstr ""

#: UI/Reports/filters/search_goods.html:31
msgid "Short"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Country.pm:46
msgid "Short name"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:129
msgid "Show Credit Limit"
msgstr ""

#: templates/demo/printPayment.html:72
msgid "Signature"
msgstr ""

#: sql/Pg-database.sql:2088
msgid "Simple"
msgstr ""

#: UI/templates/widget.html:43
msgid "Single check"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:74
msgid "Six"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:85
msgid "Sixteen"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:103
msgid "Sixty"
msgstr ""

#: UI/setup/select_coa_country.html:54 UI/setup/select_coa_details.html:85
msgid "Skip"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Activity.pm:102
msgid "Sold"
msgstr ""

#: UI/payments/payments_detail.html:281
msgid "Some"
msgstr ""

#: lib/LedgerSMB/Report/Budget/Search.pm:132 lib/LedgerSMB/Report/GL.pm:155
#: lib/LedgerSMB/Report/GL.pm:243
#: lib/LedgerSMB/Report/Inventory/Adj_Details.pm:81
#: lib/LedgerSMB/Report/Invoices/Payments.pm:135
#: lib/LedgerSMB/Scripts/payment.pm:196 lib/LedgerSMB/Scripts/recon.pm:490
#: old/bin/aa.pl:989 old/bin/ir.pl:817 old/bin/is.pl:932
#: UI/Reports/filters/gl.html:67 UI/Reports/filters/gl.html:291
#: UI/Reports/filters/inventory_adj.html:13
#: UI/Reports/filters/invoice_search.html:130
#: UI/Reports/filters/payments.html:62 UI/inventory/adjustment_setup.html:22
#: UI/journal/journal_entry.html:158 UI/payments/payment2.html:111
#: UI/payments/payment2.html:279 UI/payments/payment2.html:345
#: UI/payments/payments_detail.html:309 UI/reconciliation/report.html:138
#: UI/reconciliation/report.html:263 UI/reconciliation/report.html:336
#: templates/demo/ap_transaction.html:153
#: templates/demo/ap_transaction.tex:127
#: templates/demo/ar_transaction.html:150
#: templates/demo/ar_transaction.tex:144 templates/demo/invoice.html:194
#: templates/demo/invoice.tex:235 templates/demo/printPayment.html:50
msgid "Source"
msgstr ""

#: UI/payments/payment2.html:120
msgid "Source documentation"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Search_Adj.pm:89
msgid "Source starting with"
msgstr ""

#: templates/demo/sales_quotation.html:156
msgid "Special order items are subject to a 10% cancellation fee."
msgstr ""

#: templates/demo/sales_order.html:174
msgid "Special order items are subject to a 10% order cancellation fee."
msgstr ""

#: templates/demo/sales_quotation.tex:39
msgid "Special order items are subject to a 10\\% cancellation fee."
msgstr ""

#: UI/lib/report_base.html:70
msgid "Standard"
msgstr ""

#: lib/LedgerSMB/Report/Listings/SIC.pm:63
msgid "Standard Industrial Codes"
msgstr ""

#: lib/LedgerSMB/Report/Budget/Search.pm:124
#: lib/LedgerSMB/Report/Budget/Search.pm:72
#: lib/LedgerSMB/Report/Budget/Variance.pm:122
#: lib/LedgerSMB/Report/Contact/History.pm:152 lib/LedgerSMB/Report/GL.pm:235
#: lib/LedgerSMB/Report/Inventory/Search_Adj.pm:83
#: lib/LedgerSMB/Report/Invoices/COGS.pm:158
#: lib/LedgerSMB/Report/Listings/Business_Unit.pm:67
#: UI/Contact/divs/credit.html:34 UI/Contact/divs/employee.html:136
#: UI/Reports/filters/budget_search.html:33 UI/budgetting/budget_entry.html:37
#: UI/business_units/edit.html:55
msgid "Start Date"
msgstr ""

#: t/data/04-complex_template.html:114
msgid "Start Date:"
msgstr ""

#: UI/payments/payments_filter.html:130
msgid "Start Source Numbering At"
msgstr ""

#: lib/LedgerSMB/Report/Contact/EmployeeSearch.pm:74
msgid "Start date"
msgstr ""

#: UI/setup/complete.html:32
msgid "Start using LedgerSMB"
msgstr ""

#: old/bin/arap.pl:586 UI/Reports/filters/purchase_history.html:110
msgid "Startdate"
msgstr ""

#: lib/LedgerSMB/Report/Trial_Balance.pm:164 UI/Reports/co/filter_bm.html:91
msgid "Starting Balance"
msgstr ""

#: UI/Contact/divs/credit.html:129 UI/Contact/divs/credit.html:130
#: UI/timecards/entry_filter.html:27
msgid "Starting Date"
msgstr ""

#: t/data/04-complex_template.html:177
msgid "Starting Date:"
msgstr ""

#: old/bin/aa.pl:795 old/bin/io.pl:1582 old/bin/ir.pl:475 old/bin/is.pl:567
msgid "State"
msgstr ""

#: UI/Contact/divs/address.html:87 UI/Contact/divs/address.html:154
#: UI/Reports/filters/contact_search.html:60
#: UI/Reports/filters/purchase_history.html:83
#: t/data/04-complex_template.html:387
msgid "State/Province"
msgstr ""

#: t/data/04-complex_template.html:454
msgid "State/Province:"
msgstr ""

#: templates/demo/statement.html:20 templates/demo/statement.html:36
#: templates/demo/statement.tex:48
msgid "Statement"
msgstr ""

#: lib/LedgerSMB/Report/Reconciliation/Summary.pm:142
#: UI/reconciliation/new_report.html:24
msgid "Statement Balance"
msgstr ""

#: lib/LedgerSMB/Report/Reconciliation/Summary.pm:138
#: UI/reconciliation/new_report.html:32 UI/reconciliation/report.html:38
msgid "Statement Date"
msgstr ""

#: UI/Reports/filters/reconciliation_search.html:9
msgid "Statement Date From"
msgstr ""

#: UI/Reports/filters/reconciliation_search.html:16
msgid "Statement Date To"
msgstr ""

#: lib/LedgerSMB/Scripts/report_aging.pm:154
#: lib/LedgerSMB/Scripts/report_aging.pm:201
#: UI/Reports/filters/search_goods.html:25
msgid "Status"
msgstr ""

#: UI/journal/journal_entry.html:139
msgid "Status:"
msgstr ""

#: old/bin/ic.pl:601
msgid "Stock"
msgstr ""

#: old/bin/ic.pl:1977 old/bin/ic.pl:2084 locale/menu.xml:493
msgid "Stock Assembly"
msgstr ""

#: UI/users/preferences.html:15
msgid "Strength"
msgstr ""

#: UI/users/preferences.html:109
msgid "Stylesheet"
msgstr ""

#: t/data/04-complex_template.html:231
msgid "Subcontract GIFI:"
msgstr ""

#: UI/Contact/divs/notes.html:46 UI/Contact/divs/notes.html:47
#: UI/budgetting/budget_entry.html:146 UI/budgetting/budget_entry.html:174
#: UI/email.html:54
msgid "Subject"
msgstr ""

#: UI/Contact/divs/notes.html:67
msgid "Subject: [_1]"
msgstr ""

#: UI/Reports/filters/reconciliation_search.html:67
msgid "Submission Status"
msgstr ""

#: UI/reconciliation/report.html:433
msgid "Submit"
msgstr ""

#: lib/LedgerSMB/Report/Reconciliation/Summary.pm:149
#: UI/Reports/filters/reconciliation_search.html:62
msgid "Submitted"
msgstr ""

#: old/bin/io.pl:1240 old/bin/ir.pl:685 old/bin/is.pl:784 old/bin/oe.pl:771
#: UI/Reports/filters/gl.html:336
#: UI/Reports/filters/invoice_outstanding.html:269
#: UI/Reports/filters/invoice_search.html:416
#: UI/Reports/filters/orders.html:213 UI/Reports/filters/search_goods.html:436
#: UI/payments/payment2.html:331 UI/payments/payment2.html:475
#: UI/payments/use_overpayment2.html:138 UI/payments/use_overpayment2.html:327
#: templates/demo/ap_transaction.html:107
#: templates/demo/ar_transaction.html:105 templates/demo/invoice.html:134
#: templates/demo/invoice.tex:189 templates/demo/product_receipt.html:122
#: templates/demo/purchase_order.html:124 templates/demo/sales_order.html:127
#: templates/demo/sales_quotation.html:109
#: templates/demo/sales_quotation.tex:122
msgid "Subtotal"
msgstr ""

#: UI/Configuration/sequence.html:13
msgid "Suffix"
msgstr ""

#: old/bin/pe.pl:801 UI/Reports/filters/aging.html:56
#: UI/Reports/filters/invoice_outstanding.html:110
#: UI/Reports/filters/purchase_history.html:214
msgid "Summary"
msgstr ""

#: UI/accounts/edit.html:208
msgid "Summary account for"
msgstr ""

#: lib/LedgerSMB/Report/Timecards.pm:110
msgid "Sun"
msgstr ""

#: UI/Contact/divs/person.html:87
msgid "Surname"
msgstr ""

#: locale/menu.xml:670
msgid "System"
msgstr ""

#: UI/Configuration/settings.html:8
msgid "System Defaults"
msgstr ""

#: UI/setup/confirm_operation.html:118
msgid "System Info"
msgstr ""

#: UI/setup/system_info.html:8
msgid "System Information"
msgstr ""

#: UI/setup/confirm_operation.html:111
msgid "System diagnostics"
msgstr ""

#: templates/demo/printPayment.html:62
msgid "TOTAL"
msgstr ""

#: lib/LedgerSMB/Report/Assets/Net_Book_Value.pm:75
#: lib/LedgerSMB/Report/Listings/Asset.pm:126
#: lib/LedgerSMB/Report/Listings/Asset.pm:88
#: lib/LedgerSMB/Scripts/asset.pm:756 lib/LedgerSMB/Scripts/asset.pm:865
#: lib/LedgerSMB/Scripts/asset.pm:973
msgid "Tag"
msgstr ""

#: UI/asset/edit_asset.html:36 UI/asset/search_asset.html:36
msgid "Tag:"
msgstr ""

#: lib/LedgerSMB/Report/COA.pm:114 lib/LedgerSMB/Report/Contact/Purchase.pm:89
#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:240
#: lib/LedgerSMB/Report/Invoices/Transactions.pm:284 old/bin/ic.pl:513
#: old/bin/ic.pl:642 UI/Reports/filters/invoice_outstanding.html:199
#: UI/Reports/filters/invoice_search.html:347
#: UI/Reports/filters/orders.html:198 UI/accounts/edit.html:292
#: UI/accounts/edit.html:357 UI/accounts/edit.html:408
#: UI/accounts/edit.html:457 UI/accounts/edit.html:496
msgid "Tax"
msgstr ""

#: UI/Reports/filters/invoice_search.html:73
msgid "Tax Account"
msgstr ""

#: old/bin/ir.pl:602 old/bin/is.pl:714
msgid "Tax Code"
msgstr ""

#: lib/LedgerSMB/Report/Taxform/Details.pm:118
#: lib/LedgerSMB/Report/Taxform/Summary.pm:127 UI/Contact/divs/credit.html:282
#: UI/Contact/divs/credit.html:283
msgid "Tax Form"
msgstr ""

#: old/bin/aa.pl:816
msgid "Tax Form Applied"
msgstr ""

#: lib/LedgerSMB/Report/Taxform/Details.pm:129
msgid "Tax Form Details Report"
msgstr ""

#: lib/LedgerSMB/Report/Taxform/List.pm:71
msgid "Tax Form List"
msgstr ""

#: lib/LedgerSMB/Report/Taxform/Summary.pm:137
msgid "Tax Form Report"
msgstr ""

#: UI/Reports/filters/taxforms.html:15
msgid "Tax Form Reports"
msgstr ""

#: locale/menu.xml:595
msgid "Tax Forms"
msgstr ""

#: lib/LedgerSMB/Report/Taxform/Summary.pm:97 old/bin/aa.pl:728
#: old/bin/ir.pl:414 old/bin/is.pl:472 UI/Reports/filters/taxforms.html:101
msgid "Tax ID"
msgstr ""

#: UI/Contact/divs/employee.html:121 UI/setup/new_user.html:145
msgid "Tax ID/SSN"
msgstr ""

#: old/bin/ir.pl:589 old/bin/is.pl:700 old/bin/oe.pl:743
#: UI/Contact/divs/credit.html:334 UI/Contact/divs/credit.html:335
msgid "Tax Included"
msgstr ""

#: UI/Contact/divs/company.html:85
msgid "Tax Number/SSN"
msgstr ""

#: t/data/04-complex_template.html:222
msgid "Tax Number/SSN:"
msgstr ""

#: templates/demo/ar_transaction.tex:87
msgid "Tax Number: [_1]"
msgstr ""

#: UI/am-taxes.html:19
msgid "Tax Rules"
msgstr ""

#: UI/Reports/filters/invoice_search.html:63
msgid "Tax Status"
msgstr ""

#: templates/demo/product_receipt.html:147
#: templates/demo/purchase_order.html:149
msgid "Tax included"
msgstr ""

#: UI/am-taxes.html:80
msgid ""
"Tax items can be added to the list on this page by checking the \"Tax\" "
"checkmark on the account configuration page."
msgstr ""

#: old/bin/io.pl:258
msgid "TaxForm"
msgstr ""

#: UI/Reports/filters/invoice_search.html:66
msgid "Taxable"
msgstr ""

#: old/bin/am.pl:127 UI/Contact/divs/credit.html:307 locale/menu.xml:688
msgid "Taxes"
msgstr ""

#: old/bin/am.pl:258
msgid "Taxes saved!"
msgstr ""

#: templates/demo/ap_transaction.html:178
#: templates/demo/ar_transaction.html:178 templates/demo/invoice.html:236
msgid "Taxes shown are included in price."
msgstr ""

#: templates/demo/ar_transaction.html:50
msgid "Taxnumber:"
msgstr ""

#: templates/demo/ap_transaction.html:51
msgid "Taxnumber: [_1]"
msgstr ""

#: templates/demo/ar_transaction.html:47 templates/demo/ar_transaction.tex:42
#: templates/demo/letterhead.tex:48
msgid "Tel:"
msgstr ""

#: templates/demo/ap_transaction.html:48 templates/demo/ar_transaction.tex:77
#: templates/demo/bin_list.html:51 templates/demo/bin_list.html:62
#: templates/demo/invoice.html:56 templates/demo/invoice.html:69
#: templates/demo/letterhead.html:47 templates/demo/packing_list.html:52
#: templates/demo/pick_list.html:52 templates/demo/printPayment.html:34
#: templates/demo/product_receipt.html:51
#: templates/demo/product_receipt.html:61
#: templates/demo/product_receipt.tex:68
#: templates/demo/product_receipt.tex:103
#: templates/demo/purchase_order.html:51 templates/demo/purchase_order.html:61
#: templates/demo/purchase_order.tex:68 templates/demo/purchase_order.tex:103
#: templates/demo/request_quotation.html:51
#: templates/demo/request_quotation.html:61
#: templates/demo/request_quotation.tex:68
#: templates/demo/request_quotation.tex:103 templates/demo/sales_order.html:52
#: templates/demo/sales_order.html:63 templates/demo/sales_order.tex:67
#: templates/demo/sales_order.tex:102 templates/demo/sales_quotation.html:48
#: templates/demo/sales_quotation.tex:73 templates/demo/work_order.html:51
#: templates/demo/work_order.html:62 templates/demo/work_order.tex:76
#: templates/demo/work_order.tex:111
msgid "Tel: [_1]"
msgstr ""

#: UI/templates/widget.html:21
msgid "Template"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Templates.pm:82
msgid "Template Listing"
msgstr ""

#: old/bin/aa.pl:1307 old/bin/gl.pl:499
msgid "Template Saved!"
msgstr ""

#: lib/LedgerSMB/Report/Listings/TemplateTrans.pm:139
msgid "Template Transactions"
msgstr ""

#: UI/setup/confirm_operation.html:78 UI/setup/template_info.html:25
#: locale/menu.xml:725
msgid "Templates"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:78
msgid "Ten"
msgstr ""

#: old/bin/oe.pl:403
msgid "Terms"
msgstr ""

#: templates/demo/product_receipt.html:142
#: templates/demo/purchase_order.html:144 templates/demo/sales_order.html:144
#: templates/demo/sales_quotation.html:125
msgid "Terms Net [_1] days"
msgstr ""

#: t/data/04-complex_template.html:210
msgid "Terms:"
msgstr ""

#: templates/demo/sales_order.tex:177
msgid "Terms: Net [_1]  days"
msgstr ""

#: templates/demo/sales_quotation.tex:142
msgid "Terms: [_1] days"
msgstr ""

#: templates/demo/invoice.tex:250 templates/demo/sales_order.tex:191
msgid "Thank You for your valued business!"
msgstr ""

#: templates/demo/invoice.html:210
msgid "Thank you for your valued business!"
msgstr ""

#: templates/demo/printPayment.html:87
msgid "The amount of"
msgstr ""

#: UI/email.html:139
msgid ""
"The e-mail body may contain variables to be expanded - e.g. <%invnumber%> - "
"the table below specifies the available variables and their expansions for "
"this e-mail"
msgstr ""

#: lib/LedgerSMB/Scripts/recon.pm:502
msgid "Their Balance"
msgstr ""

#: UI/reconciliation/report.html:269
msgid "Their Credits"
msgstr ""

#: UI/reconciliation/report.html:268
msgid "Their Debits"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:543
msgid ""
"There are NULL values in the amounts column of your\n"
"source database. Please either find professional help to migrate your\n"
"database, or delete the offending rows through PgAdmin III or psql"
msgstr ""

#: UI/reconciliation/report.html:11
msgid ""
"There are [_1] unapproved reconciliations before the end date of this "
"report."
msgstr ""

#: UI/reconciliation/report.html:8
msgid ""
"There are [_1] unapproved transactions before the end date of this report."
msgstr ""

#: UI/payments/use_overpayment1.html:58
msgid "There is no customer with overpayments"
msgstr ""

#: UI/payments/use_overpayment1.html:56
msgid "There is no vendor with overpayments"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:82
msgid "Thirteen"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:100
msgid "Thirty"
msgstr ""

#: UI/Contact/divs/address.html:83 UI/Contact/divs/contact_info.html:28
msgid "This Account"
msgstr ""

#: UI/setup/complete_migration_revert.html:10
msgid "This database operation has completed successfully."
msgstr ""

#: UI/setup/complete.html:14
msgid ""
"This database operation has completed successfully.  LedgerSMB may now be "
"used."
msgstr ""

#: templates/demo/printPayment.html:91
msgid "This document has been approved by"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:1280
msgid "This gl movement, is a consecuence of a payment transaction"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:2017
msgid "This gl movement, is the result of a overpayment transaction"
msgstr ""

#: UI/journal/journal_entry.html:125
msgid "This transaction has been reversed by transaction [_1] with ID [_2]"
msgstr ""

#: old/bin/aa.pl:764 old/bin/ir.pl:444 old/bin/is.pl:536
msgid "This transaction is reversed by transaction [_1] with ID [_2]"
msgstr ""

#: UI/journal/journal_entry.html:131
msgid "This transaction reverses transaction [_1] with ID [_2]"
msgstr ""

#: old/bin/aa.pl:763 old/bin/ir.pl:443 old/bin/is.pl:535
#: UI/journal/journal_entry.html:133
msgid "This transaction will reverse transaction [_1] with ID [_2]"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1231
msgid ""
"This will <b>keep</b> the transactions but will <b>ignore</b> the non-"
"necessary reconciliations"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:855
msgid ""
"This will <b>remove</b> the business references in <u>vendor</u> and "
"<u>customer</u> tables"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:111
msgid "Thousand"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:71
msgid "Three"
msgstr ""

#: UI/Contact/divs/credit.html:156 UI/Contact/divs/credit.html:157
#: t/data/04-complex_template.html:302
msgid "Threshold"
msgstr ""

#: UI/Reports/filters/balance_sheet.html:155
msgid "Through date"
msgstr ""

#: lib/LedgerSMB/Report/Timecards.pm:126
msgid "Thu"
msgstr ""

#: old/lib/LedgerSMB/IIAA.pm:332 UI/setup/complete.html:41
#: UI/setup/db-patches-log.html:18
msgid "Time"
msgstr ""

#: templates/demo/timecard.html:16 templates/demo/timecard.html:30
#: templates/demo/timecard.tex:33
msgid "Time Card"
msgstr ""

#: UI/timecards/entry_filter.html:22
msgid "Time Frame"
msgstr ""

#: UI/timecards/timecard.html:67
msgid "Time In"
msgstr ""

#: UI/timecards/timecard.html:83
msgid "Time Out"
msgstr ""

#: UI/Reports/filters/timecards.html:5
msgid "Time and Material Cards"
msgstr ""

#: UI/timecards/timecard.html:14
msgid "Time or Materials Card"
msgstr ""

#: UI/templates/widget.html:77
msgid "Timecard"
msgstr ""

#: UI/timecards/entry_filter.html:5 UI/timecards/entry_filter.html:6
msgid "Timecard Criteria"
msgstr ""

#: lib/LedgerSMB/Report/Timecards.pm:161 locale/menu.xml:553
msgid "Timecards"
msgstr ""

#: old/bin/am.pl:315
msgid "Times"
msgstr ""

#: UI/Reports/filters/balance_sheet.html:107
msgid "Timing"
msgstr ""

#: old/bin/ic.pl:1100 old/bin/oe.pl:2183 old/bin/pe.pl:741
#: UI/Contact/divs/credit.html:462 UI/Reports/co/filter_bm.html:26
#: UI/Reports/co/filter_cd.html:44 UI/Reports/filters/aging.html:33
#: UI/Reports/filters/income_statement.html:137
#: UI/Reports/filters/income_statement.html:180
#: UI/Reports/filters/purchase_history.html:123
#: UI/Reports/filters/purchase_history.html:141
#: UI/Reports/filters/search_goods.html:234 UI/asset/begin_approval.html:30
#: UI/email.html:27 UI/lib/report_base.html:123 UI/lib/report_base.html:213
#: UI/payments/payments_detail.html:122 templates/demo/invoice.html:43
#: templates/demo/request_quotation.tex:43 templates/demo/sales_order.html:40
#: templates/demo/work_order.html:39
msgid "To"
msgstr ""

#: lib/LedgerSMB/Report/Reconciliation/Summary.pm:186
msgid "To Amount"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Activity.pm:168
#: lib/LedgerSMB/Report/Invoices/Payments.pm:189
#: lib/LedgerSMB/Report/Reconciliation/Summary.pm:182
#: lib/LedgerSMB/Report/Taxform/Details.pm:117
#: lib/LedgerSMB/Report/Taxform/Summary.pm:126
#: lib/LedgerSMB/Report/Timecards.pm:149
#: lib/LedgerSMB/Report/Trial_Balance.pm:209
#: lib/LedgerSMB/Report/Unapproved/Drafts.pm:150
#: UI/Reports/filters/unapproved.html:42
msgid "To Date"
msgstr ""

#: UI/payments/payments_detail.html:336
msgid "To Pay"
msgstr ""

#: old/bin/oe.pl:2198
msgid "To Warehouse"
msgstr ""

#: t/data/04-strings-for-translation.html:7
msgid "To [_1]"
msgstr ""

#: UI/payments/use_overpayment2.html:124
msgid "To be used"
msgstr ""

#: UI/setup/begin_backup.html:28 UI/setup/begin_backup.html:42
msgid "To email"
msgstr ""

#: UI/setup/begin_backup.html:60
msgid "To my browser"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:1001
msgid "To pay"
msgstr ""

#: templates/demo/product_receipt.html:39
#: templates/demo/purchase_order.html:39
#: templates/demo/request_quotation.html:39
msgid "To:"
msgstr ""

#: UI/payments/payment2.html:166
msgid "Toggle all removal checkboxes"
msgstr ""

#: lib/LedgerSMB/Report/Aging.pm:166
#: lib/LedgerSMB/Report/Inventory/History.pm:163
#: lib/LedgerSMB/Report/Inventory/Search.pm:324
#: lib/LedgerSMB/Report/Invoices/COGS.pm:122
#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:245
#: lib/LedgerSMB/Report/Invoices/Transactions.pm:288
#: lib/LedgerSMB/Report/Taxform/Details.pm:105
#: lib/LedgerSMB/Report/Taxform/Summary.pm:114
#: lib/LedgerSMB/Scripts/asset.pm:680 lib/LedgerSMB/Scripts/payment.pm:943
#: old/bin/ir.pl:762 old/bin/is.pl:876 old/bin/oe.pl:807
#: UI/Reports/filters/invoice_outstanding.html:206
#: UI/Reports/filters/invoice_search.html:353 UI/payments/payment1.html:95
#: UI/payments/payment1.html:105 UI/payments/payment2.html:480
#: UI/payments/payment2.html:489 UI/payments/payments_detail.html:332
#: UI/reconciliation/report.html:230 UI/reconciliation/report.html:302
#: UI/reconciliation/report.html:375 templates/demo/ap_transaction.html:104
#: templates/demo/ap_transaction.html:119
#: templates/demo/ar_transaction.html:102
#: templates/demo/ar_transaction.html:117 templates/demo/invoice.html:144
#: templates/demo/invoice.tex:198 templates/demo/product_receipt.html:120
#: templates/demo/product_receipt.html:138
#: templates/demo/product_receipt.html:143
#: templates/demo/purchase_order.html:122
#: templates/demo/purchase_order.html:140
#: templates/demo/purchase_order.html:145 templates/demo/sales_order.html:125
#: templates/demo/sales_order.html:145 templates/demo/sales_quotation.html:107
#: templates/demo/sales_quotation.html:127
#: templates/demo/sales_quotation.tex:129 templates/demo/statement.html:71
#: templates/demo/statement.html:87 templates/demo/statement.tex:58
#: templates/demo/statement.tex:63 templates/demo/timecard.html:111
#: templates/demo/timecard.tex:61
msgid "Total"
msgstr ""

#: lib/LedgerSMB/Scripts/asset.pm:814
msgid "Total Accum. Dep."
msgstr ""

#: lib/LedgerSMB/Report/Invoices/Payments.pm:132
msgid "Total Paid"
msgstr ""

#: old/bin/aa.pl:948 old/bin/ir.pl:773 old/bin/is.pl:885
msgid "Total paid"
msgstr ""

#: UI/accounts/edit.html:433
msgid "Tracking Items"
msgstr ""

#: old/bin/is.pl:358 old/bin/oe.pl:417
msgid "Trade Discount"
msgstr ""

#: old/bin/aa.pl:1116 old/bin/am.pl:995
msgid "Transaction"
msgstr ""

#: locale/menu.xml:245
msgid "Transaction Approval"
msgstr ""

#: lib/LedgerSMB/Report/PNL/Invoice.pm:83
#: UI/Reports/filters/purchase_history.html:127
#: UI/Reports/filters/purchase_history.html:338
msgid "Transaction Date"
msgstr ""

#: old/bin/gl.pl:711
msgid "Transaction Date missing!"
msgstr ""

#: old/bin/pe.pl:736
msgid "Transaction Dates"
msgstr ""

#: locale/menu.xml:590
msgid "Transaction Templates"
msgstr ""

#: lib/LedgerSMB/Report/Unapproved/Batch_Overview.pm:189
#: UI/Reports/filters/batches.html:12 UI/Reports/filters/unapproved.html:20
#: UI/reconciliation/report.html:126 UI/reconciliation/report.html:261
msgid "Transaction Type"
msgstr ""

#: UI/Reports/filters/gl.html:114
msgid "Transaction status"
msgstr ""

#: lib/LedgerSMB/Report/Taxform/Summary.pm:105
#: UI/setup/confirm_operation.html:150
msgid "Transactions"
msgstr ""

#: old/bin/oe.pl:2187 old/bin/oe.pl:2303 locale/menu.xml:217
#: locale/menu.xml:355
msgid "Transfer"
msgstr ""

#: old/bin/oe.pl:2069 old/bin/oe.pl:2214
msgid "Transfer Inventory"
msgstr ""

#: old/bin/oe.pl:2087
msgid "Transfer from"
msgstr ""

#: old/bin/oe.pl:2091
msgid "Transfer to"
msgstr ""

#: old/bin/pe.pl:301 old/bin/pe.pl:471
msgid "Translation"
msgstr ""

#: old/bin/pe.pl:71
msgid "Translation deleted!"
msgstr ""

#: UI/accounts/edit.html:594 locale/menu.xml:513 locale/menu.xml:577
msgid "Translations"
msgstr ""

#: old/bin/pe.pl:55
msgid "Translations saved!"
msgstr ""

#: lib/LedgerSMB/Report/Trial_Balance.pm:133 locale/menu.xml:412
msgid "Trial Balance"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:114
msgid "Trillion"
msgstr ""

#: lib/LedgerSMB/Report/Timecards.pm:118
msgid "Tue"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:81
msgid "Twelve"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:89
msgid "Twenty"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:98
msgid "Twenty Eight"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:95
msgid "Twenty Five"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:94
msgid "Twenty Four"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:99
msgid "Twenty Nine"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:90
msgid "Twenty One"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:91
msgid "Twenty One-o"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:97
msgid "Twenty Seven"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:96
msgid "Twenty Six"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:93
msgid "Twenty Three"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:92
msgid "Twenty Two"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:70
msgid "Two"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/History.pm:149
#: lib/LedgerSMB/Report/Unapproved/Batch_Overview.pm:128
#: lib/LedgerSMB/Report/Unapproved/Drafts.pm:85
#: lib/LedgerSMB/Scripts/asset.pm:658 old/bin/io.pl:1666
#: UI/Contact/divs/address.html:84 UI/Contact/divs/address.html:122
#: UI/Contact/divs/contact_info.html:29 UI/Contact/divs/credit.html:29
#: UI/Contact/divs/wage.html:7 UI/Reports/filters/search_goods.html:13
#: t/data/04-complex_template.html:384
msgid "Type"
msgstr ""

#: locale/menu.xml:14
msgid "Type of Business"
msgstr ""

#: t/data/04-complex_template.html:411 t/data/04-complex_template.html:494
#: t/data/04-complex_template.html:526
msgid "Type:"
msgstr ""

#: UI/payments/use_overpayment2.html:347
msgid "UPDATE"
msgstr ""

#: UI/Reports/filters/batches.html:30 UI/Reports/filters/gl.html:135
#: UI/Reports/filters/invoice_search.html:230
#: UI/Reports/filters/trial_balance.html:62
msgid "Unapproved"
msgstr ""

#: UI/payments/payment2.html:314
msgid "Uncheck to remove entry from payment"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:837
msgid ""
"Undefined businesses.<br>\n"
"Please make sure business used by vendors and constomers are defined.<br>\n"
"<i><b>Hover on buttons</b> to see their effects and impacts</i>"
msgstr ""

#: lib/LedgerSMB/Scripts/template.pm:119
msgid "Unexpected file name, expected [_1], got [_2]"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:521
msgid "Unique AR Invoice numbers"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:418
msgid "Unique Customernumber"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:437
msgid "Unique Vendornumber"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1050 lib/LedgerSMB/Upgrade_Tests.pm:505
msgid "Unique nonobsolete partnumbers"
msgstr ""

#: lib/LedgerSMB/Report/Contact/History.pm:92
#: lib/LedgerSMB/Report/Inventory/History.pm:133
#: lib/LedgerSMB/Report/Inventory/Search.pm:231 old/bin/ic.pl:1238
#: old/bin/ic.pl:768 old/bin/io.pl:247 old/bin/oe.pl:1903
#: UI/Reports/filters/purchase_history.html:289
#: UI/Reports/filters/search_goods.html:287 UI/payroll/income.html:68
#: templates/demo/invoice.tex:169 templates/demo/product_receipt.tex:135
#: templates/demo/purchase_order.tex:135 templates/demo/sales_order.tex:136
#: templates/demo/sales_quotation.tex:104
msgid "Unit"
msgstr ""

#: templates/demo/invoice.html:113 templates/demo/request_quotation.tex:140
msgid "Unit Price"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1154
msgid "Unknown "
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1136
msgid ""
"Unknown account category (should be "
"A(sset)/L(iability)/E(xpense)/I(ncome)/(e)Q(uity))"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1118
msgid "Unknown charttype; should be H(eader)/A(ccount)"
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:321
msgid "Unknown database found."
msgstr ""

#: lib/LedgerSMB/Report/Unapproved/Batch_Overview.pm:251
msgid "Unlock"
msgstr ""

#: lib/LedgerSMB/Report/Unapproved/Batch_Detail.pm:216
msgid "Unlock Batch"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:1220
msgid "Unneeded Reconciliations"
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:254
msgid "Unsupported LedgerSMB version detected."
msgstr ""

#: UI/Reports/filters/payments.html:16
msgid "Unsupported Number"
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:187
msgid "Unsupported SQL-Ledger version detected."
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:752
msgid "Unsupported account categories"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:772
msgid "Unsupported account link combinations"
msgstr ""

#: lib/LedgerSMB/Scripts/contact.pm:481
msgid "Unsupported account type"
msgstr ""

#: UI/Reports/filters/contact_search.html:15
msgid "Unsupported.  Expect errors"
msgstr ""

#: UI/Reports/filters/search_goods.html:33
msgid "Unused"
msgstr ""

#: UI/Reports/filters/invoice_search.html:251
msgid "Unvoided"
msgstr ""

#: UI/payments/payment2.html:258
msgid "Up"
msgstr ""

#: lib/LedgerSMB/Scripts/budgets.pm:97 old/bin/aa.pl:1105 old/bin/ic.pl:810
#: old/bin/ic.pl:817 old/bin/ir.pl:918 old/bin/is.pl:1024 old/bin/oe.pl:2000
#: old/bin/pe.pl:513 UI/Contact/divs/credit.html:85 UI/am-taxes.html:71
#: UI/payments/payment2.html:500 UI/payments/payments_detail.html:477
#: UI/payroll/deduction.html:85 UI/payroll/income.html:90
#: UI/reconciliation/report.html:413
msgid "Update"
msgstr ""

#: old/bin/ic.pl:761
msgid "Updated"
msgstr ""

#: UI/setup/upgrade_info.html:99
msgid "Upgrade"
msgstr ""

#: UI/setup/upgrade_info.html:8
msgid "Upgrade Info"
msgstr ""

#: UI/setup/complete.html:38 UI/setup/db-patches-log.html:15
msgid "Upgrade run log"
msgstr ""

#: UI/Configuration/rate.html:85 UI/file/attachment_screen.html:45
#: UI/file/internal-file-list.html:67 UI/templates/widget.html:134
msgid "Upload"
msgstr ""

#: UI/reconciliation/report.html:92
msgid "Upload file format"
msgstr ""

#: UI/Configuration/rate.html:87 UI/file/attachment_screen.html:47
#: UI/file/internal-file-list.html:69
msgid "Uploaded"
msgstr ""

#: lib/LedgerSMB/Report/File/Incoming.pm:59
#: lib/LedgerSMB/Report/File/Internal.pm:57
msgid "Uploaded At"
msgstr ""

#: lib/LedgerSMB/Report/File/Incoming.pm:62
#: lib/LedgerSMB/Report/File/Internal.pm:60
msgid "Uploaded By"
msgstr ""

#: lib/LedgerSMB/Scripts/currency.pm:400
msgid "Uploaded rates"
msgstr ""

#: UI/Configuration/rate.html:86 UI/file/attachment_screen.html:46
#: UI/file/internal-file-list.html:68
msgid "Uploading..."
msgstr ""

#: UI/file/attachment_screen.html:23 UI/file/internal-file-list.html:50
msgid "Url"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Asset.pm:101 UI/asset/edit_asset.html:90
#: UI/asset/search_asset.html:85
msgid "Usable Life"
msgstr ""

#: locale/menu.xml:170
msgid "Use AR Overpayment"
msgstr ""

#: UI/payments/use_overpayment1.html:8 locale/menu.xml:180
msgid "Use Overpayment"
msgstr ""

#: old/bin/io.pl:1749
msgid "Use Shipto"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:1895
msgid "Use overpayment/prepayment"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:147
msgid "Use web service for current exchange rates"
msgstr ""

#: lib/LedgerSMB/Report/Budget/Variance.pm:89
#: lib/LedgerSMB/Report/Inventory/Activity.pm:132
msgid "Used"
msgstr ""

#: lib/LedgerSMB/Scripts/contact.pm:205 UI/Contact/divs/user.html:7
#: t/data/04-complex_template.html:36
msgid "User"
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:1368
msgid "User already exists. Import?"
msgstr ""

#: UI/Contact/divs/user.html:73 UI/setup/new_user.html:36
msgid "User creation"
msgstr ""

#: lib/LedgerSMB/Report/Listings/User.pm:57 UI/Contact/divs/user.html:33
#: UI/Contact/divs/user.html:53 UI/setup/edit_user.html:30
#: UI/setup/edit_user.html:43 UI/setup/list_users.html:11
#: UI/setup/new_user.html:22 UI/setup/ui-db-credentials.html:4
msgid "Username"
msgstr ""

#: lib/LedgerSMB/Report/Listings/User.pm:81 UI/setup/complete.html:98
#: UI/setup/confirm_operation.html:51 UI/setup/confirm_operation.html:151
#: locale/menu.xml:735
msgid "Users"
msgstr ""

#: lib/LedgerSMB/Scripts/currency.pm:254 UI/Contact/pricelist.csv:34
#: UI/Contact/pricelist.html:46 UI/Contact/pricelist.tex:46
msgid "Valid From"
msgstr ""

#: UI/Contact/pricelist.csv:37 UI/Contact/pricelist.html:49
#: UI/Contact/pricelist.tex:50 UI/am-taxes.html:17
msgid "Valid To"
msgstr ""

#: UI/Configuration/rate.html:43
msgid "Valid from"
msgstr ""

#: old/bin/oe.pl:497 templates/demo/sales_quotation.html:61
#: templates/demo/sales_quotation.tex:92
msgid "Valid until"
msgstr ""

#: UI/Reports/filters/income_statement.html:20
msgid "Valuation"
msgstr ""

#: UI/email.html:142
msgid "Variable"
msgstr ""

#: lib/LedgerSMB/Report/Budget/Variance.pm:94
#: lib/LedgerSMB/Report/Inventory/Adj_Details.pm:107
msgid "Variance"
msgstr ""

#: UI/reconciliation/report.html:61
msgid "Variance:"
msgstr ""

#: lib/LedgerSMB/Report/Aging.pm:86
#: lib/LedgerSMB/Report/Invoices/Outstanding.pm:193
#: lib/LedgerSMB/Report/Invoices/Transactions.pm:247
#: lib/LedgerSMB/Report/Orders.pm:182 lib/LedgerSMB/Report/Orders.pm:188
#: old/bin/aa.pl:649 old/bin/ic.pl:996 old/bin/ir.pl:374 old/bin/oe.pl:2453
#: old/bin/oe.pl:2604 old/bin/pe.pl:1068 old/bin/pe.pl:918
#: UI/Contact/divs/credit.html:11 UI/Contact/divs/credit.html:12
#: UI/Reports/filters/aging.html:13
#: UI/Reports/filters/invoice_outstanding.html:5
#: UI/Reports/filters/invoice_search.html:5 UI/Reports/filters/orders.html:4
#: UI/payments/payments_detail.html:4 UI/payments/payments_filter.html:36
#: UI/payments/use_overpayment1.html:26 UI/payments/use_overpayment2.html:21
#: UI/payments/use_overpayment2.html:149 sql/Pg-database.sql:237
msgid "Vendor"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/Transactions.pm:246
msgid "Vendor Account"
msgstr ""

#: lib/LedgerSMB/Report/GL.pm:112
msgid "Vendor Customer"
msgstr ""

#: locale/menu.xml:157
msgid "Vendor History"
msgstr ""

#: UI/Contact/divs/credit.html:367 t/data/04-complex_template.html:334
#: locale/menu.xml:109
msgid "Vendor Invoice"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:116
msgid "Vendor Invoice/AP Transaction Number"
msgstr ""

#: UI/Reports/filters/orders.html:5 UI/payments/payment1.html:43
#: UI/payments/payment2.html:27
msgid "Vendor Name"
msgstr ""

#: lib/LedgerSMB/Report/Invoices/Payments.pm:114
#: lib/LedgerSMB/Report/Invoices/Payments.pm:177
#: lib/LedgerSMB/Scripts/configuration.pm:124 old/bin/io.pl:1530
#: UI/Reports/filters/invoice_outstanding.html:6
#: UI/Reports/filters/invoice_search.html:6
#: UI/Reports/filters/overpayments.html:19 UI/Reports/filters/payments.html:15
#: UI/Reports/filters/purchase_history.html:54
#: UI/Reports/filters/taxforms.html:34 UI/asset/edit_asset.html:193
#: UI/asset/search_asset.html:165 UI/payments/payments_filter.html:35
msgid "Vendor Number"
msgstr ""

#: UI/Contact/pricelist.csv:20 UI/Contact/pricelist.html:34
#: UI/Contact/pricelist.tex:29
msgid "Vendor Partnumber"
msgstr ""

#: old/bin/ic.pl:998
msgid "Vendor Reference Number"
msgstr ""

#: old/bin/aa.pl:1482 old/bin/ir.pl:1253 old/bin/oe.pl:1194
msgid "Vendor missing!"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:867
msgid "Vendor not in a business"
msgstr ""

#: old/bin/arap.pl:143 old/bin/pe.pl:1122
msgid "Vendor not on file!"
msgstr ""

#: lib/LedgerSMB/Report/Unapproved/Drafts.pm:105
#: UI/Reports/filters/gl.html:241
msgid "Vendor/Customer"
msgstr ""

#: UI/users/preferences.html:15
msgid "Verify"
msgstr ""

#: UI/setup/confirm_operation.html:132
msgid "View schema patch log"
msgstr ""

#: UI/Reports/filters/invoice_search.html:245
msgid "Voided"
msgstr ""

#: lib/LedgerSMB/Report/Unapproved/Batch_Detail.pm:147
msgid "Voucher List"
msgstr ""

#: locale/menu.xml:63 locale/menu.xml:129 locale/menu.xml:185
msgid "Vouchers"
msgstr ""

#: UI/Contact/divs/wage.html:2
msgid "Wages and Deductions"
msgstr ""

#: lib/LedgerSMB/Scripts/contact.pm:206
msgid "Wages/Deductions"
msgstr ""

#: old/bin/oe.pl:1785 UI/Reports/filters/search_goods.html:415
#: templates/demo/bin_list.html:78 templates/demo/bin_list.tex:97
#: templates/demo/packing_list.html:69 templates/demo/packing_list.tex:105
#: templates/demo/pick_list.html:69 templates/demo/pick_list.tex:97
msgid "Warehouse"
msgstr ""

#: lib/LedgerSMB/Report/Listings/Warehouse.pm:58 locale/menu.xml:487
msgid "Warehouses"
msgstr ""

#: UI/oe-save-warn.html:4
msgid "Warning!"
msgstr ""

#: UI/users/preferences.html:57
msgid "Warning: Your password will expire in [_1] days"
msgstr ""

#: UI/users/preferences.html:53
msgid "Warning: Your password will expire in [_1] months"
msgstr ""

#: UI/users/preferences.html:55
msgid "Warning: Your password will expire in [_1] weeks"
msgstr ""

#: UI/users/preferences.html:51
msgid "Warning: Your password will expire in [_1] years"
msgstr ""

#: UI/users/preferences.html:59
msgid "Warning: Your password will expire today"
msgstr ""

#: lib/LedgerSMB/Report/Timecards.pm:122
msgid "Wed"
msgstr ""

#: old/bin/am.pl:272 UI/timecards/entry_filter.html:20
msgid "Week"
msgstr ""

#: lib/LedgerSMB/Report/Timecards.pm:85
msgid "Week Starting"
msgstr ""

#: old/bin/arap.pl:527
msgid "Week(s)"
msgstr ""

#: UI/timecards/timecard-week.html:49
msgid "Weekly Time and Materials Entry"
msgstr ""

#: old/bin/am.pl:273
msgid "Weeks"
msgstr ""

#: lib/LedgerSMB/Report/Inventory/Search.pm:243 old/bin/ic.pl:521
#: old/bin/ic.pl:548 old/bin/ic.pl:570
#: UI/Reports/filters/search_goods.html:366
msgid "Weight"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:58
msgid "Weight Unit"
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:378 lib/LedgerSMB/Upgrade_Tests.pm:600
msgid ""
"When customers are defined, an AR account must be defined,\n"
" however, your setup doesn't. Please go back and define one."
msgstr ""

#: lib/LedgerSMB/Upgrade_Tests.pm:399 lib/LedgerSMB/Upgrade_Tests.pm:621
msgid ""
"When vendors are defined, an AP account must be defined,\n"
" however, your setup doesn't. Please go back and define one."
msgstr ""

#: UI/setup/begin_backup.html:8
msgid "Where shall we send the backup?"
msgstr ""

#: sql/Pg-database.sql:2379
msgid "Whole Month Straight Line"
msgstr ""

#: lib/LedgerSMB/Scripts/configuration.pm:134
msgid "Widgit Themes"
msgstr ""

#: old/bin/am.pl:1002 old/bin/io.pl:1099 old/bin/oe.pl:256
#: old/bin/printer.pl:75 templates/demo/work_order.html:16
#: templates/demo/work_order.html:32 templates/demo/work_order.tex:124
msgid "Work Order"
msgstr ""

#: UI/templates/widget.html:79
msgid "Work order"
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:158
msgid "Would you like to migrate the database?"
msgstr ""

#: lib/LedgerSMB/Scripts/setup.pm:161
msgid "Would you like to upgrade the database?"
msgstr ""

#: UI/Contact/pricelist.html:111 UI/Reports/aging_report.html:105
#: UI/Reports/display_report.html:68
msgid "XLS"
msgstr ""

#: UI/Contact/pricelist.html:117 UI/Reports/aging_report.html:109
#: UI/Reports/display_report.html:72
msgid "XLSX"
msgstr ""

#: old/bin/am.pl:274 old/bin/pe.pl:728 UI/lib/report_base.html:181
#: UI/lib/report_base.html:269
msgid "Year"
msgstr ""

#: locale/menu.xml:401
msgid "Year End"
msgstr ""

#: old/bin/arap.pl:529
msgid "Year(s)"
msgstr ""

#: UI/accounts/yearend.html:22 UI/accounts/yearend.html:38
msgid "Yearend"
msgstr ""

#: UI/accounts/yearend_complete.html:3
msgid "Yearend complete"
msgstr ""

#: old/bin/am.pl:275
msgid "Years"
msgstr ""

#: lib/LedgerSMB/Report/Taxform/List.pm:100 old/bin/oe.pl:1353
#: UI/Configuration/settings.html:43 UI/setup/confirm_operation.html:30
msgid "Yes"
msgstr ""

#: UI/setup/mismatch.html:11
msgid ""
"Your database server version ([_1]) is incompatible with this LedgerSMB "
"version, which requires [_2]"
msgstr ""

#: t/data/04-complex_template.html:388
msgid "ZIP/Post Code"
msgstr ""

#: old/lib/LedgerSMB/Num2text.pm:67
msgid "Zero"
msgstr ""

#: UI/Contact/divs/address.html:88 UI/Contact/divs/address.html:161
msgid "Zip/Post Code"
msgstr ""

#: t/data/04-complex_template.html:463
msgid "Zip/Post Code:"
msgstr ""

#: UI/Reports/filters/contact_search.html:65
#: UI/Reports/filters/purchase_history.html:91
msgid "Zip/Postal Code"
msgstr ""

#: old/bin/io.pl:1585
msgid "Zipcode"
msgstr ""

#: lib/LedgerSMB/Report/PNL.pm:168
msgid ""
"[_1]\n"
"[_2]"
msgstr ""

#: old/bin/gl.pl:314
msgid "[_1] Cash Transfer Transaction"
msgstr ""

#: old/bin/aa.pl:521
msgid "[_1] Credit Note"
msgstr ""

#: old/bin/aa.pl:525
msgid "[_1] Debit Note"
msgstr ""

#: old/bin/gl.pl:317
msgid "[_1] General Ledger Transaction"
msgstr ""

#: old/bin/arap.pl:167
msgid "[_1] Number"
msgstr ""

#: old/bin/aa.pl:514
msgid "[_1] [_2] Transaction"
msgstr ""

#: old/bin/ic.pl:1621
msgid "[_1]: Customer not on file!"
msgstr ""

#: old/bin/ic.pl:1543
msgid "[_1]: Vendor not on file!"
msgstr ""

#: old/lib/LedgerSMB/oldHandler.pm:161
msgid "__action not defined!"
msgstr ""

#: UI/Reports/co/filter_bm.html:112
msgid "balance"
msgstr ""

#: UI/templates/widget.html:98
msgid "csv"
msgstr ""

#: old/bin/ic.pl:1058 old/bin/oe.pl:405 UI/Contact/divs/credit.html:180
#: UI/Contact/divs/credit.html:198 t/data/04-complex_template.html:215
#: t/data/04-complex_template.html:255
msgid "days"
msgstr ""

#: UI/Reports/co/filter_bm.html:98
msgid "debits"
msgstr ""

#: lib/LedgerSMB/Scripts/currency.pm:64
msgid "default"
msgstr ""

#: lib/LedgerSMB/Scripts/admin.pm:136 lib/LedgerSMB/Scripts/currency.pm:162
#: lib/LedgerSMB/Scripts/currency.pm:274 lib/LedgerSMB/Scripts/currency.pm:72
msgid "delete"
msgstr ""

#: sql/Pg-database.sql:2454
msgid "depreciation"
msgstr ""

#: sql/Pg-database.sql:2455
msgid "disposal"
msgstr ""

#: old/bin/am.pl:710 old/bin/am.pl:801 old/bin/am.pl:879 old/bin/am.pl:927
#: old/bin/am.pl:979
msgid "done"
msgstr ""

#: old/bin/am.pl:804 old/bin/am.pl:930 old/bin/am.pl:982
msgid "failed"
msgstr ""

#: UI/templates/widget.html:99
msgid "html"
msgstr ""

#: sql/Pg-database.sql:2456
msgid "import"
msgstr ""

#: sql/Pg-database.sql:2381
msgid "in months"
msgstr ""

#: lib/LedgerSMB/Scripts/currency.pm:159 lib/LedgerSMB/Scripts/currency.pm:69
msgid "in use"
msgstr ""

#: sql/Pg-database.sql:2378 sql/Pg-database.sql:2384
msgid "in years"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:2045
msgid "is bigger than the amount available"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:1746
msgid "is lesser than 0"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:1740
msgid "is lesser than the amount to be paid"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:2002
msgid "is null"
msgstr ""

#: UI/Configuration/sequence.html:9
msgid "label"
msgstr ""

#: sql/Pg-database.sql:2457
msgid "partial disposal"
msgstr ""

#: UI/Reports/aging_report.html:92 UI/Reports/display_report.html:53
msgid "permalink"
msgstr ""

#: sql/Pg-database.sql:2342
msgid "production"
msgstr ""

#: lib/LedgerSMB/Scripts/currency.pm:154
msgid "system type"
msgstr ""

#: UI/templates/widget.html:100
msgid "tex"
msgstr ""

#: sql/Pg-database.sql:2341
msgid "time"
msgstr ""

#: old/bin/arap.pl:556
msgid "time(s)"
msgstr ""

#: old/bin/ic.pl:1413
msgid "unexpected error!"
msgstr ""

#: lib/LedgerSMB/Scripts/payment.pm:2054 lib/LedgerSMB/Scripts/payment.pm:2073
msgid "use of an overpayment"
msgstr ""
