log4perl.rootlogger = ERROR, Basic, Debug, DebugPanel
log4perl.logger.LedgerSMB = INFO
log4perl.logger.LedgerSMB.DBObject = INFO

log4perl.appender.Screen = Log::Log4perl::Appender::Screen
log4perl.appender.Screen.layout = PatternLayout
log4perl.appender.Screen.layout.ConversionPattern = Req:%Z %p - %m%n

# Filter for debug level
log4perl.filter.MatchDebug = Log::Log4perl::Filter::LevelMatch
log4perl.filter.MatchDebug.LevelToMatch = INFO
log4perl.filter.MatchDebug.AcceptOnMatch = false

# Filter for everything but debug,trace level
log4perl.filter.MatchRest = Log::Log4perl::Filter::LevelMatch
log4perl.filter.MatchRest.LevelToMatch = INFO
log4perl.filter.MatchRest.AcceptOnMatch = true

# layout for DEBUG,TRACE messages
log4perl.appender.Debug = Log::Log4perl::Appender::Screen
log4perl.appender.Debug.layout = PatternLayout
log4perl.appender.Debug.layout.ConversionPattern = Req:%Z %d - %p - %l -- %m%n
log4perl.appender.Debug.Filter = MatchDebug

# layout for non-DEBUG messages
log4perl.appender.Basic = Log::Log4perl::Appender::Screen
log4perl.appender.Basic.layout = PatternLayout
log4perl.appender.Basic.layout.ConversionPattern = Req:%Z %d - %p - %M -- %m%n
log4perl.appender.Basic.Filter = MatchRest

log4perl.appender.DebugPanel              = Log::Log4perl::Appender::TestBuffer
log4perl.appender.DebugPanel.name         = psgi_debug_panel
log4perl.appender.DebugPanel.mode         = append
log4perl.appender.DebugPanel.layout       = PatternLayout
log4perl.appender.DebugPanel.layout.ConversionPattern = %r >> %p >> %m >> %c >> at %F line %L%n
#log4perl.appender.DebugPanel.Threshold = TRACE

