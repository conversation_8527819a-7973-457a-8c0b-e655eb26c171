{"abstract": "parser and builder for application/x-www-form-urlencoded", "author": ["<PERSON><PERSON><PERSON> <<EMAIL>>"], "dynamic_config": 0, "generated_by": "Module::Build version 0.4234", "license": ["perl_5"], "meta-spec": {"url": "http://search.cpan.org/perldoc?CPAN::Meta::Spec", "version": 2}, "name": "WWW-Form-UrlEncoded", "no_index": {"directory": ["t", "xt", "inc", "share", "eg", "examples", "author", "builder"]}, "prereqs": {"configure": {"requires": {"Module::Build": "0.4005"}}, "runtime": {"requires": {"Exporter": "0", "perl": "5.008001"}}, "test": {"requires": {"JSON::PP": "2", "Test::More": "0.98"}}}, "provides": {"WWW::Form::UrlEncoded": {"file": "lib/WWW/Form/UrlEncoded.pm", "version": "0.26"}, "WWW::Form::UrlEncoded::PP": {"file": "lib/WWW/Form/UrlEncoded/PP.pm"}}, "release_status": "stable", "resources": {"bugtracker": {"web": "https://github.com/kazeburo/WWW-Form-UrlEncoded/issues"}, "homepage": "https://github.com/kazeburo/WWW-Form-UrlEncoded", "repository": {"type": "git", "url": "git://github.com/kazeburo/WWW-Form-UrlEncoded.git", "web": "https://github.com/kazeburo/WWW-Form-UrlEncoded"}}, "version": "0.26", "x_contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "x_serialization_backend": "JSON::PP version 4.16", "x_static_install": 0}