=head1 NAME

XML::LibXML::CDATASection - XML::LibXML Class for CDATA Sections

=head1 SYNOPSIS



  use XML::LibXML;
  # Only methods specific to CDATA nodes are listed here,
  # see the XML::LibXML::Node manpage for other methods

  $node = XML::LibXML::CDATASection->new( $content );

=head1 DESCRIPTION

This class provides all functions of L<<<<<< XML::LibXML::Text >>>>>>, but for CDATA nodes.


=head1 METHODS

The class inherits from L<<<<<< XML::LibXML::Node >>>>>>. The documentation for Inherited methods is not listed here.

Many functions listed here are extensively documented in the DOM Level 3 specification (L<<<<<< http://www.w3.org/TR/DOM-Level-3-Core/ >>>>>>). Please refer to the specification for extensive documentation.

=over 4

=item new

  $node = XML::LibXML::CDATASection->new( $content );

The constructor is the only provided function for this package. It is required,
because I<<<<<< libxml2 >>>>>> treats the different text node types slightly differently.



=back

=head1 AUTHOR<PERSON>,
<PERSON>,
<PERSON><PERSON>


=head1 VERSION

2.0210

=head1 COPYRIGHT

2001-2007, AxKit.com Ltd.

2002-2006, Christian Glahn.

2006-2009, Petr Pajas.

=cut


=head1 LICENSE

This program is free software; you can redistribute it and/or modify it under
the same terms as Perl itself.

