#!/usr/bin/perl
# Do not normalise this test file. It has deliberately unnormalised characters in it.
use v5.10;
use strict;
use warnings;
use utf8;
use if $^V ge v5.12.0, feature => 'unicode_strings';

use Test::More tests => 740;
use Test::Exception;

use ok 'Locale::CLDR';

my $locale = Locale::CLDR->new('en_US');

is($locale->unit(1, 'area-acre', 'narrow'), '1ac', 'English narrow 1 area-acre');
is($locale->unit(2, 'area-acre', 'narrow'), '2ac', 'English narrow 2 area-acres');
is($locale->unit(1, 'area-acre', 'short'), '1 ac', 'English short 1 area-acre');
is($locale->unit(2, 'area-acre', 'short'), '2 ac', 'English short 2 area-acres');
is($locale->unit(1, 'area-acre'), '1 acre', 'English long 1 area-acre');
is($locale->unit(2, 'area-acre'), '2 acres', 'English long 2 area-acres');
is($locale->unit(1, 'volume-acre-foot', 'narrow'), '1ac ft', 'English narrow 1 area-acre-foot');
is($locale->unit(2, 'volume-acre-foot', 'narrow'), '2ac ft', 'English narrow 2 area-acre-feet');
is($locale->unit(1, 'volume-acre-foot', 'short'), '1 ac ft', 'English short 1 area-acre-foot');
is($locale->unit(2, 'volume-acre-foot', 'short'), '2 ac ft', 'English short 2 area-acre-feet');
is($locale->unit(1, 'volume-acre-foot'), '1 acre-foot', 'English long 1 area-acre-foot');
is($locale->unit(2, 'volume-acre-foot'), '2 acre-feet', 'English long 2 area-acre-feet');
is($locale->unit(1, 'electric-ampere', 'narrow'), '1A', 'English narrow 1 electric-ampere');
is($locale->unit(2, 'electric-ampere', 'narrow'), '2A', 'English narrow 2 electric-amperes');
is($locale->unit(1, 'electric-ampere', 'short'), '1 A', 'English short 1 electric-ampere');
is($locale->unit(2, 'electric-ampere', 'short'), '2 A', 'English short 2 electric-amperes');
is($locale->unit(1, 'electric-ampere'), '1 ampere', 'English long 1 electric-ampere');
is($locale->unit(2, 'electric-ampere'), '2 amperes', 'English long 2 electric-amperes');
is($locale->unit(1, 'angle-arc-minute', 'narrow'), '1′', 'English narrow 1 minute');
is($locale->unit(2, 'angle-arc-minute', 'narrow'), '2′', 'English narrow 2 minutes');
is($locale->unit(1, 'angle-arc-minute', 'short'), '1 arcmin', 'English short 1 arc minute');
is($locale->unit(2, 'angle-arc-minute', 'short'), '2 arcmins', 'English short 2 arc minutes');
is($locale->unit(1, 'angle-arc-minute'), '1 arcminute', 'English long 1 arc minute');
is($locale->unit(2, 'angle-arc-minute'), '2 arcminutes', 'English long 2 arc minutes');
is($locale->unit(1, 'angle-arc-second', 'narrow'), '1″', 'English narrow 1 second');
is($locale->unit(2, 'angle-arc-second', 'narrow'), '2″', 'English narrow 2 seconds');
is($locale->unit(1, 'angle-arc-second', 'short'), '1 arcsec', 'English short 1 arc second');
is($locale->unit(2, 'angle-arc-second', 'short'), '2 arcsecs', 'English short 2 arc seconds');
is($locale->unit(1, 'angle-arc-second'), '1 arcsecond', 'English long 1 arc second');
is($locale->unit(2, 'angle-arc-second'), '2 arcseconds', 'English long 2 arc seconds');
is($locale->unit(1, 'length-astronomical-unit', 'narrow'), '1au', 'English narrow 1 astronomical unit');
is($locale->unit(2, 'length-astronomical-unit', 'narrow'), '2au', 'English narrow 2 astronomical units');
is($locale->unit(1, 'length-astronomical-unit', 'short'), '1 au', 'English short 1 astronomical unit');
is($locale->unit(2, 'length-astronomical-unit', 'short'), '2 au', 'English short 2 astronomical units');
is($locale->unit(1, 'length-astronomical-unit'), '1 astronomical unit', 'English long 1 astronomical unit');
is($locale->unit(2, 'length-astronomical-unit'), '2 astronomical units', 'English long 2 astronomical units');
is($locale->unit(1, 'digital-bit', 'narrow'), '1bit', 'English narrow 1 bit');
is($locale->unit(2, 'digital-bit', 'narrow'), '2bit', 'English narrow 2 bits');
is($locale->unit(1, 'digital-bit', 'short'), '1 bit', 'English short 1 bit');
is($locale->unit(2, 'digital-bit', 'short'), '2 bit', 'English short 2 bit');
is($locale->unit(1, 'digital-bit'), '1 bit', 'English long 1 bit');
is($locale->unit(2, 'digital-bit'), '2 bits', 'English long 2 bits');
is($locale->unit(1, 'volume-bushel', 'narrow'), '1bu', 'English narrow 1 bushel');
is($locale->unit(2, 'volume-bushel', 'narrow'), '2bu', 'English narrow 2 bushels');
is($locale->unit(1, 'volume-bushel', 'short'), '1 bu', 'English short 1 bushel');
is($locale->unit(2, 'volume-bushel', 'short'), '2 bu', 'English short 2 bushels');
is($locale->unit(1, 'volume-bushel'), '1 bushel', 'English long 1 bushel');
is($locale->unit(2, 'volume-bushel'), '2 bushels', 'English long 2 bushels');
is($locale->unit(1, 'digital-byte', 'narrow'), '1B', 'English narrow 1 byte');
is($locale->unit(2, 'digital-byte', 'narrow'), '2B', 'English narrow 2 bytes');
is($locale->unit(1, 'digital-byte', 'short'), '1 byte', 'English short 1 byte');
is($locale->unit(2, 'digital-byte', 'short'), '2 byte', 'English short 2 bytes');
is($locale->unit(1, 'digital-byte'), '1 byte', 'English long 1 byte');
is($locale->unit(2, 'digital-byte'), '2 bytes', 'English long 2 bytes');
is($locale->unit(1, 'energy-calorie', 'narrow'), '1cal', 'English narrow 1 calorie');
is($locale->unit(2, 'energy-calorie', 'narrow'), '2cal', 'English narrow 2 calories');
is($locale->unit(1, 'energy-calorie', 'short'), '1 cal', 'English short 1 calorie');
is($locale->unit(2, 'energy-calorie', 'short'), '2 cal', 'English short 2 calories');
is($locale->unit(1, 'energy-calorie'), '1 calorie', 'English long 1 calorie');
is($locale->unit(2, 'energy-calorie'), '2 calories', 'English long 2 calories');
is($locale->unit(1, 'mass-carat', 'narrow'), '1CD', 'English narrow 1 carat');
is($locale->unit(2, 'mass-carat', 'narrow'), '2CD', 'English narrow 2 carats');
is($locale->unit(1, 'mass-carat', 'short'), '1 CD', 'English short 1 carat');
is($locale->unit(2, 'mass-carat', 'short'), '2 CD', 'English short 2 carats');
is($locale->unit(1, 'mass-carat'), '1 carat', 'English long 1 carat');
is($locale->unit(2, 'mass-carat'), '2 carats', 'English long 2 carats');
is($locale->unit(1, 'temperature-celsius', 'narrow'), '1°C', 'English narrow 1 degree Celsius');
is($locale->unit(2, 'temperature-celsius', 'narrow'), '2°C', 'English narrow 2 degrees Celsius');
is($locale->unit(1, 'temperature-celsius', 'short'), '1°C', 'English short 1 degree Celsius');
is($locale->unit(2, 'temperature-celsius', 'short'), '2°C', 'English short 2 degrees Celsius');
is($locale->unit(1, 'temperature-celsius'), '1 degree Celsius', 'English long 1 degree Celsius');
is($locale->unit(2, 'temperature-celsius'), '2 degrees Celsius', 'English long 2 degrees Celsius');
is($locale->unit(1, 'volume-centiliter', 'narrow'), '1cL', 'English narrow 1 centiliter');
is($locale->unit(2, 'volume-centiliter', 'narrow'), '2cL', 'English narrow 2 centiliters');
is($locale->unit(1, 'volume-centiliter', 'short'), '1 cL', 'English short 1 centiliter');
is($locale->unit(2, 'volume-centiliter', 'short'), '2 cL', 'English short 2 centiliters');
is($locale->unit(1, 'volume-centiliter'), '1 centiliter', 'English long 1 centiliter');
is($locale->unit(2, 'volume-centiliter'), '2 centiliters', 'English long 2 centiliters');
is($locale->unit(1, 'length-centimeter', 'narrow'), '1cm', 'English narrow 1 centimetre');
is($locale->unit(2, 'length-centimeter', 'narrow'), '2cm', 'English narrow 2 centimetres');
is($locale->unit(1, 'length-centimeter', 'short'), '1 cm', 'English short 1 centimetre');
is($locale->unit(2, 'length-centimeter', 'short'), '2 cm', 'English short 2 centimetres');
is($locale->unit(1, 'length-centimeter'), '1 centimeter', 'English long 1 centimetre');
is($locale->unit(2, 'length-centimeter'), '2 centimeters', 'English long 2 centimetres');
is($locale->unit(1, 'volume-cubic-centimeter', 'narrow'), '1cm³', 'English narrow 1 cubic centimetre');
is($locale->unit(2, 'volume-cubic-centimeter', 'narrow'), '2cm³', 'English narrow 2 cubic centimetres');
is($locale->unit(1, 'volume-cubic-centimeter', 'short'), '1 cm³', 'English short 1 cubic centimetre');
is($locale->unit(2, 'volume-cubic-centimeter', 'short'), '2 cm³', 'English short 2 cubic centimetres');
is($locale->unit(1, 'volume-cubic-centimeter'), '1 cubic centimeter', 'English long 1 cubic centimetre');
is($locale->unit(2, 'volume-cubic-centimeter'), '2 cubic centimeters', 'English long 2 cubic centimetres');
is($locale->unit(1, 'volume-cubic-foot', 'narrow'), '1ft³', 'English narrow 1 cubic foot');
is($locale->unit(2, 'volume-cubic-foot', 'narrow'), '2ft³', 'English narrow 2 cubic feet');
is($locale->unit(1, 'volume-cubic-foot', 'short'), '1 ft³', 'English short 1 cubic foot');
is($locale->unit(2, 'volume-cubic-foot', 'short'), '2 ft³', 'English short 2 cubic feet');
is($locale->unit(1, 'volume-cubic-foot'), '1 cubic foot', 'English long 1 cubic foot');
is($locale->unit(2, 'volume-cubic-foot'), '2 cubic feet', 'English long 2 cubic feet');
is($locale->unit(1, 'volume-cubic-inch', 'narrow'), '1in³', 'English narrow 1 cubic inch');
is($locale->unit(2, 'volume-cubic-inch', 'narrow'), '2in³', 'English narrow 2 cubic inches');
is($locale->unit(1, 'volume-cubic-inch', 'short'), '1 in³', 'English short 1 cubic inch');
is($locale->unit(2, 'volume-cubic-inch', 'short'), '2 in³', 'English short 2 cubic inches');
is($locale->unit(1, 'volume-cubic-inch'), '1 cubic inch', 'English long 1 cubic inch');
is($locale->unit(2, 'volume-cubic-inch'), '2 cubic inches', 'English long 2 cubic inches');
is($locale->unit(1, 'volume-cubic-kilometer', 'narrow'), '1km³', 'English narrow 1 cubic kilometre');
is($locale->unit(2, 'volume-cubic-kilometer', 'narrow'), '2km³', 'English narrow 2 cubic kilometres');
is($locale->unit(1, 'volume-cubic-kilometer', 'short'), '1 km³', 'English short 1 cubic kilometre');
is($locale->unit(2, 'volume-cubic-kilometer', 'short'), '2 km³', 'English short 2 cubic kilometres');
is($locale->unit(1, 'volume-cubic-kilometer'), '1 cubic kilometer', 'English long 1 cubic kilometre');
is($locale->unit(2, 'volume-cubic-kilometer'), '2 cubic kilometers', 'English long 2 cubic kilometres');
is($locale->unit(1, 'volume-cubic-meter', 'narrow'), '1m³', 'English narrow 1 cubic metre');
is($locale->unit(2, 'volume-cubic-meter', 'narrow'), '2m³', 'English narrow 2 cubic metres');
is($locale->unit(1, 'volume-cubic-meter', 'short'), '1 m³', 'English short 1 cubic metre');
is($locale->unit(2, 'volume-cubic-meter', 'short'), '2 m³', 'English short 2 cubic metres');
is($locale->unit(1, 'volume-cubic-meter'), '1 cubic meter', 'English long 1 cubic metre');
is($locale->unit(2, 'volume-cubic-meter'), '2 cubic meters', 'English long 2 cubic metres');
is($locale->unit(1, 'volume-cubic-mile', 'narrow'), '1mi³', 'English narrow 1 cubic mile');
is($locale->unit(2, 'volume-cubic-mile', 'narrow'), '2mi³', 'English narrow 2 cubic miles');
is($locale->unit(1, 'volume-cubic-mile', 'short'), '1 mi³', 'English short 1 cubic mile');
is($locale->unit(2, 'volume-cubic-mile', 'short'), '2 mi³', 'English short 2 cubic miles');
is($locale->unit(1, 'volume-cubic-mile'), '1 cubic mile', 'English long 1 cubic mile');
is($locale->unit(2, 'volume-cubic-mile'), '2 cubic miles', 'English long 2 cubic miles');
is($locale->unit(1, 'volume-cubic-yard', 'narrow'), '1yd³', 'English narrow 1 cubic yard');
is($locale->unit(2, 'volume-cubic-yard', 'narrow'), '2yd³', 'English narrow 2 cubic yards');
is($locale->unit(1, 'volume-cubic-yard', 'short'), '1 yd³', 'English short 1 cubic yard');
is($locale->unit(2, 'volume-cubic-yard', 'short'), '2 yd³', 'English short 2 cubic yards');
is($locale->unit(1, 'volume-cubic-yard'), '1 cubic yard', 'English long 1 cubic yard');
is($locale->unit(2, 'volume-cubic-yard'), '2 cubic yards', 'English long 2 cubic yards');
is($locale->unit(1, 'volume-cup', 'narrow'), '1c', 'English narrow 1 cup');
is($locale->unit(2, 'volume-cup', 'narrow'), '2c', 'English narrow 2 cups');
is($locale->unit(1, 'volume-cup', 'short'), '1 c', 'English short 1 cup');
is($locale->unit(2, 'volume-cup', 'short'), '2 c', 'English short 2 cups');
is($locale->unit(1, 'volume-cup'), '1 cup', 'English long 1 cup');
is($locale->unit(2, 'volume-cup'), '2 cups', 'English long 2 cups');
is($locale->unit(1, 'duration-day', 'narrow'), '1d', 'English narrow 1 day');
is($locale->unit(2, 'duration-day', 'narrow'), '2d', 'English narrow 2 days');
is($locale->unit(1, 'duration-day', 'short'), '1 day', 'English short 1 day');
is($locale->unit(2, 'duration-day', 'short'), '2 days', 'English short 2 days');
is($locale->unit(1, 'duration-day'), '1 day', 'English long 1 day');
is($locale->unit(2, 'duration-day'), '2 days', 'English long 2 days');
is($locale->unit(1, 'volume-deciliter', 'narrow'), '1dL', 'English narrow 1 deciliter');
is($locale->unit(2, 'volume-deciliter', 'narrow'), '2dL', 'English narrow 2 deciliters');
is($locale->unit(1, 'volume-deciliter', 'short'), '1 dL', 'English short 1 deciliter');
is($locale->unit(2, 'volume-deciliter', 'short'), '2 dL', 'English short 2 deciliters');
is($locale->unit(1, 'volume-deciliter'), '1 deciliter', 'English long 1 deciliter');
is($locale->unit(2, 'volume-deciliter'), '2 deciliters', 'English long 2 deciliters');
is($locale->unit(1, 'length-decimeter', 'narrow'), '1dm', 'English narrow 1 decimeter');
is($locale->unit(2, 'length-decimeter', 'narrow'), '2dm', 'English narrow 2 decimeters');
is($locale->unit(1, 'length-decimeter', 'short'), '1 dm', 'English short 1 decimeter');
is($locale->unit(2, 'length-decimeter', 'short'), '2 dm', 'English short 2 decimeters');
is($locale->unit(1, 'length-decimeter'), '1 decimeter', 'English long 1 decimeter');
is($locale->unit(2, 'length-decimeter'), '2 decimeters', 'English long 2 decimeters');
is($locale->unit(1, 'angle-degree', 'narrow'), '1°', 'English narrow 1 degree');
is($locale->unit(2, 'angle-degree', 'narrow'), '2°', 'English narrow 2 degrees');
is($locale->unit(1, 'angle-degree', 'short'), '1 deg', 'English short 1 degree');
is($locale->unit(2, 'angle-degree', 'short'), '2 deg', 'English short 2 degree');
is($locale->unit(1, 'angle-degree'), '1 degree', 'English long 1 degree');
is($locale->unit(2, 'angle-degree'), '2 degrees', 'English long 2 degrees');
is($locale->unit(1, 'temperature-fahrenheit', 'narrow'), '1°', 'English narrow 1 degree Fahrenheit');
is($locale->unit(2, 'temperature-fahrenheit', 'narrow'), '2°', 'English narrow 2 degrees Fahrenheit');
is($locale->unit(1, 'temperature-fahrenheit', 'short'), '1°F', 'English short 1 degree Fahrenheit');
is($locale->unit(2, 'temperature-fahrenheit', 'short'), '2°F', 'English short 2 degrees Fahrenheit');
is($locale->unit(1, 'temperature-fahrenheit'), '1 degree Fahrenheit', 'English long 1 degree Fahrenheit');
is($locale->unit(2, 'temperature-fahrenheit'), '2 degrees Fahrenheit', 'English long 2 degrees Fahrenheit');
is($locale->unit(1, 'length-fathom', 'narrow'), '1fth', 'English narrow 1 fathom');
is($locale->unit(2, 'length-fathom', 'narrow'), '2fth', 'English narrow 2 fathoms');
is($locale->unit(1, 'length-fathom', 'short'), '1 fth', 'English short 1 fathom');
is($locale->unit(2, 'length-fathom', 'short'), '2 fth', 'English short 2 fathoms');
is($locale->unit(1, 'length-fathom'), '1 fathom', 'English long 1 fathom');
is($locale->unit(2, 'length-fathom'), '2 fathoms', 'English long 2 fathoms');
is($locale->unit(1, 'volume-fluid-ounce', 'narrow'), '1fl oz', 'English narrow 1 fluid ounce');
is($locale->unit(2, 'volume-fluid-ounce', 'narrow'), '2fl oz', 'English narrow 2 fluid ounces');
is($locale->unit(1, 'volume-fluid-ounce', 'short'), '1 fl oz', 'English short 1 fluid ounce');
is($locale->unit(2, 'volume-fluid-ounce', 'short'), '2 fl oz', 'English short 2 fluid ounces');
is($locale->unit(1, 'volume-fluid-ounce'), '1 fluid ounce', 'English long 1 fluid  ounce');
is($locale->unit(2, 'volume-fluid-ounce'), '2 fluid ounces', 'English long 2 fluid ounces');
is($locale->unit(1, 'energy-foodcalorie', 'narrow'), '1Cal', 'English narrow 1 Calorie');
is($locale->unit(2, 'energy-foodcalorie', 'narrow'), '2Cal', 'English narrow 2 Calories');
is($locale->unit(1, 'energy-foodcalorie', 'short'), '1 Cal', 'English short 1 Calorie');
is($locale->unit(2, 'energy-foodcalorie', 'short'), '2 Cal', 'English short 2 Calories');
is($locale->unit(1, 'energy-foodcalorie'), '1 Calorie', 'English long 1 Calorie');
is($locale->unit(2, 'energy-foodcalorie'), '2 Calories', 'English long 2 Calories');
is($locale->unit(1, 'length-foot', 'narrow'), '1′', 'English narrow 1 foot');
is($locale->unit(2, 'length-foot', 'narrow'), '2′', 'English narrow 2 feet');
is($locale->unit(1, 'length-foot', 'short'), '1 ft', 'English short 1 foot');
is($locale->unit(2, 'length-foot', 'short'), '2 ft', 'English short 2 feet');
is($locale->unit(1, 'length-foot'), '1 foot', 'English long 1 foot');
is($locale->unit(2, 'length-foot'), '2 feet', 'English long 2 feet');
is($locale->unit(1, 'length-furlong', 'narrow'), '1fur', 'English narrow 1 furlong');
is($locale->unit(2, 'length-furlong', 'narrow'), '2fur', 'English narrow 2 furlongs');
is($locale->unit(1, 'length-furlong', 'short'), '1 fur', 'English short 1 furlong');
is($locale->unit(2, 'length-furlong', 'short'), '2 fur', 'English short 2 furlongs');
is($locale->unit(1, 'length-furlong'), '1 furlong', 'English long 1 furlong');
is($locale->unit(2, 'length-furlong'), '2 furlongs', 'English long 2 furlongs');
is($locale->unit(1, 'acceleration-g-force', 'narrow'), '1G', 'English narrow 1 g-force');
is($locale->unit(2, 'acceleration-g-force', 'narrow'), '2Gs', 'English narrow 2 g-force');
is($locale->unit(1, 'acceleration-g-force', 'short'), '1 G', 'English short 1 g-force');
is($locale->unit(2, 'acceleration-g-force', 'short'), '2 G', 'English short 2 g-force');
is($locale->unit(1, 'acceleration-g-force'), '1 g-force', 'English long 1 g-force');
is($locale->unit(2, 'acceleration-g-force'), '2 g-force', 'English long 2 g-force');
is($locale->unit(1, 'volume-gallon', 'narrow'), '1gal', 'English narrow 1 gallon');
is($locale->unit(2, 'volume-gallon', 'narrow'), '2gal', 'English narrow 2 gallons');
is($locale->unit(1, 'volume-gallon', 'short'), '1 gal', 'English short 1 gallon');
is($locale->unit(2, 'volume-gallon', 'short'), '2 gal', 'English short 2 gallons');
is($locale->unit(1, 'volume-gallon'), '1 gallon', 'English long 1 gallon');
is($locale->unit(2, 'volume-gallon'), '2 gallons', 'English long 2 gallons');
is($locale->unit(1, 'digital-gigabit', 'narrow'), '1Gb', 'English narrow 1 gigabit');
is($locale->unit(2, 'digital-gigabit', 'narrow'), '2Gb', 'English narrow 2 gigabits');
is($locale->unit(1, 'digital-gigabit', 'short'), '1 Gb', 'English short 1 gigabit');
is($locale->unit(2, 'digital-gigabit', 'short'), '2 Gb', 'English short 2 gigabits');
is($locale->unit(1, 'digital-gigabit'), '1 gigabit', 'English long 1 gigabit');
is($locale->unit(2, 'digital-gigabit'), '2 gigabits', 'English long 2 gigabits');
is($locale->unit(1, 'digital-gigabyte', 'narrow'), '1GB', 'English narrow 1 gigabyte');
is($locale->unit(2, 'digital-gigabyte', 'narrow'), '2GB', 'English narrow 2 gigabits');
is($locale->unit(1, 'digital-gigabyte', 'short'), '1 GB', 'English short 1 gigabyte');
is($locale->unit(2, 'digital-gigabyte', 'short'), '2 GB', 'English short 2 gigabytes');
is($locale->unit(1, 'digital-gigabyte'), '1 gigabyte', 'English long 1 gigabyte');
is($locale->unit(2, 'digital-gigabyte'), '2 gigabytes', 'English long 2 gigabytes');
is($locale->unit(1, 'frequency-gigahertz', 'narrow'), '1GHz', 'English narrow 1 gigahertz');
is($locale->unit(2, 'frequency-gigahertz', 'narrow'), '2GHz', 'English narrow 2 gigahertz');
is($locale->unit(1, 'frequency-gigahertz', 'short'), '1 GHz', 'English short 1 gigahertz');
is($locale->unit(2, 'frequency-gigahertz', 'short'), '2 GHz', 'English short 2 gigahertz');
is($locale->unit(1, 'frequency-gigahertz'), '1 gigahertz', 'English long 1 gigahertz');
is($locale->unit(2, 'frequency-gigahertz'), '2 gigahertz', 'English long 2 gigahertz');
is($locale->unit(1, 'power-gigawatt', 'narrow'), '1GW', 'English narrow 1 gigawatt');
is($locale->unit(2, 'power-gigawatt', 'narrow'), '2GW', 'English narrow 2 gigawatts');
is($locale->unit(1, 'power-gigawatt', 'short'), '1 GW', 'English short 1 gigawatt');
is($locale->unit(2, 'power-gigawatt', 'short'), '2 GW', 'English short 2 gigawatts');
is($locale->unit(1, 'power-gigawatt'), '1 gigawatt', 'English long 1 gigawatt');
is($locale->unit(2, 'power-gigawatt'), '2 gigawatts', 'English long 2 gigawatts');
is($locale->unit(1, 'mass-gram', 'narrow'), '1g', 'English narrow 1 gram');
is($locale->unit(2, 'mass-gram', 'narrow'), '2g', 'English narrow 2 grams');
is($locale->unit(1, 'mass-gram', 'short'), '1 g', 'English short 1 gram');
is($locale->unit(2, 'mass-gram', 'short'), '2 g', 'English short 2 grams');
is($locale->unit(1, 'mass-gram'), '1 gram', 'English long 1 gram');
is($locale->unit(2, 'mass-gram'), '2 grams', 'English long 2 grams');
is($locale->unit(1, 'area-hectare', 'narrow'), '1ha', 'English narrow 1 hectare');
is($locale->unit(2, 'area-hectare', 'narrow'), '2ha', 'English narrow 2 hectares');
is($locale->unit(1, 'area-hectare', 'short'), '1 ha', 'English short 1 hectare');
is($locale->unit(2, 'area-hectare', 'short'), '2 ha', 'English short 2 hectares');
is($locale->unit(1, 'area-hectare'), '1 hectare', 'English long 1 hectare');
is($locale->unit(2, 'area-hectare'), '2 hectares', 'English long 2 hectares');
is($locale->unit(1, 'volume-hectoliter', 'narrow'), '1hL', 'English narrow 1 hectoliter');
is($locale->unit(2, 'volume-hectoliter', 'narrow'), '2hL', 'English narrow 2 hectoliters');
is($locale->unit(1, 'volume-hectoliter', 'short'), '1 hL', 'English short 1 hectoliter');
is($locale->unit(2, 'volume-hectoliter', 'short'), '2 hL', 'English short 2 hectoliters');
is($locale->unit(1, 'volume-hectoliter'), '1 hectoliter', 'English long 1 hectoliter');
is($locale->unit(2, 'volume-hectoliter'), '2 hectoliters', 'English long 2 hectoliters');
is($locale->unit(1, 'pressure-hectopascal', 'narrow'), '1hPa', 'English narrow 1 hectopascal');
is($locale->unit(2, 'pressure-hectopascal', 'narrow'), '2hPa', 'English narrow 2 hectopascals');
is($locale->unit(1, 'pressure-hectopascal', 'short'), '1 hPa', 'English short 1 hectopascal');
is($locale->unit(2, 'pressure-hectopascal', 'short'), '2 hPa', 'English short 2 hectopascals');
is($locale->unit(1, 'pressure-hectopascal'), '1 hectopascal', 'English long 1 hectopascal');
is($locale->unit(2, 'pressure-hectopascal'), '2 hectopascals', 'English long 2 hectopascals');
is($locale->unit(1, 'frequency-hertz', 'narrow'), '1Hz', 'English narrow 1 hertz');
is($locale->unit(2, 'frequency-hertz', 'narrow'), '2Hz', 'English narrow 2 hertz');
is($locale->unit(1, 'frequency-hertz', 'short'), '1 Hz', 'English short 1 hertz');
is($locale->unit(2, 'frequency-hertz', 'short'), '2 Hz', 'English short 2 hertz');
is($locale->unit(1, 'frequency-hertz'), '1 hertz', 'English long 1 hertz');
is($locale->unit(2, 'frequency-hertz'), '2 hertz', 'English long 2 hertz');
is($locale->unit(1, 'power-horsepower', 'narrow'), '1hp', 'English narrow 1 horsepower');
is($locale->unit(2, 'power-horsepower', 'narrow'), '2hp', 'English narrow 2 horsepower');
is($locale->unit(1, 'power-horsepower', 'short'), '1 hp', 'English short 1 horsepower');
is($locale->unit(2, 'power-horsepower', 'short'), '2 hp', 'English short 2 horsepower');
is($locale->unit(1, 'power-horsepower'), '1 horsepower', 'English long 1 horsepower');
is($locale->unit(2, 'power-horsepower'), '2 horsepower', 'English long 2 horsepower');
is($locale->unit(1, 'duration-hour', 'narrow'), '1h', 'English narrow 1 hour');
is($locale->unit(2, 'duration-hour', 'narrow'), '2h', 'English narrow 2 hours');
is($locale->unit(1, 'duration-hour', 'short'), '1 hr', 'English short 1 hour');
is($locale->unit(2, 'duration-hour', 'short'), '2 hr', 'English short 2 hours');
is($locale->unit(1, 'duration-hour'), '1 hour', 'English long 1 hour');
is($locale->unit(2, 'duration-hour'), '2 hours', 'English long 2 hours');
is($locale->unit(1, 'length-inch', 'narrow'), '1″', 'English narrow 1 inch');
is($locale->unit(2, 'length-inch', 'narrow'), '2″', 'English narrow 2 inches');
is($locale->unit(1, 'length-inch', 'short'), '1 in', 'English short 1 inch');
is($locale->unit(2, 'length-inch', 'short'), '2 in', 'English short 2 inches');
is($locale->unit(1, 'length-inch'), '1 inch', 'English long 1 inch');
is($locale->unit(2, 'length-inch'), '2 inches', 'English long 2 inches');
is($locale->unit(1, 'pressure-inch-ofhg', 'narrow'), '1″ Hg', 'English narrow 1 inch of mercury');
is($locale->unit(2, 'pressure-inch-ofhg', 'narrow'), '2″ Hg', 'English narrow 2 inches of mercury');
is($locale->unit(1, 'pressure-inch-ofhg', 'short'), '1 inHg', 'English short 1 inch of mercury');
is($locale->unit(2, 'pressure-inch-ofhg', 'short'), '2 inHg', 'English short 2 inches of mercury');
is($locale->unit(1, 'pressure-inch-ofhg'), '1 inch of mercury', 'English long 1 inch of mercury');
is($locale->unit(2, 'pressure-inch-ofhg'), '2 inches of mercury', 'English long 2 inches of mercury');
is($locale->unit(1, 'energy-joule', 'narrow'), '1J', 'English narrow 1 joule');
is($locale->unit(2, 'energy-joule', 'narrow'), '2J', 'English narrow 2 joules');
is($locale->unit(1, 'energy-joule', 'short'), '1 J', 'English short 1 joule');
is($locale->unit(2, 'energy-joule', 'short'), '2 J', 'English short 2 joules');
is($locale->unit(1, 'energy-joule'), '1 joule', 'English long 1 joule');
is($locale->unit(2, 'energy-joule'), '2 joules', 'English long 2 joules');
is($locale->unit(1, 'concentr-karat', 'narrow'), '1kt', 'English narrow 1 karat');
is($locale->unit(2, 'concentr-karat', 'narrow'), '2kt', 'English narrow 2 karats');
is($locale->unit(1, 'concentr-karat', 'short'), '1 kt', 'English short 1 karat');
is($locale->unit(2, 'concentr-karat', 'short'), '2 kt', 'English short 2 karats');
is($locale->unit(1, 'concentr-karat'), '1 karat', 'English long 1 karat');
is($locale->unit(2, 'concentr-karat'), '2 karats', 'English long 2 karats');
is($locale->unit(1, 'temperature-kelvin', 'narrow'), '1K', 'English narrow 1 kelvin');
is($locale->unit(2, 'temperature-kelvin', 'narrow'), '2K', 'English narrow 2 kelvins');
is($locale->unit(1, 'temperature-kelvin', 'short'), '1 K', 'English short 1 kelvin');
is($locale->unit(2, 'temperature-kelvin', 'short'), '2 K', 'English short 2 kelvins');
is($locale->unit(1, 'temperature-kelvin'), '1 kelvin', 'English long 1 kelvin');
is($locale->unit(2, 'temperature-kelvin'), '2 kelvins', 'English long 2 kelvins');
is($locale->unit(1, 'digital-kilobit', 'narrow'), '1kb', 'English narrow 1 kilobit');
is($locale->unit(2, 'digital-kilobit', 'narrow'), '2kb', 'English narrow 2 kilobits');
is($locale->unit(1, 'digital-kilobit', 'short'), '1 kb', 'English short 1 kilobit');
is($locale->unit(2, 'digital-kilobit', 'short'), '2 kb', 'English short 2 kilobits');
is($locale->unit(1, 'digital-kilobit'), '1 kilobit', 'English long 1 kilobit');
is($locale->unit(2, 'digital-kilobit'), '2 kilobits', 'English long 2 kilobits');
is($locale->unit(1, 'digital-kilobyte', 'narrow'), '1kB', 'English narrow 1 kilobyte');
is($locale->unit(2, 'digital-kilobyte', 'narrow'), '2kB', 'English narrow 2 kilobytes');
is($locale->unit(1, 'digital-kilobyte', 'short'), '1 kB', 'English short 1 kilobyte');
is($locale->unit(2, 'digital-kilobyte', 'short'), '2 kB', 'English short 2 kilobytes');
is($locale->unit(1, 'digital-kilobyte'), '1 kilobyte', 'English long 1 kilobyte');
is($locale->unit(2, 'digital-kilobyte'), '2 kilobytes', 'English long 2 kilobytes');
is($locale->unit(1, 'energy-kilocalorie', 'narrow'), '1kcal', 'English narrow 1 kilocalorie');
is($locale->unit(2, 'energy-kilocalorie', 'narrow'), '2kcal', 'English narrow 2 kilocalories');
is($locale->unit(1, 'energy-kilocalorie', 'short'), '1 kcal', 'English short 1 kilocalorie');
is($locale->unit(2, 'energy-kilocalorie', 'short'), '2 kcal', 'English short 2 kilocalories');
is($locale->unit(1, 'energy-kilocalorie'), '1 kilocalorie', 'English long 1 kilocalorie');
is($locale->unit(2, 'energy-kilocalorie'), '2 kilocalories', 'English long 2 kilocalories');
is($locale->unit(1, 'mass-kilogram', 'narrow'), '1kg', 'English narrow 1 kilogram');
is($locale->unit(2, 'mass-kilogram', 'narrow'), '2kg', 'English narrow 2 kilograms');
is($locale->unit(1, 'mass-kilogram', 'short'), '1 kg', 'English short 1 kilogram');
is($locale->unit(2, 'mass-kilogram', 'short'), '2 kg', 'English short 2 kilograms');
is($locale->unit(1, 'mass-kilogram'), '1 kilogram', 'English long 1 kilogram');
is($locale->unit(2, 'mass-kilogram'), '2 kilograms', 'English long 2 kilograms');
is($locale->unit(1, 'frequency-kilohertz', 'narrow'), '1kHz', 'English narrow 1 kilohertz');
is($locale->unit(2, 'frequency-kilohertz', 'narrow'), '2kHz', 'English narrow 2 kilohertz');
is($locale->unit(1, 'frequency-kilohertz', 'short'), '1 kHz', 'English short 1 kilohertz');
is($locale->unit(2, 'frequency-kilohertz', 'short'), '2 kHz', 'English short 2 kilohertz');
is($locale->unit(1, 'frequency-kilohertz'), '1 kilohertz', 'English long 1 kilohertz');
is($locale->unit(2, 'frequency-kilohertz'), '2 kilohertz', 'English long 2 kilohertz');
is($locale->unit(1, 'energy-kilojoule', 'narrow'), '1kJ', 'English narrow 1 kilojoule');
is($locale->unit(2, 'energy-kilojoule', 'narrow'), '2kJ', 'English narrow 2 kilojoules');
is($locale->unit(1, 'energy-kilojoule', 'short'), '1 kJ', 'English short 1 kilojoule');
is($locale->unit(2, 'energy-kilojoule', 'short'), '2 kJ', 'English short 2 kilojoules');
is($locale->unit(1, 'energy-kilojoule'), '1 kilojoule', 'English long 1 kilojoule');
is($locale->unit(2, 'energy-kilojoule'), '2 kilojoules', 'English long 2 kilojoules');
is($locale->unit(1, 'length-kilometer', 'narrow'), '1km', 'English narrow 1 kilometre');
is($locale->unit(2, 'length-kilometer', 'narrow'), '2km', 'English narrow 2 kilometres');
is($locale->unit(1, 'length-kilometer', 'short'), '1 km', 'English short 1 kilometre');
is($locale->unit(2, 'length-kilometer', 'short'), '2 km', 'English short 2 kilometres');
is($locale->unit(1, 'length-kilometer'), '1 kilometer', 'English long 1 kilometre');
is($locale->unit(2, 'length-kilometer'), '2 kilometers', 'English long 2 kilometres');
is($locale->unit(1, 'speed-kilometer-per-hour', 'narrow'), '1km/h', 'English narrow 1 kilometre per hour');
is($locale->unit(2, 'speed-kilometer-per-hour', 'narrow'), '2km/h', 'English narrow 2 kilometres per hour');
is($locale->unit(1, 'speed-kilometer-per-hour', 'short'), '1 km/h', 'English short 1 kilometre per hour');
is($locale->unit(2, 'speed-kilometer-per-hour', 'short'), '2 km/h', 'English short 2 kilometres per hour');
is($locale->unit(1, 'speed-kilometer-per-hour'), '1 kilometer per hour', 'English long 1 kilometre per hour');
is($locale->unit(2, 'speed-kilometer-per-hour'), '2 kilometers per hour', 'English long 2 kilometres per hour');
is($locale->unit(1, 'power-kilowatt', 'narrow'), '1kW', 'English narrow 1 kilowatt');
is($locale->unit(2, 'power-kilowatt', 'narrow'), '2kW', 'English narrow 2 kilowatts');
is($locale->unit(1, 'power-kilowatt', 'short'), '1 kW', 'English short 1 kilowatt');
is($locale->unit(2, 'power-kilowatt', 'short'), '2 kW', 'English short 2 kilowatts');
is($locale->unit(1, 'power-kilowatt'), '1 kilowatt', 'English long 1 kilowatt');
is($locale->unit(2, 'power-kilowatt'), '2 kilowatts', 'English long 2 kilowatts');
is($locale->unit(1, 'energy-kilowatt-hour', 'narrow'), '1kWh', 'English narrow 1 kilowatt hour');
is($locale->unit(2, 'energy-kilowatt-hour', 'narrow'), '2kWh', 'English narrow 2 kilowatt-hours');
is($locale->unit(1, 'energy-kilowatt-hour', 'short'), '1 kWh', 'English short 1 kilowatt hour');
is($locale->unit(2, 'energy-kilowatt-hour', 'short'), '2 kWh', 'English short 2 kilowatt-hours');
is($locale->unit(1, 'energy-kilowatt-hour'), '1 kilowatt hour', 'English long 1 kilowatt hour');
is($locale->unit(2, 'energy-kilowatt-hour'), '2 kilowatt-hours', 'English long 2 kilowatt-hours');
is($locale->unit(1, 'length-light-year', 'narrow'), '1ly', 'English narrow 1 light year');
is($locale->unit(2, 'length-light-year', 'narrow'), '2ly', 'English narrow 2 light years');
is($locale->unit(1, 'length-light-year', 'short'), '1 ly', 'English short 1 light year');
is($locale->unit(2, 'length-light-year', 'short'), '2 ly', 'English short 2 light years');
is($locale->unit(1, 'length-light-year'), '1 light year', 'English long 1 light year');
is($locale->unit(2, 'length-light-year'), '2 light years', 'English long 2 light years');
is($locale->unit(1, 'volume-liter', 'narrow'), '1L', 'English narrow 1 litre');
is($locale->unit(2, 'volume-liter', 'narrow'), '2L', 'English narrow 2 litres');
is($locale->unit(1, 'volume-liter', 'short'), '1 L', 'English short 1 litre');
is($locale->unit(2, 'volume-liter', 'short'), '2 L', 'English short 2 litres');
is($locale->unit(1, 'volume-liter'), '1 liter', 'English long 1 litre');
is($locale->unit(2, 'volume-liter'), '2 liters', 'English long 2 litres');
is($locale->unit(1, 'consumption-liter-per-kilometer', 'narrow'), '1L/km', 'English narrow 1 litre per kilometer');
is($locale->unit(2, 'consumption-liter-per-kilometer', 'narrow'), '2L/km', 'English narrow 2 litres per kilometer');
is($locale->unit(1, 'consumption-liter-per-kilometer', 'short'), '1 L/km', 'English short 1 litre per kilometer');
is($locale->unit(2, 'consumption-liter-per-kilometer', 'short'), '2 L/km', 'English short 2 litres per kilometer');
is($locale->unit(1, 'consumption-liter-per-kilometer'), '1 liter per kilometer', 'English long 1 litre per kilometer');
is($locale->unit(2, 'consumption-liter-per-kilometer'), '2 liters per kilometer', 'English long 2 litres per kilometer');
is($locale->unit(1, 'light-lux', 'narrow'), '1lx', 'English narrow 1 lux');
is($locale->unit(2, 'light-lux', 'narrow'), '2lx', 'English narrow 2 lux');
is($locale->unit(1, 'light-lux', 'short'), '1 lx', 'English short 1 lux');
is($locale->unit(2, 'light-lux', 'short'), '2 lx', 'English short 2 lux');
is($locale->unit(1, 'light-lux'), '1 lux', 'English long 1 lux');
is($locale->unit(2, 'light-lux'), '2 lux', 'English long 2 lux');
is($locale->unit(1, 'digital-megabit', 'narrow'), '1Mb', 'English narrow 1 megabit');
is($locale->unit(2, 'digital-megabit', 'narrow'), '2Mb', 'English narrow 2 megabits');
is($locale->unit(1, 'digital-megabit', 'short'), '1 Mb', 'English short 1 megabit');
is($locale->unit(2, 'digital-megabit', 'short'), '2 Mb', 'English short 2 megabits');
is($locale->unit(1, 'digital-megabit'), '1 megabit', 'English long 1 megabit');
is($locale->unit(2, 'digital-megabit'), '2 megabits', 'English long 2 megabits');
is($locale->unit(1, 'digital-megabyte', 'narrow'), '1MB', 'English narrow 1 megabyte');
is($locale->unit(2, 'digital-megabyte', 'narrow'), '2MB', 'English narrow 2 megabytes');
is($locale->unit(1, 'digital-megabyte', 'short'), '1 MB', 'English short 1 megabyte');
is($locale->unit(2, 'digital-megabyte', 'short'), '2 MB', 'English short 2 megabytes');
is($locale->unit(1, 'digital-megabyte'), '1 megabyte', 'English long 1 megabyte');
is($locale->unit(2, 'digital-megabyte'), '2 megabytes', 'English long 2 megabytes');
is($locale->unit(1, 'frequency-megahertz', 'narrow'), '1MHz', 'English narrow 1 megahertz');
is($locale->unit(2, 'frequency-megahertz', 'narrow'), '2MHz', 'English narrow 2 megahertz');
is($locale->unit(1, 'frequency-megahertz', 'short'), '1 MHz', 'English short 1 megahertz');
is($locale->unit(2, 'frequency-megahertz', 'short'), '2 MHz', 'English short 2 megahertz');
is($locale->unit(1, 'frequency-megahertz'), '1 megahertz', 'English long 1 megahertz');
is($locale->unit(2, 'frequency-megahertz'), '2 megahertz', 'English long 2 megahertz');
is($locale->unit(1, 'volume-megaliter', 'narrow'), '1ML', 'English narrow 1 megaliter');
is($locale->unit(2, 'volume-megaliter', 'narrow'), '2ML', 'English narrow 2 megaliters');
is($locale->unit(1, 'volume-megaliter', 'short'), '1 ML', 'English short 1 megaliter');
is($locale->unit(2, 'volume-megaliter', 'short'), '2 ML', 'English short 2 megaliters');
is($locale->unit(1, 'volume-megaliter'), '1 megaliter', 'English long 1 megaliter');
is($locale->unit(2, 'volume-megaliter'), '2 megaliters', 'English long 2 megaliters');
is($locale->unit(1, 'power-megawatt', 'narrow'), '1MW', 'English narrow 1 megawatt');
is($locale->unit(2, 'power-megawatt', 'narrow'), '2MW', 'English narrow 2 megawatts');
is($locale->unit(1, 'power-megawatt', 'short'), '1 MW', 'English short 1 megawatt');
is($locale->unit(2, 'power-megawatt', 'short'), '2 MW', 'English short 2 megawatts');
is($locale->unit(1, 'power-megawatt'), '1 megawatt', 'English long 1 megawatt');
is($locale->unit(2, 'power-megawatt'), '2 megawatts', 'English long 2 megawatts');
is($locale->unit(1, 'length-meter', 'narrow'), '1m', 'English narrow 1 meter');
is($locale->unit(2, 'length-meter', 'narrow'), '2m', 'English narrow 2 meters');
is($locale->unit(1, 'length-meter', 'short'), '1 m', 'English short 1 meter');
is($locale->unit(2, 'length-meter', 'short'), '2 m', 'English short 2 meters');
is($locale->unit(1, 'length-meter'), '1 meter', 'English long 1 meter');
is($locale->unit(2, 'length-meter'), '2 meters', 'English long 2 meters');
is($locale->unit(1, 'speed-meter-per-second', 'narrow'), '1m/s', 'English narrow 1 meter per second');
is($locale->unit(2, 'speed-meter-per-second', 'narrow'), '2m/s', 'English narrow 2 meters per second');
is($locale->unit(1, 'speed-meter-per-second', 'short'), '1 m/s', 'English short 1 meter per second');
is($locale->unit(2, 'speed-meter-per-second', 'short'), '2 m/s', 'English short 2 meters per second');
is($locale->unit(1, 'speed-meter-per-second'), '1 meter per second', 'English long 1 meter per second');
is($locale->unit(2, 'speed-meter-per-second'), '2 meters per second', 'English long 2 meters per second');
is($locale->unit(1, 'acceleration-meter-per-square-second', 'narrow'), '1m/s²', 'English narrow 1 meter per second squared');
is($locale->unit(2, 'acceleration-meter-per-square-second', 'narrow'), '2m/s²', 'English narrow 2 meters per second squared');
is($locale->unit(1, 'acceleration-meter-per-square-second', 'short'), '1 m/s²', 'English short 1 meter per second squared');
is($locale->unit(2, 'acceleration-meter-per-square-second', 'short'), '2 m/s²', 'English short 2 meters per second squared');
is($locale->unit(1, 'acceleration-meter-per-square-second'), '1 meter per second squared', 'English long 1 meter per second squared');
is($locale->unit(2, 'acceleration-meter-per-square-second'), '2 meters per second squared', 'English long 2 meters per second squared');
is($locale->unit(1, 'mass-tonne', 'narrow'), '1t', 'English narrow 1 metric ton');
is($locale->unit(2, 'mass-tonne', 'narrow'), '2t', 'English narrow 2 metric tons');
is($locale->unit(1, 'mass-tonne', 'short'), '1 t', 'English short 1 metric ton');
is($locale->unit(2, 'mass-tonne', 'short'), '2 t', 'English short 2 metric tons');
is($locale->unit(1, 'mass-tonne'), '1 metric ton', 'English long 1 metric ton');
is($locale->unit(2, 'mass-tonne'), '2 metric tons', 'English long 2 metric tons');
is($locale->unit(1, 'mass-microgram', 'narrow'), '1μg', 'English narrow 1 microgram');
is($locale->unit(2, 'mass-microgram', 'narrow'), '2μg', 'English narrow 2 micrograms');
is($locale->unit(1, 'mass-microgram', 'short'), '1 μg', 'English short 1 microgram');
is($locale->unit(2, 'mass-microgram', 'short'), '2 μg', 'English short 2 micrograms');
is($locale->unit(1, 'mass-microgram'), '1 microgram', 'English long 1 microgram');
is($locale->unit(2, 'mass-microgram'), '2 micrograms', 'English long 2 micrograms');
is($locale->unit(1, 'length-micrometer', 'narrow'), '1μm', 'English narrow 1 micrometer');
is($locale->unit(2, 'length-micrometer', 'narrow'), '2μm', 'English narrow 2 micrometers');
is($locale->unit(1, 'length-micrometer', 'short'), '1 μm', 'English short 1 micrometer');
is($locale->unit(2, 'length-micrometer', 'short'), '2 μm', 'English short 2 micrometers');
is($locale->unit(1, 'length-micrometer'), '1 micrometer', 'English long 1 micrometer');
is($locale->unit(2, 'length-micrometer'), '2 micrometers', 'English long 2 micrometers');
is($locale->unit(1, 'duration-microsecond', 'narrow'), '1μs', 'English narrow 1 microsecond');
is($locale->unit(2, 'duration-microsecond', 'narrow'), '2μs', 'English narrow 2 microseconds');
is($locale->unit(1, 'duration-microsecond', 'short'), '1 μs', 'English short 1 microsecond');
is($locale->unit(2, 'duration-microsecond', 'short'), '2 μs', 'English short 2 microseconds');
is($locale->unit(1, 'duration-microsecond'), '1 microsecond', 'English long 1 microsecond');
is($locale->unit(2, 'duration-microsecond'), '2 microseconds', 'English long 2 microseconds');
is($locale->unit(1, 'length-mile', 'narrow'), '1mi', 'English narrow 1 mile');
is($locale->unit(2, 'length-mile', 'narrow'), '2mi', 'English narrow 2 miles');
is($locale->unit(1, 'length-mile', 'short'), '1 mi', 'English short 1 mile');
is($locale->unit(2, 'length-mile', 'short'), '2 mi', 'English short 2 miles');
is($locale->unit(1, 'length-mile'), '1 mile', 'English long 1 mile');
is($locale->unit(2, 'length-mile'), '2 miles', 'English long 2 miles');
is($locale->unit(1, 'consumption-mile-per-gallon', 'narrow'), '1mpg', 'English narrow 1 mile per gallon');
is($locale->unit(2, 'consumption-mile-per-gallon', 'narrow'), '2mpg', 'English narrow 2 miles per gallon');
is($locale->unit(1, 'consumption-mile-per-gallon', 'short'), '1 mpg', 'English short 1 mile per gallon');
is($locale->unit(2, 'consumption-mile-per-gallon', 'short'), '2 mpg', 'English short 2 miles per gallon');
is($locale->unit(1, 'consumption-mile-per-gallon'), '1 mile per gallon', 'English long 1 mile per gallon');
is($locale->unit(2, 'consumption-mile-per-gallon'), '2 miles per gallon', 'English long 2 miles per gallon');
is($locale->unit(1, 'speed-mile-per-hour', 'narrow'), '1mph', 'English narrow 1 mile per hour');
is($locale->unit(2, 'speed-mile-per-hour', 'narrow'), '2mph', 'English narrow 2 miles per hour');
is($locale->unit(1, 'speed-mile-per-hour', 'short'), '1 mph', 'English short 1 mile per hour');
is($locale->unit(2, 'speed-mile-per-hour', 'short'), '2 mph', 'English short 2 miles per hour');
is($locale->unit(1, 'speed-mile-per-hour'), '1 mile per hour', 'English long 1 mile per hour');
is($locale->unit(2, 'speed-mile-per-hour'), '2 miles per hour', 'English long 2 miles per hour');
is($locale->unit(1, 'electric-milliampere', 'narrow'), '1mA', 'English narrow 1 milliampere');
is($locale->unit(2, 'electric-milliampere', 'narrow'), '2mA', 'English narrow 2 milliamperes');
is($locale->unit(1, 'electric-milliampere', 'short'), '1 mA', 'English short 1 milliampere');
is($locale->unit(2, 'electric-milliampere', 'short'), '2 mA', 'English short 2 milliamperes');
is($locale->unit(1, 'electric-milliampere'), '1 milliampere', 'English long 1 milliampere');
is($locale->unit(2, 'electric-milliampere'), '2 milliamperes', 'English long 2 milliamperes');
is($locale->unit(1, 'pressure-millibar', 'narrow'), '1mb', 'English narrow 1 millibar');
is($locale->unit(2, 'pressure-millibar', 'narrow'), '2mb', 'English narrow 2 millibars');
is($locale->unit(1, 'pressure-millibar', 'short'), '1 mbar', 'English short 1 millibar');
is($locale->unit(2, 'pressure-millibar', 'short'), '2 mbar', 'English short 2 millibars');
is($locale->unit(1, 'pressure-millibar'), '1 millibar', 'English long 1 millibar');
is($locale->unit(2, 'pressure-millibar'), '2 millibars', 'English long 2 millibars');
is($locale->unit(1, 'mass-milligram', 'narrow'), '1mg', 'English narrow 1 milligram');
is($locale->unit(2, 'mass-milligram', 'narrow'), '2mg', 'English narrow 2 milligrams');
is($locale->unit(1, 'mass-milligram', 'short'), '1 mg', 'English short 1 milligram');
is($locale->unit(2, 'mass-milligram', 'short'), '2 mg', 'English short 2 milligrams');
is($locale->unit(1, 'mass-milligram'), '1 milligram', 'English long 1 milligram');
is($locale->unit(2, 'mass-milligram'), '2 milligrams', 'English long 2 milligrams');
is($locale->unit(1, 'volume-milliliter', 'narrow'), '1mL', 'English narrow 1 milliliter');
is($locale->unit(2, 'volume-milliliter', 'narrow'), '2mL', 'English narrow 2 milliliters');
is($locale->unit(1, 'volume-milliliter', 'short'), '1 mL', 'English short 1 milliliter');
is($locale->unit(2, 'volume-milliliter', 'short'), '2 mL', 'English short 2 milliliters');
is($locale->unit(1, 'volume-milliliter'), '1 milliliter', 'English long 1 milliliter');
is($locale->unit(2, 'volume-milliliter'), '2 milliliters', 'English long 2 milliliters');
is($locale->unit(1, 'length-millimeter', 'narrow'), '1mm', 'English narrow 1 millimetre');
is($locale->unit(2, 'length-millimeter', 'narrow'), '2mm', 'English narrow 2 millimetres');
is($locale->unit(1, 'length-millimeter', 'short'), '1 mm', 'English short 1 millimetre');
is($locale->unit(2, 'length-millimeter', 'short'), '2 mm', 'English short 2 millimetres');
is($locale->unit(1, 'length-millimeter'), '1 millimeter', 'English long 1 millimetre');
is($locale->unit(2, 'length-millimeter'), '2 millimeters', 'English long 2 millimetres');
is($locale->unit(1, 'pressure-millimeter-ofhg', 'narrow'), '1mmHg', 'English narrow 1 millimetre of mercury');
is($locale->unit(2, 'pressure-millimeter-ofhg', 'narrow'), '2mmHg', 'English narrow 2 millimetres of mercury');
is($locale->unit(1, 'pressure-millimeter-ofhg', 'short'), '1 mmHg', 'English short 1 millimetre of mercury');
is($locale->unit(2, 'pressure-millimeter-ofhg', 'short'), '2 mmHg', 'English short 2 millimetres of mercury');
is($locale->unit(1, 'pressure-millimeter-ofhg'), '1 millimeter of mercury', 'English long 1 millimetre of mercury');
is($locale->unit(2, 'pressure-millimeter-ofhg'), '2 millimeters of mercury', 'English long 2 millimetres of mercury');
is($locale->unit(1, 'duration-millisecond', 'narrow'), '1ms', 'English narrow 1 millisecond');
is($locale->unit(2, 'duration-millisecond', 'narrow'), '2ms', 'English narrow 2 milliseconds');
is($locale->unit(1, 'duration-millisecond', 'short'), '1 ms', 'English short 1 millisecond');
is($locale->unit(2, 'duration-millisecond', 'short'), '2 ms', 'English short 2 milliseconds');
is($locale->unit(1, 'duration-millisecond'), '1 millisecond', 'English long 1 millisecond');
is($locale->unit(2, 'duration-millisecond'), '2 milliseconds', 'English long 2 milliseconds');
is($locale->unit(1, 'power-milliwatt', 'narrow'), '1mW', 'English narrow 1 milliwatt');
is($locale->unit(2, 'power-milliwatt', 'narrow'), '2mW', 'English narrow 2 milliwatts');
is($locale->unit(1, 'power-milliwatt', 'short'), '1 mW', 'English short 1 milliwatt');
is($locale->unit(2, 'power-milliwatt', 'short'), '2 mW', 'English short 2 milliwatts');
is($locale->unit(1, 'power-milliwatt'), '1 milliwatt', 'English long 1 milliwatt');
is($locale->unit(2, 'power-milliwatt'), '2 milliwatts', 'English long 2 milliwatts');
is($locale->unit(1, 'duration-minute', 'narrow'), '1m', 'English narrow 1 minute');
is($locale->unit(2, 'duration-minute', 'narrow'), '2m', 'English narrow 2 minutes');
is($locale->unit(1, 'duration-minute', 'short'), '1 min', 'English short 1 minute');
is($locale->unit(2, 'duration-minute', 'short'), '2 min', 'English short 2 minutes');
is($locale->unit(1, 'duration-minute'), '1 minute', 'English long 1 minute');
is($locale->unit(2, 'duration-minute'), '2 minutes', 'English long 2 minutes');
is($locale->unit(1, 'duration-month', 'narrow'), '1m', 'English narrow 1 month');
is($locale->unit(2, 'duration-month', 'narrow'), '2m', 'English narrow 2 months');
is($locale->unit(1, 'duration-month', 'short'), '1 mth', 'English short 1 month');
is($locale->unit(2, 'duration-month', 'short'), '2 mths', 'English short 2 months');
is($locale->unit(1, 'duration-month'), '1 month', 'English long 1 month');
is($locale->unit(2, 'duration-month'), '2 months', 'English long 2 months');
is($locale->unit(1, 'length-nanometer', 'narrow'), '1nm', 'English narrow 1 nanometer');
is($locale->unit(2, 'length-nanometer', 'narrow'), '2nm', 'English narrow 2 nanometers');
is($locale->unit(1, 'length-nanometer', 'short'), '1 nm', 'English short 1 nanometer');
is($locale->unit(2, 'length-nanometer', 'short'), '2 nm', 'English short 2 nanometers');
is($locale->unit(1, 'length-nanometer'), '1 nanometer', 'English long 1 nanometer');
is($locale->unit(2, 'length-nanometer'), '2 nanometers', 'English long 2 nanometers');
is($locale->unit(1, 'duration-nanosecond', 'narrow'), '1ns', 'English narrow 1 nanosecond');
is($locale->unit(2, 'duration-nanosecond', 'narrow'), '2ns', 'English narrow 2 nanoseconds');
is($locale->unit(1, 'duration-nanosecond', 'short'), '1 ns', 'English short 1 nanosecond');
is($locale->unit(2, 'duration-nanosecond', 'short'), '2 ns', 'English short 2 nanoseconds');
is($locale->unit(1, 'duration-nanosecond'), '1 nanosecond', 'English long 1 nanosecond');
is($locale->unit(2, 'duration-nanosecond'), '2 nanoseconds', 'English long 2 nanoseconds');
is($locale->unit(1, 'length-nautical-mile', 'narrow'), '1nmi', 'English narrow 1 nautical mile');
is($locale->unit(2, 'length-nautical-mile', 'narrow'), '2nmi', 'English narrow 2 nautical miles');
is($locale->unit(1, 'length-nautical-mile', 'short'), '1 nmi', 'English short 1 nautical mile');
is($locale->unit(2, 'length-nautical-mile', 'short'), '2 nmi', 'English short 2 nautical miles');
is($locale->unit(1, 'length-nautical-mile'), '1 nautical mile', 'English long 1 nautical mile');
is($locale->unit(2, 'length-nautical-mile'), '2 nautical miles', 'English long 2 nautical miles');
is($locale->unit(1, 'electric-ohm', 'narrow'), '1Ω', 'English narrow 1 ohm');
is($locale->unit(2, 'electric-ohm', 'narrow'), '2Ω', 'English narrow 2 ohms');
is($locale->unit(1, 'electric-ohm', 'short'), '1 Ω', 'English short 1 ohm');
is($locale->unit(2, 'electric-ohm', 'short'), '2 Ω', 'English short 2 ohms');
is($locale->unit(1, 'electric-ohm'), '1 ohm', 'English long 1 ohm');
is($locale->unit(2, 'electric-ohm'), '2 ohms', 'English long 2 ohms');
is($locale->unit(1, 'mass-ounce', 'narrow'), '1oz', 'English narrow 1 ounce');
is($locale->unit(2, 'mass-ounce', 'narrow'), '2oz', 'English narrow 2 ounces');
is($locale->unit(1, 'mass-ounce', 'short'), '1 oz', 'English short 1 ounce');
is($locale->unit(2, 'mass-ounce', 'short'), '2 oz', 'English short 2 ounces');
is($locale->unit(1, 'mass-ounce'), '1 ounce', 'English long 1 ounce');
is($locale->unit(2, 'mass-ounce'), '2 ounces', 'English long 2 ounces');
is($locale->unit(1, 'mass-ounce-troy', 'narrow'), '1oz t', 'English narrow 1 troy ounce');
is($locale->unit(2, 'mass-ounce-troy', 'narrow'), '2oz t', 'English narrow 2 troy ounces');
is($locale->unit(1, 'mass-ounce-troy', 'short'), '1 oz t', 'English short 1 troy ounce');
is($locale->unit(2, 'mass-ounce-troy', 'short'), '2 oz t', 'English short 2 troy ounces');
is($locale->unit(1, 'mass-ounce-troy'), '1 troy ounce', 'English long 1 troy ounce');
is($locale->unit(2, 'mass-ounce-troy'), '2 troy ounces', 'English long 2 troy ounces');
is($locale->unit(1, 'speed-millimeter-per-second', 'narrow'), '1mm/s', 'English narrow 1 millimetre per second');
is($locale->unit(2, 'speed-millimeter-per-second', 'narrow'), '2mm/s', 'English narrow 2 millimetres per second');
is($locale->unit(1, 'speed-millimeter-per-second', 'short'), '1 mm/s', 'English short 1 millimetre per second');
is($locale->unit(2, 'speed-millimeter-per-second', 'short'), '2 mm/s', 'English short 2 millimetres per second');
is($locale->unit(1, 'speed-millimeter-per-second'), '1 millimeter per second', 'English long 1 millimetre per second');
is($locale->unit(2, 'speed-millimeter-per-second'), '2 millimeters per second', 'English long 2 millimetres per second');
is($locale->unit(1, 'length-parsec', 'narrow'), '1pc', 'English narrow 1 parsec');
is($locale->unit(2, 'length-parsec', 'narrow'), '2pc', 'English narrow 2 parsecs');
is($locale->unit(1, 'length-parsec', 'short'), '1 pc', 'English short 1 parsec');
is($locale->unit(2, 'length-parsec', 'short'), '2 pc', 'English short 2 parsecs');
is($locale->unit(1, 'length-parsec'), '1 parsec', 'English long 1 parsec');
is($locale->unit(2, 'length-parsec'), '2 parsecs', 'English long 2 parsecs');
is($locale->unit(1, 'length-picometer', 'narrow'), '1pm', 'English narrow 1 picometre');
is($locale->unit(2, 'length-picometer', 'narrow'), '2pm', 'English narrow 2 picometres');
is($locale->unit(1, 'length-picometer', 'short'), '1 pm', 'English short 1 picometre');
is($locale->unit(2, 'length-picometer', 'short'), '2 pm', 'English short 2 picometres');
is($locale->unit(1, 'length-picometer'), '1 picometer', 'English long 1 picometre');
is($locale->unit(2, 'length-picometer'), '2 picometers', 'English long 2 picometres');
is($locale->unit(1, 'volume-pint', 'narrow'), '1pt', 'English narrow 1 pint');
is($locale->unit(2, 'volume-pint', 'narrow'), '2pt', 'English narrow 2 pints');
is($locale->unit(1, 'volume-pint', 'short'), '1 pt', 'English short 1 pint');
is($locale->unit(2, 'volume-pint', 'short'), '2 pt', 'English short 2 pints');
is($locale->unit(1, 'volume-pint'), '1 pint', 'English long 1 pint');
is($locale->unit(2, 'volume-pint'), '2 pints', 'English long 2 pints');
is($locale->unit(1, 'mass-pound', 'narrow'), '1#', 'English narrow 1 pound');
is($locale->unit(2, 'mass-pound', 'narrow'), '2#', 'English narrow 2 pounds');
is($locale->unit(1, 'mass-pound', 'short'), '1 lb', 'English short 1 pound');
is($locale->unit(2, 'mass-pound', 'short'), '2 lb', 'English short 2 pounds');
is($locale->unit(1, 'mass-pound'), '1 pound', 'English long 1 pound');
is($locale->unit(2, 'mass-pound'), '2 pounds', 'English long 2 pounds');
is($locale->unit(1, 'pressure-pound-force-per-square-inch', 'narrow'), '1psi', 'English narrow 1 pound per square inch');
is($locale->unit(2, 'pressure-pound-force-per-square-inch', 'narrow'), '2psi', 'English narrow 2 pounds per square inch');
is($locale->unit(1, 'pressure-pound-force-per-square-inch', 'short'), '1 psi', 'English short 1 pound per square inch');
is($locale->unit(2, 'pressure-pound-force-per-square-inch', 'short'), '2 psi', 'English short 2 pounds per square inch');
is($locale->unit(1, 'pressure-pound-force-per-square-inch'), '1 pound-force per square inch', 'English long 1 pound per square inch');
is($locale->unit(2, 'pressure-pound-force-per-square-inch'), '2 pounds-force per square inch', 'English long 2 pounds per square inch');
is($locale->unit(1, 'volume-quart', 'narrow'), '1qt', 'English narrow 1 quart');
is($locale->unit(2, 'volume-quart', 'narrow'), '2qt', 'English narrow 2 quarts');
is($locale->unit(1, 'volume-quart', 'short'), '1 qt', 'English short 1 quart');
is($locale->unit(2, 'volume-quart', 'short'), '2 qt', 'English short 2 quarts');
is($locale->unit(1, 'volume-quart'), '1 quart', 'English long 1 quart');
is($locale->unit(2, 'volume-quart'), '2 quarts', 'English long 2 quarts');
is($locale->unit(1, 'angle-radian', 'narrow'), '1rad', 'English narrow 1 radian');
is($locale->unit(2, 'angle-radian', 'narrow'), '2rad', 'English narrow 2 radians');
is($locale->unit(1, 'angle-radian', 'short'), '1 rad', 'English short 1 radian');
is($locale->unit(2, 'angle-radian', 'short'), '2 rad', 'English short 2 radians');
is($locale->unit(1, 'angle-radian'), '1 radian', 'English long 1 radian');
is($locale->unit(2, 'angle-radian'), '2 radians', 'English long 2 radians');
is($locale->unit(1, 'duration-second', 'narrow'), '1s', 'English narrow 1 second');
is($locale->unit(2, 'duration-second', 'narrow'), '2s', 'English narrow 2 seconds');
is($locale->unit(1, 'duration-second', 'short'), '1 sec', 'English short 1 second');
is($locale->unit(2, 'duration-second', 'short'), '2 sec', 'English short 2 seconds');
is($locale->unit(1, 'duration-second'), '1 second', 'English long 1 second');
is($locale->unit(2, 'duration-second'), '2 seconds', 'English long 2 seconds');
is($locale->unit(1, 'area-square-centimeter', 'narrow'), '1cm²', 'English narrow 1 square centimeter');
is($locale->unit(2, 'area-square-centimeter', 'narrow'), '2cm²', 'English narrow 2 square centimeters');
is($locale->unit(1, 'area-square-centimeter', 'short'), '1 cm²', 'English short 1 square centimeter');
is($locale->unit(2, 'area-square-centimeter', 'short'), '2 cm²', 'English short 2 square centimeters');
is($locale->unit(1, 'area-square-centimeter'), '1 square centimeter', 'English long 1 square centimeter');
is($locale->unit(2, 'area-square-centimeter'), '2 square centimeters', 'English long 2 square centimeters');
is($locale->unit(1, 'area-square-foot', 'narrow'), '1ft²', 'English narrow 1 square foot');
is($locale->unit(2, 'area-square-foot', 'narrow'), '2ft²', 'English narrow 2 square feet');
is($locale->unit(1, 'area-square-foot', 'short'), '1 sq ft', 'English short 1 square foot');
is($locale->unit(2, 'area-square-foot', 'short'), '2 sq ft', 'English short 2 square feet');
is($locale->unit(1, 'area-square-foot'), '1 square foot', 'English long 1 square foot');
is($locale->unit(2, 'area-square-foot'), '2 square feet', 'English long 2 square feet');
is($locale->unit(1, 'area-square-inch', 'narrow'), '1in²', 'English narrow 1 square inch');
is($locale->unit(2, 'area-square-inch', 'narrow'), '2in²', 'English narrow 2 square inches');
is($locale->unit(1, 'area-square-inch', 'short'), '1 in²', 'English short 1 square inch');
is($locale->unit(2, 'area-square-inch', 'short'), '2 in²', 'English short 2 square inches');
is($locale->unit(1, 'area-square-inch'), '1 square inch', 'English long 1 square inch');
is($locale->unit(2, 'area-square-inch'), '2 square inches', 'English long 2 square inches');
is($locale->unit(1, 'area-square-kilometer', 'narrow'), '1km²', 'English narrow 1 square kilometre');
is($locale->unit(2, 'area-square-kilometer', 'narrow'), '2km²', 'English narrow 2 square kilometres');
is($locale->unit(1, 'area-square-kilometer', 'short'), '1 km²', 'English short 1 square kilometre');
is($locale->unit(2, 'area-square-kilometer', 'short'), '2 km²', 'English short 2 square kilometres');
is($locale->unit(1, 'area-square-kilometer'), '1 square kilometer', 'English long 1 square kilometre');
is($locale->unit(2, 'area-square-kilometer'), '2 square kilometers', 'English long 2 square kilometres');
is($locale->unit(1, 'area-square-meter', 'narrow'), '1m²', 'English narrow 1 square meter');
is($locale->unit(2, 'area-square-meter', 'narrow'), '2m²', 'English narrow 2 square meters');
is($locale->unit(1, 'area-square-meter', 'short'), '1 m²', 'English short 1 square meter');
is($locale->unit(2, 'area-square-meter', 'short'), '2 m²', 'English short 2 square metres');
is($locale->unit(1, 'area-square-meter'), '1 square meter', 'English long 1 square metre');
is($locale->unit(2, 'area-square-meter'), '2 square meters', 'English long 2 square metres');
is($locale->unit(1, 'area-square-mile', 'narrow'), '1mi²', 'English narrow 1 square mile');
is($locale->unit(2, 'area-square-mile', 'narrow'), '2mi²', 'English narrow 2 square miles');
is($locale->unit(1, 'area-square-mile', 'short'), '1 sq mi', 'English short 1 square mile');
is($locale->unit(2, 'area-square-mile', 'short'), '2 sq mi', 'English short 2 square miles');
is($locale->unit(1, 'area-square-mile'), '1 square mile', 'English long 1 square mile');
is($locale->unit(2, 'area-square-mile'), '2 square miles', 'English long 2 square miles');
is($locale->unit(1, 'area-square-yard', 'narrow'), '1yd²', 'English narrow 1 square yard');
is($locale->unit(2, 'area-square-yard', 'narrow'), '2yd²', 'English narrow 2 square yards');
is($locale->unit(1, 'area-square-yard', 'short'), '1 yd²', 'English short 1 square yard');
is($locale->unit(2, 'area-square-yard', 'short'), '2 yd²', 'English short 2 square yards');
is($locale->unit(1, 'area-square-yard'), '1 square yard', 'English long 1 square yard');
is($locale->unit(2, 'area-square-yard'), '2 square yards', 'English long 2 square yards');
is($locale->unit(1, 'mass-stone', 'narrow'), '1st', 'English narrow 1 stone');
is($locale->unit(2, 'mass-stone', 'narrow'), '2st', 'English narrow 2 stones');
is($locale->unit(1, 'mass-stone', 'short'), '1 st', 'English short 1 stone');
is($locale->unit(2, 'mass-stone', 'short'), '2 st', 'English short 2 stones');
is($locale->unit(1, 'mass-stone'), '1 stone', 'English long 1 stone');
is($locale->unit(2, 'mass-stone'), '2 stones', 'English long 2 stones');
is($locale->unit(1, 'volume-tablespoon', 'narrow'), '1tbsp', 'English narrow 1 tablespoon');
is($locale->unit(2, 'volume-tablespoon', 'narrow'), '2tbsp', 'English narrow 2 tablespoons');
is($locale->unit(1, 'volume-tablespoon', 'short'), '1 tbsp', 'English short 1 tablespoon');
is($locale->unit(2, 'volume-tablespoon', 'short'), '2 tbsp', 'English short 2 tablespoons');
is($locale->unit(1, 'volume-tablespoon'), '1 tablespoon', 'English long 1 tablespoon');
is($locale->unit(2, 'volume-tablespoon'), '2 tablespoons', 'English long 2 tablespoons');
is($locale->unit(1, 'volume-teaspoon', 'narrow'), '1tsp', 'English narrow 1 teaspoon');
is($locale->unit(2, 'volume-teaspoon', 'narrow'), '2tsp', 'English narrow 2 teaspoons');
is($locale->unit(1, 'volume-teaspoon', 'short'), '1 tsp', 'English short 1 teaspoon');
is($locale->unit(2, 'volume-teaspoon', 'short'), '2 tsp', 'English short 2 teaspoons');
is($locale->unit(1, 'volume-teaspoon'), '1 teaspoon', 'English long 1 teaspoon');
is($locale->unit(2, 'volume-teaspoon'), '2 teaspoons', 'English long 2 teaspoons');
is($locale->unit(1, 'terabit', 'narrow'), '1Tb', 'English narrow 1 terabit');
is($locale->unit(2, 'terabit', 'narrow'), '2Tb', 'English narrow 2 terabits');
is($locale->unit(1, 'terabit', 'short'), '1 Tb', 'English short 1 terabit');
is($locale->unit(2, 'terabit', 'short'), '2 Tb', 'English short 2 terabits');
is($locale->unit(1, 'terabit'), '1 terabit', 'English long 1 terabit');
is($locale->unit(2, 'terabit'), '2 terabits', 'English long 2 terabits');
is($locale->unit(1, 'terabyte', 'narrow'), '1TB', 'English narrow 1 terabyte');
is($locale->unit(2, 'terabyte', 'narrow'), '2TB', 'English narrow 2 terabytes');
is($locale->unit(1, 'terabyte', 'short'), '1 TB', 'English short 1 terabyte');
is($locale->unit(2, 'terabyte', 'short'), '2 TB', 'English short 2 terabytes');
is($locale->unit(1, 'terabyte'), '1 terabyte', 'English long 1 terabyte');
is($locale->unit(2, 'terabyte'), '2 terabytes', 'English long 2 terabytes');
is($locale->unit(1, 'mass-ton', 'narrow'), '1tn', 'English narrow 1 ton');
is($locale->unit(2, 'mass-ton', 'narrow'), '2tn', 'English narrow 2 tons');
is($locale->unit(1, 'mass-ton', 'short'), '1 tn', 'English short 1 ton');
is($locale->unit(2, 'mass-ton', 'short'), '2 tn', 'English short 2 tons');
is($locale->unit(1, 'mass-ton'), '1 ton', 'English long 1 ton');
is($locale->unit(2, 'mass-ton'), '2 tons', 'English long 2 tons');
is($locale->unit(1, 'electric-volt', 'narrow'), '1V', 'English narrow 1 volt');
is($locale->unit(2, 'electric-volt', 'narrow'), '2V', 'English narrow 2 volts');
is($locale->unit(1, 'electric-volt', 'short'), '1 V', 'English short 1 volt');
is($locale->unit(2, 'electric-volt', 'short'), '2 V', 'English short 2 volts');
is($locale->unit(1, 'electric-volt'), '1 volt', 'English long 1 volt');
is($locale->unit(2, 'electric-volt'), '2 volts', 'English long 2 volts');
is($locale->unit(1, 'power-watt', 'narrow'), '1W', 'English narrow 1 watt');
is($locale->unit(2, 'power-watt', 'narrow'), '2W', 'English narrow 2 watts');
is($locale->unit(1, 'power-watt', 'short'), '1 W', 'English short 1 watt');
is($locale->unit(2, 'power-watt', 'short'), '2 W', 'English short 2 watts');
is($locale->unit(1, 'power-watt'), '1 watt', 'English long 1 watt');
is($locale->unit(2, 'power-watt'), '2 watts', 'English long 2 watts');
is($locale->unit(1, 'duration-week', 'narrow'), '1w', 'English narrow 1 week');
is($locale->unit(2, 'duration-week', 'narrow'), '2w', 'English narrow 2 weeks');
is($locale->unit(1, 'duration-week', 'short'), '1 wk', 'English short 1 week');
is($locale->unit(2, 'duration-week', 'short'), '2 wks', 'English short 2 weeks');
is($locale->unit(1, 'duration-week'), '1 week', 'English long 1 week');
is($locale->unit(2, 'duration-week'), '2 weeks', 'English long 2 weeks');
is($locale->unit(1, 'length-yard', 'narrow'), '1yd', 'English narrow 1 yard');
is($locale->unit(2, 'length-yard', 'narrow'), '2yd', 'English narrow 2 yards');
is($locale->unit(1, 'length-yard', 'short'), '1 yd', 'English short 1 yard');
is($locale->unit(2, 'length-yard', 'short'), '2 yd', 'English short 2 yards');
is($locale->unit(1, 'length-yard'), '1 yard', 'English long 1 yard');
is($locale->unit(2, 'length-yard'), '2 yards', 'English long 2 yards');
is($locale->unit(1, 'duration-year', 'narrow'), '1y', 'English narrow 1 year');
is($locale->unit(2, 'duration-year', 'narrow'), '2y', 'English narrow 2 years');
is($locale->unit(1, 'duration-year', 'short'), '1 yr', 'English short 1 year');
is($locale->unit(2, 'duration-year', 'short'), '2 yrs', 'English short 2 years');
is($locale->unit(1, 'duration-year'), '1 year', 'English long 1 year');
is($locale->unit(2, 'duration-year'), '2 years', 'English long 2 years');
is($locale->duration_unit('hm', 1, 2), '1:02', 'English duration hour, minuet');
is($locale->duration_unit('hms', 1, 2, 3 ), '1:02:03', 'English duration hour, minuet, second');
is($locale->duration_unit('ms', 1, 2 ), '1:02', 'English duration minuet, second');
is($locale->is_yes('Yes'), 1, 'English is yes');
is($locale->is_yes('es'), 0, 'English is not yes');
is($locale->is_no('nO'), 1, 'English is no');
is($locale->is_no('N&'), 0, 'English is not no');