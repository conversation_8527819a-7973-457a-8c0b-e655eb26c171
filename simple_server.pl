#!/usr/bin/perl
use strict;
use warnings;
use HTTP::Server::Simple::CGI;

package MyWebServer;
use base qw(HTTP::Server::Simple::CGI);

sub handle_request {
    my $self = shift;
    my $cgi  = shift;
    
    print "HTTP/1.0 200 OK\r\n";
    print "Content-Type: text/html\r\n\r\n";
    print "<html><head><title>LedgerSMB Test</title></head>";
    print "<body><h1>LedgerSMB Server Test</h1>";
    print "<p>Server is running on port 5500</p>";
    print "<p>Current directory: " . `pwd` . "</p>";
    print "</body></html>";
}

my $pid = MyWebServer->new(5500)->background();
print "Server started with PID $pid on port 5500\n";
print "Visit http://localhost:5500\n";
