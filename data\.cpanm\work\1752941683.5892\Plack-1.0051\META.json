{"abstract": "Perl Superglue for Web frameworks and Web Servers (PSGI toolkit)", "author": ["<PERSON><PERSON><PERSON>"], "dynamic_config": 0, "generated_by": "Dist::Milla version v1.0.22, Dist::Zilla version 6.025, CPAN::Meta::Converter version 2.150010", "license": ["perl_5"], "meta-spec": {"url": "http://search.cpan.org/perldoc?CPAN::Meta::Spec", "version": 2}, "name": "Plack", "no_index": {"directory": ["eg", "examples", "inc", "share", "t", "xt"]}, "prereqs": {"configure": {"requires": {"ExtUtils::MakeMaker": "0", "File::ShareDir::Install": "0.06"}}, "develop": {"requires": {"Dist::Milla": "v1.0.22", "Test::Pod": "1.41"}}, "runtime": {"requires": {"Apache::LogFormat::Compiler": "0.33", "Cookie::Baker": "0.07", "Devel::StackTrace": "1.23", "Devel::StackTrace::AsHTML": "0.11", "File::ShareDir": "1.00", "Filesys::Notify::Simple": "0", "HTTP::Entity::Parser": "0.25", "HTTP::Headers::Fast": "0.18", "HTTP::Message": "5.814", "HTTP::Tiny": "0.034", "Hash::MultiValue": "0.05", "Pod::Usage": "1.36", "Stream::Buffered": "0.02", "Test::TCP": "2.15", "Try::Tiny": "0", "URI": "1.59", "WWW::Form::UrlEncoded": "0.23", "parent": "0", "perl": "5.012000"}, "suggests": {"CGI::Compile": "0", "CGI::Emulate::PSGI": "0", "FCGI": "0", "FCGI::ProcManager": "0", "LWP::UserAgent": "5.814", "Log::Dispatch": "2.25", "Log::Log4perl": "0", "Module::Refresh": "0"}}, "test": {"requires": {"Test::More": "0.88", "Test::Requires": "0"}, "suggests": {"Authen::Simple::Passwd": "0", "CGI::Compile": "0", "CGI::Emulate::PSGI": "0", "HTTP::Headers": "0", "HTTP::Request::AsCGI": "0", "HTTP::Server::Simple::PSGI": "0", "IO::Handle::Util": "0", "LWP::Protocol::http10": "0", "LWP::UserAgent": "5.814", "Log::Dispatch::Array": "0", "MIME::Types": "0", "Test::MockTime::HiRes": "0.06"}}}, "release_status": "stable", "resources": {"bugtracker": {"web": "https://github.com/plack/Plack/issues"}, "homepage": "https://github.com/plack/Plack", "repository": {"type": "git", "url": "https://github.com/plack/Plack.git", "web": "https://github.com/plack/Plack"}}, "version": "1.0051", "x_authority": "cpan:MIYAGAWA", "x_contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> 'fREW' <PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Ashley Pond V <<EMAIL>>", "Ask <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "ben hengst <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "chansen <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "cho45 <<EMAIL>>", "<PERSON> <<EMAIL>>", "chromatic <<EMAIL>>", "Cosimo Streppone <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "dmaestro <<EMAIL>>", "<PERSON>ubi<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "fayland <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "franck cuny <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "hiratara <<EMAIL>>", "HIROSE <PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "kakuno <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <kazu<PERSON><PERSON>@gmail.com>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "mala <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "mickey <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "osfameron <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Perlover <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <piotr.r<PERSON>@gmail.com>", "punytan <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "runarb <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Sawyer X <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "Slaven Rezic <<EMAIL>>", "smcmurray <<EMAIL>>", "<PERSON> <stephen<PERSON><PERSON><EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <***********>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "The Dumb Terminal <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<PERSON><PERSON>@pobox.com>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "vti <<EMAIL>>", "<PERSON> <<EMAIL>>", "xaicron <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "yappo <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "唐鳳 <<EMAIL>>"], "x_generated_by_perl": "v5.34.1", "x_serialization_backend": "Cpanel::JSON::XS version 4.27", "x_spdx_expression": "Artistic-1.0-Perl OR GPL-1.0-or-later", "x_static_install": 1}