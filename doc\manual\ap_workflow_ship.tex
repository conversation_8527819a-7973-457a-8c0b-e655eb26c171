\setlength{\unitlength}{3947sp}%
%
\begingroup\makeatletter\ifx\SetFigFont\undefined%
\gdef\SetFigFont#1#2#3#4#5{%
  \reset@font\fontsize{#1}{#2pt}%
  \fontfamily{#3}\fontseries{#4}\fontshape{#5}%
  \selectfont}%
\fi\endgroup%
\begin{picture}(2424,6024)(4789,-6073)
\thinlines
{\color[rgb]{0,0,0}\put(4801,-886){\framebox(2400,825){}}
}%
{\color[rgb]{0,0,0}\put(4801,-2536){\framebox(2400,825){}}
}%
{\color[rgb]{0,0,0}\put(4801,-4261){\framebox(2400,825){}}
}%
{\color[rgb]{0,0,0}\put(4801,-6061){\framebox(2400,900){}}
}%
{\color[rgb]{0,0,0}\put(6001,-886){\vector( 0,-1){825}}
}%
{\color[rgb]{0,0,0}\put(6001,-2536){\vector( 0,-1){900}}
}%
{\color[rgb]{0,0,0}\put(6001,-4261){\vector( 0,-1){900}}
}%
\put(6001,-2161){\makebox(0,0)[b]{\smash{\SetFigFont{12}{14.4}{\rmdefault}{\mddefault}{\updefault}{\color[rgb]{0,0,0}Purchase Order}%
}}}
\put(6001,-511){\makebox(0,0)[b]{\smash{\SetFigFont{12}{14.4}{\rmdefault}{\mddefault}{\updefault}{\color[rgb]{0,0,0}RFQ}%
}}}
\put(6001,-3886){\makebox(0,0)[b]{\smash{\SetFigFont{12}{14.4}{\rmdefault}{\mddefault}{\updefault}{\color[rgb]{0,0,0}Receiving}%
}}}
\put(6001,-5611){\makebox(0,0)[b]{\smash{\SetFigFont{12}{14.4}{\rmdefault}{\mddefault}{\updefault}{\color[rgb]{0,0,0}AP Invoice}%
}}}
\end{picture}
