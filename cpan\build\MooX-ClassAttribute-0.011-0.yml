---
distribution: !!perl/hash:CPAN::Distribution
  CALLED_FOR: MooX::ClassAttribute
  CHECKSUM_STATUS: OK
  CONTAINSMODS:
    Method::Generate::ClassAccessor: 1
    MooX::CaptainHook: 1
    MooX::CaptainHook::HandleMoose::Hack: 1
    MooX::CaptainHook::OnApplication: 1
    MooX::CaptainHook::OnApplication::Moose: 1
    MooX::CaptainHook::OnInflation: 1
    MooX::ClassAttribute: 1
    MooX::ClassAttribute::HandleMoose: 1
  ID: T/TO/TOBYINK/MooX-ClassAttribute-0.011.tar.gz
  RO:
    CPAN_USERID: TOBYINK
    CPAN_VERSION: '0.011'
    DESCRIPTION: ~
  archived: tar
  badtestcnt: 1
  build_dir: E:\mohammi\LedgerSMB\cpan\build\MooX-ClassAttribute-0.011-0
  incommandcolor: 2
  localfile: E:\mohammi\LedgerSMB\cpan\sources\authors\id\T\TO\TOBYINK\MooX-ClassAttribute-0.011.tar.gz
  make: !!perl/hash:CPAN::Distrostatus
    COMMANDID: 0
    FAILED: false
    TEXT: YES
    TIME: 1752942421
  make_test: !!perl/hash:CPAN::Distrostatus
    COMMANDID: 0
    FAILED: true
    TEXT: NO
    TIME: 1752942429
  mandatory: true
  negative_prefs_cache: 0
  prefs: {}
  prereq_pm:
    build_requires: {}
    opt_build_requires: {}
    opt_requires: {}
    requires:
      Exporter::Shiny: '0'
      Moo: '1.000000'
      Role::Tiny: '1.000000'
      perl: '5.008000'
  reqtype: r
  sponsored_mods: {}
  unwrapped: !!perl/hash:CPAN::Distrostatus
    COMMANDID: 0
    FAILED: false
    TEXT: YES
    TIME: 1752942412
  writemakefile: !!perl/hash:CPAN::Distrostatus
    COMMANDID: 0
    FAILED: false
    TEXT: YES
    TIME: 1752942414
perl:
  $^X: E:\mohammi\LedgerSMB\perl\bin\perl.exe
  mtime_$^X: 1746967770
  mtime_dll: 0
  sitearchexp: E:\mohammi\LedgerSMB\perl\site\lib
time: 1752942429
