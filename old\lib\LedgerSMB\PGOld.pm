=head1 NAME

LedgerSMB::PGOld - Old DBObject replacement for 1.3-era LedgerSMB code

=head1 SYNPOSIS

This is like DBObject but uses the PGObject::Simple for base functionality.

=cut

# This is temporary until we can get rid of it.  Basically the following
# namespaces need to be moved to Moose:
#
# LedgerSMB::Setting
# LedgerSMB::DBObject
# Then we can delete this module.

package LedgerSMB::PGOld;

use strict;
use warnings;

use parent 'PGObject::Simple';

=head1 METHODS

See PGObject::Simple

=over

=item new(%args)

Constructor.

Recognized arguments are:

=over

=item base

A hashref which is imported as properties of the new object.

=back

=cut

sub new {
    my $self = PGObject::Simple::new(@_);
    return $self;
}

sub funcschema {
    my $self = shift;
    return $self->dbh->{private_LedgerSMB}->{schema};
}

=back

=cut

1;
