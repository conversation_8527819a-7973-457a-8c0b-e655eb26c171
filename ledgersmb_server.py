#!/usr/bin/env python3
import http.server
import socketserver
import os
import webbrowser
from urllib.parse import urlparse, parse_qs

class LedgerSMBHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        # Parse the URL
        parsed_path = urlparse(self.path)
        
        # Handle setup.pl requests
        if parsed_path.path == '/setup.pl':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            html_content = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>LedgerSMB Setup</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; }
                    .container { max-width: 800px; margin: 0 auto; }
                    .header { background: #2c5aa0; color: white; padding: 20px; text-align: center; }
                    .content { padding: 20px; border: 1px solid #ddd; }
                    .form-group { margin: 15px 0; }
                    label { display: block; margin-bottom: 5px; font-weight: bold; }
                    input, select { width: 100%; padding: 8px; margin-bottom: 10px; }
                    button { background: #2c5aa0; color: white; padding: 10px 20px; border: none; cursor: pointer; }
                    .info { background: #e7f3ff; padding: 15px; margin: 15px 0; border-left: 4px solid #2c5aa0; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>LedgerSMB Setup</h1>
                        <p>Version 1.13 Development</p>
                    </div>
                    <div class="content">
                        <div class="info">
                            <h3>Database Configuration</h3>
                            <p><strong>Host:</strong> localhost</p>
                            <p><strong>Port:</strong> 5432</p>
                            <p><strong>User:</strong> yemen</p>
                            <p><strong>Database:</strong> ledgersmb_db</p>
                        </div>
                        
                        <h3>Setup Database</h3>
                        <form action="/setup.pl" method="post">
                            <div class="form-group">
                                <label for="company_name">Company Name:</label>
                                <input type="text" id="company_name" name="company_name" value="My Company" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="admin_user">Admin Username:</label>
                                <input type="text" id="admin_user" name="admin_user" value="admin" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="admin_password">Admin Password:</label>
                                <input type="password" id="admin_password" name="admin_password" required>
                            </div>
                            
                            <button type="submit">Create Database</button>
                        </form>
                        
                        <div class="info">
                            <h3>Status</h3>
                            <p>✅ PostgreSQL 14 Connected</p>
                            <p>⚠️ Perl modules need to be installed</p>
                            <p>⚠️ This is a development setup</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            """
            self.wfile.write(html_content.encode())
            return
            
        # Handle login.pl requests
        elif parsed_path.path == '/login.pl':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            html_content = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>LedgerSMB Login</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                    .login-container { max-width: 400px; margin: 100px auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                    .header { text-align: center; margin-bottom: 30px; }
                    .form-group { margin: 15px 0; }
                    label { display: block; margin-bottom: 5px; font-weight: bold; }
                    input, select { width: 100%; padding: 10px; margin-bottom: 10px; border: 1px solid #ddd; border-radius: 4px; }
                    button { width: 100%; background: #2c5aa0; color: white; padding: 12px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
                    button:hover { background: #1e3d6f; }
                </style>
            </head>
            <body>
                <div class="login-container">
                    <div class="header">
                        <h2>LedgerSMB</h2>
                        <p>Accounting System</p>
                    </div>
                    
                    <form action="/login.pl" method="post">
                        <div class="form-group">
                            <label for="username">Username:</label>
                            <input type="text" id="username" name="username" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="password">Password:</label>
                            <input type="password" id="password" name="password" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="database">Database:</label>
                            <select id="database" name="database">
                                <option value="ledgersmb_db">ledgersmb_db</option>
                            </select>
                        </div>
                        
                        <button type="submit">Login</button>
                    </form>
                </div>
            </body>
            </html>
            """
            self.wfile.write(html_content.encode())
            return
            
        # Default handler for other requests
        else:
            super().do_GET()
    
    def do_POST(self):
        # Handle POST requests (form submissions)
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>LedgerSMB - Development Mode</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .container { max-width: 800px; margin: 0 auto; }
                .header { background: #2c5aa0; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; border: 1px solid #ddd; }
                .warning { background: #fff3cd; padding: 15px; margin: 15px 0; border-left: 4px solid #ffc107; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>LedgerSMB Development Server</h1>
                </div>
                <div class="content">
                    <div class="warning">
                        <h3>Development Mode Active</h3>
                        <p>This is a development server. To fully run LedgerSMB, you need to:</p>
                        <ul>
                            <li>Install required Perl modules</li>
                            <li>Configure PostgreSQL database properly</li>
                            <li>Set up proper web server (Apache/nginx)</li>
                        </ul>
                    </div>
                    
                    <h3>Current Status:</h3>
                    <ul>
                        <li>✅ PostgreSQL 14 available</li>
                        <li>✅ Perl 5.40.2 installed</li>
                        <li>⚠️ Missing Perl dependencies</li>
                        <li>⚠️ Development server only</li>
                    </ul>
                    
                    <p><a href="/setup.pl">Back to Setup</a> | <a href="/login.pl">Login Page</a></p>
                </div>
            </div>
        </body>
        </html>
        """
        self.wfile.write(html_content.encode())

# Change to the LedgerSMB directory
os.chdir('E:/mohammi/LedgerSMB')

PORT = 5500
Handler = LedgerSMBHandler

with socketserver.TCPServer(("", PORT), Handler) as httpd:
    print(f"LedgerSMB Development Server running on port {PORT}")
    print(f"Setup: http://localhost:{PORT}/setup.pl")
    print(f"Login: http://localhost:{PORT}/login.pl")
    print("Press Ctrl+C to stop the server")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped.")
