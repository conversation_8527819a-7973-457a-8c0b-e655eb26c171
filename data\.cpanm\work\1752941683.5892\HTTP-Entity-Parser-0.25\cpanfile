requires 'perl', '5.008001';
requires 'Stream::Buffered';
requires 'Module::Load';
requires 'JSON::MaybeXS' => '1.003007';
requires 'Encode';
requires 'HTTP::MultiPartParser';
requires 'File::Temp';
requires 'WWW::Form::UrlEncoded', '0.23';
suggests 'WWW::Form::UrlEncoded::XS', '0.23';
requires 'Hash::MultiValue';

on 'test' => sub {
    requires 'Test::More', '0.98';
    requires 'File::Spec::Functions';
    requires 'Cwd';
    requires 'HTTP::Message' => 6;
};
