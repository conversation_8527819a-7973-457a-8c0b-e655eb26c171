# This is a full (minimal) nginx configuration file
# The development team uses this file on their continuous integration
# (testing) infrastructure. It's here as an example, not meant to be
# installed as-is.

daemon off;
error_log logs/error.log info;
pid /tmp/nginx.pid;
worker_processes 1;

events {
   worker_connections 1024;
}

http {
   client_body_temp_path /tmp/client_body;
   proxy_temp_path /tmp/proxy_temp;
   fastcgi_temp_path /tmp/fastcgi_temp;
   scgi_temp_path /tmp/scgi_temp;
   uwsgi_temp_path /tmp/uwsgi_temp;

   sendfile on;
   tcp_nopush on;
   tcp_nodelay on;
   keepalive_timeout 65;
   types_hash_max_size 2048;
   include /etc/nginx/mime.types;
   default_type application/octet-stream;

   access_log logs/nginx-access.log;
   error_log logs/nginx-error.log info;

   gzip off;
   gzip_static on;

   server {
      listen 5000 default_server;
      listen [::]:5000 default_server ipv6only=on;

      root UI;

      access_log logs/nginx-access.log;
      error_log logs/nginx-error.log info;

      # Don't log status polls
      location /nginx_status {
               stub_status on;
               access_log off;
               allow 127.0.0.1;
               allow ::1;
               deny all;
      }

      # Configuration files don't exist
      location ^~ \.conf$ {
         return 404;
      }

      # 'Hidden' files don't exist
      location ~ /\. {
         return 404;
      }

      location = / {
         return 301 /login.pl;
      }

      location / {
         try_files $uri @strippedprefix @starman;
      }

      location @strippedprefix {
         rewrite ^/([a-z0-9A-Z]+)/(.*) /$2 break;
      }

      location @starman {
         proxy_pass              http://lsmb:5762;
         proxy_read_timeout      300;
         proxy_set_header        Host $host;
         proxy_set_header        X-Real-IP $remote_addr;
         proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
         proxy_set_header        X-Forwarded-Host $host;
         proxy_set_header        X-Forwarded-Server $host;
         proxy_set_header        X-Forwarded-Proto $scheme;
      }
   }
}
