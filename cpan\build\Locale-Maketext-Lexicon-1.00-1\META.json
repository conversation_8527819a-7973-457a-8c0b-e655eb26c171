{"abstract": "Use other catalog formats in Maketext", "author": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "dynamic_config": 0, "generated_by": "Dist::Zilla version 5.013, CPAN::Meta::Converter version 2.133380", "license": ["mit"], "meta-spec": {"url": "http://search.cpan.org/perldoc?CPAN::Meta::Spec", "version": "2"}, "name": "Locale-Maketext-Lexicon", "prereqs": {"configure": {"requires": {"ExtUtils::MakeMaker": "6.30"}}, "develop": {"requires": {"Test::Pod": "1.41"}}, "runtime": {"recommends": {"HTML::Parser": "3.56", "Lingua::EN::Sentence": "0.25", "PPI": "1.203", "Template": "2.20", "Template::Constants": "2.75", "Text::Haml": "0", "YAML": "0.66", "YAML::Loader": "0.66"}, "requires": {"Locale::Maketext": "1.17", "perl": "5.005"}}}, "release_status": "stable", "resources": {"bugtracker": {"web": "https://github.com/clintongormley/locale-maketext-lexicon/issues"}, "homepage": "http://search.cpan.org/dist/Locale-Maketext-Lexicon", "repository": {"type": "git", "url": "git://github.com/clintongormley/locale-maketext-lexicon.git", "web": "https://github.com/clintongormley/locale-maketext-lexicon"}}, "version": "1.00"}