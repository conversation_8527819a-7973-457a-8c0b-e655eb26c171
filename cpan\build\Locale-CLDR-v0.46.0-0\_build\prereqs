do{ my $x = {
       'build_requires' => {
                             'File::Spec' => 0,
                             'Test::Exception' => 0,
                             'Test::More' => '0.98',
                             'ok' => 0
                           },
       'conflicts' => {},
       'recommends' => {},
       'requires' => {
                       'Class::Load' => 0,
                       'DateTime' => '0.72',
                       'DateTime::Locale' => 0,
                       'List::Util' => '1.45',
                       'Moo' => '2',
                       'MooX::ClassAttribute' => '0.011',
                       'Type::Tiny' => 0,
                       'Unicode::Regex::Set' => 0,
                       'bigfloat' => 0,
                       'namespace::autoclean' => '0.16',
                       'perl' => '5.12.0',
                       'version' => '0.95'
                     },
       'test_requires' => {}
     };
$x; }