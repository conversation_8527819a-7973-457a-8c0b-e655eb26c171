---
abstract: 'fork test'
author:
  - '<PERSON><PERSON><PERSON> <tokuhirom  slkjfd gmail.com>'
build_requires:
  App::Prove: '0'
  Test::Builder::Tester: '0'
  Test::Requires: '0'
  Time::HiRes: '0'
configure_requires:
  ExtUtils::MakeMaker: '6.64'
dynamic_config: 0
generated_by: 'Minilla/v3.0.1, CPAN::Meta::Converter version 2.150010'
license: perl
meta-spec:
  url: http://module-build.sourceforge.net/META-spec-v1.4.html
  version: '1.4'
name: Test-SharedFork
no_index:
  directory:
    - t
    - xt
    - inc
    - share
    - eg
    - examples
    - author
    - builder
provides:
  Test::SharedFork:
    file: lib/Test/SharedFork.pm
    version: '0.35'
  Test::SharedFork::Array:
    file: lib/Test/SharedFork/Array.pm
  Test::SharedFork::Scalar:
    file: lib/Test/SharedFork/Scalar.pm
  Test::SharedFork::Store:
    file: lib/Test/SharedFork/Store.pm
requires:
  File::Temp: '0'
  Test::Builder: '0.32'
  Test::Builder::Module: '0'
  Test::More: '0.88'
  perl: 5.008_001
resources:
  bugtracker: https://github.com/tokuhirom/Test-SharedFork/issues
  homepage: https://github.com/tokuhirom/Test-SharedFork
  repository: git://github.com/tokuhirom/Test-SharedFork.git
version: '0.35'
x_contributors:
  - 'lestrrat <<EMAIL>>'
  - 'Vyacheslav Matyukhin <<EMAIL>>'
  - 'Michael G. Schwern <<EMAIL>>'
  - 'Perlover <<EMAIL>>'
  - 'Neil Bowers <<EMAIL>>'
  - 'Rob Hoelz <<EMAIL>>'
  - 'rwhitworth <<EMAIL>>'
  - 'Kenichi Ishigaki <<EMAIL>>'
  - 'Tokuhiro Matsuno <<EMAIL>>'
  - 'Chad Granum <<EMAIL>>'
  - 'Chad Granum <<EMAIL>>'
x_serialization_backend: 'CPAN::Meta::YAML version 0.020'
