// -*- C++ -*-

// Copyright (C) 2005-2023 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the terms
// of the GNU General Public License as published by the Free Software
// Foundation; either version 3, or (at your option) any later
// version.

// This library is distributed in the hope that it will be useful, but
// WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
// General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

// Copyright (C) 2004 <PERSON><PERSON> and <PERSON>, IBM-HRL.

// Permission to use, copy, modify, sell, and distribute this software
// is hereby granted without fee, provided that the above copyright
// notice appears in all copies, and that both that copyright notice
// and this permission notice appear in supporting documentation. None
// of the above authors, nor IBM Haifa Research Laboratories, make any
// representation about the suitability of this software for any
// purpose. It is provided "as is" without express or implied
// warranty.

/**
 * @file cc_hash_table_map_/entry_list_fn_imps.hpp
 * Contains implementations of cc_ht_map_'s entry-list related functions.
 */

#ifdef PB_DS_CLASS_C_DEC

PB_DS_CLASS_T_DEC
void
PB_DS_CLASS_C_DEC::
deallocate_links_in_list(entry_pointer p_e)
{
  while (p_e != 0)
    {
      entry_pointer p_dealloc_e = p_e;
      p_e = p_e->m_p_next;
      s_entry_allocator.deallocate(p_dealloc_e, 1);
    }
}

PB_DS_CLASS_T_DEC
inline typename PB_DS_CLASS_C_DEC::entry_pointer
PB_DS_CLASS_C_DEC::
get_entry(const_reference r_val, true_type)
{
  // Following line might throw an exception.
  entry_pointer p_e = s_entry_allocator.allocate(1);

  // Following lines* cannot* throw an exception.
  new (&p_e->m_value) value_type(r_val);
  return p_e;
}

PB_DS_CLASS_T_DEC
inline typename PB_DS_CLASS_C_DEC::entry_pointer
PB_DS_CLASS_C_DEC::
get_entry(const_reference r_val, false_type)
{
  // Following line might throw an exception.
  entry_pointer p_e = s_entry_allocator.allocate(1);
  cond_dealtor_t cond(p_e);

  // Following lines might throw an exception.
  new (&p_e->m_value) value_type(r_val);
  cond.set_no_action();
  return p_e;
}

PB_DS_CLASS_T_DEC
inline void
PB_DS_CLASS_C_DEC::
rels_entry(entry_pointer p_e)
{
  // The following lines cannot throw exceptions (unless if key-data dtors do).
  p_e->m_value.~value_type();
  s_entry_allocator.deallocate(p_e, 1);
}

#endif
