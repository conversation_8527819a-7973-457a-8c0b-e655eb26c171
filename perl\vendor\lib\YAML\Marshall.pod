=pod

=for comment
DO NOT EDIT. This Pod was generated by Swim v0.1.48.
See http://github.com/ingydotnet/swim-pm#readme

=encoding utf8

=head1 NAME

YAML::Marshall - YAML marshalling class you can mixin to your classes

=head1 SYNOPSIS

    package Bar;
    use Foo -base;
    use YAML::Marshall -mixin;

=head1 DESCRIPTION

For classes that want to handle their own YAML serialization.

=head1 AUTHOR

ingy döt Net <<EMAIL>>

=head1 COPYRIGHT

Copyright 2001-2014. Ingy döt Net

This program is free software; you can redistribute it and/or modify it under
the same terms as Perl itself.

See L<http://www.perl.com/perl/misc/Artistic.html>

=cut
