---
abstract: 'Simple and dumb file system watcher'
author:
  - '<PERSON><PERSON><PERSON> <<EMAIL>>'
build_requires:
  ExtUtils::MakeMaker: '0'
  File::Temp: '0'
  Test::More: '0'
  Test::SharedFork: '0'
configure_requires:
  ExtUtils::MakeMaker: '0'
dynamic_config: 0
generated_by: 'Dist::Milla version v1.0.20, Dist::Zilla version 6.012, CPAN::Meta::Converter version 2.143240, CPAN::Meta::Converter version 2.150010'
license: perl
meta-spec:
  url: http://module-build.sourceforge.net/META-spec-v1.4.html
  version: '1.4'
name: Filesys-Notify-Simple
no_index:
  directory:
    - eg
    - examples
    - inc
    - share
    - t
    - xt
requires:
  perl: '5.008001'
resources:
  bugtracker: https://github.com/miyagawa/Filesys-Notify-Simple/issues
  homepage: https://github.com/miyagawa/Filesys-Notify-Simple
  repository: https://github.com/miyagawa/Filesys-Notify-Simple.git
version: '0.14'
x_contributors:
  - '<PERSON><PERSON>-<PERSON><PERSON><PERSON> <<EMAIL>>'
  - '<PERSON><PERSON>ras <<EMAIL>>'
  - 'Ilya <<EMAIL>>'
  - 'Kenta Sato <<EMAIL>>'
  - 'Kent Fredric <<EMAIL>>'
  - 'Masahiro Chiba <<EMAIL>>'
  - 'Matthew Somerville <<EMAIL>>'
  - 'mono <<EMAIL>>'
  - 'Tatsuhiko Miyagawa <<EMAIL>>'
  - 'Yasutaka ATARASHI <<EMAIL>>'
x_generated_by_perl: v5.20.1
x_serialization_backend: 'CPAN::Meta::YAML version 0.020'
x_static_install: 1
