digraph g {
node [ fontsize = "10", shape = record ];
edge [];

"ac_tax_form" [shape = record, label = "{<col0> \N| entry_id:  integer\lreportable:  boolean\l}" ];

"acc_trans" [shape = record, label = "{<col0> \N| trans_id:  integer\lchart_id:  integer\ltransdate:  date\lsource:  text\lcleared:  boolean\lmemo:  text\linvoice_id:  integer\lapproved:  boolean\lvoucher_id:  integer\lentry_id:  serial\lamount_bc:  numeric\lamount_tc:  numeric\lcurr:  character(3)\l}" ];

"account" [shape = record, label = "{<col0> \N| id:  serial\laccno:  text\ldescription:  text\lis_temp:  boolean\lcategory:  character(1)\lgifi_accno:  text\lheading:  integer\lcontra:  boolean\ltax:  boolean\lobsolete:  boolean\l}" ];

"account_checkpoint" [shape = record, label = "{<col0> \N| end_date:  date\laccount_id:  integer\lid:  serial\ldebits:  numeric\lcredits:  numeric\lamount_bc:  numeric\lamount_tc:  numeric\lcurr:  character(3)\l}" ];

"account_heading" [shape = record, label = "{<col0> \N| id:  serial\laccno:  text\lparent_id:  integer\ldescription:  text\lcategory:  character(1)\l}" ];

"account_heading_translation" [shape = record, label = "{<col0> \N| trans_id:  integer\llanguage_code:  character varying(6)\ldescription:  text\l}" ];

"account_link" [shape = record, label = "{<col0> \N| account_id:  integer\ldescription:  text\l}" ];

"account_link_description" [shape = record, label = "{<col0> \N| description:  text\lsummary:  boolean\lcustom:  boolean\l}" ];

"account_translation" [shape = record, label = "{<col0> \N| trans_id:  integer\llanguage_code:  character varying(6)\ldescription:  text\l}" ];

"ap" [shape = record, label = "{<col0> \N| id:  integer\linvnumber:  text\ltransdate:  date\lentity_id:  integer\ltaxincluded:  boolean\lduedate:  date\linvoice:  boolean\lordnumber:  text\lcurr:  character(3)\lnotes:  text\lperson_id:  integer\ltill:  character varying(20)\lquonumber:  text\lintnotes:  text\lshipvia:  text\llanguage_code:  character varying(6)\lponumber:  text\lshippingpoint:  text\lon_hold:  boolean\lapproved:  boolean\lreverse:  boolean\lterms:  smallint\ldescription:  text\lforce_closed:  boolean\lcrdate:  date\lis_return:  boolean\lentity_credit_account:  integer\lamount_bc:  numeric\lamount_tc:  numeric\lnetamount_bc:  numeric\lnetamount_tc:  numeric\l}" ];

"ar" [shape = record, label = "{<col0> \N| id:  integer\linvnumber:  text\ltransdate:  date\lentity_id:  integer\ltaxincluded:  boolean\lduedate:  date\linvoice:  boolean\lshippingpoint:  text\lterms:  smallint\lnotes:  text\lcurr:  character(3)\lordnumber:  text\lperson_id:  integer\ltill:  character varying(20)\lquonumber:  text\lintnotes:  text\lshipvia:  text\llanguage_code:  character varying(6)\lponumber:  text\lon_hold:  boolean\lreverse:  boolean\lapproved:  boolean\lentity_credit_account:  integer\lforce_closed:  boolean\ldescription:  text\lis_return:  boolean\lcrdate:  date\lsetting_sequence:  text\lamount_bc:  numeric\lamount_tc:  numeric\lnetamount_bc:  numeric\lnetamount_tc:  numeric\l}" ];

"assembly" [shape = record, label = "{<col0> \N| id:  integer\lparts_id:  integer\lqty:  numeric\lbom:  boolean\ladj:  boolean\l}" ];

"asset_class" [shape = record, label = "{<col0> \N| id:  serial\llabel:  text\lasset_account_id:  integer\ldep_account_id:  integer\lmethod:  integer\l}" ];

"asset_dep_method" [shape = record, label = "{<col0> \N| id:  serial\lmethod:  text\lsproc:  text\lunit_label:  text\lshort_name:  text\lunit_class:  integer\l}" ];

"asset_disposal_method" [shape = record, label = "{<col0> \N| label:  text\lid:  serial\lmultiple:  integer\lshort_label:  character(1)\l}" ];

"asset_item" [shape = record, label = "{<col0> \N| id:  serial\ldescription:  text\ltag:  text\lpurchase_value:  numeric\lsalvage_value:  numeric\lusable_life:  numeric\lpurchase_date:  date\lstart_depreciation:  date\llocation_id:  integer\ldepartment_id:  integer\linvoice_id:  integer\lasset_account_id:  integer\ldep_account_id:  integer\lexp_account_id:  integer\lobsolete_by:  integer\lasset_class_id:  integer\l}" ];

"asset_note" [shape = record, label = "{<col0> \N| id:  integer\lnote_class:  integer\lnote:  text\lvector:  tsvector\lcreated:  timestamp without time zone\lcreated_by:  text\lref_key:  integer\lsubject:  text\l}" ];

"asset_report" [shape = record, label = "{<col0> \N| id:  serial\lreport_date:  date\lgl_id:  bigint\lasset_class:  bigint\lreport_class:  integer\lentered_by:  bigint\lapproved_by:  bigint\lentered_at:  timestamp without time zone\lapproved_at:  timestamp without time zone\ldepreciated_qty:  numeric\ldont_approve:  boolean\lsubmitted:  boolean\l}" ];

"asset_report_class" [shape = record, label = "{<col0> \N| id:  integer\lclass:  text\l}" ];

"asset_report_line" [shape = record, label = "{<col0> \N| asset_id:  bigint\lreport_id:  bigint\lamount:  numeric\ldepartment_id:  integer\lwarehouse_id:  integer\l}" ];

"asset_rl_to_disposal_method" [shape = record, label = "{<col0> \N| report_id:  integer\lasset_id:  integer\ldisposal_method_id:  integer\lpercent_disposed:  numeric\l}" ];

"asset_unit_class" [shape = record, label = "{<col0> \N| id:  integer\lclass:  text\l}" ];

"audittrail" [shape = record, label = "{<col0> \N| trans_id:  integer\ltablename:  text\lreference:  text\lformname:  text\laction:  text\ltransdate:  timestamp without time zone\lperson_id:  integer\lentry_id:  bigserial\lrolname:  text\l}" ];

"batch" [shape = record, label = "{<col0> \N| id:  serial\lbatch_class_id:  integer\lcontrol_code:  text\ldescription:  text\ldefault_date:  date\lapproved_on:  date\lapproved_by:  integer\lcreated_by:  integer\llocked_by:  integer\lcreated_on:  date\l}" ];

"batch_class" [shape = record, label = "{<col0> \N| id:  serial\lclass:  character varying\l}" ];

"bu_class_to_module" [shape = record, label = "{<col0> \N| bu_class_id:  integer\lmodule_id:  integer\l}" ];

"budget_info" [shape = record, label = "{<col0> \N| id:  serial\lstart_date:  date\lend_date:  date\lreference:  text\ldescription:  text\lentered_by:  integer\lapproved_by:  integer\lobsolete_by:  integer\lentered_at:  timestamp without time zone\lapproved_at:  timestamp without time zone\lobsolete_at:  timestamp without time zone\l}" ];

"budget_line" [shape = record, label = "{<col0> \N| budget_id:  integer\laccount_id:  integer\ldescription:  text\lamount:  numeric\lamount_tc:  numeric\lcurr:  character(3)\l}" ];

"budget_note" [shape = record, label = "{<col0> \N| id:  integer\lnote_class:  integer\lnote:  text\lvector:  tsvector\lcreated:  timestamp without time zone\lcreated_by:  text\lref_key:  integer\lsubject:  text\l}" ];

"budget_to_business_unit" [shape = record, label = "{<col0> \N| budget_id:  integer\lbu_id:  integer\lbu_class:  integer\l}" ];

"business" [shape = record, label = "{<col0> \N| id:  serial\ldescription:  text\ldiscount:  numeric\llast_updated:  timestamp without time zone\l}" ];

"business_unit" [shape = record, label = "{<col0> \N| id:  serial\lclass_id:  integer\lcontrol_code:  text\ldescription:  text\lstart_date:  date\lend_date:  date\lparent_id:  integer\lcredit_id:  integer\l}" ];

"business_unit_ac" [shape = record, label = "{<col0> \N| entry_id:  integer\lclass_id:  integer\lbu_id:  integer\l}" ];

"business_unit_class" [shape = record, label = "{<col0> \N| id:  serial\llabel:  text\lactive:  boolean\lordering:  integer\l}" ];

"business_unit_inv" [shape = record, label = "{<col0> \N| entry_id:  integer\lclass_id:  integer\lbu_id:  integer\l}" ];

"business_unit_jl" [shape = record, label = "{<col0> \N| entry_id:  integer\lbu_class:  integer\lbu_id:  integer\l}" ];

"business_unit_oitem" [shape = record, label = "{<col0> \N| entry_id:  integer\lclass_id:  integer\lbu_id:  integer\l}" ];

"business_unit_translation" [shape = record, label = "{<col0> \N| trans_id:  integer\llanguage_code:  character varying(6)\ldescription:  text\l}" ];

"company" [shape = record, label = "{<col0> \N| id:  serial\lentity_id:  integer\llegal_name:  text\ltax_id:  text\lsales_tax_id:  text\llicense_number:  text\lsic_code:  character varying\lcreated:  date\l}" ];

"contact_class" [shape = record, label = "{<col0> \N| id:  serial\lclass:  text\l}" ];

"country" [shape = record, label = "{<col0> \N| id:  serial\lname:  text\lshort_name:  text\litu:  text\l}" ];

"country_tax_form" [shape = record, label = "{<col0> \N| country_id:  integer\lform_name:  text\lid:  serial\ldefault_reportable:  boolean\lis_accrual:  boolean\l}" ];

"cr_coa_to_account" [shape = record, label = "{<col0> \N| chart_id:  integer\laccount:  text\l}" ];

"cr_report" [shape = record, label = "{<col0> \N| id:  bigserial\lchart_id:  integer\ltheir_total:  numeric\lapproved:  boolean\lsubmitted:  boolean\lend_date:  date\lupdated:  timestamp without time zone\lentered_by:  integer\lentered_username:  text\ldeleted:  boolean\ldeleted_by:  integer\lapproved_by:  integer\lapproved_username:  text\lrecon_fx:  boolean\l}" ];

"cr_report_line" [shape = record, label = "{<col0> \N| id:  bigserial\lreport_id:  integer\lscn:  text\ltheir_balance:  numeric\lour_balance:  numeric\luser:  integer\lclear_time:  date\linsert_time:  timestamp with time zone\ltrans_type:  text\lpost_date:  date\lcleared:  boolean\l}" ];

"cr_report_line_links" [shape = record, label = "{<col0> \N| report_line_id:  integer\lentry_id:  integer\lunique_exempt:  boolean\lcleared:  boolean\l}" ];

"currency" [shape = record, label = "{<col0> \N| curr:  character(3)\ldescription:  text\l}" ];

"db_patch_log" [shape = record, label = "{<col0> \N| when_applied:  timestamp without time zone\lpath:  text\lsha:  text\lsqlstate:  text\lerror:  text\l}" ];

"db_patches" [shape = record, label = "{<col0> \N| sha:  text\lpath:  text\llast_updated:  timestamp without time zone\l}" ];

"defaults" [shape = record, label = "{<col0> \N| setting_key:  text\lvalue:  text\l}" ];

"eca_invoice" [shape = record, label = "{<col0> \N| order_id:  integer\ljournal_id:  integer\lon_hold:  boolean\lreverse:  boolean\lcredit_id:  integer\ldue:  date\llanguage_code:  character(6)\lforce_closed:  boolean\lorder_number:  text\l}" ];

"eca_note" [shape = record, label = "{<col0> \N| id:  integer\lnote_class:  integer\lnote:  text\lvector:  tsvector\lcreated:  timestamp without time zone\lcreated_by:  text\lref_key:  integer\lsubject:  text\l}" ];

"eca_tax" [shape = record, label = "{<col0> \N| eca_id:  integer\lchart_id:  integer\l}" ];

"eca_to_contact" [shape = record, label = "{<col0> \N| credit_id:  integer\lcontact_class_id:  integer\lcontact:  text\ldescription:  text\l}" ];

"eca_to_location" [shape = record, label = "{<col0> \N| location_id:  integer\llocation_class:  integer\lcredit_id:  integer\l}" ];

"email" [shape = record, label = "{<col0> \N| workflow_id:  integer\lfrom:  text\lto:  text\lcc:  text\lbcc:  text\lnotify:  boolean\lsubject:  text\lbody:  text\lsent_date:  date\lexpansions:  jsonb\l}" ];

"employee_class" [shape = record, label = "{<col0> \N| label:  text\lid:  serial\l}" ];

"employee_to_ec" [shape = record, label = "{<col0> \N| employee_id:  integer\lec_id:  integer\l}" ];

"entity" [shape = record, label = "{<col0> \N| id:  serial\lname:  text\lentity_class:  integer\lcreated:  date\lcontrol_code:  text\lcountry_id:  integer\l}" ];

"entity_bank_account" [shape = record, label = "{<col0> \N| id:  serial\lentity_id:  integer\lbic:  character varying\liban:  character varying\lremark:  character varying\l}" ];

"entity_class" [shape = record, label = "{<col0> \N| id:  serial\lclass:  text\lactive:  boolean\l}" ];

"entity_credit_account" [shape = record, label = "{<col0> \N| id:  serial\lentity_id:  integer\lentity_class:  integer\lpay_to_name:  text\ldiscount:  numeric\ldescription:  text\ldiscount_terms:  integer\ldiscount_account_id:  integer\ltaxincluded:  boolean\lcreditlimit:  numeric\lterms:  smallint\lmeta_number:  character varying(32)\lbusiness_id:  integer\llanguage_code:  character varying(6)\lpricegroup_id:  integer\lcurr:  character(3)\lstartdate:  date\lenddate:  date\lthreshold:  numeric\lemployee_id:  integer\lprimary_contact:  integer\lar_ap_account_id:  integer\lcash_account_id:  integer\lbank_account:  integer\ltaxform_id:  integer\l}" ];

"entity_employee" [shape = record, label = "{<col0> \N| entity_id:  integer\lstartdate:  date\lenddate:  date\lrole:  character varying(20)\lssn:  text\lsales:  boolean\lmanager_id:  integer\lemployeenumber:  character varying(32)\ldob:  date\lis_manager:  boolean\l}" ];

"entity_note" [shape = record, label = "{<col0> \N| id:  integer\lnote_class:  integer\lnote:  text\lvector:  tsvector\lcreated:  timestamp without time zone\lcreated_by:  text\lref_key:  integer\lsubject:  text\lentity_id:  integer\l}" ];

"entity_other_name" [shape = record, label = "{<col0> \N| entity_id:  integer\lother_name:  text\l}" ];

"entity_to_contact" [shape = record, label = "{<col0> \N| entity_id:  integer\lcontact_class_id:  integer\lcontact:  text\ldescription:  text\l}" ];

"entity_to_location" [shape = record, label = "{<col0> \N| location_id:  integer\llocation_class:  integer\lentity_id:  integer\l}" ];

"exchangerate_default" [shape = record, label = "{<col0> \N| rate_type:  integer\lcurr:  character(3)\lvalid_from:  date\lvalid_to:  date\lrate:  numeric\l}" ];

"exchangerate_type" [shape = record, label = "{<col0> \N| id:  serial\ldescription:  text\lbuiltin:  boolean\l}" ];

"file_base" [shape = record, label = "{<col0> \N| content:  bytea\lmime_type_id:  integer\lfile_name:  text\ldescription:  text\luploaded_by:  integer\luploaded_at:  timestamp without time zone\lid:  serial\lref_key:  integer\lfile_class:  integer\l}" ];

"file_class" [shape = record, label = "{<col0> \N| id:  serial\lclass:  text\l}" ];

"file_eca" [shape = record, label = "{<col0> \N| content:  bytea\lmime_type_id:  integer\lfile_name:  text\ldescription:  text\luploaded_by:  integer\luploaded_at:  timestamp without time zone\lid:  integer\lref_key:  integer\lfile_class:  integer\l}" ];

"file_email" [shape = record, label = "{<col0> \N| content:  bytea\lmime_type_id:  integer\lfile_name:  text\ldescription:  text\luploaded_by:  integer\luploaded_at:  timestamp without time zone\lid:  integer\lref_key:  integer\lfile_class:  integer\l}" ];

"file_entity" [shape = record, label = "{<col0> \N| content:  bytea\lmime_type_id:  integer\lfile_name:  text\ldescription:  text\luploaded_by:  integer\luploaded_at:  timestamp without time zone\lid:  integer\lref_key:  integer\lfile_class:  integer\l}" ];

"file_incoming" [shape = record, label = "{<col0> \N| content:  bytea\lmime_type_id:  integer\lfile_name:  text\ldescription:  text\luploaded_by:  integer\luploaded_at:  timestamp without time zone\lid:  integer\lref_key:  integer\lfile_class:  integer\l}" ];

"file_internal" [shape = record, label = "{<col0> \N| content:  bytea\lmime_type_id:  integer\lfile_name:  text\ldescription:  text\luploaded_by:  integer\luploaded_at:  timestamp without time zone\lid:  integer\lref_key:  integer\lfile_class:  integer\l}" ];

"file_order" [shape = record, label = "{<col0> \N| content:  bytea\lmime_type_id:  integer\lfile_name:  text\ldescription:  text\luploaded_by:  integer\luploaded_at:  timestamp without time zone\lid:  integer\lref_key:  integer\lfile_class:  integer\l}" ];

"file_order_to_order" [shape = record, label = "{<col0> \N| file_id:  integer\lsource_class:  integer\lref_key:  integer\ldest_class:  integer\lattached_by:  integer\lattached_at:  timestamp without time zone\l}" ];

"file_order_to_tx" [shape = record, label = "{<col0> \N| file_id:  integer\lsource_class:  integer\lref_key:  integer\ldest_class:  integer\lattached_by:  integer\lattached_at:  timestamp without time zone\l}" ];

"file_part" [shape = record, label = "{<col0> \N| content:  bytea\lmime_type_id:  integer\lfile_name:  text\ldescription:  text\luploaded_by:  integer\luploaded_at:  timestamp without time zone\lid:  integer\lref_key:  integer\lfile_class:  integer\l}" ];

"file_reconciliation" [shape = record, label = "{<col0> \N| content:  bytea\lmime_type_id:  integer\lfile_name:  text\ldescription:  text\luploaded_by:  integer\luploaded_at:  timestamp without time zone\lid:  integer\lref_key:  integer\lfile_class:  integer\l}" ];

"file_secondary_attachment" [shape = record, label = "{<col0> \N| file_id:  integer\lsource_class:  integer\lref_key:  integer\ldest_class:  integer\lattached_by:  integer\lattached_at:  timestamp without time zone\l}" ];

"file_transaction" [shape = record, label = "{<col0> \N| content:  bytea\lmime_type_id:  integer\lfile_name:  text\ldescription:  text\luploaded_by:  integer\luploaded_at:  timestamp without time zone\lid:  integer\lref_key:  integer\lfile_class:  integer\l}" ];

"file_tx_to_order" [shape = record, label = "{<col0> \N| file_id:  integer\lsource_class:  integer\lref_key:  integer\ldest_class:  integer\lattached_by:  integer\lattached_at:  timestamp without time zone\l}" ];

"file_view_catalog" [shape = record, label = "{<col0> \N| file_class:  integer\lview_name:  text\l}" ];

"gifi" [shape = record, label = "{<col0> \N| accno:  text\ldescription:  text\llast_updated:  timestamp without time zone\l}" ];

"gl" [shape = record, label = "{<col0> \N| id:  integer\lreference:  text\ldescription:  text\ltransdate:  date\lperson_id:  integer\lnotes:  text\lapproved:  boolean\ltrans_type_code:  character(2)\l}" ];

"inventory_report" [shape = record, label = "{<col0> \N| id:  serial\ltransdate:  date\lsource:  text\ltrans_id:  integer\l}" ];

"inventory_report_line" [shape = record, label = "{<col0> \N| adjust_id:  integer\lparts_id:  integer\lcounted:  numeric\lexpected:  numeric\lvariance:  numeric\l}" ];

"invoice" [shape = record, label = "{<col0> \N| id:  serial\ltrans_id:  integer\lparts_id:  integer\ldescription:  text\lqty:  numeric\lallocated:  numeric\lsellprice:  numeric\lprecision:  integer\lfxsellprice:  numeric\ldiscount:  numeric\lassemblyitem:  boolean\lunit:  character varying\ldeliverydate:  date\lserialnumber:  text\lvendor_sku:  text\lnotes:  text\l}" ];

"invoice_note" [shape = record, label = "{<col0> \N| id:  integer\lnote_class:  integer\lnote:  text\lvector:  tsvector\lcreated:  timestamp without time zone\lcreated_by:  text\lref_key:  integer\lsubject:  text\l}" ];

"invoice_tax_form" [shape = record, label = "{<col0> \N| invoice_id:  integer\lreportable:  boolean\l}" ];

"jcitems" [shape = record, label = "{<col0> \N| id:  serial\lbusiness_unit_id:  integer\lparts_id:  integer\ldescription:  text\lqty:  numeric\lallocated:  numeric\lsellprice:  numeric\lfxsellprice:  numeric\lserialnumber:  text\lcheckedin:  timestamp with time zone\lcheckedout:  timestamp with time zone\lperson_id:  integer\lnotes:  text\ltotal:  numeric\lnon_billable:  numeric\ljctype:  integer\lcurr:  character(3)\l}" ];

"jctype" [shape = record, label = "{<col0> \N| id:  integer\llabel:  text\ldescription:  text\lis_service:  boolean\lis_timecard:  boolean\l}" ];

"job" [shape = record, label = "{<col0> \N| bu_id:  integer\lparts_id:  integer\lproduction:  numeric\lcompleted:  numeric\l}" ];

"journal_entry" [shape = record, label = "{<col0> \N| id:  serial\lreference:  text\ldescription:  text\llocked_by:  integer\ljournal:  integer\lpost_date:  date\leffective_start:  date\leffective_end:  date\lcurrency:  character(3)\lapproved:  boolean\lis_template:  boolean\lentered_by:  integer\lapproved_by:  integer\l}" ];

"journal_line" [shape = record, label = "{<col0> \N| id:  serial\laccount_id:  integer\ljournal_id:  integer\lamount:  numeric\lcleared:  boolean\lreconciliation_report:  integer\lline_type:  text\lamount_tc:  numeric\lcurr:  character(3)\l}" ];

"journal_note" [shape = record, label = "{<col0> \N| id:  integer\lnote_class:  integer\lnote:  text\lvector:  tsvector\lcreated:  timestamp without time zone\lcreated_by:  text\lref_key:  integer\lsubject:  text\linternal_only:  boolean\l}" ];

"journal_type" [shape = record, label = "{<col0> \N| id:  serial\lname:  text\l}" ];

"language" [shape = record, label = "{<col0> \N| code:  character varying(6)\ldescription:  text\llast_updated:  timestamp without time zone\l}" ];

"location" [shape = record, label = "{<col0> \N| id:  serial\lline_one:  text\lline_two:  text\lline_three:  text\lcity:  text\lstate:  text\lcountry_id:  integer\lmail_code:  text\lcreated:  date\linactive_date:  timestamp without time zone\lactive:  boolean\l}" ];

"location_class" [shape = record, label = "{<col0> \N| id:  serial\lclass:  text\lauthoritative:  boolean\l}" ];

"location_class_to_entity_class" [shape = record, label = "{<col0> \N| id:  serial\llocation_class:  integer\lentity_class:  integer\l}" ];

"lsmb_module" [shape = record, label = "{<col0> \N| id:  integer\llabel:  text\l}" ];

"lsmb_sequence" [shape = record, label = "{<col0> \N| label:  text\lsetting_key:  text\lprefix:  text\lsuffix:  text\lsequence:  text\laccept_input:  boolean\l}" ];

"makemodel" [shape = record, label = "{<col0> \N| parts_id:  integer\lbarcode:  text\lmake:  text\lmodel:  text\l}" ];

"menu_acl" [shape = record, label = "{<col0> \N| id:  serial\lrole_name:  character varying\lacl_type:  character varying\lnode_id:  integer\l}" ];

"menu_node" [shape = record, label = "{<col0> \N| id:  serial\llabel:  character varying\lparent:  integer\lposition:  integer\lurl:  text\lstandalone:  boolean\lmenu:  boolean\l}" ];

"mfg_lot" [shape = record, label = "{<col0> \N| id:  serial\llot_number:  text\lparts_id:  integer\lqty:  numeric\lstock_date:  date\l}" ];

"mfg_lot_item" [shape = record, label = "{<col0> \N| id:  serial\lmfg_lot_id:  integer\lparts_id:  integer\lqty:  numeric\l}" ];

"mime_type" [shape = record, label = "{<col0> \N| id:  serial\lmime_type:  text\linvoice_include:  boolean\l}" ];

"new_shipto" [shape = record, label = "{<col0> \N| id:  serial\ltrans_id:  integer\loe_id:  integer\llocation_id:  integer\l}" ];

"note" [shape = record, label = "{<col0> \N| id:  serial\lnote_class:  integer\lnote:  text\lvector:  tsvector\lcreated:  timestamp without time zone\lcreated_by:  text\lref_key:  integer\lsubject:  text\l}" ];

"note_class" [shape = record, label = "{<col0> \N| id:  serial\lclass:  text\l}" ];

"oe" [shape = record, label = "{<col0> \N| id:  serial\lordnumber:  text\ltransdate:  date\lentity_id:  integer\lamount_tc:  numeric\lnetamount_tc:  numeric\lreqdate:  date\ltaxincluded:  boolean\lshippingpoint:  text\lnotes:  text\lcurr:  character(3)\lperson_id:  integer\lclosed:  boolean\lquotation:  boolean\lquonumber:  text\lintnotes:  text\lshipvia:  text\llanguage_code:  character varying(6)\lponumber:  text\lterms:  smallint\lentity_credit_account:  integer\loe_class_id:  integer\lworkflow_id:  integer\l}" ];

"oe_class" [shape = record, label = "{<col0> \N| id:  smallint\loe_class:  text\l}" ];

"open_forms" [shape = record, label = "{<col0> \N| id:  serial\lsession_id:  integer\lform_name:  character varying(100)\llast_used:  timestamp without time zone\l}" ];

"orderitems" [shape = record, label = "{<col0> \N| id:  serial\ltrans_id:  integer\lparts_id:  integer\ldescription:  text\lqty:  numeric\lsellprice:  numeric\lprecision:  integer\ldiscount:  numeric\lunit:  character varying(5)\lreqdate:  date\lship:  numeric\lserialnumber:  text\lnotes:  text\l}" ];

"parts" [shape = record, label = "{<col0> \N| id:  serial\lpartnumber:  text\ldescription:  text\lunit:  character varying(5)\llistprice:  numeric\lsellprice:  numeric\llastcost:  numeric\lpriceupdate:  date\lweight:  numeric\lonhand:  numeric\lnotes:  text\lmakemodel:  boolean\lassembly:  boolean\lalternate:  boolean\lrop:  numeric\linventory_accno_id:  integer\lincome_accno_id:  integer\lexpense_accno_id:  integer\lreturns_accno_id:  integer\lbin:  text\lobsolete:  boolean\lbom:  boolean\limage:  text\ldrawing:  text\lmicrofiche:  text\lpartsgroup_id:  integer\lavgcost:  numeric\l}" ];

"parts_translation" [shape = record, label = "{<col0> \N| trans_id:  integer\llanguage_code:  character varying(6)\ldescription:  text\l}" ];

"partscustomer" [shape = record, label = "{<col0> \N| parts_id:  integer\lcredit_id:  integer\lpricegroup_id:  integer\lpricebreak:  numeric\lsellprice:  numeric\lvalidfrom:  date\lvalidto:  date\lcurr:  character(3)\lentry_id:  serial\lqty:  numeric\l}" ];

"partsgroup" [shape = record, label = "{<col0> \N| id:  serial\lpartsgroup:  text\lparent:  integer\l}" ];

"partsgroup_translation" [shape = record, label = "{<col0> \N| trans_id:  integer\llanguage_code:  character varying(6)\ldescription:  text\l}" ];

"partstax" [shape = record, label = "{<col0> \N| parts_id:  integer\lchart_id:  integer\ltaxcategory_id:  integer\l}" ];

"partsvendor" [shape = record, label = "{<col0> \N| credit_id:  integer\lparts_id:  integer\lpartnumber:  text\lleadtime:  smallint\llastcost:  numeric\lcurr:  character(3)\lentry_id:  serial\l}" ];

"payment" [shape = record, label = "{<col0> \N| id:  serial\lreference:  text\lgl_id:  integer\lpayment_class:  integer\lpayment_date:  date\lclosed:  boolean\lentity_credit_id:  integer\lemployee_id:  integer\lcurrency:  character(3)\lnotes:  text\lreversing:  integer\l}" ];

"payment_links" [shape = record, label = "{<col0> \N| payment_id:  integer\lentry_id:  integer\ltype:  integer\l}" ];

"payment_type" [shape = record, label = "{<col0> \N| id:  serial\llabel:  text\l}" ];

"payroll_deduction" [shape = record, label = "{<col0> \N| entry_id:  serial\lentity_id:  integer\ltype_id:  integer\lrate:  numeric\l}" ];

"payroll_deduction_class" [shape = record, label = "{<col0> \N| id:  integer\lcountry_id:  integer\llabel:  text\lstored_proc_name:  name\l}" ];

"payroll_deduction_type" [shape = record, label = "{<col0> \N| id:  serial\laccount_id:  integer\lpdc_id:  integer\lcountry_id:  integer\llabel:  text\lunit:  text\ldefault_amount:  numeric\lcalc_percent:  boolean\l}" ];

"payroll_employee_class" [shape = record, label = "{<col0> \N| id:  serial\llabel:  text\l}" ];

"payroll_employee_class_to_income_type" [shape = record, label = "{<col0> \N| ec_id:  integer\lit_id:  integer\l}" ];

"payroll_income_category" [shape = record, label = "{<col0> \N| id:  serial\llabel:  text\l}" ];

"payroll_income_class" [shape = record, label = "{<col0> \N| id:  integer\lcountry_id:  integer\llabel:  text\l}" ];

"payroll_income_type" [shape = record, label = "{<col0> \N| id:  serial\laccount_id:  integer\lpic_id:  integer\lcountry_id:  integer\llabel:  text\lunit:  text\ldefault_amount:  numeric\l}" ];

"payroll_paid_timeoff" [shape = record, label = "{<col0> \N| employee_id:  integer\lpto_class_id:  integer\lreport_id:  integer\lamount:  numeric\l}" ];

"payroll_pto_class" [shape = record, label = "{<col0> \N| id:  serial\llabel:  text\l}" ];

"payroll_report" [shape = record, label = "{<col0> \N| id:  serial\lec_id:  integer\lpayment_date:  date\lcreated_by:  integer\lapproved_by:  integer\l}" ];

"payroll_report_line" [shape = record, label = "{<col0> \N| id:  serial\lreport_id:  integer\lemployee_id:  integer\lit_id:  integer\lqty:  numeric\lrate:  numeric\ldescription:  text\l}" ];

"payroll_wage" [shape = record, label = "{<col0> \N| entry_id:  serial\lentity_id:  integer\ltype_id:  integer\lrate:  numeric\l}" ];

"person" [shape = record, label = "{<col0> \N| id:  serial\lentity_id:  integer\lsalutation_id:  integer\lfirst_name:  text\lmiddle_name:  text\llast_name:  text\lcreated:  date\lbirthdate:  date\lpersonal_id:  text\l}" ];

"person_to_company" [shape = record, label = "{<col0> \N| location_id:  integer\lperson_id:  integer\lcompany_id:  integer\l}" ];

"pricegroup" [shape = record, label = "{<col0> \N| id:  serial\lpricegroup:  text\llast_updated:  timestamp without time zone\l}" ];

"recurring" [shape = record, label = "{<col0> \N| id:  integer\lreference:  text\lstartdate:  date\lnextdate:  date\lenddate:  date\lrecurring_interval:  interval\lhowmany:  integer\lpayment:  boolean\l}" ];

"recurringemail" [shape = record, label = "{<col0> \N| id:  integer\lformname:  text\lformat:  text\lmessage:  text\l}" ];

"recurringprint" [shape = record, label = "{<col0> \N| id:  integer\lformname:  text\lformat:  text\lprinter:  text\l}" ];

"robot" [shape = record, label = "{<col0> \N| id:  serial\lentity_id:  integer\lfirst_name:  text\lmiddle_name:  text\llast_name:  text\lcreated:  date\l}" ];

"salutation" [shape = record, label = "{<col0> \N| id:  serial\lsalutation:  text\l}" ];

"session" [shape = record, label = "{<col0> \N| session_id:  serial\ltoken:  character varying(32)\llast_used:  timestamp without time zone\lttl:  integer\lusers_id:  integer\lnotify_pasword:  interval\l}" ];

"sic" [shape = record, label = "{<col0> \N| code:  character varying(6)\lsictype:  character(1)\ldescription:  text\llast_updated:  timestamp without time zone\l}" ];

"status" [shape = record, label = "{<col0> \N| trans_id:  integer\lformname:  text\lprinted:  boolean\lemailed:  boolean\lspoolfile:  text\l}" ];

"tax" [shape = record, label = "{<col0> \N| chart_id:  integer\lrate:  numeric\lminvalue:  numeric\lmaxvalue:  numeric\ltaxnumber:  text\lvalidto:  timestamp without time zone\lpass:  integer\ltaxmodule_id:  integer\l}" ];

"tax_extended" [shape = record, label = "{<col0> \N| tax_basis:  numeric\lrate:  numeric\lentry_id:  integer\l}" ];

"taxcategory" [shape = record, label = "{<col0> \N| taxcategory_id:  serial\ltaxcategoryname:  text\ltaxmodule_id:  integer\l}" ];

"taxmodule" [shape = record, label = "{<col0> \N| taxmodule_id:  serial\ltaxmodulename:  text\l}" ];

"template" [shape = record, label = "{<col0> \N| id:  serial\ltemplate_name:  text\llanguage_code:  character varying(6)\ltemplate:  text\lformat:  text\llast_modified:  timestamp without time zone\l}" ];

"trans_type" [shape = record, label = "{<col0> \N| code:  character(2)\ldescription:  character varying(1000)\l}" ];

"transactions" [shape = record, label = "{<col0> \N| id:  integer\ltable_name:  text\llocked_by:  integer\lapproved:  boolean\lapproved_by:  integer\lapproved_at:  timestamp without time zone\ltransdate:  date\lworkflow_id:  integer\l}" ];

"translation" [shape = record, label = "{<col0> \N| trans_id:  integer\llanguage_code:  character varying(6)\ldescription:  text\l}" ];

"trial_balance__yearend_types" [shape = record, label = "{<col0> \N| type:  text\l}" ];

"user_preference" [shape = record, label = "{<col0> \N| id:  serial\luser_id:  integer\lname:  text\lvalue:  text\l}" ];

"users" [shape = record, label = "{<col0> \N| id:  serial\lusername:  character varying(30)\lnotify_password:  interval\lentity_id:  integer\l}" ];

"voucher" [shape = record, label = "{<col0> \N| trans_id:  integer\lbatch_id:  integer\lid:  serial\lbatch_class:  integer\l}" ];

"warehouse" [shape = record, label = "{<col0> \N| id:  serial\ldescription:  text\llast_updated:  timestamp without time zone\l}" ];

"warehouse_inventory" [shape = record, label = "{<col0> \N| entity_id:  integer\lwarehouse_id:  integer\lparts_id:  integer\ltrans_id:  integer\lorderitems_id:  integer\lqty:  numeric\lshippingdate:  date\lentry_id:  serial\l}" ];

"workflow" [shape = record, label = "{<col0> \N| workflow_id:  integer\ltype:  character varying(50)\lstate:  character varying(30)\llast_update:  timestamp without time zone\l}" ];

"workflow_context" [shape = record, label = "{<col0> \N| workflow_id:  integer\lcontext:  jsonb\l}" ];

"workflow_history" [shape = record, label = "{<col0> \N| workflow_hist_id:  integer\lworkflow_id:  integer\laction:  character varying(25)\ldescription:  character varying(255)\lstate:  character varying(30)\lworkflow_user:  character varying(50)\lhistory_date:  timestamp without time zone\l}" ];

"yearend" [shape = record, label = "{<col0> \N| trans_id:  integer\lreversed:  boolean\ltransdate:  date\l}" ];


"ac_tax_form" -> "acc_trans" [label="ac_tax_form_entry_id_fkey"];
"acc_trans" -> "transactions" [label="acc_trans_trans_id_fkey"];
"acc_trans" -> "account" [label="acc_trans_chart_id_fkey"];
"acc_trans" -> "invoice" [label="acc_trans_invoice_id_fkey"];
"acc_trans" -> "voucher" [label="acc_trans_voucher_id_fkey"];
"acc_trans" -> "currency" [label="acc_trans_curr_fkey"];
"account" -> "gifi" [label="account_gifi_accno_fkey"];
"account" -> "account_heading" [label="account_heading_fkey"];
"account_checkpoint" -> "account" [label="account_checkpoint_account_id_fkey"];
"account_checkpoint" -> "currency" [label="account_checkpoint_curr_fkey"];
"account_heading" -> "account_heading" [label="account_heading_parent_id_fkey"];
"account_heading_translation" -> "account_heading" [label="account_heading_translation_trans_id_fkey"];
"account_heading_translation" -> "account_heading" [label="account_heading_translation_trans_id_fkey1"];
"account_link" -> "account" [label="account_link_account_id_fkey"];
"account_link" -> "account_link_description" [label="account_link_description_fkey"];
"account_translation" -> "account" [label="account_translation_trans_id_fkey"];
"account_translation" -> "account" [label="account_translation_trans_id_fkey1"];
"ap" -> "transactions" [label="ap_id_fkey"];
"ap" -> "entity" [label="ap_entity_id_fkey"];
"ap" -> "currency" [label="ap_curr_fkey"];
"ap" -> "entity_employee" [label="ap_person_id_fkey"];
"ap" -> "entity_credit_account" [label="ap_entity_credit_account_fkey"];
"ar" -> "transactions" [label="ar_id_fkey"];
"ar" -> "entity" [label="ar_entity_id_fkey"];
"ar" -> "currency" [label="ar_curr_fkey"];
"ar" -> "entity_employee" [label="ar_person_id_fkey"];
"ar" -> "entity_credit_account" [label="ar_entity_credit_account_fkey"];
"assembly" -> "parts" [label="assembly_id_fkey"];
"assembly" -> "parts" [label="assembly_parts_id_fkey"];
"asset_class" -> "account" [label="asset_class_asset_account_id_fkey"];
"asset_class" -> "account" [label="asset_class_dep_account_id_fkey"];
"asset_class" -> "asset_dep_method" [label="asset_class_method_fkey"];
"asset_dep_method" -> "asset_unit_class" [label="asset_dep_method_unit_class_fkey"];
"asset_item" -> "warehouse" [label="asset_item_location_id_fkey"];
"asset_item" -> "business_unit" [label="asset_item_department_id_fkey"];
"asset_item" -> "eca_invoice" [label="asset_item_invoice_id_fkey"];
"asset_item" -> "account" [label="asset_item_asset_account_id_fkey"];
"asset_item" -> "account" [label="asset_item_dep_account_id_fkey"];
"asset_item" -> "account" [label="asset_item_exp_account_id_fkey"];
"asset_item" -> "asset_item" [label="asset_item_obsolete_by_fkey"];
"asset_item" -> "asset_class" [label="asset_item_asset_class_id_fkey"];
"asset_note" -> "asset_item" [label="asset_note_ref_key_fkey"];
"asset_report" -> "gl" [label="asset_report_gl_id_fkey"];
"asset_report" -> "asset_class" [label="asset_report_asset_class_fkey"];
"asset_report" -> "asset_report_class" [label="asset_report_report_class_fkey"];
"asset_report" -> "entity" [label="asset_report_entered_by_fkey"];
"asset_report" -> "entity" [label="asset_report_approved_by_fkey"];
"asset_report_line" -> "asset_item" [label="asset_report_line_asset_id_fkey"];
"asset_report_line" -> "asset_report" [label="asset_report_line_report_id_fkey"];
"asset_report_line" -> "business_unit" [label="asset_report_line_department_id_fkey"];
"asset_report_line" -> "warehouse" [label="asset_report_line_warehouse_id_fkey"];
"asset_rl_to_disposal_method" -> "asset_report" [label="asset_rl_to_disposal_method_report_id_fkey"];
"asset_rl_to_disposal_method" -> "asset_item" [label="asset_rl_to_disposal_method_asset_id_fkey"];
"asset_rl_to_disposal_method" -> "asset_disposal_method" [label="asset_rl_to_disposal_method_disposal_method_id_fkey"];
"audittrail" -> "person" [label="audittrail_person_id_fkey"];
"batch" -> "batch_class" [label="batch_batch_class_id_fkey"];
"batch" -> "entity_employee" [label="batch_approved_by_fkey"];
"batch" -> "entity_employee" [label="batch_created_by_fkey"];
"batch" -> "session" [label="batch_locked_by_fkey"];
"bu_class_to_module" -> "business_unit_class" [label="bu_class_to_module_bu_class_id_fkey"];
"bu_class_to_module" -> "lsmb_module" [label="bu_class_to_module_module_id_fkey"];
"budget_info" -> "entity" [label="budget_info_entered_by_fkey"];
"budget_info" -> "entity" [label="budget_info_approved_by_fkey"];
"budget_info" -> "entity" [label="budget_info_obsolete_by_fkey"];
"budget_line" -> "budget_info" [label="budget_line_budget_id_fkey"];
"budget_line" -> "account" [label="budget_line_account_id_fkey"];
"budget_line" -> "currency" [label="budget_line_curr_fkey"];
"budget_note" -> "budget_info" [label="budget_note_ref_key_fkey"];
"budget_to_business_unit" -> "budget_info" [label="budget_to_business_unit_budget_id_fkey"];
"budget_to_business_unit" -> "business_unit" [label="budget_to_business_unit_bu_id_fkey"];
"budget_to_business_unit" -> "business_unit_class" [label="budget_to_business_unit_bu_class_fkey"];
"business_unit" -> "business_unit_class" [label="business_unit_class_id_fkey"];
"business_unit" -> "business_unit" [label="business_unit_parent_id_fkey"];
"business_unit" -> "entity_credit_account" [label="business_unit_credit_id_fkey"];
"business_unit_ac" -> "acc_trans" [label="business_unit_ac_entry_id_fkey"];
"business_unit_ac" -> "business_unit" [label="business_unit_ac_class_id_bu_id_fkey"];
"business_unit_ac" -> "business_unit_class" [label="business_unit_ac_class_id_fkey"];
"business_unit_inv" -> "invoice" [label="business_unit_inv_entry_id_fkey"];
"business_unit_inv" -> "business_unit" [label="business_unit_inv_class_id_bu_id_fkey"];
"business_unit_inv" -> "business_unit_class" [label="business_unit_inv_class_id_fkey"];
"business_unit_jl" -> "journal_line" [label="business_unit_jl_entry_id_fkey"];
"business_unit_jl" -> "business_unit_class" [label="business_unit_jl_bu_class_fkey"];
"business_unit_jl" -> "business_unit" [label="business_unit_jl_bu_id_fkey"];
"business_unit_oitem" -> "orderitems" [label="business_unit_oitem_entry_id_fkey"];
"business_unit_oitem" -> "business_unit" [label="business_unit_oitem_class_id_bu_id_fkey"];
"business_unit_oitem" -> "business_unit_class" [label="business_unit_oitem_class_id_fkey"];
"business_unit_translation" -> "business_unit" [label="business_unit_translation_trans_id_fkey"];
"company" -> "entity" [label="company_entity_id_fkey"];
"company" -> "sic" [label="company_sic_code_fkey"];
"country_tax_form" -> "country" [label="country_tax_form_country_id_fkey"];
"cr_coa_to_account" -> "account" [label="cr_coa_to_account_chart_id_fkey"];
"cr_report" -> "account" [label="cr_report_chart_id_fkey"];
"cr_report" -> "entity" [label="cr_report_entered_by_fkey"];
"cr_report" -> "entity" [label="cr_report_deleted_by_fkey"];
"cr_report" -> "entity" [label="cr_report_approved_by_fkey"];
"cr_report_line" -> "cr_report" [label="cr_report_line_report_id_fkey"];
"cr_report_line" -> "entity" [label="cr_report_line_user_fkey"];
"cr_report_line_links" -> "cr_report_line" [label="cr_report_line_links_report_line_id_fkey"];
"cr_report_line_links" -> "acc_trans" [label="cr_report_line_links_entry_id_fkey"];
"eca_invoice" -> "journal_entry" [label="eca_invoice_journal_id_fkey"];
"eca_invoice" -> "entity_credit_account" [label="eca_invoice_credit_id_fkey"];
"eca_invoice" -> "language" [label="eca_invoice_language_code_fkey"];
"eca_note" -> "entity_credit_account" [label="eca_note_ref_key_fkey"];
"eca_tax" -> "entity_credit_account" [label="eca_tax_eca_id_fkey"];
"eca_tax" -> "account" [label="eca_tax_chart_id_fkey"];
"eca_to_contact" -> "entity_credit_account" [label="eca_to_contact_credit_id_fkey"];
"eca_to_contact" -> "contact_class" [label="eca_to_contact_contact_class_id_fkey"];
"eca_to_location" -> "location" [label="eca_to_location_location_id_fkey"];
"eca_to_location" -> "location_class" [label="eca_to_location_location_class_fkey"];
"eca_to_location" -> "entity_credit_account" [label="eca_to_location_credit_id_fkey"];
"email" -> "workflow" [label="email_workflow_id_fkey"];
"employee_to_ec" -> "entity_employee" [label="employee_to_ec_employee_id_fkey"];
"employee_to_ec" -> "employee_class" [label="employee_to_ec_ec_id_fkey"];
"entity" -> "entity_class" [label="entity_entity_class_fkey"];
"entity" -> "country" [label="entity_country_id_fkey"];
"entity_bank_account" -> "entity" [label="entity_bank_account_entity_id_fkey"];
"entity_credit_account" -> "entity" [label="entity_credit_account_entity_id_fkey"];
"entity_credit_account" -> "entity_class" [label="entity_credit_account_entity_class_fkey"];
"entity_credit_account" -> "account" [label="entity_credit_account_discount_account_id_fkey"];
"entity_credit_account" -> "business" [label="entity_credit_account_business_id_fkey"];
"entity_credit_account" -> "language" [label="entity_credit_account_language_code_fkey"];
"entity_credit_account" -> "pricegroup" [label="entity_credit_account_pricegroup_id_fkey"];
"entity_credit_account" -> "currency" [label="entity_credit_account_curr_fkey"];
"entity_credit_account" -> "entity_employee" [label="entity_credit_account_employee_id_fkey"];
"entity_credit_account" -> "person" [label="entity_credit_account_primary_contact_fkey"];
"entity_credit_account" -> "account" [label="entity_credit_account_ar_ap_account_id_fkey"];
"entity_credit_account" -> "account" [label="entity_credit_account_cash_account_id_fkey"];
"entity_credit_account" -> "entity_bank_account" [label="entity_credit_account_bank_account_fkey"];
"entity_credit_account" -> "country_tax_form" [label="entity_credit_account_taxform_id_fkey"];
"entity_employee" -> "entity" [label="entity_employee_entity_id_fkey"];
"entity_employee" -> "entity" [label="entity_employee_manager_id_fkey"];
"entity_note" -> "entity" [label="entity_note_ref_key_fkey"];
"entity_note" -> "entity" [label="entity_note_entity_id_fkey"];
"entity_other_name" -> "entity" [label="entity_other_name_entity_id_fkey"];
"entity_to_contact" -> "entity" [label="entity_to_contact_entity_id_fkey"];
"entity_to_contact" -> "contact_class" [label="entity_to_contact_contact_class_id_fkey"];
"entity_to_location" -> "location" [label="entity_to_location_location_id_fkey"];
"entity_to_location" -> "location_class" [label="entity_to_location_location_class_fkey"];
"entity_to_location" -> "entity" [label="entity_to_location_entity_id_fkey"];
"exchangerate_default" -> "exchangerate_type" [label="exchangerate_default_rate_type_fkey"];
"exchangerate_default" -> "currency" [label="exchangerate_default_curr_fkey"];
"file_base" -> "mime_type" [label="file_base_mime_type_id_fkey"];
"file_base" -> "entity" [label="file_base_uploaded_by_fkey"];
"file_base" -> "file_class" [label="file_base_file_class_fkey"];
"file_eca" -> "entity_credit_account" [label="file_eca_ref_key_fkey"];
"file_email" -> "email" [label="file_email_ref_key_fkey"];
"file_entity" -> "entity" [label="file_entity_ref_key_fkey"];
"file_order" -> "oe" [label="file_order_ref_key_fkey"];
"file_order_to_order" -> "file_order" [label="file_order_to_order_file_id_fkey"];
"file_order_to_order" -> "oe" [label="file_order_to_order_ref_key_fkey"];
"file_order_to_tx" -> "file_order" [label="file_order_to_tx_file_id_fkey"];
"file_order_to_tx" -> "gl" [label="file_order_to_tx_ref_key_fkey"];
"file_part" -> "parts" [label="file_part_ref_key_fkey"];
"file_reconciliation" -> "cr_report" [label="file_reconciliation_ref_key_fkey"];
"file_secondary_attachment" -> "file_class" [label="file_secondary_attachment_source_class_fkey"];
"file_secondary_attachment" -> "file_class" [label="file_secondary_attachment_dest_class_fkey"];
"file_secondary_attachment" -> "entity" [label="file_secondary_attachment_attached_by_fkey"];
"file_transaction" -> "transactions" [label="file_transaction_ref_key_fkey"];
"file_tx_to_order" -> "file_transaction" [label="file_tx_to_order_file_id_fkey"];
"file_tx_to_order" -> "oe" [label="file_tx_to_order_ref_key_fkey"];
"file_view_catalog" -> "file_class" [label="file_view_catalog_file_class_fkey"];
"gl" -> "transactions" [label="gl_id_fkey"];
"gl" -> "person" [label="gl_person_id_fkey"];
"gl" -> "trans_type" [label="gl_trans_type_code_fkey"];
"inventory_report" -> "gl" [label="inventory_report_trans_id_fkey"];
"inventory_report_line" -> "inventory_report" [label="inventory_report_line_adjust_id_fkey"];
"inventory_report_line" -> "parts" [label="inventory_report_line_parts_id_fkey"];
"invoice" -> "transactions" [label="invoice_trans_id_fkey"];
"invoice" -> "parts" [label="invoice_parts_id_fkey"];
"invoice_note" -> "invoice" [label="invoice_note_ref_key_fkey"];
"invoice_tax_form" -> "invoice" [label="invoice_tax_form_invoice_id_fkey"];
"jcitems" -> "business_unit" [label="jcitems_business_unit_id_fkey"];
"jcitems" -> "person" [label="jcitems_person_id_fkey"];
"jcitems" -> "jctype" [label="jcitems_jctype_fkey"];
"jcitems" -> "currency" [label="jcitems_curr_fkey"];
"job" -> "business_unit" [label="job_bu_id_fkey"];
"journal_entry" -> "session" [label="journal_entry_locked_by_fkey"];
"journal_entry" -> "journal_type" [label="journal_entry_journal_fkey"];
"journal_entry" -> "entity" [label="journal_entry_entered_by_fkey"];
"journal_entry" -> "entity" [label="journal_entry_approved_by_fkey"];
"journal_line" -> "account" [label="journal_line_account_id_fkey"];
"journal_line" -> "journal_entry" [label="journal_line_journal_id_fkey"];
"journal_line" -> "cr_report" [label="journal_line_reconciliation_report_fkey"];
"journal_line" -> "account_link_description" [label="journal_line_line_type_fkey"];
"journal_line" -> "currency" [label="journal_line_curr_fkey"];
"journal_note" -> "journal_entry" [label="journal_note_ref_key_fkey"];
"location" -> "country" [label="location_country_id_fkey"];
"location_class_to_entity_class" -> "location_class" [label="location_class_to_entity_class_location_class_fkey"];
"location_class_to_entity_class" -> "entity_class" [label="location_class_to_entity_class_entity_class_fkey"];
"lsmb_sequence" -> "defaults" [label="lsmb_sequence_setting_key_fkey"];
"makemodel" -> "parts" [label="makemodel_parts_id_fkey"];
"menu_acl" -> "menu_node" [label="menu_acl_node_id_fkey"];
"menu_node" -> "menu_node" [label="menu_node_parent_fkey"];
"mfg_lot" -> "parts" [label="mfg_lot_parts_id_fkey"];
"mfg_lot_item" -> "mfg_lot" [label="mfg_lot_item_mfg_lot_id_fkey"];
"mfg_lot_item" -> "parts" [label="mfg_lot_item_parts_id_fkey"];
"new_shipto" -> "transactions" [label="new_shipto_trans_id_fkey"];
"new_shipto" -> "oe" [label="new_shipto_oe_id_fkey"];
"new_shipto" -> "location" [label="new_shipto_location_id_fkey"];
"note" -> "note_class" [label="note_note_class_fkey"];
"oe" -> "entity" [label="oe_entity_id_fkey"];
"oe" -> "currency" [label="oe_curr_fkey"];
"oe" -> "person" [label="oe_person_id_fkey"];
"oe" -> "entity_credit_account" [label="oe_entity_credit_account_fkey"];
"oe" -> "oe_class" [label="oe_oe_class_id_fkey"];
"oe" -> "workflow" [label="oe_workflow_id_fkey"];
"open_forms" -> "session" [label="open_forms_session_id_fkey"];
"orderitems" -> "parts" [label="orderitems_parts_id_fkey"];
"parts" -> "account" [label="parts_inventory_accno_id_fkey"];
"parts" -> "account" [label="parts_income_accno_id_fkey"];
"parts" -> "account" [label="parts_expense_accno_id_fkey"];
"parts" -> "account" [label="parts_returns_accno_id_fkey"];
"parts" -> "partsgroup" [label="parts_partsgroup_id_fkey"];
"parts_translation" -> "parts" [label="parts_translation_trans_id_fkey"];
"partscustomer" -> "parts" [label="partscustomer_parts_id_fkey"];
"partscustomer" -> "entity_credit_account" [label="partscustomer_credit_id_fkey"];
"partscustomer" -> "pricegroup" [label="partscustomer_pricegroup_id_fkey"];
"partscustomer" -> "currency" [label="partscustomer_curr_fkey"];
"partsgroup" -> "partsgroup" [label="partsgroup_parent_fkey"];
"partsgroup_translation" -> "partsgroup" [label="partsgroup_translation_trans_id_fkey"];
"partstax" -> "parts" [label="partstax_parts_id_fkey"];
"partstax" -> "account" [label="partstax_chart_id_fkey"];
"partstax" -> "taxcategory" [label="partstax_taxcategory_id_fkey"];
"partsvendor" -> "entity_credit_account" [label="partsvendor_credit_id_fkey"];
"partsvendor" -> "currency" [label="partsvendor_curr_fkey"];
"payment" -> "gl" [label="payment_gl_id_fkey"];
"payment" -> "entity_credit_account" [label="payment_entity_credit_id_fkey"];
"payment" -> "person" [label="payment_employee_id_fkey"];
"payment_links" -> "payment" [label="payment_links_payment_id_fkey"];
"payment_links" -> "acc_trans" [label="payment_links_entry_id_fkey"];
"payroll_deduction" -> "entity" [label="payroll_deduction_entity_id_fkey"];
"payroll_deduction" -> "payroll_deduction_type" [label="payroll_deduction_type_id_fkey"];
"payroll_deduction_class" -> "country" [label="payroll_deduction_class_country_id_fkey"];
"payroll_deduction_type" -> "account" [label="payroll_deduction_type_account_id_fkey"];
"payroll_deduction_type" -> "payroll_deduction_class" [label="payroll_deduction_type_pdc_id_country_id_fkey"];
"payroll_employee_class_to_income_type" -> "payroll_employee_class" [label="payroll_employee_class_to_income_type_ec_id_fkey"];
"payroll_employee_class_to_income_type" -> "payroll_income_type" [label="payroll_employee_class_to_income_type_it_id_fkey"];
"payroll_income_class" -> "country" [label="payroll_income_class_country_id_fkey"];
"payroll_income_type" -> "account" [label="payroll_income_type_account_id_fkey"];
"payroll_income_type" -> "payroll_income_class" [label="payroll_income_type_pic_id_country_id_fkey"];
"payroll_income_type" -> "payroll_income_category" [label="payroll_income_type_pic_id_fkey"];
"payroll_paid_timeoff" -> "entity" [label="payroll_paid_timeoff_employee_id_fkey"];
"payroll_paid_timeoff" -> "payroll_pto_class" [label="payroll_paid_timeoff_pto_class_id_fkey"];
"payroll_paid_timeoff" -> "payroll_report" [label="payroll_paid_timeoff_report_id_fkey"];
"payroll_report" -> "payroll_employee_class" [label="payroll_report_ec_id_fkey"];
"payroll_report" -> "entity_employee" [label="payroll_report_created_by_fkey"];
"payroll_report" -> "entity_employee" [label="payroll_report_approved_by_fkey"];
"payroll_report_line" -> "payroll_report" [label="payroll_report_line_report_id_fkey"];
"payroll_report_line" -> "entity" [label="payroll_report_line_employee_id_fkey"];
"payroll_report_line" -> "payroll_income_type" [label="payroll_report_line_it_id_fkey"];
"payroll_wage" -> "entity" [label="payroll_wage_entity_id_fkey"];
"payroll_wage" -> "payroll_income_type" [label="payroll_wage_type_id_fkey"];
"person" -> "entity" [label="person_entity_id_fkey"];
"person" -> "salutation" [label="person_salutation_id_fkey"];
"person_to_company" -> "location" [label="person_to_company_location_id_fkey"];
"person_to_company" -> "person" [label="person_to_company_person_id_fkey"];
"person_to_company" -> "company" [label="person_to_company_company_id_fkey"];
"recurring" -> "transactions" [label="recurring_id_fkey"];
"recurringemail" -> "recurring" [label="recurringemail_id_fkey"];
"recurringprint" -> "recurring" [label="recurringprint_id_fkey"];
"robot" -> "entity" [label="robot_entity_id_fkey"];
"session" -> "users" [label="session_users_id_fkey"];
"status" -> "transactions" [label="status_trans_id_fkey"];
"tax" -> "account" [label="tax_chart_id_fkey"];
"tax" -> "taxmodule" [label="tax_taxmodule_id_fkey"];
"tax_extended" -> "acc_trans" [label="tax_extended_entry_id_fkey"];
"taxcategory" -> "taxmodule" [label="taxcategory_taxmodule_id_fkey"];
"template" -> "language" [label="template_language_code_fkey"];
"transactions" -> "session" [label="transactions_locked_by_fkey"];
"transactions" -> "entity" [label="transactions_approved_by_fkey"];
"transactions" -> "workflow" [label="transactions_workflow_id_fkey"];
"user_preference" -> "users" [label="user_preference_user_id_fkey"];
"users" -> "entity" [label="users_entity_id_fkey"];
"voucher" -> "transactions" [label="voucher_trans_id_fkey"];
"voucher" -> "batch" [label="voucher_batch_id_fkey"];
"voucher" -> "batch_class" [label="voucher_batch_class_fkey"];
"warehouse_inventory" -> "entity_employee" [label="warehouse_inventory_entity_id_fkey"];
"workflow_context" -> "workflow" [label="workflow_context_workflow_id_fkey"];
"workflow_history" -> "workflow" [label="workflow_history_workflow_id_fkey"];
"yearend" -> "gl" [label="yearend_trans_id_fkey"];
}


