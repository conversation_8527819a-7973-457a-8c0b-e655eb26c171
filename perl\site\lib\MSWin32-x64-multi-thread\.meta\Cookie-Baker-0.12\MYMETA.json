{"abstract": "Cookie string generator / parser", "author": ["<PERSON><PERSON><PERSON> <<EMAIL>>"], "dynamic_config": 0, "generated_by": "Minilla/v3.1.23, CPAN::Meta::Converter version 2.150010", "license": ["perl_5"], "meta-spec": {"url": "http://search.cpan.org/perldoc?CPAN::Meta::Spec", "version": 2}, "name": "<PERSON><PERSON><PERSON><PERSON>", "no_index": {"directory": ["t", "xt", "inc", "share", "eg", "examples", "author", "builder"]}, "prereqs": {"configure": {"requires": {"Module::Build::Tiny": "0.035"}}, "develop": {"requires": {"Test::CPAN::Meta": "0", "Test::MinimumVersion::Fast": "0.04", "Test::PAUSE::Permissions": "0.07", "Test::Pod": "1.41", "Test::Spellunker": "v0.2.7"}}, "runtime": {"requires": {"Exporter": "0", "URI::Escape": "0", "perl": "5.008001"}, "suggests": {"Cookie::Baker::XS": "0"}}, "test": {"requires": {"Test::More": "0.98", "Test::Time": "0"}}}, "provides": {"Cookie::Baker": {"file": "lib/Cookie/Baker.pm", "version": "0.12"}}, "release_status": "stable", "resources": {"bugtracker": {"web": "https://github.com/kazeburo/<PERSON><PERSON>-<PERSON>/issues"}, "homepage": "https://github.com/kazeburo/<PERSON><PERSON>-<PERSON>", "repository": {"type": "git", "url": "https://github.com/kazeburo/Cookie-Baker.git", "web": "https://github.com/kazeburo/<PERSON><PERSON>-<PERSON>"}}, "version": "0.12", "x_contributors": ["<PERSON> <<EMAIL>>", "Ichinose Shogo <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "kwry <<EMAIL>>", "yo<PERSON><PERSON><PERSON><PERSON><PERSON> <883514+yoshi<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com>"], "x_serialization_backend": "JSON::PP version 4.16", "x_static_install": 1}