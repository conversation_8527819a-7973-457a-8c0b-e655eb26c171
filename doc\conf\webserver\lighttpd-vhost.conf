# This is a 'vhost' definition file example for use with Starman/LedgerSMB
# reverse proxying.
#
# Please replace the following parameters:
#
#   * WORKING_DIR
#   * YOUR_SERVER_NAME
#   * SSL_KEY_FILE
#   * SSL_CERT_FILE
#   * STARMAN_HOST

server.modules = (
	"mod_expire",
	"mod_access",
	"mod_alias",
	"mod_compress",
 	"mod_redirect",
)

server.name YOUR_SERVER_NAME;

server.document-root        = "WORKING_DIR/UI"
server.errorlog             = "/var/log/lighttpd/error.log"
server.pid-file             = "/var/run/lighttpd.pid"
server.username             = "www-data"
server.groupname            = "www-data"

  # If you own a publicly exposed server, consider submitting it
  # to the SSL security tests available at
  #    https://www.ssllabs.com/ssltest/

  # Replace snippets/snakeoil.conf with either your own version of snakeoil.conf
  # or modified versions of the following ssl_certificate, ssl_certificate_key lines
  include snippets/snakeoil.conf;
  #ssl_certificate SSL_CERT_FILE;
  # after expansion, the above may look like:
  # ssl_certificate /etc/certs/example.com.pem;
  #ssl_certificate_key SSL_KEY_FILE;

server.port                 = 443
ssl.engine                  = "enable"
ssl.pemfile                 = "SSL_CERT_FILE"

index-file.names            = ( "login.pl", "index.php", "index.html", "index.lighttpd.html" )
url.access-deny             = ( "~", ".*" )
static-file.exclude-extensions = ( ".php", ".pl", ".fcgi" )

compress.cache-dir          = "/var/cache/lighttpd/compress/"
compress.filetype           = ( "application/javascript", "text/css", "text/html", "text/plain" )

server.modules   += ( "mod_proxy" )

proxy.server     = ( ".pl" =>
                     (
                       ( "host" => "STARMAN_HOST",
                         "port" => 5762
                       )
                     ),
                    "/erp/api" =>
                      (
                        ( "host" => "STARMAN_HOST",
                          "port" => 5762
                        )
                      )
                   )

expire.url = (
              "/css/"    => "access plus 2 months",
              "/images/" => "access plus 2 months",
              "/js/"     => "access plus 2 months",
)

##  MimeType handling
## -------------------
##
## Use the "Content-Type" extended attribute to obtain mime type if
## possible
##
mimetype.use-xattr        = "enable"

##
## mimetype mapping
##
mimetype.assign             = (
  ".pdf"          =>      "application/pdf",
  ".css"          =>      "text/css",
  ".gif"          =>      "image/gif",
  ".jpeg"         =>      "image/jpeg",
  ".jpg"          =>      "image/jpeg",
  ".png"          =>      "image/png",
  ".xbm"          =>      "image/x-xbitmap",
  ".xpm"          =>      "image/x-xpixmap",
  ".xwd"          =>      "image/x-xwindowdump",
  ".csv"          =>      "text/csv",
  ".html"         =>      "text/html",
  ".js"           =>      "text/javascript",
  ".log"          =>      "text/plain",
  ".xml"          =>      "text/xml",
  ".ods"          =>      "application/vnd.oasis.opendocument.spreadsheet",
  ".odg"          =>      "application/vnd.oasis.opendocument.graphics",
  ".odc"          =>      "application/vnd.oasis.opendocument.chart",
  ".odf"          =>      "application/vnd.oasis.opendocument.formula",
  ".odi"          =>      "application/vnd.oasis.opendocument.image",
  ".ots"          =>      "application/vnd.oasis.opendocument.spreadsheet-template",
  ".xls"          =>      "application/vnd.ms-excel",
  ".xlsx"         =>      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  ""              =>      "application/octet-stream",
)
