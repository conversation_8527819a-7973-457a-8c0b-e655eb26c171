0.008   2018-06-23 (PERLANCAR)

	- No functional changes.

	- [doc] Fix sentence.


0.007   2018-06-23 (PERLANCAR)

	- No functional changes.

	- [doc] Mention DateTime::Format::ISO8601::Format.

	- Tweak Abstract.


0.006   2018-02-06 (PERLANCAR)

        - Allow PnW [RT#124294].

        - Allow decimal fractions (note that ISO standard only allows decimal
          fractions for the smallest unit, but we haven't enforced this).

        - [doc] Document parse_duration_as_deltas().

        - [Bugfix] Forgot to load DateTime::Duration when creating object
          [RT#124293].


0.005   2017-08-02 (PERLANCAR)

	- Fix parsing PT0S (RT#122682, thanks <PERSON><PERSON>).


0.004   2017-07-20 (PERLANCAR)

	- [Bugfix] Avoid mixing PnYnMnDTnHnMnS and PnW styles according to ISO
          8601 (Thanks <PERSON>).


0.003   2017-07-20 (PERLANCAR)

	- Add support for duration parsing (thanks <PERSON>).


0.002   2016-06-29 (PERLANCAR)

	- [Bugfix] Handle zero duration correctly, it should not be
	  formatted as 'P' but 'PT0H0M0S'.


0.001   2016-06-29 (PERLANCAR)

        - First release.
