digraph g {
graph [
rankdir = "LR",
concentrate = true,
ratio = auto
];
node [
fontsize = "10",
shape = record
];
edge [
];

"ac_tax_form" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > entry_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > reportable </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"acc_trans" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > trans_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > chart_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > transdate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > source </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > cleared </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > memo </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > invoice_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol9"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > approved </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> <TR><TD PORT="ltcol13" ></TD><TD align="left" > voucher_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol13"> </TD></TR> <TR><TD PORT="ltcol14" ></TD><TD align="left" > entry_id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol14"> </TD></TR> <TR><TD PORT="ltcol15" ></TD><TD align="left" > amount_bc </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol15"> </TD></TR> <TR><TD PORT="ltcol16" ></TD><TD align="left" > amount_tc </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol16"> </TD></TR> <TR><TD PORT="ltcol17" ></TD><TD align="left" > curr </TD><TD align="left" > character(3) </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol17"> </TD></TR> </TABLE>> ];

"account" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > accno </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > is_temp </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > category </TD><TD align="left" > character(1) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > gifi_accno </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > heading </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > contra </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > tax </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > obsolete </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> </TABLE>> ];

"account_checkpoint" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > end_date </TD><TD align="left" > date </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > account_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > debits </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > credits </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > amount_bc </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > amount_tc </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > curr </TD><TD align="left" > character(3) </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol9"> </TD></TR> </TABLE>> ];

"account_heading" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > accno </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > parent_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > category </TD><TD align="left" > character(1) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> </TABLE>> ];

"account_heading_translation" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > trans_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > language_code </TD><TD align="left" > character varying(6) </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"account_link" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > account_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"account_link_description" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > summary </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > custom </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"account_translation" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > trans_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > language_code </TD><TD align="left" > character varying(6) </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"ap" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > invnumber </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > transdate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > entity_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > taxincluded </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > duedate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> <TR><TD PORT="ltcol11" ></TD><TD align="left" > invoice </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol11"> </TD></TR> <TR><TD PORT="ltcol12" ></TD><TD align="left" > ordnumber </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol12"> </TD></TR> <TR><TD PORT="ltcol13" ></TD><TD align="left" > curr </TD><TD align="left" > character(3) </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol13"> </TD></TR> <TR><TD PORT="ltcol14" ></TD><TD align="left" > notes </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol14"> </TD></TR> <TR><TD PORT="ltcol15" ></TD><TD align="left" > person_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol15"> </TD></TR> <TR><TD PORT="ltcol16" ></TD><TD align="left" > till </TD><TD align="left" > character varying(20) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol16"> </TD></TR> <TR><TD PORT="ltcol17" ></TD><TD align="left" > quonumber </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol17"> </TD></TR> <TR><TD PORT="ltcol18" ></TD><TD align="left" > intnotes </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol18"> </TD></TR> <TR><TD PORT="ltcol19" ></TD><TD align="left" > shipvia </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol19"> </TD></TR> <TR><TD PORT="ltcol20" ></TD><TD align="left" > language_code </TD><TD align="left" > character varying(6) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol20"> </TD></TR> <TR><TD PORT="ltcol21" ></TD><TD align="left" > ponumber </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol21"> </TD></TR> <TR><TD PORT="ltcol22" ></TD><TD align="left" > shippingpoint </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol22"> </TD></TR> <TR><TD PORT="ltcol23" ></TD><TD align="left" > on_hold </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol23"> </TD></TR> <TR><TD PORT="ltcol24" ></TD><TD align="left" > approved </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol24"> </TD></TR> <TR><TD PORT="ltcol25" ></TD><TD align="left" > reverse </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol25"> </TD></TR> <TR><TD PORT="ltcol26" ></TD><TD align="left" > terms </TD><TD align="left" > smallint </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol26"> </TD></TR> <TR><TD PORT="ltcol27" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol27"> </TD></TR> <TR><TD PORT="ltcol28" ></TD><TD align="left" > force_closed </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol28"> </TD></TR> <TR><TD PORT="ltcol29" ></TD><TD align="left" > crdate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol29"> </TD></TR> <TR><TD PORT="ltcol30" ></TD><TD align="left" > is_return </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol30"> </TD></TR> <TR><TD PORT="ltcol31" ></TD><TD align="left" > entity_credit_account </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol31"> </TD></TR> <TR><TD PORT="ltcol32" ></TD><TD align="left" > amount_bc </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol32"> </TD></TR> <TR><TD PORT="ltcol33" ></TD><TD align="left" > amount_tc </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol33"> </TD></TR> <TR><TD PORT="ltcol34" ></TD><TD align="left" > netamount_bc </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol34"> </TD></TR> <TR><TD PORT="ltcol35" ></TD><TD align="left" > netamount_tc </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol35"> </TD></TR> </TABLE>> ];

"ar" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > invnumber </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > transdate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > entity_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > taxincluded </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > duedate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> <TR><TD PORT="ltcol11" ></TD><TD align="left" > invoice </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol11"> </TD></TR> <TR><TD PORT="ltcol12" ></TD><TD align="left" > shippingpoint </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol12"> </TD></TR> <TR><TD PORT="ltcol13" ></TD><TD align="left" > terms </TD><TD align="left" > smallint </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol13"> </TD></TR> <TR><TD PORT="ltcol14" ></TD><TD align="left" > notes </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol14"> </TD></TR> <TR><TD PORT="ltcol15" ></TD><TD align="left" > curr </TD><TD align="left" > character(3) </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol15"> </TD></TR> <TR><TD PORT="ltcol16" ></TD><TD align="left" > ordnumber </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol16"> </TD></TR> <TR><TD PORT="ltcol17" ></TD><TD align="left" > person_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol17"> </TD></TR> <TR><TD PORT="ltcol18" ></TD><TD align="left" > till </TD><TD align="left" > character varying(20) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol18"> </TD></TR> <TR><TD PORT="ltcol19" ></TD><TD align="left" > quonumber </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol19"> </TD></TR> <TR><TD PORT="ltcol20" ></TD><TD align="left" > intnotes </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol20"> </TD></TR> <TR><TD PORT="ltcol21" ></TD><TD align="left" > shipvia </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol21"> </TD></TR> <TR><TD PORT="ltcol22" ></TD><TD align="left" > language_code </TD><TD align="left" > character varying(6) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol22"> </TD></TR> <TR><TD PORT="ltcol23" ></TD><TD align="left" > ponumber </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol23"> </TD></TR> <TR><TD PORT="ltcol24" ></TD><TD align="left" > on_hold </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol24"> </TD></TR> <TR><TD PORT="ltcol25" ></TD><TD align="left" > reverse </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol25"> </TD></TR> <TR><TD PORT="ltcol26" ></TD><TD align="left" > approved </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol26"> </TD></TR> <TR><TD PORT="ltcol27" ></TD><TD align="left" > entity_credit_account </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol27"> </TD></TR> <TR><TD PORT="ltcol28" ></TD><TD align="left" > force_closed </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol28"> </TD></TR> <TR><TD PORT="ltcol29" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol29"> </TD></TR> <TR><TD PORT="ltcol30" ></TD><TD align="left" > is_return </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol30"> </TD></TR> <TR><TD PORT="ltcol31" ></TD><TD align="left" > crdate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol31"> </TD></TR> <TR><TD PORT="ltcol32" ></TD><TD align="left" > setting_sequence </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol32"> </TD></TR> <TR><TD PORT="ltcol33" ></TD><TD align="left" > amount_bc </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol33"> </TD></TR> <TR><TD PORT="ltcol34" ></TD><TD align="left" > amount_tc </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol34"> </TD></TR> <TR><TD PORT="ltcol35" ></TD><TD align="left" > netamount_bc </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol35"> </TD></TR> <TR><TD PORT="ltcol36" ></TD><TD align="left" > netamount_tc </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol36"> </TD></TR> </TABLE>> ];

"assembly" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > parts_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > qty </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > bom </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > adj </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> </TABLE>> ];

"asset_class" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > label </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > asset_account_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > dep_account_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > method </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol5"> </TD></TR> </TABLE>> ];

"asset_dep_method" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > method </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > sproc </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > unit_label </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > short_name </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > unit_class </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol6"> </TD></TR> </TABLE>> ];

"asset_disposal_method" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > label </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > multiple </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > short_label </TD><TD align="left" > character(1) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"asset_item" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > tag </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > purchase_value </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > salvage_value </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > usable_life </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > purchase_date </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > start_depreciation </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > location_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol9"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > department_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol10"> </TD></TR> <TR><TD PORT="ltcol11" ></TD><TD align="left" > invoice_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol11"> </TD></TR> <TR><TD PORT="ltcol12" ></TD><TD align="left" > asset_account_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol12"> </TD></TR> <TR><TD PORT="ltcol13" ></TD><TD align="left" > dep_account_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol13"> </TD></TR> <TR><TD PORT="ltcol14" ></TD><TD align="left" > exp_account_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol14"> </TD></TR> <TR><TD PORT="ltcol15" ></TD><TD align="left" > obsolete_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol15"> </TD></TR> <TR><TD PORT="ltcol16" ></TD><TD align="left" > asset_class_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol16"> </TD></TR> </TABLE>> ];

"asset_note" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > note_class </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > note </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > vector </TD><TD align="left" > tsvector </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > created </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > created_by </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > subject </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> </TABLE>> ];

"asset_report" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > report_date </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > gl_id </TD><TD align="left" > bigint </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > asset_class </TD><TD align="left" > bigint </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > report_class </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > entered_by </TD><TD align="left" > bigint </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > approved_by </TD><TD align="left" > bigint </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > entered_at </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > approved_at </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > depreciated_qty </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> <TR><TD PORT="ltcol11" ></TD><TD align="left" > dont_approve </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol11"> </TD></TR> <TR><TD PORT="ltcol12" ></TD><TD align="left" > submitted </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol12"> </TD></TR> </TABLE>> ];

"asset_report_class" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > class </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"asset_report_line" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > asset_id </TD><TD align="left" > bigint </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > report_id </TD><TD align="left" > bigint </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > amount </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > department_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > warehouse_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol5"> </TD></TR> </TABLE>> ];

"asset_rl_to_disposal_method" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > report_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > asset_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > disposal_method_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > percent_disposed </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"asset_unit_class" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > class </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"audittrail" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > trans_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > tablename </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > reference </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > formname </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > action </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > transdate </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > person_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > entry_id </TD><TD align="left" > bigserial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > rolname </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> </TABLE>> ];

"batch" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > batch_class_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > control_code </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > default_date </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > approved_on </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > approved_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > created_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > locked_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol9"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > created_on </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> </TABLE>> ];

"batch_class" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > class </TD><TD align="left" > character varying </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"bu_class_to_module" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > bu_class_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > module_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"budget_info" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > start_date </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > end_date </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > reference </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > entered_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > approved_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > obsolete_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > entered_at </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > approved_at </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> <TR><TD PORT="ltcol11" ></TD><TD align="left" > obsolete_at </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol11"> </TD></TR> </TABLE>> ];

"budget_line" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > budget_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > account_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > amount </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > amount_tc </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > curr </TD><TD align="left" > character(3) </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol6"> </TD></TR> </TABLE>> ];

"budget_note" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > note_class </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > note </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > vector </TD><TD align="left" > tsvector </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > created </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > created_by </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > subject </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> </TABLE>> ];

"budget_to_business_unit" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > budget_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > bu_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > bu_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"business" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > discount </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > last_updated </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"business_unit" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > class_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > control_code </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > start_date </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > end_date </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > parent_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > credit_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol8"> </TD></TR> </TABLE>> ];

"business_unit_ac" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > entry_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > class_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > bu_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"business_unit_class" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > label </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > active </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > ordering </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"business_unit_inv" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > entry_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > class_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > bu_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"business_unit_jl" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > entry_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > bu_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > bu_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"business_unit_oitem" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > entry_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > class_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > bu_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"business_unit_translation" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > trans_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > language_code </TD><TD align="left" > character varying(6) </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"company" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > entity_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > legal_name </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > tax_id </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > sales_tax_id </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > license_number </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > sic_code </TD><TD align="left" > character varying </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > created </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> </TABLE>> ];

"contact_class" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > class </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"country" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > name </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > short_name </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > itu </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"country_tax_form" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > country_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > form_name </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > default_reportable </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > is_accrual </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> </TABLE>> ];

"cr_coa_to_account" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > chart_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > account </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"cr_report" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > bigserial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > chart_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > their_total </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > approved </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > submitted </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > end_date </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > updated </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > entered_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > entered_username </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > deleted </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> <TR><TD PORT="ltcol11" ></TD><TD align="left" > deleted_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol11"> </TD></TR> <TR><TD PORT="ltcol12" ></TD><TD align="left" > approved_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol12"> </TD></TR> <TR><TD PORT="ltcol13" ></TD><TD align="left" > approved_username </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol13"> </TD></TR> <TR><TD PORT="ltcol14" ></TD><TD align="left" > recon_fx </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol14"> </TD></TR> </TABLE>> ];

"cr_report_line" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > bigserial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > report_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > scn </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > their_balance </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > our_balance </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > user </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > clear_time </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > insert_time </TD><TD align="left" > timestamp with time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > trans_type </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> <TR><TD PORT="ltcol11" ></TD><TD align="left" > post_date </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol11"> </TD></TR> <TR><TD PORT="ltcol15" ></TD><TD align="left" > cleared </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol15"> </TD></TR> </TABLE>> ];

"cr_report_line_links" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > report_line_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > entry_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > unique_exempt </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > cleared </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"currency" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > curr </TD><TD align="left" > character(3) </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"db_patch_log" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > when_applied </TD><TD align="left" > timestamp without time zone </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > path </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > sha </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > sqlstate </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > error </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> </TABLE>> ];

"db_patches" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > sha </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > path </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > last_updated </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"defaults" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > setting_key </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > value </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"eca_invoice" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > order_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > journal_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > on_hold </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > reverse </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > credit_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > due </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > language_code </TD><TD align="left" > character(6) </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > force_closed </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > order_number </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> </TABLE>> ];

"eca_note" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > note_class </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > note </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > vector </TD><TD align="left" > tsvector </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > created </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > created_by </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > subject </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> </TABLE>> ];

"eca_tax" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > eca_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > chart_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"eca_to_contact" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > credit_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > contact_class_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > contact </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"eca_to_location" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > location_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > location_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > credit_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"email" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > workflow_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > from </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > to </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > cc </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > bcc </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > notify </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > subject </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > body </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > sent_date </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > expansions </TD><TD align="left" > jsonb </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> </TABLE>> ];

"employee_class" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > label </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"employee_to_ec" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > employee_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > ec_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"entity" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > name </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > entity_class </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > created </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > control_code </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > country_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol6"> </TD></TR> </TABLE>> ];

"entity_bank_account" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > entity_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > bic </TD><TD align="left" > character varying </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > iban </TD><TD align="left" > character varying </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > remark </TD><TD align="left" > character varying </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> </TABLE>> ];

"entity_class" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > class </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > active </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"entity_credit_account" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > entity_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > entity_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > pay_to_name </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > discount </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > discount_terms </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > discount_account_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > taxincluded </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > creditlimit </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> <TR><TD PORT="ltcol11" ></TD><TD align="left" > terms </TD><TD align="left" > smallint </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol11"> </TD></TR> <TR><TD PORT="ltcol12" ></TD><TD align="left" > meta_number </TD><TD align="left" > character varying(32) </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol12"> </TD></TR> <TR><TD PORT="ltcol13" ></TD><TD align="left" > business_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol13"> </TD></TR> <TR><TD PORT="ltcol14" ></TD><TD align="left" > language_code </TD><TD align="left" > character varying(6) </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol14"> </TD></TR> <TR><TD PORT="ltcol15" ></TD><TD align="left" > pricegroup_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol15"> </TD></TR> <TR><TD PORT="ltcol16" ></TD><TD align="left" > curr </TD><TD align="left" > character(3) </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol16"> </TD></TR> <TR><TD PORT="ltcol17" ></TD><TD align="left" > startdate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol17"> </TD></TR> <TR><TD PORT="ltcol18" ></TD><TD align="left" > enddate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol18"> </TD></TR> <TR><TD PORT="ltcol19" ></TD><TD align="left" > threshold </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol19"> </TD></TR> <TR><TD PORT="ltcol20" ></TD><TD align="left" > employee_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol20"> </TD></TR> <TR><TD PORT="ltcol21" ></TD><TD align="left" > primary_contact </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol21"> </TD></TR> <TR><TD PORT="ltcol22" ></TD><TD align="left" > ar_ap_account_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol22"> </TD></TR> <TR><TD PORT="ltcol23" ></TD><TD align="left" > cash_account_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol23"> </TD></TR> <TR><TD PORT="ltcol24" ></TD><TD align="left" > bank_account </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol24"> </TD></TR> <TR><TD PORT="ltcol25" ></TD><TD align="left" > taxform_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol25"> </TD></TR> </TABLE>> ];

"entity_employee" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > entity_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > startdate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > enddate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > role </TD><TD align="left" > character varying(20) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > ssn </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > sales </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > manager_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > employeenumber </TD><TD align="left" > character varying(32) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > dob </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > is_manager </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> </TABLE>> ];

"entity_note" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > note_class </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > note </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > vector </TD><TD align="left" > tsvector </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > created </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > created_by </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > subject </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > entity_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol9"> </TD></TR> </TABLE>> ];

"entity_other_name" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > entity_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > other_name </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"entity_to_contact" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > entity_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > contact_class_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > contact </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"entity_to_location" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > location_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > location_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > entity_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"exchangerate_default" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > rate_type </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > curr </TD><TD align="left" > character(3) </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > valid_from </TD><TD align="left" > date </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > valid_to </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > rate </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> </TABLE>> ];

"exchangerate_type" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > builtin </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"file_base" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > content </TD><TD align="left" > bytea </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > mime_type_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > file_name </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > uploaded_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > uploaded_at </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > file_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol9"> </TD></TR> </TABLE>> ];

"file_class" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > class </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"file_eca" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > content </TD><TD align="left" > bytea </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > mime_type_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > file_name </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > uploaded_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > uploaded_at </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > file_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> </TABLE>> ];

"file_email" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > content </TD><TD align="left" > bytea </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > mime_type_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > file_name </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > uploaded_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > uploaded_at </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > file_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> </TABLE>> ];

"file_entity" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > content </TD><TD align="left" > bytea </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > mime_type_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > file_name </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > uploaded_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > uploaded_at </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > file_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> </TABLE>> ];

"file_incoming" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > content </TD><TD align="left" > bytea </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > mime_type_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > file_name </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > uploaded_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > uploaded_at </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > file_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> </TABLE>> ];

"file_internal" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > content </TD><TD align="left" > bytea </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > mime_type_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > file_name </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > uploaded_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > uploaded_at </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > file_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> </TABLE>> ];

"file_order" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > content </TD><TD align="left" > bytea </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > mime_type_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > file_name </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > uploaded_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > uploaded_at </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > file_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> </TABLE>> ];

"file_order_to_order" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > file_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > source_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > dest_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > attached_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > attached_at </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> </TABLE>> ];

"file_order_to_tx" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > file_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > source_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > dest_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > attached_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > attached_at </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> </TABLE>> ];

"file_part" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > content </TD><TD align="left" > bytea </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > mime_type_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > file_name </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > uploaded_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > uploaded_at </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > file_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> </TABLE>> ];

"file_reconciliation" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > content </TD><TD align="left" > bytea </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > mime_type_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > file_name </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > uploaded_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > uploaded_at </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > file_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> </TABLE>> ];

"file_secondary_attachment" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > file_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > source_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > dest_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > attached_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > attached_at </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> </TABLE>> ];

"file_transaction" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > content </TD><TD align="left" > bytea </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > mime_type_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > file_name </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > uploaded_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > uploaded_at </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > file_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> </TABLE>> ];

"file_tx_to_order" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > file_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > source_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > dest_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > attached_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > attached_at </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> </TABLE>> ];

"file_view_catalog" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > file_class </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > view_name </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"gifi" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > accno </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > last_updated </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"gl" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > reference </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > transdate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > person_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > notes </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > approved </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > trans_type_code </TD><TD align="left" > character(2) </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol8"> </TD></TR> </TABLE>> ];

"inventory_report" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > transdate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > source </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > trans_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol6"> </TD></TR> </TABLE>> ];

"inventory_report_line" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > adjust_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > parts_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > counted </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > expected </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > variance </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> </TABLE>> ];

"invoice" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > trans_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > parts_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > qty </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > allocated </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > sellprice </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > precision </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > fxsellprice </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > discount </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> <TR><TD PORT="ltcol11" ></TD><TD align="left" > assemblyitem </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol11"> </TD></TR> <TR><TD PORT="ltcol12" ></TD><TD align="left" > unit </TD><TD align="left" > character varying </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol12"> </TD></TR> <TR><TD PORT="ltcol13" ></TD><TD align="left" > deliverydate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol13"> </TD></TR> <TR><TD PORT="ltcol14" ></TD><TD align="left" > serialnumber </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol14"> </TD></TR> <TR><TD PORT="ltcol15" ></TD><TD align="left" > vendor_sku </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol15"> </TD></TR> <TR><TD PORT="ltcol16" ></TD><TD align="left" > notes </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol16"> </TD></TR> </TABLE>> ];

"invoice_note" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > note_class </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > note </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > vector </TD><TD align="left" > tsvector </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > created </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > created_by </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > subject </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> </TABLE>> ];

"invoice_tax_form" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > invoice_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > reportable </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"jcitems" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > business_unit_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > parts_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > qty </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > allocated </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > sellprice </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > fxsellprice </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > serialnumber </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > checkedin </TD><TD align="left" > timestamp with time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> <TR><TD PORT="ltcol11" ></TD><TD align="left" > checkedout </TD><TD align="left" > timestamp with time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol11"> </TD></TR> <TR><TD PORT="ltcol12" ></TD><TD align="left" > person_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol12"> </TD></TR> <TR><TD PORT="ltcol13" ></TD><TD align="left" > notes </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol13"> </TD></TR> <TR><TD PORT="ltcol14" ></TD><TD align="left" > total </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol14"> </TD></TR> <TR><TD PORT="ltcol15" ></TD><TD align="left" > non_billable </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol15"> </TD></TR> <TR><TD PORT="ltcol16" ></TD><TD align="left" > jctype </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol16"> </TD></TR> <TR><TD PORT="ltcol17" ></TD><TD align="left" > curr </TD><TD align="left" > character(3) </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol17"> </TD></TR> </TABLE>> ];

"jctype" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > label </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > is_service </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > is_timecard </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> </TABLE>> ];

"job" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > bu_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > parts_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > production </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > completed </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"journal_entry" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > reference </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > locked_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > journal </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > post_date </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > effective_start </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > effective_end </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > currency </TD><TD align="left" > character(3) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > approved </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> <TR><TD PORT="ltcol11" ></TD><TD align="left" > is_template </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol11"> </TD></TR> <TR><TD PORT="ltcol12" ></TD><TD align="left" > entered_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol12"> </TD></TR> <TR><TD PORT="ltcol13" ></TD><TD align="left" > approved_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol13"> </TD></TR> </TABLE>> ];

"journal_line" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > account_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > journal_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > amount </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > cleared </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > reconciliation_report </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > line_type </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > amount_tc </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > curr </TD><TD align="left" > character(3) </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol9"> </TD></TR> </TABLE>> ];

"journal_note" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > note_class </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > note </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > vector </TD><TD align="left" > tsvector </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > created </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > created_by </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > subject </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > internal_only </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> </TABLE>> ];

"journal_type" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > name </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"language" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > code </TD><TD align="left" > character varying(6) </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > last_updated </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"location" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > line_one </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > line_two </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > line_three </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > city </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > state </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > country_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > mail_code </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > created </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > inactive_date </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> <TR><TD PORT="ltcol11" ></TD><TD align="left" > active </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol11"> </TD></TR> </TABLE>> ];

"location_class" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > class </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > authoritative </TD><TD align="left" > boolean </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"location_class_to_entity_class" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > location_class </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > entity_class </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"lsmb_module" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > label </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"lsmb_sequence" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > label </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > setting_key </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > prefix </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > suffix </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > sequence </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > accept_input </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> </TABLE>> ];

"makemodel" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > parts_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > barcode </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > make </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > model </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"menu_acl" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > role_name </TD><TD align="left" > character varying </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > acl_type </TD><TD align="left" > character varying </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > node_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"menu_node" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > label </TD><TD align="left" > character varying </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > parent </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > position </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > url </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > standalone </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > menu </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> </TABLE>> ];

"mfg_lot" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > lot_number </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > parts_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > qty </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > stock_date </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> </TABLE>> ];

"mfg_lot_item" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > mfg_lot_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > parts_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > qty </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"mime_type" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > mime_type </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > invoice_include </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"new_shipto" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > trans_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > oe_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > location_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"note" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > note_class </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > note </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > vector </TD><TD align="left" > tsvector </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > created </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > created_by </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > ref_key </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > subject </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> </TABLE>> ];

"note_class" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > class </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"oe" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > ordnumber </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > transdate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > entity_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > amount_tc </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > netamount_tc </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > reqdate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > taxincluded </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > shippingpoint </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > notes </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> <TR><TD PORT="ltcol11" ></TD><TD align="left" > curr </TD><TD align="left" > character(3) </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol11"> </TD></TR> <TR><TD PORT="ltcol12" ></TD><TD align="left" > person_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol12"> </TD></TR> <TR><TD PORT="ltcol13" ></TD><TD align="left" > closed </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol13"> </TD></TR> <TR><TD PORT="ltcol14" ></TD><TD align="left" > quotation </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol14"> </TD></TR> <TR><TD PORT="ltcol15" ></TD><TD align="left" > quonumber </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol15"> </TD></TR> <TR><TD PORT="ltcol16" ></TD><TD align="left" > intnotes </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol16"> </TD></TR> <TR><TD PORT="ltcol17" ></TD><TD align="left" > shipvia </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol17"> </TD></TR> <TR><TD PORT="ltcol18" ></TD><TD align="left" > language_code </TD><TD align="left" > character varying(6) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol18"> </TD></TR> <TR><TD PORT="ltcol19" ></TD><TD align="left" > ponumber </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol19"> </TD></TR> <TR><TD PORT="ltcol20" ></TD><TD align="left" > terms </TD><TD align="left" > smallint </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol20"> </TD></TR> <TR><TD PORT="ltcol21" ></TD><TD align="left" > entity_credit_account </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol21"> </TD></TR> <TR><TD PORT="ltcol22" ></TD><TD align="left" > oe_class_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol22"> </TD></TR> <TR><TD PORT="ltcol23" ></TD><TD align="left" > workflow_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol23"> </TD></TR> </TABLE>> ];

"oe_class" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > smallint </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > oe_class </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"open_forms" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > session_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > form_name </TD><TD align="left" > character varying(100) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > last_used </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"orderitems" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > trans_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > parts_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > qty </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > sellprice </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > precision </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > discount </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > unit </TD><TD align="left" > character varying(5) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > reqdate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> <TR><TD PORT="ltcol11" ></TD><TD align="left" > ship </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol11"> </TD></TR> <TR><TD PORT="ltcol12" ></TD><TD align="left" > serialnumber </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol12"> </TD></TR> <TR><TD PORT="ltcol13" ></TD><TD align="left" > notes </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol13"> </TD></TR> </TABLE>> ];

"parts" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > partnumber </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > unit </TD><TD align="left" > character varying(5) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > listprice </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > sellprice </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > lastcost </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > priceupdate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > weight </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > onhand </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> <TR><TD PORT="ltcol11" ></TD><TD align="left" > notes </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol11"> </TD></TR> <TR><TD PORT="ltcol12" ></TD><TD align="left" > makemodel </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol12"> </TD></TR> <TR><TD PORT="ltcol13" ></TD><TD align="left" > assembly </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol13"> </TD></TR> <TR><TD PORT="ltcol14" ></TD><TD align="left" > alternate </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol14"> </TD></TR> <TR><TD PORT="ltcol15" ></TD><TD align="left" > rop </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol15"> </TD></TR> <TR><TD PORT="ltcol16" ></TD><TD align="left" > inventory_accno_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol16"> </TD></TR> <TR><TD PORT="ltcol17" ></TD><TD align="left" > income_accno_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol17"> </TD></TR> <TR><TD PORT="ltcol18" ></TD><TD align="left" > expense_accno_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol18"> </TD></TR> <TR><TD PORT="ltcol19" ></TD><TD align="left" > returns_accno_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol19"> </TD></TR> <TR><TD PORT="ltcol20" ></TD><TD align="left" > bin </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol20"> </TD></TR> <TR><TD PORT="ltcol21" ></TD><TD align="left" > obsolete </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol21"> </TD></TR> <TR><TD PORT="ltcol22" ></TD><TD align="left" > bom </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol22"> </TD></TR> <TR><TD PORT="ltcol23" ></TD><TD align="left" > image </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol23"> </TD></TR> <TR><TD PORT="ltcol24" ></TD><TD align="left" > drawing </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol24"> </TD></TR> <TR><TD PORT="ltcol25" ></TD><TD align="left" > microfiche </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol25"> </TD></TR> <TR><TD PORT="ltcol26" ></TD><TD align="left" > partsgroup_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol26"> </TD></TR> <TR><TD PORT="ltcol27" ></TD><TD align="left" > avgcost </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol27"> </TD></TR> </TABLE>> ];

"parts_translation" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > trans_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > language_code </TD><TD align="left" > character varying(6) </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"partscustomer" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > parts_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > credit_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > pricegroup_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > pricebreak </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > sellprice </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > validfrom </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > validto </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > curr </TD><TD align="left" > character(3) </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > entry_id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > qty </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> </TABLE>> ];

"partsgroup" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > partsgroup </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > parent </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"partsgroup_translation" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > trans_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > language_code </TD><TD align="left" > character varying(6) </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"partstax" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > parts_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > chart_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > taxcategory_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"partsvendor" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > credit_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > parts_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > partnumber </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > leadtime </TD><TD align="left" > smallint </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > lastcost </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > curr </TD><TD align="left" > character(3) </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > entry_id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> </TABLE>> ];

"payment" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > reference </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > gl_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > payment_class </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > payment_date </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > closed </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > entity_credit_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > employee_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > currency </TD><TD align="left" > character(3) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> <TR><TD PORT="ltcol10" ></TD><TD align="left" > notes </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol10"> </TD></TR> <TR><TD PORT="ltcol11" ></TD><TD align="left" > reversing </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol11"> </TD></TR> </TABLE>> ];

"payment_links" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > payment_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > entry_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > type </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"payment_type" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > label </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"payroll_deduction" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > entry_id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > entity_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > type_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > rate </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"payroll_deduction_class" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > country_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > label </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > stored_proc_name </TD><TD align="left" > name </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"payroll_deduction_type" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > account_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > pdc_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > country_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > label </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > unit </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > default_amount </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > calc_percent </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> </TABLE>> ];

"payroll_employee_class" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > label </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"payroll_employee_class_to_income_type" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > ec_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > it_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"payroll_income_category" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > label </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"payroll_income_class" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > country_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > label </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"payroll_income_type" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > account_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > pic_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > country_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > label </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > unit </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > default_amount </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> </TABLE>> ];

"payroll_paid_timeoff" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > employee_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > pto_class_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > report_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > amount </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"payroll_pto_class" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > label </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"payroll_report" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > ec_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > payment_date </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > created_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > approved_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol5"> </TD></TR> </TABLE>> ];

"payroll_report_line" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > report_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > employee_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > it_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > qty </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > rate </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> </TABLE>> ];

"payroll_wage" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > entry_id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > entity_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > type_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > rate </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"person" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > entity_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > salutation_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > first_name </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > middle_name </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > last_name </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > created </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > birthdate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> <TR><TD PORT="ltcol9" ></TD><TD align="left" > personal_id </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol9"> </TD></TR> </TABLE>> ];

"person_to_company" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > location_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > person_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > company_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"pricegroup" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > pricegroup </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > last_updated </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"recurring" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > reference </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > startdate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > nextdate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > enddate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > recurring_interval </TD><TD align="left" > interval </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > howmany </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > payment </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> </TABLE>> ];

"recurringemail" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > formname </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > format </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > message </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"recurringprint" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > formname </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > format </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > printer </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"robot" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > entity_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > first_name </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > middle_name </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > last_name </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > created </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> </TABLE>> ];

"salutation" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > salutation </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"session" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > session_id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > token </TD><TD align="left" > character varying(32) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > last_used </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > ttl </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > users_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > notify_pasword </TD><TD align="left" > interval </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> </TABLE>> ];

"sic" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > code </TD><TD align="left" > character varying(6) </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > sictype </TD><TD align="left" > character(1) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > last_updated </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"status" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > trans_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > formname </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > printed </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > emailed </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > spoolfile </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> </TABLE>> ];

"tax" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > chart_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > rate </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > minvalue </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > maxvalue </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > taxnumber </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > validto </TD><TD align="left" > timestamp without time zone </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > pass </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > taxmodule_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol8"> </TD></TR> </TABLE>> ];

"tax_extended" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > tax_basis </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > rate </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > entry_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"taxcategory" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > taxcategory_id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > taxcategoryname </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > taxmodule_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"taxmodule" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > taxmodule_id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > taxmodulename </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"template" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > template_name </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > language_code </TD><TD align="left" > character varying(6) </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > template </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > format </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > last_modified </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> </TABLE>> ];

"trans_type" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > code </TD><TD align="left" > character(2) </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > description </TD><TD align="left" > character varying(1000) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"transactions" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > table_name </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > locked_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > approved </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > approved_by </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > approved_at </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > transdate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > workflow_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol8"> </TD></TR> </TABLE>> ];

"translation" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > trans_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > language_code </TD><TD align="left" > character varying(6) </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"trial_balance__yearend_types" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > type </TD><TD align="left" > text </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> </TABLE>> ];

"user_preference" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > user_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > name </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > value </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"users" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > username </TD><TD align="left" > character varying(30) </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > notify_password </TD><TD align="left" > interval </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > entity_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"voucher" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > trans_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > batch_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > batch_class </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"warehouse" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > description </TD><TD align="left" > text </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > last_updated </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];

"warehouse_inventory" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > entity_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > warehouse_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > parts_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > trans_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > orderitems_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > qty </TD><TD align="left" > numeric </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > shippingdate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> <TR><TD PORT="ltcol8" ></TD><TD align="left" > entry_id </TD><TD align="left" > serial </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol8"> </TD></TR> </TABLE>> ];

"workflow" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > workflow_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > type </TD><TD align="left" > character varying(50) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > state </TD><TD align="left" > character varying(30) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > last_update </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> </TABLE>> ];

"workflow_context" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > workflow_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > context </TD><TD align="left" > jsonb </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> </TABLE>> ];

"workflow_history" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > workflow_hist_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > workflow_id </TD><TD align="left" > integer </TD><TD align="left" >  </TD><TD align="left" > FK </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > action </TD><TD align="left" > character varying(25) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> <TR><TD PORT="ltcol4" ></TD><TD align="left" > description </TD><TD align="left" > character varying(255) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol4"> </TD></TR> <TR><TD PORT="ltcol5" ></TD><TD align="left" > state </TD><TD align="left" > character varying(30) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol5"> </TD></TR> <TR><TD PORT="ltcol6" ></TD><TD align="left" > workflow_user </TD><TD align="left" > character varying(50) </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol6"> </TD></TR> <TR><TD PORT="ltcol7" ></TD><TD align="left" > history_date </TD><TD align="left" > timestamp without time zone </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol7"> </TD></TR> </TABLE>> ];

"yearend" [shape = plaintext, label = < <TABLE BORDER="1" CELLBORDER="0" CELLSPACING="0"> <TR ><TD PORT="ltcol0"> </TD> <TD bgcolor="grey90" border="1" COLSPAN="4"> \N </TD> <TD PORT="rtcol0"></TD></TR>  <TR><TD PORT="ltcol1" ></TD><TD align="left" > trans_id </TD><TD align="left" > integer </TD><TD align="left" > PK </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol1"> </TD></TR> <TR><TD PORT="ltcol2" ></TD><TD align="left" > reversed </TD><TD align="left" > boolean </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol2"> </TD></TR> <TR><TD PORT="ltcol3" ></TD><TD align="left" > transdate </TD><TD align="left" > date </TD><TD align="left" >  </TD><TD align="left" >  </TD><TD align="left" PORT="rtcol3"> </TD></TR> </TABLE>> ];



"ac_tax_form":rtcol1 -> "acc_trans":ltcol14 [label="ac_tax_form_entry_id_fkey"];
"acc_trans":rtcol1 -> "transactions":ltcol1 [label="acc_trans_trans_id_fkey"];
"acc_trans":rtcol2 -> "account":ltcol1 [label="acc_trans_chart_id_fkey"];
"acc_trans":rtcol9 -> "invoice":ltcol1 [label="acc_trans_invoice_id_fkey"];
"acc_trans":rtcol13 -> "voucher":ltcol3 [label="acc_trans_voucher_id_fkey"];
"acc_trans":rtcol17 -> "currency":ltcol1 [label="acc_trans_curr_fkey"];
"account":rtcol6 -> "gifi":ltcol1 [label="account_gifi_accno_fkey"];
"account":rtcol7 -> "account_heading":ltcol1 [label="account_heading_fkey"];
"account_checkpoint":rtcol2 -> "account":ltcol1 [label="account_checkpoint_account_id_fkey"];
"account_checkpoint":rtcol9 -> "currency":ltcol1 [label="account_checkpoint_curr_fkey"];
"account_heading":rtcol3 -> "account_heading":ltcol1 [label="account_heading_parent_id_fkey"];
"account_heading_translation":rtcol1 -> "account_heading":ltcol1 [label="account_heading_translation_trans_id_fkey"];
"account_heading_translation":rtcol1 -> "account_heading":ltcol1 [label="account_heading_translation_trans_id_fkey1"];
"account_link":rtcol1 -> "account":ltcol1 [label="account_link_account_id_fkey"];
"account_link":rtcol2 -> "account_link_description":ltcol1 [label="account_link_description_fkey"];
"account_translation":rtcol1 -> "account":ltcol1 [label="account_translation_trans_id_fkey"];
"account_translation":rtcol1 -> "account":ltcol1 [label="account_translation_trans_id_fkey1"];
"ap":rtcol1 -> "transactions":ltcol1 [label="ap_id_fkey"];
"ap":rtcol4 -> "entity":ltcol1 [label="ap_entity_id_fkey"];
"ap":rtcol13 -> "currency":ltcol1 [label="ap_curr_fkey"];
"ap":rtcol15 -> "entity_employee":ltcol1 [label="ap_person_id_fkey"];
"ap":rtcol31 -> "entity_credit_account":ltcol1 [label="ap_entity_credit_account_fkey"];
"ar":rtcol1 -> "transactions":ltcol1 [label="ar_id_fkey"];
"ar":rtcol4 -> "entity":ltcol1 [label="ar_entity_id_fkey"];
"ar":rtcol15 -> "currency":ltcol1 [label="ar_curr_fkey"];
"ar":rtcol17 -> "entity_employee":ltcol1 [label="ar_person_id_fkey"];
"ar":rtcol27 -> "entity_credit_account":ltcol1 [label="ar_entity_credit_account_fkey"];
"assembly":rtcol1 -> "parts":ltcol1 [label="assembly_id_fkey"];
"assembly":rtcol2 -> "parts":ltcol1 [label="assembly_parts_id_fkey"];
"asset_class":rtcol3 -> "account":ltcol1 [label="asset_class_asset_account_id_fkey"];
"asset_class":rtcol4 -> "account":ltcol1 [label="asset_class_dep_account_id_fkey"];
"asset_class":rtcol5 -> "asset_dep_method":ltcol1 [label="asset_class_method_fkey"];
"asset_dep_method":rtcol6 -> "asset_unit_class":ltcol1 [label="asset_dep_method_unit_class_fkey"];
"asset_item":rtcol9 -> "warehouse":ltcol1 [label="asset_item_location_id_fkey"];
"asset_item":rtcol10 -> "business_unit":ltcol1 [label="asset_item_department_id_fkey"];
"asset_item":rtcol11 -> "eca_invoice":ltcol2 [label="asset_item_invoice_id_fkey"];
"asset_item":rtcol12 -> "account":ltcol1 [label="asset_item_asset_account_id_fkey"];
"asset_item":rtcol13 -> "account":ltcol1 [label="asset_item_dep_account_id_fkey"];
"asset_item":rtcol14 -> "account":ltcol1 [label="asset_item_exp_account_id_fkey"];
"asset_item":rtcol15 -> "asset_item":ltcol1 [label="asset_item_obsolete_by_fkey"];
"asset_item":rtcol16 -> "asset_class":ltcol1 [label="asset_item_asset_class_id_fkey"];
"asset_note":rtcol7 -> "asset_item":ltcol1 [label="asset_note_ref_key_fkey"];
"asset_report":rtcol3 -> "gl":ltcol1 [label="asset_report_gl_id_fkey"];
"asset_report":rtcol4 -> "asset_class":ltcol1 [label="asset_report_asset_class_fkey"];
"asset_report":rtcol5 -> "asset_report_class":ltcol1 [label="asset_report_report_class_fkey"];
"asset_report":rtcol6 -> "entity":ltcol1 [label="asset_report_entered_by_fkey"];
"asset_report":rtcol7 -> "entity":ltcol1 [label="asset_report_approved_by_fkey"];
"asset_report_line":rtcol1 -> "asset_item":ltcol1 [label="asset_report_line_asset_id_fkey"];
"asset_report_line":rtcol2 -> "asset_report":ltcol1 [label="asset_report_line_report_id_fkey"];
"asset_report_line":rtcol4 -> "business_unit":ltcol1 [label="asset_report_line_department_id_fkey"];
"asset_report_line":rtcol5 -> "warehouse":ltcol1 [label="asset_report_line_warehouse_id_fkey"];
"asset_rl_to_disposal_method":rtcol1 -> "asset_report":ltcol1 [label="asset_rl_to_disposal_method_report_id_fkey"];
"asset_rl_to_disposal_method":rtcol2 -> "asset_item":ltcol1 [label="asset_rl_to_disposal_method_asset_id_fkey"];
"asset_rl_to_disposal_method":rtcol3 -> "asset_disposal_method":ltcol2 [label="asset_rl_to_disposal_method_disposal_method_id_fkey"];
"audittrail":rtcol7 -> "person":ltcol2 [label="audittrail_person_id_fkey"];
"batch":rtcol2 -> "batch_class":ltcol1 [label="batch_batch_class_id_fkey"];
"batch":rtcol7 -> "entity_employee":ltcol1 [label="batch_approved_by_fkey"];
"batch":rtcol8 -> "entity_employee":ltcol1 [label="batch_created_by_fkey"];
"batch":rtcol9 -> "session":ltcol1 [label="batch_locked_by_fkey"];
"bu_class_to_module":rtcol1 -> "business_unit_class":ltcol1 [label="bu_class_to_module_bu_class_id_fkey"];
"bu_class_to_module":rtcol2 -> "lsmb_module":ltcol1 [label="bu_class_to_module_module_id_fkey"];
"budget_info":rtcol6 -> "entity":ltcol1 [label="budget_info_entered_by_fkey"];
"budget_info":rtcol7 -> "entity":ltcol1 [label="budget_info_approved_by_fkey"];
"budget_info":rtcol8 -> "entity":ltcol1 [label="budget_info_obsolete_by_fkey"];
"budget_line":rtcol1 -> "budget_info":ltcol1 [label="budget_line_budget_id_fkey"];
"budget_line":rtcol2 -> "account":ltcol1 [label="budget_line_account_id_fkey"];
"budget_line":rtcol6 -> "currency":ltcol1 [label="budget_line_curr_fkey"];
"budget_note":rtcol7 -> "budget_info":ltcol1 [label="budget_note_ref_key_fkey"];
"budget_to_business_unit":rtcol1 -> "budget_info":ltcol1 [label="budget_to_business_unit_budget_id_fkey"];
"budget_to_business_unit":rtcol2 -> "business_unit":ltcol1 [label="budget_to_business_unit_bu_id_fkey"];
"budget_to_business_unit":rtcol3 -> "business_unit_class":ltcol1 [label="budget_to_business_unit_bu_class_fkey"];
"business_unit":rtcol2 -> "business_unit_class":ltcol1 [label="business_unit_class_id_fkey"];
"business_unit":rtcol7 -> "business_unit":ltcol1 [label="business_unit_parent_id_fkey"];
"business_unit":rtcol8 -> "entity_credit_account":ltcol1 [label="business_unit_credit_id_fkey"];
"business_unit_ac":rtcol1 -> "acc_trans":ltcol14 [label="business_unit_ac_entry_id_fkey"];
"business_unit_ac":rtcol0 -> "business_unit":ltcol0 [label="business_unit_ac_class_id_bu_id_fkey"];
"business_unit_ac":rtcol2 -> "business_unit_class":ltcol1 [label="business_unit_ac_class_id_fkey"];
"business_unit_inv":rtcol1 -> "invoice":ltcol1 [label="business_unit_inv_entry_id_fkey"];
"business_unit_inv":rtcol0 -> "business_unit":ltcol0 [label="business_unit_inv_class_id_bu_id_fkey"];
"business_unit_inv":rtcol2 -> "business_unit_class":ltcol1 [label="business_unit_inv_class_id_fkey"];
"business_unit_jl":rtcol1 -> "journal_line":ltcol1 [label="business_unit_jl_entry_id_fkey"];
"business_unit_jl":rtcol2 -> "business_unit_class":ltcol1 [label="business_unit_jl_bu_class_fkey"];
"business_unit_jl":rtcol3 -> "business_unit":ltcol1 [label="business_unit_jl_bu_id_fkey"];
"business_unit_oitem":rtcol1 -> "orderitems":ltcol1 [label="business_unit_oitem_entry_id_fkey"];
"business_unit_oitem":rtcol0 -> "business_unit":ltcol0 [label="business_unit_oitem_class_id_bu_id_fkey"];
"business_unit_oitem":rtcol2 -> "business_unit_class":ltcol1 [label="business_unit_oitem_class_id_fkey"];
"business_unit_translation":rtcol1 -> "business_unit":ltcol1 [label="business_unit_translation_trans_id_fkey"];
"company":rtcol2 -> "entity":ltcol1 [label="company_entity_id_fkey"];
"company":rtcol7 -> "sic":ltcol1 [label="company_sic_code_fkey"];
"country_tax_form":rtcol1 -> "country":ltcol1 [label="country_tax_form_country_id_fkey"];
"cr_coa_to_account":rtcol1 -> "account":ltcol1 [label="cr_coa_to_account_chart_id_fkey"];
"cr_report":rtcol2 -> "account":ltcol1 [label="cr_report_chart_id_fkey"];
"cr_report":rtcol8 -> "entity":ltcol1 [label="cr_report_entered_by_fkey"];
"cr_report":rtcol11 -> "entity":ltcol1 [label="cr_report_deleted_by_fkey"];
"cr_report":rtcol12 -> "entity":ltcol1 [label="cr_report_approved_by_fkey"];
"cr_report_line":rtcol2 -> "cr_report":ltcol1 [label="cr_report_line_report_id_fkey"];
"cr_report_line":rtcol7 -> "entity":ltcol1 [label="cr_report_line_user_fkey"];
"cr_report_line_links":rtcol1 -> "cr_report_line":ltcol1 [label="cr_report_line_links_report_line_id_fkey"];
"cr_report_line_links":rtcol2 -> "acc_trans":ltcol14 [label="cr_report_line_links_entry_id_fkey"];
"eca_invoice":rtcol2 -> "journal_entry":ltcol1 [label="eca_invoice_journal_id_fkey"];
"eca_invoice":rtcol5 -> "entity_credit_account":ltcol1 [label="eca_invoice_credit_id_fkey"];
"eca_invoice":rtcol7 -> "language":ltcol1 [label="eca_invoice_language_code_fkey"];
"eca_note":rtcol7 -> "entity_credit_account":ltcol1 [label="eca_note_ref_key_fkey"];
"eca_tax":rtcol1 -> "entity_credit_account":ltcol1 [label="eca_tax_eca_id_fkey"];
"eca_tax":rtcol2 -> "account":ltcol1 [label="eca_tax_chart_id_fkey"];
"eca_to_contact":rtcol1 -> "entity_credit_account":ltcol1 [label="eca_to_contact_credit_id_fkey"];
"eca_to_contact":rtcol2 -> "contact_class":ltcol1 [label="eca_to_contact_contact_class_id_fkey"];
"eca_to_location":rtcol1 -> "location":ltcol1 [label="eca_to_location_location_id_fkey"];
"eca_to_location":rtcol2 -> "location_class":ltcol1 [label="eca_to_location_location_class_fkey"];
"eca_to_location":rtcol3 -> "entity_credit_account":ltcol1 [label="eca_to_location_credit_id_fkey"];
"email":rtcol1 -> "workflow":ltcol1 [label="email_workflow_id_fkey"];
"employee_to_ec":rtcol1 -> "entity_employee":ltcol1 [label="employee_to_ec_employee_id_fkey"];
"employee_to_ec":rtcol2 -> "employee_class":ltcol2 [label="employee_to_ec_ec_id_fkey"];
"entity":rtcol3 -> "entity_class":ltcol1 [label="entity_entity_class_fkey"];
"entity":rtcol6 -> "country":ltcol1 [label="entity_country_id_fkey"];
"entity_bank_account":rtcol2 -> "entity":ltcol1 [label="entity_bank_account_entity_id_fkey"];
"entity_credit_account":rtcol2 -> "entity":ltcol1 [label="entity_credit_account_entity_id_fkey"];
"entity_credit_account":rtcol3 -> "entity_class":ltcol1 [label="entity_credit_account_entity_class_fkey"];
"entity_credit_account":rtcol8 -> "account":ltcol1 [label="entity_credit_account_discount_account_id_fkey"];
"entity_credit_account":rtcol13 -> "business":ltcol1 [label="entity_credit_account_business_id_fkey"];
"entity_credit_account":rtcol14 -> "language":ltcol1 [label="entity_credit_account_language_code_fkey"];
"entity_credit_account":rtcol15 -> "pricegroup":ltcol1 [label="entity_credit_account_pricegroup_id_fkey"];
"entity_credit_account":rtcol16 -> "currency":ltcol1 [label="entity_credit_account_curr_fkey"];
"entity_credit_account":rtcol20 -> "entity_employee":ltcol1 [label="entity_credit_account_employee_id_fkey"];
"entity_credit_account":rtcol21 -> "person":ltcol1 [label="entity_credit_account_primary_contact_fkey"];
"entity_credit_account":rtcol22 -> "account":ltcol1 [label="entity_credit_account_ar_ap_account_id_fkey"];
"entity_credit_account":rtcol23 -> "account":ltcol1 [label="entity_credit_account_cash_account_id_fkey"];
"entity_credit_account":rtcol24 -> "entity_bank_account":ltcol1 [label="entity_credit_account_bank_account_fkey"];
"entity_credit_account":rtcol25 -> "country_tax_form":ltcol3 [label="entity_credit_account_taxform_id_fkey"];
"entity_employee":rtcol1 -> "entity":ltcol1 [label="entity_employee_entity_id_fkey"];
"entity_employee":rtcol7 -> "entity":ltcol1 [label="entity_employee_manager_id_fkey"];
"entity_note":rtcol7 -> "entity":ltcol1 [label="entity_note_ref_key_fkey"];
"entity_note":rtcol9 -> "entity":ltcol1 [label="entity_note_entity_id_fkey"];
"entity_other_name":rtcol1 -> "entity":ltcol1 [label="entity_other_name_entity_id_fkey"];
"entity_to_contact":rtcol1 -> "entity":ltcol1 [label="entity_to_contact_entity_id_fkey"];
"entity_to_contact":rtcol2 -> "contact_class":ltcol1 [label="entity_to_contact_contact_class_id_fkey"];
"entity_to_location":rtcol1 -> "location":ltcol1 [label="entity_to_location_location_id_fkey"];
"entity_to_location":rtcol2 -> "location_class":ltcol1 [label="entity_to_location_location_class_fkey"];
"entity_to_location":rtcol3 -> "entity":ltcol1 [label="entity_to_location_entity_id_fkey"];
"exchangerate_default":rtcol1 -> "exchangerate_type":ltcol1 [label="exchangerate_default_rate_type_fkey"];
"exchangerate_default":rtcol2 -> "currency":ltcol1 [label="exchangerate_default_curr_fkey"];
"file_base":rtcol2 -> "mime_type":ltcol1 [label="file_base_mime_type_id_fkey"];
"file_base":rtcol5 -> "entity":ltcol1 [label="file_base_uploaded_by_fkey"];
"file_base":rtcol9 -> "file_class":ltcol1 [label="file_base_file_class_fkey"];
"file_eca":rtcol8 -> "entity_credit_account":ltcol1 [label="file_eca_ref_key_fkey"];
"file_email":rtcol8 -> "email":ltcol1 [label="file_email_ref_key_fkey"];
"file_entity":rtcol8 -> "entity":ltcol1 [label="file_entity_ref_key_fkey"];
"file_order":rtcol8 -> "oe":ltcol1 [label="file_order_ref_key_fkey"];
"file_order_to_order":rtcol1 -> "file_order":ltcol7 [label="file_order_to_order_file_id_fkey"];
"file_order_to_order":rtcol3 -> "oe":ltcol1 [label="file_order_to_order_ref_key_fkey"];
"file_order_to_tx":rtcol1 -> "file_order":ltcol7 [label="file_order_to_tx_file_id_fkey"];
"file_order_to_tx":rtcol3 -> "gl":ltcol1 [label="file_order_to_tx_ref_key_fkey"];
"file_part":rtcol8 -> "parts":ltcol1 [label="file_part_ref_key_fkey"];
"file_reconciliation":rtcol8 -> "cr_report":ltcol1 [label="file_reconciliation_ref_key_fkey"];
"file_secondary_attachment":rtcol2 -> "file_class":ltcol1 [label="file_secondary_attachment_source_class_fkey"];
"file_secondary_attachment":rtcol4 -> "file_class":ltcol1 [label="file_secondary_attachment_dest_class_fkey"];
"file_secondary_attachment":rtcol5 -> "entity":ltcol1 [label="file_secondary_attachment_attached_by_fkey"];
"file_transaction":rtcol8 -> "transactions":ltcol1 [label="file_transaction_ref_key_fkey"];
"file_tx_to_order":rtcol1 -> "file_transaction":ltcol7 [label="file_tx_to_order_file_id_fkey"];
"file_tx_to_order":rtcol3 -> "oe":ltcol1 [label="file_tx_to_order_ref_key_fkey"];
"file_view_catalog":rtcol1 -> "file_class":ltcol1 [label="file_view_catalog_file_class_fkey"];
"gl":rtcol1 -> "transactions":ltcol1 [label="gl_id_fkey"];
"gl":rtcol5 -> "person":ltcol1 [label="gl_person_id_fkey"];
"gl":rtcol8 -> "trans_type":ltcol1 [label="gl_trans_type_code_fkey"];
"inventory_report":rtcol6 -> "gl":ltcol1 [label="inventory_report_trans_id_fkey"];
"inventory_report_line":rtcol1 -> "inventory_report":ltcol1 [label="inventory_report_line_adjust_id_fkey"];
"inventory_report_line":rtcol2 -> "parts":ltcol1 [label="inventory_report_line_parts_id_fkey"];
"invoice":rtcol2 -> "transactions":ltcol1 [label="invoice_trans_id_fkey"];
"invoice":rtcol3 -> "parts":ltcol1 [label="invoice_parts_id_fkey"];
"invoice_note":rtcol7 -> "invoice":ltcol1 [label="invoice_note_ref_key_fkey"];
"invoice_tax_form":rtcol1 -> "invoice":ltcol1 [label="invoice_tax_form_invoice_id_fkey"];
"jcitems":rtcol2 -> "business_unit":ltcol1 [label="jcitems_business_unit_id_fkey"];
"jcitems":rtcol12 -> "person":ltcol1 [label="jcitems_person_id_fkey"];
"jcitems":rtcol16 -> "jctype":ltcol1 [label="jcitems_jctype_fkey"];
"jcitems":rtcol17 -> "currency":ltcol1 [label="jcitems_curr_fkey"];
"job":rtcol1 -> "business_unit":ltcol1 [label="job_bu_id_fkey"];
"journal_entry":rtcol4 -> "session":ltcol1 [label="journal_entry_locked_by_fkey"];
"journal_entry":rtcol5 -> "journal_type":ltcol1 [label="journal_entry_journal_fkey"];
"journal_entry":rtcol12 -> "entity":ltcol1 [label="journal_entry_entered_by_fkey"];
"journal_entry":rtcol13 -> "entity":ltcol1 [label="journal_entry_approved_by_fkey"];
"journal_line":rtcol2 -> "account":ltcol1 [label="journal_line_account_id_fkey"];
"journal_line":rtcol3 -> "journal_entry":ltcol1 [label="journal_line_journal_id_fkey"];
"journal_line":rtcol6 -> "cr_report":ltcol1 [label="journal_line_reconciliation_report_fkey"];
"journal_line":rtcol7 -> "account_link_description":ltcol1 [label="journal_line_line_type_fkey"];
"journal_line":rtcol9 -> "currency":ltcol1 [label="journal_line_curr_fkey"];
"journal_note":rtcol7 -> "journal_entry":ltcol1 [label="journal_note_ref_key_fkey"];
"location":rtcol7 -> "country":ltcol1 [label="location_country_id_fkey"];
"location_class_to_entity_class":rtcol2 -> "location_class":ltcol1 [label="location_class_to_entity_class_location_class_fkey"];
"location_class_to_entity_class":rtcol3 -> "entity_class":ltcol1 [label="location_class_to_entity_class_entity_class_fkey"];
"lsmb_sequence":rtcol2 -> "defaults":ltcol1 [label="lsmb_sequence_setting_key_fkey"];
"makemodel":rtcol1 -> "parts":ltcol1 [label="makemodel_parts_id_fkey"];
"menu_acl":rtcol4 -> "menu_node":ltcol1 [label="menu_acl_node_id_fkey"];
"menu_node":rtcol3 -> "menu_node":ltcol1 [label="menu_node_parent_fkey"];
"mfg_lot":rtcol3 -> "parts":ltcol1 [label="mfg_lot_parts_id_fkey"];
"mfg_lot_item":rtcol2 -> "mfg_lot":ltcol1 [label="mfg_lot_item_mfg_lot_id_fkey"];
"mfg_lot_item":rtcol3 -> "parts":ltcol1 [label="mfg_lot_item_parts_id_fkey"];
"new_shipto":rtcol2 -> "transactions":ltcol1 [label="new_shipto_trans_id_fkey"];
"new_shipto":rtcol3 -> "oe":ltcol1 [label="new_shipto_oe_id_fkey"];
"new_shipto":rtcol4 -> "location":ltcol1 [label="new_shipto_location_id_fkey"];
"note":rtcol2 -> "note_class":ltcol1 [label="note_note_class_fkey"];
"oe":rtcol4 -> "entity":ltcol1 [label="oe_entity_id_fkey"];
"oe":rtcol11 -> "currency":ltcol1 [label="oe_curr_fkey"];
"oe":rtcol12 -> "person":ltcol2 [label="oe_person_id_fkey"];
"oe":rtcol21 -> "entity_credit_account":ltcol1 [label="oe_entity_credit_account_fkey"];
"oe":rtcol22 -> "oe_class":ltcol1 [label="oe_oe_class_id_fkey"];
"oe":rtcol23 -> "workflow":ltcol1 [label="oe_workflow_id_fkey"];
"open_forms":rtcol2 -> "session":ltcol1 [label="open_forms_session_id_fkey"];
"orderitems":rtcol3 -> "parts":ltcol1 [label="orderitems_parts_id_fkey"];
"parts":rtcol16 -> "account":ltcol1 [label="parts_inventory_accno_id_fkey"];
"parts":rtcol17 -> "account":ltcol1 [label="parts_income_accno_id_fkey"];
"parts":rtcol18 -> "account":ltcol1 [label="parts_expense_accno_id_fkey"];
"parts":rtcol19 -> "account":ltcol1 [label="parts_returns_accno_id_fkey"];
"parts":rtcol26 -> "partsgroup":ltcol1 [label="parts_partsgroup_id_fkey"];
"parts_translation":rtcol1 -> "parts":ltcol1 [label="parts_translation_trans_id_fkey"];
"partscustomer":rtcol1 -> "parts":ltcol1 [label="partscustomer_parts_id_fkey"];
"partscustomer":rtcol2 -> "entity_credit_account":ltcol1 [label="partscustomer_credit_id_fkey"];
"partscustomer":rtcol3 -> "pricegroup":ltcol1 [label="partscustomer_pricegroup_id_fkey"];
"partscustomer":rtcol8 -> "currency":ltcol1 [label="partscustomer_curr_fkey"];
"partsgroup":rtcol3 -> "partsgroup":ltcol1 [label="partsgroup_parent_fkey"];
"partsgroup_translation":rtcol1 -> "partsgroup":ltcol1 [label="partsgroup_translation_trans_id_fkey"];
"partstax":rtcol1 -> "parts":ltcol1 [label="partstax_parts_id_fkey"];
"partstax":rtcol2 -> "account":ltcol1 [label="partstax_chart_id_fkey"];
"partstax":rtcol3 -> "taxcategory":ltcol1 [label="partstax_taxcategory_id_fkey"];
"partsvendor":rtcol1 -> "entity_credit_account":ltcol1 [label="partsvendor_credit_id_fkey"];
"partsvendor":rtcol6 -> "currency":ltcol1 [label="partsvendor_curr_fkey"];
"payment":rtcol3 -> "gl":ltcol1 [label="payment_gl_id_fkey"];
"payment":rtcol7 -> "entity_credit_account":ltcol1 [label="payment_entity_credit_id_fkey"];
"payment":rtcol8 -> "person":ltcol1 [label="payment_employee_id_fkey"];
"payment_links":rtcol1 -> "payment":ltcol1 [label="payment_links_payment_id_fkey"];
"payment_links":rtcol2 -> "acc_trans":ltcol14 [label="payment_links_entry_id_fkey"];
"payroll_deduction":rtcol2 -> "entity":ltcol1 [label="payroll_deduction_entity_id_fkey"];
"payroll_deduction":rtcol3 -> "payroll_deduction_type":ltcol1 [label="payroll_deduction_type_id_fkey"];
"payroll_deduction_class":rtcol2 -> "country":ltcol1 [label="payroll_deduction_class_country_id_fkey"];
"payroll_deduction_type":rtcol2 -> "account":ltcol1 [label="payroll_deduction_type_account_id_fkey"];
"payroll_deduction_type":rtcol0 -> "payroll_deduction_class":ltcol0 [label="payroll_deduction_type_pdc_id_country_id_fkey"];
"payroll_employee_class_to_income_type":rtcol1 -> "payroll_employee_class":ltcol1 [label="payroll_employee_class_to_income_type_ec_id_fkey"];
"payroll_employee_class_to_income_type":rtcol2 -> "payroll_income_type":ltcol1 [label="payroll_employee_class_to_income_type_it_id_fkey"];
"payroll_income_class":rtcol2 -> "country":ltcol1 [label="payroll_income_class_country_id_fkey"];
"payroll_income_type":rtcol2 -> "account":ltcol1 [label="payroll_income_type_account_id_fkey"];
"payroll_income_type":rtcol0 -> "payroll_income_class":ltcol0 [label="payroll_income_type_pic_id_country_id_fkey"];
"payroll_income_type":rtcol3 -> "payroll_income_category":ltcol1 [label="payroll_income_type_pic_id_fkey"];
"payroll_paid_timeoff":rtcol1 -> "entity":ltcol1 [label="payroll_paid_timeoff_employee_id_fkey"];
"payroll_paid_timeoff":rtcol2 -> "payroll_pto_class":ltcol1 [label="payroll_paid_timeoff_pto_class_id_fkey"];
"payroll_paid_timeoff":rtcol3 -> "payroll_report":ltcol1 [label="payroll_paid_timeoff_report_id_fkey"];
"payroll_report":rtcol2 -> "payroll_employee_class":ltcol1 [label="payroll_report_ec_id_fkey"];
"payroll_report":rtcol4 -> "entity_employee":ltcol1 [label="payroll_report_created_by_fkey"];
"payroll_report":rtcol5 -> "entity_employee":ltcol1 [label="payroll_report_approved_by_fkey"];
"payroll_report_line":rtcol2 -> "payroll_report":ltcol1 [label="payroll_report_line_report_id_fkey"];
"payroll_report_line":rtcol3 -> "entity":ltcol1 [label="payroll_report_line_employee_id_fkey"];
"payroll_report_line":rtcol4 -> "payroll_income_type":ltcol1 [label="payroll_report_line_it_id_fkey"];
"payroll_wage":rtcol2 -> "entity":ltcol1 [label="payroll_wage_entity_id_fkey"];
"payroll_wage":rtcol3 -> "payroll_income_type":ltcol1 [label="payroll_wage_type_id_fkey"];
"person":rtcol2 -> "entity":ltcol1 [label="person_entity_id_fkey"];
"person":rtcol3 -> "salutation":ltcol1 [label="person_salutation_id_fkey"];
"person_to_company":rtcol1 -> "location":ltcol1 [label="person_to_company_location_id_fkey"];
"person_to_company":rtcol2 -> "person":ltcol1 [label="person_to_company_person_id_fkey"];
"person_to_company":rtcol3 -> "company":ltcol1 [label="person_to_company_company_id_fkey"];
"recurring":rtcol1 -> "transactions":ltcol1 [label="recurring_id_fkey"];
"recurringemail":rtcol1 -> "recurring":ltcol1 [label="recurringemail_id_fkey"];
"recurringprint":rtcol1 -> "recurring":ltcol1 [label="recurringprint_id_fkey"];
"robot":rtcol2 -> "entity":ltcol1 [label="robot_entity_id_fkey"];
"session":rtcol5 -> "users":ltcol1 [label="session_users_id_fkey"];
"status":rtcol1 -> "transactions":ltcol1 [label="status_trans_id_fkey"];
"tax":rtcol1 -> "account":ltcol1 [label="tax_chart_id_fkey"];
"tax":rtcol8 -> "taxmodule":ltcol1 [label="tax_taxmodule_id_fkey"];
"tax_extended":rtcol3 -> "acc_trans":ltcol14 [label="tax_extended_entry_id_fkey"];
"taxcategory":rtcol3 -> "taxmodule":ltcol1 [label="taxcategory_taxmodule_id_fkey"];
"template":rtcol3 -> "language":ltcol1 [label="template_language_code_fkey"];
"transactions":rtcol3 -> "session":ltcol1 [label="transactions_locked_by_fkey"];
"transactions":rtcol5 -> "entity":ltcol1 [label="transactions_approved_by_fkey"];
"transactions":rtcol8 -> "workflow":ltcol1 [label="transactions_workflow_id_fkey"];
"user_preference":rtcol2 -> "users":ltcol1 [label="user_preference_user_id_fkey"];
"users":rtcol4 -> "entity":ltcol1 [label="users_entity_id_fkey"];
"voucher":rtcol1 -> "transactions":ltcol1 [label="voucher_trans_id_fkey"];
"voucher":rtcol2 -> "batch":ltcol1 [label="voucher_batch_id_fkey"];
"voucher":rtcol4 -> "batch_class":ltcol1 [label="voucher_batch_class_fkey"];
"warehouse_inventory":rtcol1 -> "entity_employee":ltcol1 [label="warehouse_inventory_entity_id_fkey"];
"workflow_context":rtcol1 -> "workflow":ltcol1 [label="workflow_context_workflow_id_fkey"];
"workflow_history":rtcol2 -> "workflow":ltcol1 [label="workflow_history_workflow_id_fkey"];
"yearend":rtcol1 -> "gl":ltcol1 [label="yearend_trans_id_fkey"];
}
