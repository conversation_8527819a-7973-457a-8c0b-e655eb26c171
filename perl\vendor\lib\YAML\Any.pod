=pod

=for comment
DO NOT EDIT. This Pod was generated by Swim v0.1.48.
See http://github.com/ingydotnet/swim-pm#readme

=encoding utf8

=head1 NAME

YAML::Any - Pick a YAML implementation and use it.

=head1 STATUS

WARNING: This module will soon be deprecated. The plan is that YAML.pm itself
will act like an I<Any> module.

=head1 SYNOPSIS

    use YAML::Any;
    $YAML::Indent = 3;
    my $yaml = Dump(@objects);

=head1 DESCRIPTION

There are several YAML implementations that support the Dump/Load API. This
module selects the best one available and uses it.

=head1 ORDER

Currently, YAML::Any will choose the first one of these YAML implementations
that is installed on your system:

=over

=item * YAML::XS

=item * YAML::Syck

=item * YAML::Old

=item * YAML

=item * YAML::Tiny

=back

=head1 OPTIONS

If you specify an option like:

    $YAML::Indent = 4;

And YAML::Any is using YAML::XS, it will use the proper variable:
$YAML::XS::Indent.

=head1 SUBROUTINES

Like all the YAML modules that YAML::Any uses, the following subroutines are
exported by default:

=over

=item * Dump

=item * Load

=back

and the following subroutines are exportable by request:

=over

=item * DumpFile

=item * LoadFile

=back

=head1 METHODS

YAML::Any provides the following class methods.

=over

=item C<< YAML::Any->order >>

This method returns a list of the current possible implementations that
YAML::Any will search for.

=item C<< YAML::Any->implementation >>

This method returns the implementation the YAML::Any will use. This result is
obtained by finding the first member of YAML::Any->order that is either
already loaded in C<%INC> or that can be loaded using C<require>. If no
implementation is found, an error will be thrown.

=back

=head1 EXAMPLES

=head2 DumpFile and LoadFile

Here is an example for C<DumpFile>:

    #!/usr/bin/perl

    use strict;
    use warnings;

    use YAML::Any qw(DumpFile);

    my $ds =
    {
        array => [5,6,100],
        string => "Hello",
    };

    DumpFile("hello.yml", $ds);

When run, this creates a file called C<hello.yml> in the current working
directory, with the following contents.

    ---
    array:
    - 5
    - 6
    - 100
    string: Hello

In turn, the following C<LoadFile> example, loads the contents from there and
accesses them:

    #!/usr/bin/perl

    use strict;
    use warnings;

    use YAML::Any qw(LoadFile);

    my ($ds) = LoadFile("hello.yml");

    print "String == '", $ds->{string}, "'\n";

Assuming C<hello.yml> exists, and is as created by the C<DumpFile> example,
it prints:

    $ perl load.pl
    String == 'Hello'
    $

=head1 AUTHOR

Ingy döt Net <<EMAIL>>

=head1 COPYRIGHT

Copyright 2001-2014. Ingy döt Net

This program is free software; you can redistribute it and/or modify it under
the same terms as Perl itself.

See L<http://www.perl.com/perl/misc/Artistic.html>

=cut
