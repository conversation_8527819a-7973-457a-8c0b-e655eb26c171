\setlength{\unitlength}{3947sp}%
%
\begingroup\makeatletter\ifx\SetFigFont\undefined%
\gdef\SetFigFont#1#2#3#4#5{%
  \reset@font\fontsize{#1}{#2pt}%
  \fontfamily{#3}\fontseries{#4}\fontshape{#5}%
  \selectfont}%
\fi\endgroup%
\begin{picture}(2674,4374)(5939,-4273)
\thinlines
{\color[rgb]{0,0,0}\put(7276,-736){\vector( 0,-1){1050}}
}%
{\color[rgb]{0,0,0}\put(7276,-2536){\vector( 0,-1){825}}
}%
{\color[rgb]{0,0,0}\put(5951,-736){\framebox(2650,825){}}
}%
{\color[rgb]{0,0,0}\put(5951,-2536){\framebox(2650,750){}}
}%
{\color[rgb]{0,0,0}\put(5951,-4261){\framebox(2650,900){}}
}%
\put(7201,-2161){\makebox(0,0)[b]{\smash{\SetFigFont{12}{14.4}{\rmdefault}{\mddefault}{\updefault}{\color[rgb]{0,0,0}Purchase Order}%
}}}
\put(7201,-3811){\makebox(0,0)[b]{\smash{\SetFigFont{12}{14.4}{\rmdefault}{\mddefault}{\updefault}{\color[rgb]{0,0,0}AP Invoice}%
}}}
\put(7276,-361){\makebox(0,0)[b]{\smash{\SetFigFont{12}{14.4}{\rmdefault}{\mddefault}{\updefault}{\color[rgb]{0,0,0}RFQ}%
}}}
\end{picture}
