
INSERT INTO location_class(id,class,authoritative)
     VALUES ('4','Physical',TRUE),
            ('5','Mailing',FALSE)
   ON CONFLICT ON CONSTRAINT location_class_pkey DO NOTHING;

SELECT SETVAL('location_class_id_seq',5);

INSERT INTO location_class_to_entity_class
       (location_class, entity_class)
SELECT lc.id, ec.id
  FROM entity_class ec
 cross
  join location_class lc
 WHERE ec.id <> 3 and lc.id < 4;

INSERT INTO location_class_to_entity_class (location_class, entity_class)
SELECT id, 3 from location_class lc where lc.id > 3;
