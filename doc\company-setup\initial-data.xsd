<?xml version="1.0" encoding="UTF-8" ?>

<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://ledgersmb.org/xml-schemas/initial-data"
           xmlns:t="http://ledgersmb.org/xml-schemas/initial-data"
           xmlns="http://ledgersmb.org/xml-schemas/initial-data"
           elementFormDefault="qualified">

  <xs:annotation>
    <xs:documentation>
    </xs:documentation>
  </xs:annotation>

  <xs:complexType name="contact-classes">
    <xs:sequence>
      <xs:element name="class" minOccurs="0" maxOccurs="unbounded">
        <xs:complexType>
          <xs:attribute name="name" type="xs:string" use="required" />
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="countries">
    <xs:sequence>
      <xs:element name="country" minOccurs="0" maxOccurs="unbounded">
        <xs:complexType>
          <xs:attribute name="code" type="xs:string" use="required" />
          <xs:attribute name="description" type="xs:string" />
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="languages">
    <xs:sequence>
      <xs:element name="language" minOccurs="0" maxOccurs="unbounded">
        <xs:complexType>
          <xs:attribute name="code" type="xs:string" use="required" />
          <xs:attribute name="description" type="xs:string" />
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="salutations">
    <xs:sequence>
      <xs:element name="salutation" minOccurs="0" maxOccurs="unbounded">
        <xs:complexType>
          <xs:attribute name="text" type="xs:string" use="required" />
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>

  <xs:element name="initial">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="contact-classes" minOccurs="0" maxOccurs="1" type="contact-classes" />
        <xs:element name="countries" minOccurs="0" maxOccurs="1" type="countries" />
        <xs:element name="languages" minOccurs="0" maxOccurs="1" type="languages" />
        <xs:element name="salutations" minOccurs="0" maxOccurs="1" type="salutations" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>

</xs:schema>
