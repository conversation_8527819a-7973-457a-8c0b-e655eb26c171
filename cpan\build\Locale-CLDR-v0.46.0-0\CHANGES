Revision history for Locale-CLDR
All dates are in ISO 8601 Format

0.0.1 2014-03-29
	- Released to CPAN

0.0.2 2014-04-12
	- This version now passes all tests on CPAN

0.0.3 201-04-12
	- This corrects a mistake in the upload of the previous version

0.0.4 2014-04-12
	- Fix bug causing some of the data files not to be correctly generated
	- Fixed count attributes for currency fall back
	- Fixed test scripts to match new corrected data
	
0.0.5 2014-04-19
	- Added Documentation for undocumented public functions

0.25.0 2014-04-26
	- Changed revision format to be API.CLDR Revision.Release
	- Added Unicode extensions to the locale codes for numbers
		and calendars
	- Added tests for the extension code
	- Added documentation for the extension code
	
0.25.1 2014-05-18
	- Updates and fixes to documentation
	- Change week data code so we no longer return a hash of all the data but
		now take a territory id and return the data specifically for that territory
	- Fixed up week data and calendar preferences to use territory containment 
	- Added currency data to the number formatting code
	- Added tests for the above changes
	- Allow for use with Perl 5.10 and above
	- Fixed tests to handle Perl 5.10 and above
	
0.25.2 2014-06-02
	- Added Pluralisation rules
	- Added warning when falling back to the likely territory when getting a default currency.
	- Added plural range code
    - Added tests for above

0.25.3 2014-06-12
	- Rule Based Number Formatting

0.25.4 2014-06-20
	- Fixes to bugs found in the rule base number formatting

0.26.0
	- Broken release

0.26.1
	- Broken release

0.26.2 2014-11-29
	- CLDR version 26 release.
	- Break data into bundles so you don't have to download all the data
	- Fix some spelling mistakes in the documentation
	- Fix bugs in the Rule based number formatting

0.26.3
	- Fixes for the En Distribution

0.26.4 2014-12-21
	- Test fixes in the BG Distribution
	
0.26.5 2014-12-26
	- Fixed currency symbols with a . in them being confused with the decimal separator.
	- Fixed overrides and currency tests for BG language

0.26.6 2014-12-29
	- Fixed tests in the base distribution to use en_US as the locale
	
0.26.7 2014-12-30
	- Added tests for French distribution
	- Fixed bug in calendars not falling back to Gregorian
	- Given all currency tests a locale with a region identifier
	- Added tests for the Breton language

0.26.8 2015-01-09
	- Added tests for all units in base distribution
	- Added compound units for different lengths
	- Added precomputed divisors for compound units
	- Fixed all_units method to return all the unit identifiers
	- Added unit_name method to get the localised name of a unit
	- Added time separator
	- Added minimum grouping digits
	- Added plural forms for rule based number formatting

0.26.9 2015-01-20
	- Added tests for Catalan.
	- Fixed a bug in Paper and Measurement systems that would crash a script using the module
		if no territory was given to the locale.

0.27.0 2015-04-07
	first draft with the CLDR v27 data
	Added month patterns
	Added cyclic names
	Bumped year in copyright message
	Fixed package inheritance to use the parent package data in the supplemental data
	Fixed tests to handle new data set

0.27.1 2015-05-03
	Added Welsh as a Language Pack

0.27.2 2015-07-16
	Added basic collation
	Added dummy Locale::CLDR::Transformations package to get CPAN to index the transformations Pack

0.27.3 2015-07-18
	Added missing required modules
	Added dummy code for building Bundles
	Removed code to generate a Makefile.PL
	Fixed incorrect name for data pack distributions
	
0.28.0 2015-10-08
	Removed generated data from the header of each file as this has been removed from the CLDR data
	Updated valid languages etc to use the new file format
	Added alias to unit names
	Added coordinate unit types
	Updated Unit tests

0.28.1 2015-10-25
	Change all references of territories to regions to match the CLDR documentation. This includes method names.
	Change minimum Perl version to 5.10.1 as tests for 5.10.0 won't run
	Added currency_format() method to get the currency format for the locale
	Added default currency format and locale id override
	Added tests for currency formats
	Added format_currency() to the number formatter to supply all the defaults for the locale to format_number()
	Fix Number Formatter to corectly handle the - sign on negative numbers
	Fix Number Formatter to corectly handle the + sign on posative numbers
	Fix Number formatter to corectly split the number format into negative and positive
	Removed the generation of negative number formats in the Locale data when the negative format is 
		simpley -<posative format>.
	Updated tests to reflect the above change
	Cleaned up variable names in test files

0.28.2 2015-11-12
	Some SEO work on the build scripts
	Fixed incorrectly named Unicode block in collation code
	Added default currency override
	Added tests for default currency override
	Fixed not numeric in == warning for Plural Rules
	Added Danish language pack

0.28.3 2016-02-10
	Added first day of week override
	Added tests for above
	Added maketext emulation and tests
	Pushed all language packs

0.29.0 2016-04-29
	Now using CLDR version 29
	Fixed bug in version line of base Transliteration Package
	Fixed text in meta description of Transliteration Package
	Moved from using Moose to Moo
	Removed Deprecated regions
	Added Region bundles and distributions
	Added some Rusian tests
	Fixed generation of day period rules
	Transliteration now requires script identifiers rather than script names

0.32.0 2018-04-14
    Now using CLDR version 32
    Updated tests to work with this version
    Added empty Unicode properties to older versions of perl can run with the current data set

0.34.0 2018-12-17
    Now using CLDR version 34
    Fixed warnings when calling 'get_node' with no value
    Fixed transliteration and added test
    Tidied documentation
    Restrict 'contained' regions to geographical regions (ignore political regions)

0.34.1 2021-04-11
    Fixed for-cash rounding problem
    Fix warning in plural rules where fraction is used
    Fix typo in POD documentation

0.34.2 2023-10-13
    Swapped bignum to bigfloat as bignum was causing problems with other modules
    
0.34.3 2023-11-05
    Fixes to Bundles to allow languages and regions to co-exist

0.34.4 2023-12-02
    Fixed recursion with likely_subtags

0.40.0 2024-01-27
    Updated to use version 40 of CLDR
    Added method to return all the installed locales as a prerequisite of locating the best match between installed locales and a requested locale

0.46.0 2025-01-17
    Updated to use version 45 of CLDR
    
    *WARNING* The minimum supported version now supported is v5.12.0 This is due to external Perl libries neading 5.12.0 as a minimum version
    
    *WARNING* There are several language packs that have as parents locales in other language packs. You wont notice
    this if you are installing from CPAN with one of the CPAN clients but if you are installing manually you will have
    to resolve these dependancies
    
    *WARNING* The locale data no longer uses Any as a place holder for missing script data. Where this was used in earlier
    versions the default script as defined by the CLDR data is used. This may have an inpact on the nameing of locales and
    if you use the locale data without using Locale::CLDR
    
    *WARNING* The Unicode cosortium has changed some of the identifieres used by units. If you are no longer getting
    values back for unit data check the id.
    
    Thanks to changes in the CLDR data, split words is now correctly splitting the trailing whitespace off words.
    likley_subtag() will now return the likley language rather than 'und'.
    
    If you ask for an accountcy number format in a locale that doesn't have one the code will return the standard format
    
    Corrected spellings of some comments and variables
    
    Added code to correctly parse an extension string added to a locale using the -u- mechanism
    
    Fixed code to allow an extension to have multiple values
    
    Added code so that the parent of a module can be altered on the fly to allow for segmentations to have a different parent than the rest of the module
    
    Added Chinease segmentation test to test the above