#ifndef _GDFONTS_H_
#define _GDFONTS_H_ 1

#ifdef __cplusplus
extern "C"
{
#endif

/*
	This is a header file for gd font, generated using
	bdftogd version 0.5 by <PERSON>, ad<PERSON><PERSON>@fi.muni.cz
	from bdf font
	-misc-fixed-medium-r-semicondensed-sans-12-116-75-75-c-60-iso8859-2
	at Thu Jan  8 14:13:20 1998.
	No copyright info was found in the original bdf.
 */

#include "gd.h"

extern BGD_EXPORT_DATA_PROT gdFontPtr gdFontSmall;
BGD_DECLARE(gdFontPtr) gdFontGetSmall(void);

#ifdef __cplusplus
}
#endif

#endif
