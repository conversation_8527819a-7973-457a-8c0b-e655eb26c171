#
# This is an example VCL file for Varnish.
#
# It does not do anything by default, delegating control to the
# builtin VCL. The builtin VCL is called when there is no explicit
# return statement.
#
# See the VCL chapters in the Users Guide at https://www.varnish-cache.org/docs/
# and https://www.varnish-cache.org/trac/wiki/VCLExamples for more examples.

# Also see https://docs.varnish-software.com/varnish-cache-plus/features/client-ssl/
# if you need SSL/TLS, only Varnish Enterprise has it directly available

# Marker to tell the VCL compiler that this VCL has been adapted to the
# new 4.0 format.
vcl 4.0;

# Please replace the following parameters:
#
#   * STARMAN_HOST

# Default backend definition. Set this to point to your content server.
backend default {
    .host = "STARMAN_HOST";
    .port = "5762";
}

sub vcl_recv {
    # Happens before we check if we have this in cache already.
    #
    # Typically you clean up the request here, removing cookies you don't need,
    # rewriting the request, etc.

    # Always cache the following file types for all users. This list of extensions
    # appears twice, once here and again in vcl_fetch so make sure you edit both
    # and keep them equal.

    set req.backend_hint = default;
    if (req.url ~ "(?i)\.(pdf|asc|dat|txt|doc|xls|ppt|tgz|csv|png|gif|jpeg|jpg|ico|swf|css|js)(\?.*)?$") {
        unset req.http.Cookie;
        return (hash);
    }
}

sub vcl_backend_response {
    # Happens after we have read the response headers from the backend.
    #
    # Here you clean the response headers, removing silly Set-Cookie headers
    # and other mistakes your backend does.

    if (bereq.url ~ "\.(jp(e?)g|png|gif|ico|swf|js|css|gz|rar|txt|bzip|pdf)$") {
        unset beresp.http.set-cookie;
    }
}

sub vcl_deliver {
    # Happens when we have all the pieces we need, and are about to send the
    # response to the client.
    #
    # You can do accounting or modifying the final object here.
}
