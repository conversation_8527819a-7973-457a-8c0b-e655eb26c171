{"abstract": "fork test", "author": ["<PERSON><PERSON><PERSON> <tokuhirom  slkjfd gmail.com>"], "dynamic_config": 0, "generated_by": "Minilla/v3.0.1", "license": ["perl_5"], "meta-spec": {"url": "http://search.cpan.org/perldoc?CPAN::Meta::Spec", "version": "2"}, "name": "Test-SharedFork", "no_index": {"directory": ["t", "xt", "inc", "share", "eg", "examples", "author", "builder"]}, "prereqs": {"configure": {"requires": {"ExtUtils::MakeMaker": "6.64"}}, "develop": {"requires": {"Test::CPAN::Meta": "0", "Test::MinimumVersion::Fast": "0.04", "Test::PAUSE::Permissions": "0.04", "Test::Pod": "1.41", "Test::Spellunker": "v0.2.7"}}, "runtime": {"requires": {"File::Temp": "0", "Test::Builder": "0.32", "Test::Builder::Module": "0", "Test::More": "0.88", "perl": "5.008_001"}}, "test": {"requires": {"App::Prove": "0", "Test::Builder::Tester": "0", "Test::Requires": "0", "Time::HiRes": "0"}}}, "provides": {"Test::SharedFork": {"file": "lib/Test/SharedFork.pm", "version": "0.35"}, "Test::SharedFork::Array": {"file": "lib/Test/SharedFork/Array.pm"}, "Test::SharedFork::Scalar": {"file": "lib/Test/SharedFork/Scalar.pm"}, "Test::SharedFork::Store": {"file": "lib/Test/SharedFork/Store.pm"}}, "release_status": "stable", "resources": {"bugtracker": {"web": "https://github.com/tokuhirom/Test-SharedFork/issues"}, "homepage": "https://github.com/tokuhirom/Test-SharedFork", "repository": {"url": "git://github.com/tokuhirom/Test-SharedFork.git", "web": "https://github.com/tokuhirom/Test-SharedFork"}}, "version": "0.35", "x_contributors": ["lestrrat <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Perlover <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "rwhitworth <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"]}