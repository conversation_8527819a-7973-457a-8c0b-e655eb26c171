{"abstract": "testing TCP program", "author": ["<PERSON><PERSON><PERSON> <<EMAIL>>"], "dynamic_config": 0, "generated_by": "Minilla/v3.1.4", "license": ["perl_5"], "meta-spec": {"url": "http://search.cpan.org/perldoc?CPAN::Meta::Spec", "version": "2"}, "name": "Test-TCP", "no_index": {"directory": ["t", "xt", "inc", "share", "eg", "examples", "author", "builder"]}, "prereqs": {"configure": {"requires": {"ExtUtils::MakeMaker": "6.64"}}, "develop": {"requires": {"File::Which": "0", "Perl::Critic": "1.105", "Test::CPAN::Meta": "0", "Test::MinimumVersion::Fast": "0.04", "Test::PAUSE::Permissions": "0.04", "Test::Perl::Critic": "1.02", "Test::Pod": "1.41", "Test::Spellunker": "v0.2.7"}}, "runtime": {"requires": {"IO::Socket::INET": "0", "IO::Socket::IP": "0", "Test::More": "0", "Test::SharedFork": "0.29", "Time::HiRes": "0", "perl": "5.008001"}}, "test": {"requires": {"File::Temp": "0", "Socket": "0", "Test::More": "0.98"}}}, "provides": {"Net::EmptyPort": {"file": "lib/Net/EmptyPort.pm"}, "Test::TCP": {"file": "lib/Test/TCP.pm", "version": "2.22"}, "Test::TCP::CheckPort": {"file": "lib/Test/TCP/CheckPort.pm"}}, "release_status": "stable", "resources": {"bugtracker": {"web": "https://github.com/tokuhirom/Test-TCP/issues"}, "homepage": "https://github.com/tokuhirom/Test-TCP", "repository": {"url": "git://github.com/tokuhirom/Test-TCP.git", "web": "https://github.com/tokuhirom/Test-TCP"}}, "version": "2.22", "x_contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <b.jak<PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <kazu<PERSON><PERSON>@gmail.com>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Syohei YOSHIDA <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "gfx <<EMAIL>>", "lestrrat <<EMAIL>>", "mattn <mattn@d0d07461-0603-4401-acd4-de1884942a52>", "openstrike <********************>", "richard.leach <<EMAIL>>", "yappo <yappo@d0d07461-0603-4401-acd4-de1884942a52>", "奥 一穂 <ka<PERSON><PERSON>@EM114-48-138-123.pool.e-mobile.ne.jp>"], "x_static_install": 1}