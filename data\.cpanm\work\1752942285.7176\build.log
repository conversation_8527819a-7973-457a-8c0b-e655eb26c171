cpanm (App::cpanminus) 1.7048 on perl 5.040002 built for MSWin32-x64-multi-thread
Work directory is E:\mohammi\LedgerSMB\data/.cpanm/work/1752942285.7176
You have LWP 6.78
Falling back to Archive::Tar 3.04
Searching DateTime::Format::Duration::ISO8601 () on cpanmetadb ...
--> Working on DateTime::Format::Duration::ISO8601
Fetching http://www.cpan.org/authors/id/P/PE/PERLANCAR/DateTime-Format-Duration-ISO8601-0.008.tar.gz
-> OK
Unpacking DateTime-Format-Duration-ISO8601-0.008.tar.gz
Entering DateTime-Format-Duration-ISO8601-0.008
Checking configure dependencies from META.json
Checking if you have ExtUtils::MakeMaker 6.58 ... Yes (7.74)
Configuring DateTime-Format-Duration-ISO8601-0.008
Running Makefile.PL
Checking if your kit is complete...
Looks good
Generating a gmake-style Makefile
Writing Makefile for DateTime::Format::Duration::ISO8601
Writing MYMETA.yml and MYMETA.json
-> OK
Checking dependencies from MYMETA.json ...
Checking if you have ExtUtils::MakeMaker 0 ... Yes (7.74)
Checking if you have warnings 0 ... Yes (1.70)
Checking if you have DateTime::Duration 0 ... Yes (1.66)
Checking if you have strict 0 ... Yes (1.13)
-> FAIL Can't configure the distribution. You probably need to have 'make'. See E:\mohammi\LedgerSMB\data\.cpanm\work\1752942285.7176\build.log for details.
