NAME

    Filesys::Notify::Simple - Simple and dumb file system watcher

SYNOPSI<PERSON>

      use Filesys::Notify::Simple;
    
      my $watcher = Filesys::Notify::Simple->new([ "." ]);
      $watcher->wait(sub {
          for my $event (@_) {
              $event->{path} # full path of the file updated
          }
      });

DESCRIPTION

    Filesys::Notify::Simple is a simple but unified interface to get
    notifications of changes to a given filesystem path. It utilizes
    inotify2 on Linux, fsevents on OS X, kqueue on FreeBSD and
    FindFirstChangeNotification on Windows if they're installed, with a
    fallback to the full directory scan if they're not available.

    There are some limitations in this module. If you don't like it, use
    File::ChangeNotify.

      * There is no file name based filter. Do it in your own code.

      * You can not get types of events (created, updated, deleted).

      * Currently wait method blocks.

    In return, this module doesn't depend on any non-core modules. Platform
    specific optimizations with Linux::Inotify2, Mac::FSEvents,
    Filesys::Notify::KQueue and Win32::ChangeNotify are truely optional.

    NOTE: Using Win32::ChangeNotify may put additional limitations.

      * Win32::ChangeNotify uses FindFirstChangeNotificationA so that
      Unicode characters can not be handled. On cygwin (1.7 or later),
      Unicode characters should be able to be handled when
      Win32::ChangeNotify is not used.

      * If more than 64 directories are included under the specified paths,
      an error occurrs.

AUTHOR

    Tatsuhiko Miyagawa <<EMAIL>>

LICENSE

    This library is free software; you can redistribute it and/or modify it
    under the same terms as Perl itself.

SEE ALSO

    File::ChangeNotify Mac::FSEvents Linux::Inotify2
    Filesys::Notify::KQueue Win32::ChangeNotify

