[
  {
    'body' => 'Ratione accusamus aspernatur aliquam',
    'header' => [
      'Content-Disposition: form-data; name="text1"'
    ]
  },
  {
    'body' => '',
    'header' => [
      'Content-Disposition: form-data; name="text2"'
    ]
  },
  {
    'body' => 'A',
    'header' => [
      'Content-Disposition: form-data; name="select"'
    ]
  },
  {
    'body' => 'B',
    'header' => [
      'Content-Disposition: form-data; name="select"'
    ]
  },
  {
    'body' => 'Voluptatem cumque voluptate sit recusandae at. Et quas facere rerum unde esse. Sit est et voluptatem. Vel temporibus velit neque odio non.

Molestias rerum ut sapiente facere repellendus illo. Eum nulla quis aut. Quidem voluptas vitae ipsam officia voluptatibus eveniet. Aspernatur cupiditate ratione aliquam quidem corrupti. Eos sunt rerum non optio culpa.',
    'header' => [
      'Content-Disposition: form-data; name="textarea"'
    ]
  },
  {
    'body' => '#!/usr/bin/perl

use strict;
use warnings;

print "Hello World :)\\n";

',
    'header' => [
      'Content-Disposition: form-data; name="upload"; filename="hello.pl"',
      'Content-Type: application/octet-stream'
    ]
  },
  {
    'body' => '#!/usr/bin/perl

use strict;
use warnings;

print "Hello World :)\\n";

',
    'header' => [
      'Content-Disposition: form-data; name="upload"; filename="hello.pl"',
      'Content-Type: application/octet-stream'
    ]
  },
  {
    'body' => '',
    'header' => [
      'Content-Disposition: form-data; name="upload1"; filename=""'
    ]
  },
  {
    'body' => '#!/usr/bin/perl

use strict;
use warnings;

print "Hello World :)\\n";

',
    'header' => [
      'Content-Disposition: form-data; name="upload2"; filename="hello.pl"',
      'Content-Type: application/octet-stream'
    ]
  },
  {
    'body' => '',
    'header' => [
      'Content-Disposition: form-data; name="upload3"; filename="blank.pl"',
      'Content-Type: application/octet-stream'
    ]
  },
  {
    'body' => '',
    'header' => [
      'Content-Disposition: form-data; name="upload4"; filename="0"'
    ]
  }
]
