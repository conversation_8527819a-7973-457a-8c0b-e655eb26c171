---
abstract: 'A Module to create locale objects with localisation data from the CLDR'
author:
  - '<PERSON> <<EMAIL>>'
build_requires:
  File::Spec: '0'
  Test::Exception: '0'
  Test::More: '0.98'
  ok: '0'
configure_requires:
  Module::Build: '0.40'
dynamic_config: 0
generated_by: 'Module::Build version 0.4234, CPAN::Meta::Converter version 2.150010'
keywords:
  - locale
  - CLDR
license: perl
meta-spec:
  url: http://module-build.sourceforge.net/META-spec-v1.4.html
  version: '1.4'
name: Locale-CLDR
provides:
  Locale::CLDR:
    file: lib/Locale/CLDR.pm
    version: v0.46.0
  Locale::CLDR::CalendarPreferences:
    file: lib/Locale/CLDR/CalendarPreferences.pm
    version: v0.46.0
  Locale::CLDR::Collator:
    file: lib/Locale/CLDR/Collator.pm
    version: v0.46.0
  Locale::CLDR::CollatorBase:
    file: lib/Locale/CLDR/CollatorBase.pm
    version: v0.46.0
  Locale::CLDR::Currencies:
    file: lib/Locale/CLDR/Currencies.pm
    version: v0.46.0
  Locale::CLDR::EraBoundries:
    file: lib/Locale/CLDR/EraBoundries.pm
    version: v0.46.0
  Locale::CLDR::LanguageMatching:
    file: lib/Locale/CLDR/LanguageMatching.pm
    version: v0.46.0
  Locale::CLDR::LikelySubtags:
    file: lib/Locale/CLDR/LikelySubtags.pm
    version: v0.46.0
  Locale::CLDR::Locales::En:
    file: lib/Locale/CLDR/Locales/En.pm
    version: v0.46.0
  Locale::CLDR::Locales::En::Latn:
    file: lib/Locale/CLDR/Locales/En/Latn.pm
    version: v0.46.0
  Locale::CLDR::Locales::En::Latn::Us:
    file: lib/Locale/CLDR/Locales/En/Latn/Us.pm
    version: v0.46.0
  Locale::CLDR::Locales::Root:
    file: lib/Locale/CLDR/Locales/Root.pm
    version: v0.46.0
  Locale::CLDR::MeasurementSystem:
    file: lib/Locale/CLDR/MeasurementSystem.pm
    version: v0.46.0
  Locale::CLDR::NumberFormatter:
    file: lib/Locale/CLDR/NumberFormatter.pm
    version: v0.46.0
  Locale::CLDR::NumberingSystems:
    file: lib/Locale/CLDR/NumberingSystems.pm
    version: v0.46.0
  Locale::CLDR::Plurals:
    file: lib/Locale/CLDR/Plurals.pm
    version: v0.46.0
  Locale::CLDR::RegionContainment:
    file: lib/Locale/CLDR/RegionContainment.pm
    version: v0.46.0
  Locale::CLDR::ValidCodes:
    file: lib/Locale/CLDR/ValidCodes.pm
    version: v0.46.0
  Locale::CLDR::WeekData:
    file: lib/Locale/CLDR/WeekData.pm
    version: v0.46.0
requires:
  Class::Load: '0'
  DateTime: '0.72'
  DateTime::Locale: '0'
  List::Util: '1.45'
  Moo: '2'
  MooX::ClassAttribute: '0.011'
  Type::Tiny: '0'
  Unicode::Regex::Set: '0'
  bigfloat: '0'
  namespace::autoclean: '0.16'
  perl: v5.12.0
  version: '0.95'
resources:
  bugtracker: https://github.com/ThePilgrim/perlcldr/issues
  homepage: https://github.com/ThePilgrim/perlcldr
  repository: https://github.com/ThePilgrim/perlcldr.git
version: v0.46.0
x_serialization_backend: 'CPAN::Meta::YAML version 0.020'
