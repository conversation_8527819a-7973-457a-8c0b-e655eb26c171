---
abstract: 'declare class attributes Moose-style... but without <PERSON>'
author:
  - '<PERSON> (TOBYINK) <<EMAIL>>'
build_requires: {}
configure_requires:
  ExtUtils::MakeMaker: '6.17'
conflicts:
  Moo: '== 1.001000'
  MooseX::ClassAttribute: '<= 0.26'
dynamic_config: 0
generated_by: 'Dist::Inkt::Profile::TOBYINK version 0.023, CPAN::Meta::Converter version 2.142690, CPAN::Meta::Converter version 2.150010'
keywords: []
license: perl
meta-spec:
  url: http://module-build.sourceforge.net/META-spec-v1.4.html
  version: '1.4'
name: MooX-ClassAttribute
no_index:
  directory:
    - eg
    - examples
    - inc
    - t
    - xt
provides:
  Method::Generate::ClassAccessor:
    file: lib/Method/Generate/ClassAccessor.pm
    version: '0.011'
  MooX::CaptainHook:
    file: lib/MooX/CaptainHook.pm
    version: '0.011'
  MooX::CaptainHook::HandleMoose::Hack:
    file: lib/MooX/CaptainHook.pm
    version: '0.011'
  MooX::CaptainHook::OnApplication:
    file: lib/MooX/CaptainHook.pm
    version: '0.011'
  MooX::CaptainHook::OnApplication::Moose:
    file: lib/MooX/CaptainHook.pm
    version: '0.011'
  MooX::CaptainHook::OnInflation:
    file: lib/MooX/CaptainHook.pm
    version: '0.011'
  MooX::ClassAttribute:
    file: lib/MooX/ClassAttribute.pm
    version: '0.011'
  MooX::ClassAttribute::HandleMoose:
    file: lib/MooX/ClassAttribute/HandleMoose.pm
    version: '0.011'
requires:
  Exporter::Shiny: '0'
  Moo: '1.000000'
  Role::Tiny: '1.000000'
  perl: '5.008000'
resources:
  X_identifier: http://purl.org/NET/cpan-uri/dist/MooX-ClassAttribute/project
  bugtracker: http://rt.cpan.org/Dist/Display.html?Queue=MooX-ClassAttribute
  homepage: https://metacpan.org/release/MooX-ClassAttribute
  license: http://dev.perl.org/licenses/
  repository: git://github.com/tobyink/p5-moox-classattribute.git
version: '0.011'
x_breaks:
  Moo: '== 1.001000'
  MooseX::ClassAttribute: '<= 0.26'
x_contributors:
  - 'Dinis Rebolo (DREBOLO) <<EMAIL>>'
x_serialization_backend: 'CPAN::Meta::YAML version 0.020'
