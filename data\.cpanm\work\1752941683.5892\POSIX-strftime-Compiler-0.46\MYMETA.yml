---
abstract: 'GNU C library compatible strftime for loggers and servers'
author:
  - '<PERSON><PERSON><PERSON> <<EMAIL>>'
build_requires:
  Test::More: '0.98'
configure_requires:
  Module::Build::Tiny: '0.035'
dynamic_config: 0
generated_by: 'Minilla/v3.1.23, CPAN::Meta::Converter version 2.150010'
license: perl
meta-spec:
  url: http://module-build.sourceforge.net/META-spec-v1.4.html
  version: '1.4'
name: POSIX-strftime-Compiler
no_index:
  directory:
    - t
    - xt
    - inc
    - share
    - eg
    - examples
    - author
    - builder
provides:
  POSIX::strftime::Compiler:
    file: lib/POSIX/strftime/Compiler.pm
    version: '0.46'
requires:
  Carp: '0'
  Exporter: '0'
  POSIX: '0'
  Time::Local: '0'
  perl: '5.008001'
resources:
  bugtracker: https://github.com/kazeburo/POSIX-strftime-Compiler/issues
  homepage: https://github.com/kazeburo/POSIX-strftime-Compiler
  repository: https://github.com/kazeburo/POSIX-strftime-Compiler.git
version: '0.46'
x_contributors:
  - 'Aristotle Pagaltzis <<EMAIL>>'
  - 'Piotr Roszatycki <<EMAIL>>'
  - 'Slaven Rezic <<EMAIL>>'
  - 'Tom Bloor <<EMAIL>>'
x_serialization_backend: 'CPAN::Meta::YAML version 0.020'
x_static_install: 1
