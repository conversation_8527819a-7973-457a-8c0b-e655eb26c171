name: No Response

# Both `issue_comment` and `scheduled` event types are required for this Action
# to work properly.
on:
  issue_comment:
    types: [created]
  schedule:
    # Schedule for five minutes after the hour, every hour
    - cron: '5 3 * * *'

jobs:
  noResponse:
    if: github.repository_owner == 'ledgersmb'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          days-before-issue-stale: -1
          days-before-pr-stale: -1
          stale-issue-label: waiting-for-user
          # Number of days of inactivity before an Issue is closed for lack of response
          days-before-close: 90
          # Label requiring a response
          close-issue-message: >
            Closing: more than 90 days without user response. Feel free to reopen with your comments.
