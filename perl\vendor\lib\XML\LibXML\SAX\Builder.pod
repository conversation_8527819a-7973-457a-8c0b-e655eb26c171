=head1 NAME

XML::LibXML::SAX::Builder - Building DOM trees from SAX events.

=head1 SYNOPSIS



  use XML::LibXML::SAX::Builder;
  my $builder = XML::LibXML::SAX::Builder->new();

  my $gen = XML::Generator::DBI->new(Handler => $builder, dbh => $dbh);
  $gen->execute("SELECT * FROM Users");

  my $doc = $builder->result();


=head1 DESCRIPTION

This is a SAX handler that generates a DOM tree from SAX events. Usage is as
above. Input is accepted from any SAX1 or SAX2 event generator.

Building DOM trees from SAX events is quite easy with
XML::LibXML::SAX::Builder. The class is designed as a SAX2 final handler not as
a filter!

Since SAX is strictly stream oriented, you should not expect anything to return
from a generator. Instead you have to ask the builder instance directly to get
the document built. XML::LibXML::SAX::Builder's result() function holds the
document generated from the last SAX stream.

=head1 AUTHORS

<PERSON>,
<PERSON>,
<PERSON><PERSON>


=head1 VERSION

2.0210

=head1 COPYRIGHT

2001-2007, AxKit.com Ltd.

2002-2006, <PERSON>.

2006-2009, <PERSON><PERSON>.

=cut


=head1 LICENSE

This program is free software; you can redistribute it and/or modify it under
the same terms as Perl itself.

