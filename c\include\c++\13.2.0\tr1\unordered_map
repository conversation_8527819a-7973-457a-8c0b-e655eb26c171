// TR1 unordered_map -*- C++ -*-

// Copyright (C) 2005-2023 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file tr1/unordered_map
 *  This is a TR1 C++ Library header. 
 */

#ifndef _GLIBCXX_TR1_UNORDERED_MAP
#define _GLIBCXX_TR1_UNORDERED_MAP 1

#pragma GCC system_header

#include <bits/requires_hosted.h> // TR1

#include <utility>
#include <bits/stl_algobase.h>
#include <bits/allocator.h>
#include <bits/stl_function.h> // equal_to, _Identity, _Select1st
#include <bits/stringfwd.h>
#include <tr1/type_traits>
#include <tr1/functional_hash.h>
#include <tr1/hashtable.h>
#include <tr1/unordered_map.h>

#endif // _GLIBCXX_TR1_UNORDERED_MAP
