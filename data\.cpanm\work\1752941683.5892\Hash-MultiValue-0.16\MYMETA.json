{"abstract": "Store multiple values per key", "author": ["<PERSON><PERSON><PERSON> <<EMAIL>>"], "dynamic_config": 0, "generated_by": "Dist::Zilla version 5.030, Dist::Milla version v1.0.14, CPAN::Meta::Converter version 2.143240, CPAN::Meta::Converter version 2.150010", "license": ["perl_5"], "meta-spec": {"url": "http://search.cpan.org/perldoc?CPAN::Meta::Spec", "version": 2}, "name": "Hash-MultiValue", "no_index": {"directory": ["t", "xt", "inc", "share", "eg", "examples"]}, "prereqs": {"build": {"requires": {"ExtUtils::MakeMaker": "0"}}, "configure": {"requires": {"ExtUtils::MakeMaker": "0"}}, "develop": {"requires": {"Dist::Milla": "v1.0.14", "Test::Pod": "1.41"}}, "runtime": {"requires": {"perl": "5.008001"}}, "test": {"requires": {"Test::More": "0"}}}, "release_status": "stable", "resources": {"bugtracker": {"web": "https://github.com/miyagawa/Hash-MultiValue/issues"}, "homepage": "https://github.com/miyagawa/Hash-MultiValue", "repository": {"type": "git", "url": "https://github.com/miyagawa/Hash-MultiValue.git", "web": "https://github.com/miyagawa/Hash-MultiValue"}}, "version": "0.16", "x_contributors": ["<PERSON> <<EMAIL>>", "chansen <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "x_serialization_backend": "JSON::PP version 4.16"}