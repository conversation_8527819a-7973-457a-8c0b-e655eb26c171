{"extends": ["config:recommended"], "baseBranches": ["master", "1.12", "1.11"], "cpanfile": {"enabled": false}, "packageRules": [{"matchManagers": ["github-actions"]}, {"matchUpdateTypes": ["minor", "patch"], "groupName": "all non-major dependencies", "groupSlug": "all-minor-patch", "automerge": true, "matchPackageNames": ["*"]}, {"allowedVersions": "<4", "matchPackageNames": ["/^selenium//"]}, {"allowedVersions": "<0.8", "matchPackageNames": ["/^@intlify/unplugin-vue-i18n/"]}, {"matchUpdateTypes": ["major"], "automerge": false, "groupName": "all stylelint related dependencies", "groupSlug": "stylelint-major", "matchPackageNames": ["/stylelint/"]}, {"matchBaseBranches": ["1.11", "1.10"], "enabled": false, "matchPackageNames": ["/^eslint/"]}, {"matchUpdateTypes": ["major"], "automerge": false, "matchPackageNames": ["*"]}], "timezone": "Etc/Greenwich", "schedule": ["after 11pm on friday", "before 7am on saturday"], "vulnerabilityAlerts": {"schedule": null}}