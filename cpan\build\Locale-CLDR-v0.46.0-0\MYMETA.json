{"abstract": "A Module to create locale objects with localisation data from the CLDR", "author": ["<PERSON> <<EMAIL>>"], "dynamic_config": 0, "generated_by": "Module::Build version 0.4234", "keywords": ["locale", "CLDR"], "license": ["perl_5"], "meta-spec": {"url": "http://search.cpan.org/perldoc?CPAN::Meta::Spec", "version": 2}, "name": "Locale-CLDR", "prereqs": {"build": {"requires": {"File::Spec": 0, "Test::Exception": 0, "Test::More": "0.98", "ok": 0}}, "configure": {"requires": {"Module::Build": "0.40"}}, "runtime": {"requires": {"Class::Load": 0, "DateTime": "0.72", "DateTime::Locale": 0, "List::Util": "1.45", "Moo": "2", "MooX::ClassAttribute": "0.011", "Type::Tiny": 0, "Unicode::Regex::Set": 0, "bigfloat": 0, "namespace::autoclean": "0.16", "perl": "v5.12.0", "version": "0.95"}}}, "provides": {"Locale::CLDR": {"file": "lib/Locale/CLDR.pm", "version": "v0.46.0"}, "Locale::CLDR::CalendarPreferences": {"file": "lib/Locale/CLDR/CalendarPreferences.pm", "version": "v0.46.0"}, "Locale::CLDR::Collator": {"file": "lib/Locale/CLDR/Collator.pm", "version": "v0.46.0"}, "Locale::CLDR::CollatorBase": {"file": "lib/Locale/CLDR/CollatorBase.pm", "version": "v0.46.0"}, "Locale::CLDR::Currencies": {"file": "lib/Locale/CLDR/Currencies.pm", "version": "v0.46.0"}, "Locale::CLDR::EraBoundries": {"file": "lib/Locale/CLDR/EraBoundries.pm", "version": "v0.46.0"}, "Locale::CLDR::LanguageMatching": {"file": "lib/Locale/CLDR/LanguageMatching.pm", "version": "v0.46.0"}, "Locale::CLDR::LikelySubtags": {"file": "lib/Locale/CLDR/LikelySubtags.pm", "version": "v0.46.0"}, "Locale::CLDR::Locales::En": {"file": "lib/Locale/CLDR/Locales/En.pm", "version": "v0.46.0"}, "Locale::CLDR::Locales::En::Latn": {"file": "lib/Locale/CLDR/Locales/En/Latn.pm", "version": "v0.46.0"}, "Locale::CLDR::Locales::En::Latn::Us": {"file": "lib/Locale/CLDR/Locales/En/Latn/Us.pm", "version": "v0.46.0"}, "Locale::CLDR::Locales::Root": {"file": "lib/Locale/CLDR/Locales/Root.pm", "version": "v0.46.0"}, "Locale::CLDR::MeasurementSystem": {"file": "lib/Locale/CLDR/MeasurementSystem.pm", "version": "v0.46.0"}, "Locale::CLDR::NumberFormatter": {"file": "lib/Locale/CLDR/NumberFormatter.pm", "version": "v0.46.0"}, "Locale::CLDR::NumberingSystems": {"file": "lib/Locale/CLDR/NumberingSystems.pm", "version": "v0.46.0"}, "Locale::CLDR::Plurals": {"file": "lib/Locale/CLDR/Plurals.pm", "version": "v0.46.0"}, "Locale::CLDR::RegionContainment": {"file": "lib/Locale/CLDR/RegionContainment.pm", "version": "v0.46.0"}, "Locale::CLDR::ValidCodes": {"file": "lib/Locale/CLDR/ValidCodes.pm", "version": "v0.46.0"}, "Locale::CLDR::WeekData": {"file": "lib/Locale/CLDR/WeekData.pm", "version": "v0.46.0"}}, "release_status": "stable", "resources": {"bugtracker": {"web": "https://github.com/ThePilgrim/perlcldr/issues"}, "homepage": "https://github.com/ThePilgrim/perlcldr", "repository": {"url": "https://github.com/ThePilgrim/perlcldr.git"}}, "version": "v0.46.0", "x_serialization_backend": "JSON::PP version 4.16"}