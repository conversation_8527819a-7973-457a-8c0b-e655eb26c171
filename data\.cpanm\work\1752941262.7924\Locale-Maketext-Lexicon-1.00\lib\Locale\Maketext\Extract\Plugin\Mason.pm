package Locale::Maketext::Extract::Plugin::<PERSON>;
$Locale::Maketext::Extract::Plugin::Mason::VERSION = '1.00';
use strict;
use base qw(Locale::Maketext::Extract::Plugin::Base);

# ABSTRACT: HTML::<PERSON> (aka <PERSON> 1) and <PERSON> (aka <PERSON> 2) format parser


sub file_types {
    return qw( * );
}

sub extract {
    my $self = shift;
    local $_ = shift;

    my $line = 1;

    # HTML::<PERSON> (aka Mason 1)
    while (m!\G(.*?<&\|[ ]*/l(?:oc)?(?:[ ]*,[ ]*(.*?))?[ ]*&>(.*?)</&>)!sg) {
        my ( $vars, $str ) = ( $2, $3 );
        $line += ( () = ( $1 =~ /\n/g ) );    # cryptocontext!
        $self->add_entry( $str, $line, $vars );
    }

    # Mason (aka Mason 2)
    while (
        m!\G(.*?<%[ ]*(?:\$(?:\.|self->))fl(?:oc)?(?:[ ]*\((.*?)\))?[ ]*{[ ]*%>(.*?)</%>)!sg
        )
    {
        my ( $vars, $str ) = ( $2, $3 );
        $line += ( () = ( $1 =~ /\n/g ) );    # cryptocontext!
        $self->add_entry( $str, $line, $vars );
    }

}


1;

__END__

=pod

=encoding UTF-8

=head1 NAME

Locale::Maketext::Extract::Plugin::Mason - HTML::Mason (aka Mason 1) and Mason (aka Mason 2) format parser

=head1 VERSION

version 1.00

=head1 SYNOPSIS

    $plugin = Locale::Maketext::Extract::Plugin::Mason->new(
        $lexicon            # A Locale::Maketext::Extract object
        @file_types         # Optionally specify a list of recognised file types
    )

    $plugin->extract($filename,$filecontents);

=head1 DESCRIPTION

Extracts strings to localise from Mason files.

=head1 SHORT PLUGIN NAME

    mason

=head1 VALID FORMATS

HTML::Mason (aka Mason 1)
 Strings inside <&|/l>...</&> and <&|/loc>...</&> are extracted.

Mason (aka Mason 2)
Strings inside <% $.floc { %>...</%> or <% $.fl { %>...</%> or
<% $self->floc { %>...</%> or <% $self->fl { %>...</%> are extracted.

=head1 KNOWN FILE TYPES

=over 4

=item All file types

=back

=head1 SEE ALSO

=over 4

=item L<xgettext.pl>

for extracting translatable strings from common template
systems and perl source files.

=item L<Locale::Maketext::Lexicon>

=item L<Locale::Maketext::Extract::Plugin::Base>

=item L<Locale::Maketext::Extract::Plugin::FormFu>

=item L<Locale::Maketext::Extract::Plugin::Perl>

=item L<Locale::Maketext::Extract::Plugin::TT2>

=item L<Locale::Maketext::Extract::Plugin::YAML>

=item L<Locale::Maketext::Extract::Plugin::TextTemplate>

=item L<Locale::Maketext::Extract::Plugin::Generic>

=back

=head1 AUTHORS

Audrey Tang E<lt><EMAIL><gt>

=head1 COPYRIGHT

Copyright 2002-2013 by Audrey Tang E<lt><EMAIL><gt>.

This software is released under the MIT license cited below.

=head2 The "MIT" License

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
DEALINGS IN THE SOFTWARE.

=head1 AUTHORS

=over 4

=item *

Clinton Gormley <<EMAIL>>

=item *

Audrey Tang <<EMAIL>>

=back

=head1 COPYRIGHT AND LICENSE

This software is Copyright (c) 2014 by Audrey Tang.

This is free software, licensed under:

  The MIT (X11) License

=cut
