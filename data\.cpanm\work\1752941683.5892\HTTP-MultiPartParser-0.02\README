NAME
    HTTP::MultiPartParser - HTTP MultiPart Parser

SYNOPSIS
        $parser = HTTP::MultiPartParser->new(
            boundary  => $boundary,
            on_header => $on_header,
            on_body   => $on_body,
        );
    
        while ($octets = read_octets_from_body()) {
            $parser->parse($octets);
        }
    
        $parser->finish;

DESCRIPTION
    This class provides a low-level API for processing MultiPart MIME data
    streams conforming to MultiPart types as defined in RFC 2616
    <http://tools.ietf.org/html/rfc2616#section-3.7.2>.

METHODS
  new
        $parser = HTTP::MultiPartParser->new( %attributes );

    This constructor returns a instance of "HTTP::MultiPartParser". Valid
    attributes inculde:

    *   "boundary" (Mandatory)

            boundary => $value

        The unquoted and unescaped *boundary* parameter value from the
        Content-Type header field. The *boundary* parameter value consist of
        a restricted set of characters as defined in RFC 2046
        <http://tools.ietf.org/html/rfc2046#section-5.1.1>.

            DIGIT / ALPHA / "'" / "(" / ")" /
            "+" / "_" / "," / "-" / "." /
            "/" / ":" / "=" / "?"

    *   "on_header" (Mandatory)

            on_header => $callback->($header)

        This callback will be invoked when the header of a part has
        successfully been received. The callback will only be invoked once
        for each part.

    *   "on_header_as"

            on_header_as => 'unparsed' | 'lines'

        Defines the $header value for the "on_header" callback.

        *   "unparsed"

            Callback invoked with an octet string containing the unparsed
            header.

        *   "lines"

            Callback invoked with an ARRAY reference, where each element of
            the array is a header line. Folding whitespace is removed from
            all lines and header continuation lines are unwrapped.

    *   "on_body" (Mandatory)

            on_body => $callback->($chunk, $final)

        This callback will be invoked when there is any data available for
        the body of a part. The callback may be invoked multiple times for
        each part.

    *   "on_error"

            on_error => $callback->($message)

        This callback will be invoked anytime an error occurs in the parser.
        After receiving an error the parser is no longer useful in its
        current state.

    *   "max_preamble_size"

            max_preamble_size => 32768

    *   "max_header_size"

            max_header_size => 32768

  parse
        $parser->parse($octets);

    Parses the given octets.

  finish
        $parser->finish;

    Finish the parsing.

  reset
        $parser->reset;

    Resets the state of the parser.

  is_aborted
        $boolean = $parser->is_aborted;

    Returns true if an error has occurred in the parser.

DIAGNOSTICS
    (F) Usage: %s
        Method called with wrong number of arguments.

SEE ALSO
    RFC 2616 section 3.7.2 Multipart Types
    <http://tools.ietf.org/html/rfc2616#section-3.7.2>
    RFC 2046 section 5.1.1 Common Syntax
    <http://tools.ietf.org/html/rfc2046#section-5.1.1>
    RFC 2388 multipart/form-data <http://tools.ietf.org/html/rfc2388>

SUPPORT
  Bugs / Feature Requests
    Please report any bugs or feature requests through the issue tracker at
    <https://github.com/chansen/p5-http-multipartparser/issues>. You will be
    notified automatically of any progress on your issue.

  SOURCE CODE
    This is open source software. The code repository is available for
    public review and contribution under the terms of the license.

    <httsp://github.com/chansen/p5-http-multipartparser>

        git clone https://github.com/chansen/p5-http-multipartparser

AUTHOR
    Christian Hansen "<EMAIL>"

COPYRIGHT
    Copyright 2012-2017 by Christian Hansen.

    This is free software; you can redistribute it and/or modify it under
    the same terms as the Perl 5 programming language system itself.

